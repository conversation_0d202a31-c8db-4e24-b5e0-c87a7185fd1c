// Styles cho customer-info component
:host {
  display: block;
}

// Custom styling cho panel expansion
mat-expansion-panel {
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1) !important;
}

// Các label được highlight để chú ý
.mat-form-field-label {
  font-weight: 500;
}

// Style cho customer suggestion trong autocomplete
.fw-bold {
  font-weight: 600;
}

.small {
  font-size: 0.85rem;
}

// Style cho textarea
textarea {
  resize: vertical;
  min-height: 80px;
}

.customer-details {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .score-display {
    .badge {
      font-size: 0.9rem;
      padding: 6px 12px !important;
    }
  }

  .statistics {
    .statistic-item {
      min-width: 80px;
    }
  }

  .order-info {
    font-size: 0.9rem;
  }

  .tags {
    mat-chip {
      font-size: 0.85rem;
    }

    .mat-success {
      background-color: #4caf50;
      color: white;
    }

    .mat-warn {
      background-color: #f44336;
      color: white;
    }

    .mat-primary {
      background-color: #3f51b5;
      color: white;
    }
  }
}
