<h2 mat-dialog-title>{{ dialogTitle }}</h2>

<form [formGroup]="categoryForm" (ngSubmit)="onSubmit()">
  <div mat-dialog-content>
    <div class="row">
      <!-- Tên danh mục -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Tên danh mục</mat-label>
          <input matInput formControlName="name" required>
          <mat-error *ngIf="categoryForm.get('name')?.hasError('required')">
            Tên danh mục là bắt buộc
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Mã danh mục -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Mã danh mục</mat-label>
          <input matInput formControlName="code" required>
          <mat-error *ngIf="categoryForm.get('code')?.hasError('required')">
            Mã danh mục là bắt buộc
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Danh mục cha -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Danh mục cha</mat-label>
          <mat-select formControlName="parentId">
            <mat-option [value]="''">Không có</mat-option>
            <mat-option *ngFor="let category of data.parentCategories" [value]="category._id">
              {{ category.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Mô tả -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Mô tả</mat-label>
          <textarea matInput formControlName="description" rows="3"></textarea>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div mat-dialog-actions align="end">
    <button type="button" mat-button (click)="onNoClick()">Hủy</button>
    <button type="submit" mat-raised-button color="primary">Lưu</button>
  </div>
</form>
