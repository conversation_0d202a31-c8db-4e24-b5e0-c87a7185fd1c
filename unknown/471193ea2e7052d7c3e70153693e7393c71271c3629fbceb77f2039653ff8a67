  <!-- <PERSON><PERSON> chuyển đổi chế độ nhập -->

<button mat-icon-button
  class="toggle-mode-btn"
  type="button"
  [matTooltip]="settings.useManual ? 'INPUT_PLACE.USE_GOOGLE_MAPS' : 'INPUT_PLACE.USE_MANUAL' | translate"
  (click)="toggleInputMode()">
  <mat-icon>{{ settings.useManual ? 'map' : 'edit_location' }}</mat-icon>
</button>

<!-- Container cho toàn bộ component -->

<div class="input-place-container">
  <!-- Hiển thị input-address khi không dùng manual -->
  <ng-container *ngIf="!settings.useManual">
    <app-input-address
      [placeholder]="placeholder"
      [oldPlaces]="oldPlaces"
      [defaultValue]="value()"
      (selectedPlace)="onSelectedPlace($event)"
      (changingPlace)="onChangingPlace($event)">
    </app-input-address>
  </ng-container>

  <!-- Hi<PERSON><PERSON> thị address-manual-selector khi dùng manual -->
  <ng-container *ngIf="settings.useManual">
    <app-address-manual-selector
      [defaultValue]="value()"
      (selectedPlace)="onSelectedPlace($event)"
      (changingPlace)="onChangingPlace($event)">
    </app-address-manual-selector>
  </ng-container>
</div>


@if(value()?.resolvedAddress) {
  <div class="text-muted mb-4">
    <small>{{ value()?.resolvedAddress }}</small>
  </div>
}

@if(!hasLatitude() && (defaultValue?.mainAddress || defaultValue?.fullAddress)) {
  <div class="text-muted mb-4 px-2">
    <small>Địa chỉ sử dụng cuối cùng: <b>{{ defaultValue?.mainAddress ?? defaultValue?.fullAddress }}</b></small>
  </div>
}

@if(hasInstruction()) {
  <div class="text-muted mb-4 px-2">
    <small>{{ value()?.instruction }}</small>
  </div>
}

