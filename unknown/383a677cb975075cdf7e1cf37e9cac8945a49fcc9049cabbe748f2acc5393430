export enum LocationType {
  Warehouse = 'Warehouse',
  Zone = 'Zone',
  Rack = 'Rack',
  Shelf = 'Shelf',
  Bin = 'Bin',
}

export enum LocationStatus {
  Active = 'Active',
  Inactive = 'Inactive',
  Reserved = 'Reserved',
}

export interface Dimensions {
  length: number;
  width: number;
  height: number;
  depth: number;
}

export interface Location {
  id: string;
  name: string;
  code: string;
  type: LocationType;
  parentId: string | null;
  capacity: number;
  dimensions: Dimensions;
  status: LocationStatus;
  level: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface LocationFormData {
  id?: string;
  name: string;
  code: string;
  autoGenerateCode: boolean;
  type: LocationType;
  parentId: string | null;
  capacity: number;
  dimensions: Dimensions;
  status: LocationStatus;
  quantity: number;
  children?: LocationFormData[];
}
