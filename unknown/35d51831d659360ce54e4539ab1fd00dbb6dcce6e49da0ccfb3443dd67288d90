import { Injectable } from '@angular/core';
import { GoodsReceipt } from '../../models/api/goods-receipt.dto';

/**
 * Interface mở rộng GoodsReceipt để thêm trường savedAt
 */
interface GoodsReceiptWithMeta extends GoodsReceipt {
  savedAt?: Date;
}

@Injectable()
export class ActionButtonsService {
  constructor() {}

  /**
   * Chuẩn bị dữ liệu để lưu
   * @param formData Dữ liệu form
   * @param status Trạng thái ('draft' hoặc 'completed')
   * @returns Dữ liệu đã chuẩn bị
   */
  prepareDataForSave(formData: GoodsReceipt, status: 'draft' | 'completed'): GoodsReceipt {
    if (!formData) return {} as GoodsReceipt;

    // Tạo bản sao của dữ liệu và chuyển đổi thành GoodsReceiptWithMeta
    const data = { ...formData } as GoodsReceiptWithMeta;

    // Đặt trạng thái
    data.status = status;

    // Thêm thời gian lưu (metadata, không phải là một phần của GoodsReceiptFormData)
    data.savedAt = new Date();

    // Trả về dữ liệu đã chuẩn bị (loại bỏ các trường metadata)
    const { savedAt, ...result } = data;

    // Log thông tin để debug
    console.log('Prepared data for save:', { ...result, savedAt });

    return result;
  }

  /**
   * Kiểm tra thông tin cơ bản
   * @param formData Dữ liệu form
   * @returns True nếu thông tin cơ bản hợp lệ
   */
  validateBasicInfo(formData: GoodsReceipt): boolean {
    console.log(formData);
    if (!formData) return false;

    // Kiểm tra các trường bắt buộc cơ bản
    return !!(
      formData.createdBy &&
      formData.createdBy._id &&
      formData.receivedAt &&
      formData.supplier &&
      formData.supplier._id
    );
  }

  /**
   * Kiểm tra dữ liệu đầy đủ trước khi hoàn thành
   * @param formData Dữ liệu form
   * @returns Kết quả kiểm tra
   */
  validateCompleteData(formData: GoodsReceipt): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Kiểm tra thông tin cơ bản
    if (!this.validateBasicInfo(formData)) {
      errors.push('Thông tin cơ bản không đầy đủ');
    }

    // Kiểm tra danh sách sản phẩm
    if (!formData.items || formData.items.length === 0) {
      errors.push('Chưa có sản phẩm nào');
    }

    // Kiểm tra thông tin thanh toán
    if (!formData.payment || !formData.payment.initialPayments || formData.payment.initialPayments.length === 0) {
      errors.push('Chưa có thông tin thanh toán');
    }

    // Kiểm tra nếu có thanh toán bằng chuyển khoản thì phải có tài khoản ngân hàng
    if (formData.payment && formData.payment.initialPayments) {
      const bankTransferPayment = formData.payment.initialPayments.find(p => p.method === 'bank_transfer');
      if (bankTransferPayment && !bankTransferPayment.bankAccountId) {
        errors.push('Có thanh toán chuyển khoản nhưng chưa chọn tài khoản ngân hàng');
      }
    }

    // Kiểm tra nếu có công nợ thì phải có ngày hẹn thanh toán
    if (formData.payment &&
        formData.payment.debt &&
        formData.payment.debt.debtAmount > 0 &&
        !formData.payment.debt.dueDate) {
      errors.push('Chưa chọn ngày hẹn thanh toán');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
