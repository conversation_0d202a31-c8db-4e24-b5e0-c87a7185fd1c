<div class="location-dialog">
  <h2 mat-dialog-title>{{ 'WAREHOUSE.SELECT_LOCATION' | translate }}</h2>

  <div id="location-dialog-description" class="sr-only">
    {{ 'WAREHOUSE.LOCATION_PICKER_MODAL.LOCATION_DIALOG_DESCRIPTION' | translate }}
  </div>

  <mat-dialog-content>
    <div class="location-tree-container">
      <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
        <!-- Node không có con -->
        <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding role="treeitem"
                       [attr.aria-selected]="selectedLocationId === node.id"
                       [class.selected]="selectedLocationId === node.id">
          <button mat-icon-button disabled></button>
          <div class="node-wrapper" (click)="selectLocation(node.id)">
            <mat-icon class="location-icon">place</mat-icon>
            <span class="location-name">{{ node.name }}</span>
          </div>
        </mat-tree-node>

        <!-- Node có con -->
        <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding role="treeitem"
                       [attr.aria-expanded]="treeControl.isExpanded(node)"
                       [attr.aria-selected]="selectedLocationId === node.id"
                       [class.selected]="selectedLocationId === node.id">
          <button mat-icon-button matTreeNodeToggle
                  [attr.aria-label]="'toggle ' + node.name">
            <mat-icon class="mat-icon-rtl-mirror">
              {{treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right'}}
            </mat-icon>
          </button>
          <div class="node-wrapper" (click)="selectLocation(node.id)">
            <mat-icon class="location-icon">folder</mat-icon>
            <span class="location-name">{{ node.name }}</span>
          </div>
        </mat-tree-node>
      </mat-tree>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()" cdkFocusInitial>
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" (click)="onSave()" [disabled]="!selectedLocationId">
      {{ 'COMMON.SELECT' | translate }}
    </button>
  </mat-dialog-actions>
</div>
