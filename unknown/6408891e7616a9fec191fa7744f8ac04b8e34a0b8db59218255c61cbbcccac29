import { Coordinates } from 'salehub_shared_contracts';

export function getGoogleMapDirectionUrl(addr1: Coordinates, addr2: Coordinates) {
  return `https://www.google.com/maps/dir/?api=1&origin=${addr1.lat},${addr1.lng}&origin_place_id=ChIJpV4FPvD3NjEResWuayk6kyw&destination=${addr2.lat},${addr2.lng}&travelmode=driving`;
}

export function getGoogleMapDisplayUrl(addr1: Coordinates) {
  return `https://www.google.com/maps/@?api=1&map_action=map&center=${addr1.lat},${addr1.lng}&zoom=19&basemap=roadmap&layer=traffic`;
}

export function getGoogleMapStreetViewUrl(addr1: Coordinates) {
  return `https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=${addr1.lat},${addr1.lng}&heading=-45&pitch=38&fov=80`;
}

export function getIframeMap(addr1: Coordinates, addr2: Coordinates) {
  return `https://directionsdebug.firebaseapp.com/embed.html?origin=${addr1.lat}%2C${addr1.lng}&destination=${addr2.lat}%2C${addr2.lng}&mode=driving`;
}
