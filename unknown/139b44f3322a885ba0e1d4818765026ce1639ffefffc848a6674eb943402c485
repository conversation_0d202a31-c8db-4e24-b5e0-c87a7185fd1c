import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import {
  LocationFormData,
  LocationType,
  LocationStatus,
  Dimensions,
  Location
} from '../../models/api/location.dto';
import { LocationService } from '../../services/location.service';
import { LocationChildFormComponent } from '../location-child-form/location-child-form.component';
import { LocationFormPreviewComponent } from '../location-form-preview/location-form-preview.component';

@Component({
  selector: 'app-location-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    LocationChildFormComponent,
    LocationFormPreviewComponent
  ],
  templateUrl: './location-form.component.html',
  styleUrls: ['./location-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LocationFormComponent implements OnInit, OnDestroy {
  @Input() editMode = false;
  @Input() locationId: string | null = null;
  @Input() parentId: string | null = null;
  @Input() currentLevel = 1;

  @Output() formSubmit = new EventEmitter<LocationFormData>();
  @Output() formCancel = new EventEmitter<void>();

  locationForm!: FormGroup;
  parentLocations: Location[] = [];
  locationTypes = Object.values(LocationType);
  locationStatuses = Object.values(LocationStatus);
  formReady = false;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private locationService: LocationService
  ) {
    // Dependencies injection constructor
  }

  ngOnInit(): void {
    this.initForm();
    this.locationForm.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {
      // Form data thay đổi, preview sẽ cập nhật thông qua binding
    });
    this.loadParentLocations();

    if (this.editMode && this.locationId) {
      this.loadLocationData();
    }

    // Xử lý tự sinh mã
    this.locationForm.get('autoGenerateCode')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(autoGenerate => {
        const codeControl = this.locationForm.get('code');
        if (autoGenerate) {
          codeControl?.disable();
          this.generateCode();
        } else {
          codeControl?.enable();
        }
      });

    // Xử lý thay đổi loại vị trí
    this.locationForm.get('type')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.locationForm.get('autoGenerateCode')?.value) {
          this.generateCode();
        }
      });

    // Xử lý thay đổi vị trí cha
    this.locationForm.get('parentId')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.locationForm.get('autoGenerateCode')?.value) {
          this.generateCode();
        }
      });

    this.formReady = true;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.locationForm = this.fb.group({
      name: ['', Validators.required],
      code: ['', Validators.required],
      autoGenerateCode: [true],
      type: [this.getCurrentLevelLocationType(), Validators.required],
      parentId: [this.parentId],
      capacity: [0, [Validators.required, Validators.min(0)]],
      dimensions: this.fb.group({
        length: [0, [Validators.required, Validators.min(0)]],
        width: [0, [Validators.required, Validators.min(0)]],
        height: [0, [Validators.required, Validators.min(0)]],
        depth: [0, [Validators.required, Validators.min(0)]]
      }),
      status: [LocationStatus.Active, Validators.required],
      quantity: [1, [Validators.required, Validators.min(1)]],
      children: this.fb.array([])
    });
  }

  private getCurrentLevelLocationType(): LocationType {
    switch (this.currentLevel) {
    case 1: return LocationType.Warehouse;
    case 2: return LocationType.Zone;
    case 3: return LocationType.Rack;
    case 4: return LocationType.Shelf;
    case 5: return LocationType.Bin;
    default: return LocationType.Warehouse;
    }
  }

  private loadParentLocations(): void {
    this.locationService.getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe(locations => {
        // Lọc ra các vị trí có thể là cha (level < 5)
        this.parentLocations = locations.filter(loc => loc.level < 5);
      });
  }

  private loadLocationData(): void {
    if (!this.locationId) return;

    this.locationService.getLocationById(this.locationId)
      .pipe(takeUntil(this.destroy$))
      .subscribe(location => {
        if (location) {
          this.locationForm.patchValue({
            name: location.name,
            code: location.code,
            autoGenerateCode: false,
            type: location.type,
            parentId: location.parentId,
            capacity: location.capacity,
            dimensions: location.dimensions,
            status: location.status,
            quantity: 1
          });

          // Nếu đang edit thì disable số lượng
          this.locationForm.get('quantity')?.disable();
        }
      });
  }

  private generateCode(): void {
    const type = this.locationForm.get('type')?.value;
    const parentId = this.locationForm.get('parentId')?.value;

    let parentCode = '';
    if (parentId) {
      const parent = this.parentLocations.find(loc => loc.id === parentId);
      parentCode = parent?.code || '';
    }

    const generatedCode = this.locationService.generateLocationCode(parentCode, type);
    this.locationForm.get('code')?.setValue(generatedCode);
  }

  // Helper getters
  get childrenFormArray(): FormArray {
    return this.locationForm.get('children') as FormArray;
  }

  get showChildren(): boolean {
    return this.childrenFormArray.length > 0;
  }

  getChildFormGroup(i: number): FormGroup {
    return this.childrenFormArray.at(i) as FormGroup;
  }

  addChild(): void {
    if (this.currentLevel < 5) {
      const childForm = this.createChildFormGroup();
      this.childrenFormArray.push(childForm);
    }
  }

  removeChild(index: number): void {
    this.childrenFormArray.removeAt(index);
  }

  private createChildFormGroup(): FormGroup {
    return this.fb.group({
      name: ['', Validators.required],
      code: ['', Validators.required],
      autoGenerateCode: [true],
      type: [this.getNextLevelLocationType(), Validators.required],
      capacity: [0, [Validators.required, Validators.min(0)]],
      dimensions: this.fb.group({
        length: [0, [Validators.required, Validators.min(0)]],
        width: [0, [Validators.required, Validators.min(0)]],
        height: [0, [Validators.required, Validators.min(0)]],
        depth: [0, [Validators.required, Validators.min(0)]]
      }),
      status: [LocationStatus.Active, Validators.required],
      quantity: [1, [Validators.required, Validators.min(1)]],
      children: this.fb.array([])
    });
  }

  private getNextLevelLocationType(): LocationType {
    switch (this.currentLevel) {
    case 1: return LocationType.Zone;
    case 2: return LocationType.Rack;
    case 3: return LocationType.Shelf;
    case 4: return LocationType.Bin;
    default: return LocationType.Zone;
    }
  }

  onSubmit(): void {
    if (this.locationForm.invalid) {
      this.markFormGroupTouched(this.locationForm);
      return;
    }

    const formValue = this.locationForm.getRawValue();
    this.formSubmit.emit(formValue);
  }

  onCancelClick(): void {
    this.formCancel.emit();
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }

      if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          }
        });
      }
    });
  }

  getLocationTypeLabel(type: LocationType): string {
    return `WAREHOUSE.LOCATION.TYPE.${type.toUpperCase()}`;
  }

  getLocationStatusLabel(status: LocationStatus): string {
    return `WAREHOUSE.LOCATION.STATUS.${status.toUpperCase()}`;
  }
}
