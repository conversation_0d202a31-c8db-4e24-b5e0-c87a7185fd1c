import { WarehouseLocation } from '@shared/models/api/warehouse-entities.dto';

/**
 * Dữ liệu đầu vào cho WarehouseLocationPickerModal
 */
export interface WarehouseLocationPickerModalData {
  /**
   * <PERSON>h sách vị trí kho (tùy chọn)
   */
  locations?: WarehouseLocation[];

  /**
   * ID vị trí đã chọn (tùy chọn)
   */
  selectedLocationId?: string;

  /**
   * ID kho hàng để lọc vị trí
   */
  warehouseId: string;
}

/**
 * Kết quả trả về từ WarehouseLocationPickerModal
 */
export type WarehouseLocationPickerModalResult = string | undefined;
