/* Styles cho component thông tin tài chính */
:host {
  display: block;
  width: 100%;
}

/* Styles cho hàng có thể click */
.clickable-row {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

/* Styles cho icon nhỏ */
.small-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

/* Styles cho nút nhỏ */
.small-button {
  width: 24px;
  height: 24px;
  line-height: 24px;
  margin-left: 4px;
}

/* Styles cho container đã thanh toán */
.amount-paid-container {
  position: relative;

  .full-payment-btn {
    position: absolute;
    right: 0;
    top: 0;
    height: 42px;
    margin-top: 4px;
    margin-right: 4px;
    z-index: 1;
  }
}

/* Styles cho hàng công nợ */
.debt-row {
  font-size: 1.1rem;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .amount-paid-container {
    .full-payment-btn {
      position: static;
      width: 100%;
      margin-top: 8px;
    }
  }
}
