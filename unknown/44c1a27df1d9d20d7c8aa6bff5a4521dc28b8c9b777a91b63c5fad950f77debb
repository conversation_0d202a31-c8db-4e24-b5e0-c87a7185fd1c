.variant-selector-container {
  padding: 16px;
  max-height: 80vh;
  overflow-y: auto;

  .attributes-section {
    margin-bottom: 24px;

    .attribute-group {
      margin-bottom: 16px;

      .group-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
        color: #333;
      }

      .attribute-values {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .attribute-button {
          min-width: 60px;
          padding: 4px 12px;
          border-radius: 4px;

          &.selected {
            background-color: #1976d2;
            color: white;
          }
        }
      }
    }
  }

  .units-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 12px;
      color: #333;
    }

    .unit-values {
      .unit-radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .unit-radio-button {
          margin-bottom: 8px;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;
  }
}
