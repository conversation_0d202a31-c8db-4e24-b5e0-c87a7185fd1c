import { WarehouseList, CategoryList, BrandList } from 'salehub_shared_contracts/requests/shared/list';
import { OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { ProductList } from 'salehub_shared_contracts/requests/shared/product';

/**
 * Dữ liệu đầu vào cho OrderProductPickerModal
 */
export interface OrderProductPickerModalData {
  // Danh sách sản phẩm để hiển thị
  list: ProductList;

  // Danh sách các sản phẩm đã được chọn
  data?: OrderItemBaseDetails[];

  // Từ khóa tìm kiếm
  searchTerm?: string;

  // Cài đặt cho phép bán khi hết hàng
  settings?: { allowSellWhenOutOfStock: boolean };

  // Kho được chọn để hiển thị tồn kho
  selectedWarehouseId?: string | null;

  // Danh sách kho
  warehouseList?: WarehouseList;

  // Danh sách danh mục
  categoryList?: CategoryList;

  // Danh sách thương hiệu
  brandList?: BrandList;
}

/**
 * Kết quả trả về từ OrderProductPickerModal
 */
export type OrderProductPickerModalResult = OrderItemBaseDetails[] | null;
