<mat-card class="mb-3">
  <mat-card-content>
    <h5 class="mb-3">{{ 'WAREHOUSE.GOODS_RECEIPT.FINANCIAL.TITLE' | translate }}</h5>

    <div [formGroup]="financialForm">
      <!-- Mã phiếu -->
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.FINANCIAL.RECEIPT_NUMBER' | translate }}</mat-label>
        <input matInput formControlName="receiptNumber" placeholder="{{ 'WAREHOUSE.GOODS_RECEIPT.FINANCIAL.RECEIPT_NUMBER_PLACEHOLDER' | translate }}">
      </mat-form-field>

      <!-- Tổng tiền hàng -->
      <div class="d-flex justify-content-between align-items-center mb-2">
        <span>{{ 'WAREHOUSE.GOODS_RECEIPT.FINANCIAL.SUB_TOTAL' | translate }}:</span>
        <strong>{{ subTotal | number }} VND</strong>
      </div>

      <!-- <PERSON><PERSON><PERSON><PERSON> giá tổng -->
      <mat-form-field appearance="outline" class="w-100 mb-2">
        <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.FINANCIAL.TOTAL_DISCOUNT' | translate }}</mat-label>
        <input matInput type="number" min="0" formControlName="totalDiscount">
        <span matSuffix>VND</span>
      </mat-form-field>

      <!-- Chi phí khác (trả cho NCC) -->
      <app-supplier-costs
        [additionalCosts]="additionalCosts"
        [subTotal]="subTotal"
        (additionalCostsChange)="onAdditionalCostsChange($event)">
      </app-supplier-costs>

      <!-- Chi phí nhập khác -->
      <app-other-costs
        [additionalCosts]="additionalCosts"
        [subTotal]="subTotal"
        (additionalCostsChange)="onAdditionalCostsChange($event)">
      </app-other-costs>

      <!-- Thuế -->
      <app-taxes
        [taxes]="taxes"
        [subTotal]="subTotal"
        (taxesChange)="onTaxesChange($event)">
      </app-taxes>

      <hr>

      <!-- Cần trả nhà cung cấp -->
      <div class="d-flex justify-content-between align-items-center mb-3">
        <span class="fw-bold">{{ 'WAREHOUSE.GOODS_RECEIPT.FINANCIAL.SUPPLIER_PAYMENT' | translate }}:</span>
        <strong class="fs-5">{{ supplierPayment | number }} VND</strong>
      </div>

      <!-- Thông tin thanh toán -->
      <app-payment
        [supplierPayment]="supplierPayment"
        [paymentForm]="financialForm"
        [bankAccounts]="bankAccounts"
        (dataChange)="onPaymentChange($event)">
      </app-payment>
    </div>
  </mat-card-content>
</mat-card>

<!-- Các nút chức năng -->
<app-action-buttons
  [isValid]="financialForm.valid"
  [formData]="getFormData()"
  (saveDraft)="onSaveDraft($event)"
  (complete)="onComplete($event)">
</app-action-buttons>
