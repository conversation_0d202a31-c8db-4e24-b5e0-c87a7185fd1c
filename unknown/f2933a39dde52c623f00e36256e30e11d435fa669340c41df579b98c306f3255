.serial-number-modal {
  min-width: 500px;
  max-width: 800px;
  padding: 0;
}

.product-info {
  margin-bottom: 20px;

  h3 {
    margin-bottom: 5px;
    font-size: 18px;
    font-weight: 500;
  }

  .sku {
    color: #666;
    margin: 0;
    font-size: 14px;
  }
}

.serial-count {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  p {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
  }
}

.serial-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.serial-header {
  display: flex;
  padding: 10px 16px;
  background-color: #f5f5f5;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;

  .serial-number {
    flex: 1;
  }

  .serial-status {
    flex: 1;
  }
}

.serial-row {
  display: flex;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .serial-number {
    flex: 1;
    padding-right: 8px;
  }

  .serial-status {
    flex: 1;
    padding-left: 8px;
  }
}

.modal-content {
  padding: 16px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
}

/* Responsive styles */
@media (max-width: 600px) {
  .serial-number-modal {
    min-width: 300px;
  }

  .serial-row {
    flex-direction: column;
    padding: 16px;

    .serial-number,
    .serial-status {
      width: 100%;
      padding: 0;
    }
  }

  .serial-header {
    display: none;
  }
}
