import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { TranslateModule } from '@ngx-translate/core';
import { Observable, Subject, takeUntil } from 'rxjs';

import { BasicInfoService } from './basic-info.service';
import { EmbeddedEmployee as Employee, EmbeddedSupplier as Supplier, EmbeddedWarehouse as Warehouse } from '@shared/models/api/entity-embedded.dto';
import { GoodsReceipt } from '../../models/api/goods-receipt.dto'

/**
 * Component thông tin cơ bản
 */
@Component({
  selector: 'app-basic-info',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatAutocompleteModule,
    TranslateModule
  ],
  templateUrl: './basic-info.component.html',
  styleUrls: ['./basic-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BasicInfoComponent implements OnInit, OnDestroy {
  /**
   * Danh sách nhân viên
   */
  @Input() employeeList: Employee[] = [];

  /**
   * Danh sách kho hàng
   */
  @Input() warehouseList: Warehouse[] = [];

  /**
   * Danh sách nhà cung cấp
   */
  @Input() supplierList: Supplier[] = [];

  /**
   * Dữ liệu thông tin cơ bản ban đầu
   */
  @Input() initialData?: Partial<GoodsReceipt>;

  /**
   * Sự kiện khi dữ liệu thay đổi
   */
  @Output() dataChange = new EventEmitter<Partial<GoodsReceipt>>();

  /**
   * Sự kiện khi kho hàng thay đổi
   */
  @Output() warehouseChanged = new EventEmitter<string>();

  /**
   * Form thông tin cơ bản
   */
  basicInfoForm!: FormGroup;

  /**
   * Danh sách nhà cung cấp đã lọc
   */
  filteredSuppliers!: Observable<Supplier[]>;

  /**
   * Subject để hủy các subscription khi component bị hủy
   */
  private destroy$ = new Subject<void>();

  constructor(private basicInfoService: BasicInfoService) { }

  ngOnInit(): void {
    // Khởi tạo form
    this.initForm();

    // Thiết lập autocomplete cho nhà cung cấp
    this.setupSupplierAutocomplete();

    // Theo dõi thay đổi form
    this.watchFormChanges();

    // Cập nhật form với dữ liệu ban đầu nếu có
    if (this.initialData) {
      this.basicInfoService.patchFormData(this.basicInfoForm, this.initialData);
    }
  }

  /**
   * Khởi tạo form
   */
  private initForm(): void {
    this.basicInfoForm = this.basicInfoService.createBasicInfoForm();
  }

  /**
   * Thiết lập autocomplete cho nhà cung cấp
   */
  private setupSupplierAutocomplete(): void {
    this.filteredSuppliers = this.basicInfoService.setupSupplierAutocomplete(
      this.basicInfoForm,
      this.supplierList
    );
  }

  /**
   * Theo dõi thay đổi form
   */
  private watchFormChanges(): void {
    // Theo dõi thay đổi của toàn bộ form
    this.basicInfoForm.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(value => {
      this.dataChange.emit(value);
    });

    // Theo dõi thay đổi của warehouseId
    this.basicInfoForm.get('warehouseId')?.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(warehouseId => {
      if (warehouseId) {
        this.warehouseChanged.emit(warehouseId);
      }
    });
  }

  /**
   * Xử lý khi chọn nhà cung cấp từ autocomplete
   */
  onSupplierSelected(event: { option: { value: Supplier } }): void {
    console.log(event);
    const supplier = event.option.value;
    this.basicInfoService.handleSupplierSelection(this.basicInfoForm, supplier);
  }

  /**
   * Hiển thị tên nhà cung cấp trong autocomplete
   */
  displaySupplierFn = (supplier: Supplier | null): string => {
    return this.basicInfoService.displaySupplierFn(supplier);
  }

  /**
   * Lấy dữ liệu form
   */
  getFormData(): Partial<GoodsReceipt> {
    return this.basicInfoService.getFormData(this.basicInfoForm);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
