.child-form-container {
  padding: 1rem;
  margin: 1rem 0;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f5f5f5;
  position: relative;

  /* Override fix lỗi gạch dọc trong form fields */
  ::ng-deep {
    .mat-mdc-text-field-wrapper {
      .mdc-notched-outline {
        .mdc-notched-outline__leading {
          border-right-style: none !important;
          border-color: rgba(0, 0, 0, 0.12) !important;
        }

        .mdc-notched-outline__notch {
          border-left-style: none !important;
          border-right-style: none !important;
          border-color: rgba(0, 0, 0, 0.12) !important;
        }

        .mdc-notched-outline__trailing {
          border-left-style: none !important;
          border-color: rgba(0, 0, 0, 0.12) !important;
        }
      }
    }
  }

  .remove-button {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    z-index: 1;
  }

  .child-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .form-row {
      width: 100%;

      mat-form-field {
        width: 100%;
      }
    }

    .code-row {
      display: flex;
      align-items: center;
      gap: 1rem;

      mat-form-field {
        flex: 1;
      }

      .auto-code-checkbox {
        margin-top: -1rem;
      }
    }

    .dimensions-row {
      h4 {
        margin-bottom: 0.5rem;
        color: rgba(0, 0, 0, 0.6);
      }

      .dimensions-inputs {
        display: flex;
        gap: 1rem;

        mat-form-field {
          flex: 1;
        }
      }
    }
  }
}

// Responsive styles
@media screen and (max-width: 768px) {
  .child-form-container {
    .child-form {
      .dimensions-row {
        .dimensions-inputs {
          flex-direction: column;
          gap: 0;
        }
      }
    }
  }
}
