<h2 mat-dialog-title>
  {{ isEditMode ? 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.EDIT_TITLE' : 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.ADD_TITLE' | translate }}
</h2>

<div [ngClass]="{'mat-dialog-content': true}">
  <form [formGroup]="rejectForm" class="reject-form">
    <!-- Sản phẩm -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.PRODUCT' | translate }}</mat-label>
      <mat-select formControlName="_id">
        <mat-option *ngFor="let item of items" [value]="item._id">
          {{ getProductDisplayName(item) }}
        </mat-option>
      </mat-select>
      <mat-error *ngIf="rejectForm.get('_id')?.hasError('required')">
        {{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.PRODUCT_REQUIRED' | translate }}
      </mat-error>
    </mat-form-field>

    <!-- Số lượng -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.QUANTITY' | translate }}</mat-label>
      <input matInput type="number" min="1" [max]="selectedItemQuantity" formControlName="quantity"
        placeholder="{{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.QUANTITY_PLACEHOLDER' | translate }}">
      <mat-hint *ngIf="selectedItemQuantity > 0">
        {{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.RECEIVED_QUANTITY' | translate }}: {{ selectedItemQuantity }}
      </mat-hint>
      <mat-error *ngIf="rejectForm.get('quantity')?.hasError('required')">
        {{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.QUANTITY_REQUIRED' | translate }}
      </mat-error>
      <mat-error *ngIf="rejectForm.get('quantity')?.hasError('min')">
        {{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.QUANTITY_MIN' | translate }}
      </mat-error>
      <mat-error *ngIf="rejectForm.get('quantity')?.hasError('max')">
        {{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.QUANTITY_MAX' | translate: { max: selectedItemQuantity } }}
      </mat-error>
    </mat-form-field>

    <!-- Lý do -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.REASON' | translate }}</mat-label>
      <textarea matInput formControlName="reason"
        placeholder="{{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.REASON_PLACEHOLDER' | translate }}"
        rows="4" maxlength="500"></textarea>
      <mat-hint align="end">{{ rejectForm.get('reason')?.value?.length || 0 }}/500</mat-hint>
      <mat-error *ngIf="rejectForm.get('reason')?.hasError('required')">
        {{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.REASON_REQUIRED' | translate }}
      </mat-error>
      <mat-error *ngIf="rejectForm.get('reason')?.hasError('maxlength')">
        {{ 'WAREHOUSE.QUALITY_CHECK_REJECT_DIALOG.REASON_MAX_LENGTH' | translate }}
      </mat-error>
    </mat-form-field>
  </form>
</div>

<div [ngClass]="{'mat-dialog-actions': true}" align="end">
  <button mat-button (click)="cancel()">
    {{ 'COMMON.CANCEL' | translate }}
  </button>
  <button mat-raised-button color="primary" [disabled]="rejectForm.invalid" (click)="save()">
    {{ 'COMMON.SAVE' | translate }}
  </button>
</div>
