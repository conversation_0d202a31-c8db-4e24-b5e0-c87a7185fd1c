<h2 mat-dialog-title>{{ dialogTitle }}</h2>

<form [formGroup]="supplierForm" (ngSubmit)="onSubmit()">
  <div mat-dialog-content>
    <div class="row">
      <!-- Tên nhà cung cấp -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Tên nhà cung cấp</mat-label>
          <input matInput formControlName="name" required>
          <mat-error *ngIf="supplierForm.get('name')?.hasError('required')">
            Tên nhà cung cấp là bắt buộc
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Mã nhà cung cấp -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>M<PERSON> nhà cung cấp</mat-label>
          <input matInput formControlName="code" required>
          <mat-error *ngIf="supplierForm.get('code')?.hasError('required')">
            Mã nhà cung cấp là bắt buộc
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Người liên hệ -->
      <div class="col-md-6 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Người liên hệ</mat-label>
          <input matInput formControlName="contactPerson">
          <mat-icon matSuffix>person</mat-icon>
        </mat-form-field>
      </div>

      <!-- Số điện thoại -->
      <div class="col-md-6 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Số điện thoại</mat-label>
          <input matInput formControlName="phone">
          <mat-icon matSuffix>phone</mat-icon>
        </mat-form-field>
      </div>

      <!-- Email -->
      <div class="col-md-6 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Email</mat-label>
          <input matInput formControlName="email" type="email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="supplierForm.get('email')?.hasError('email')">
            Email không hợp lệ
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Mã số thuế -->
      <div class="col-md-6 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Mã số thuế</mat-label>
          <input matInput formControlName="taxCode">
        </mat-form-field>
      </div>

      <!-- Địa chỉ -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Địa chỉ</mat-label>
          <textarea matInput formControlName="address" rows="2"></textarea>
          <mat-icon matSuffix>location_on</mat-icon>
        </mat-form-field>
      </div>

      <!-- Điều khoản thanh toán -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Điều khoản thanh toán</mat-label>
          <textarea matInput formControlName="paymentTerm" rows="2"></textarea>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div mat-dialog-actions align="end">
    <button type="button" mat-button (click)="onNoClick()">Hủy</button>
    <button type="submit" mat-raised-button color="primary">Lưu</button>
  </div>
</form>
