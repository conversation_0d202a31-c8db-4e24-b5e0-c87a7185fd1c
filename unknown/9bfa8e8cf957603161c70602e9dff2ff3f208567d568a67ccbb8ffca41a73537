import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, FormArray, FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { LocationType, LocationStatus } from '../../models/api/location.dto';

@Component({
  selector: 'app-location-child-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './location-child-form.component.html',
  styleUrls: ['./location-child-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LocationChildFormComponent implements OnInit {
  @Input() formGroup!: FormGroup;
  @Input() level!: number;
  @Input() parentFormGroup!: FormGroup;
  @Output() removeChild = new EventEmitter<void>();

  locationTypes = Object.values(LocationType);
  locationStatuses = Object.values(LocationStatus);

  constructor() { /* Constructor dependency injection */ }

  ngOnInit() {
    // Đảm bảo children array đã được tạo trong form trước khi sử dụng
    if (!this.formGroup.get('children')) {
      // Tạo FormArray rỗng cho children nếu chưa tồn tại
      this.formGroup.addControl('children', new FormArray([]));
    }
  }

  get childrenFormArray(): FormArray | null {
    return this.formGroup?.get('children') as FormArray | null;
  }

  get showChildren(): boolean {
    return this.childrenFormArray != null && this.childrenFormArray.length > 0;
  }

  addChild(): void {
    if (this.level < 5 && this.childrenFormArray) {
      const childForm = this.createChildFormGroup();
      this.childrenFormArray.push(childForm);
    }
  }

  removeChildForm(index: number): void {
    if (this.childrenFormArray) {
      this.childrenFormArray.removeAt(index);
    }
  }

  getChildFormGroup(i: number): FormGroup | null {
    return this.childrenFormArray?.at(i) as FormGroup | null;
  }

  onRemoveChild(): void {
    this.removeChild.emit();
  }

  private createChildFormGroup(): FormGroup {
    // Create a form group without relying on FormBuilder
    const dimensionsGroup = new FormGroup({
      length: new FormControl(0, [Validators.required, Validators.min(0)]),
      width: new FormControl(0, [Validators.required, Validators.min(0)]),
      height: new FormControl(0, [Validators.required, Validators.min(0)]),
      depth: new FormControl(0, [Validators.required, Validators.min(0)])
    });

    return new FormGroup({
      name: new FormControl('', Validators.required),
      code: new FormControl('', Validators.required),
      autoGenerateCode: new FormControl(true),
      type: new FormControl(this.getNextLevelLocationType(), Validators.required),
      capacity: new FormControl(0, [Validators.required, Validators.min(0)]),
      dimensions: dimensionsGroup,
      status: new FormControl(LocationStatus.Active, Validators.required),
      quantity: new FormControl(1, [Validators.required, Validators.min(1)]),
      children: new FormArray([])
    });
  }

  private getNextLevelLocationType(): LocationType {
    switch (this.level) {
    case 2: return LocationType.Rack;
    case 3: return LocationType.Shelf;
    case 4: return LocationType.Bin;
    default: return LocationType.Zone;
    }
  }

  getLocationTypeLabel(type: LocationType): string {
    return `WAREHOUSE.LOCATION.TYPE.${type.toUpperCase()}`;
  }

  getLocationStatusLabel(status: LocationStatus): string {
    return `WAREHOUSE.LOCATION.STATUS.${status.toUpperCase()}`;
  }
}
