import { Pipe, PipeTransform } from '@angular/core';
import { formatMoney, formatPhoneNumber } from '@shared/utils';

@Pipe({
  standalone: true,
  name: 'formatString'
})
export class FormatStringPipe implements PipeTransform {
  transform(value: number | string | undefined, type?: 'money' | 'phone' | 'round'): string {
    if(!value) {
      return value as string;
    }

    if(type === 'money') {
      return formatMoney(value);
    }
    if(type === 'phone') {
      return formatPhoneNumber(value as string);
    }
    if(type === 'round') {
      return String(Math.round(Number(value)));
    }

    return value as string;
  }
}
