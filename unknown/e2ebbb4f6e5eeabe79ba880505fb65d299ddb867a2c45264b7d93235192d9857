.inventory-check-container {
  padding: 20px;
  height: 100%;
}

.left-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-list {
  margin-top: 10px;
  overflow-y: auto;
  max-height: calc(100vh - 250px);
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-list-header {
  display: flex;
  padding: 10px 12px;
  background-color: #f5f5f5;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 10px;

  .header-actions {
    min-width: 100px;
  }

  .header-info {
    flex: 1;
    padding: 0 10px;
  }

  .header-unit,
  .header-stock,
  .header-difference,
  .header-difference-value {
    width: 100px;
    text-align: center;
    padding: 0 8px;
  }

  .header-actual {
    width: 120px;
    text-align: center;
    padding: 0 8px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;

  p {
    margin-top: 10px;
    font-size: 16px;
  }
}

.right-panel {
  height: 100%;
}

.card {
  height: 100%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.summary-section {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;

  h5 {
    margin-bottom: 15px;
    font-weight: 500;
    color: #333;
  }
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.summary-row.total {
  font-weight: bold;
  border-bottom: none;
  margin-top: 15px;
  padding-top: 10px;
  border-top: 2px solid #e0e0e0;
}

.summary-value {
  text-align: right;
}

.quantity {
  font-weight: 500;
}

.value {
  color: #666;
  font-size: 0.9em;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  button {
    min-width: 120px;

    &:disabled {
      opacity: 0.7;
    }
  }
}

/* Styles for product rows (will be implemented later) */
.product-row {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;

  &:hover {
    background-color: #f9f9f9;
  }

  .product-info {
    flex: 1;

    .product-name {
      font-weight: 500;
    }

    .product-sku {
      font-size: 0.9em;
      color: #666;
    }
  }

  .product-unit,
  .product-stock,
  .product-actual,
  .product-difference,
  .product-difference-value {
    padding: 0 10px;
    text-align: center;
  }

  .product-actions {
    display: flex;

    button {
      margin-left: 5px;
    }
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .left-panel, .right-panel {
    height: auto;
  }

  .product-list {
    max-height: 500px;
  }

  .product-list-header {
    display: none;
  }
}

@media (max-width: 768px) {
  .inventory-check-container {
    padding: 10px;
  }

  .header-section {
    padding: 10px;
  }

  .action-buttons {
    flex-direction: column;

    button {
      margin-bottom: 10px;
      width: 100%;
    }
  }

  .product-row {
    flex-direction: column;
    align-items: flex-start;

    .product-unit,
    .product-stock,
    .product-actual,
    .product-difference,
    .product-difference-value {
      width: 100%;
      text-align: left;
      margin-bottom: 5px;
    }
  }
}
