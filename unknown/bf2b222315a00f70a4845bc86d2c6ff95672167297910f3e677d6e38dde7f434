<div class="location-list-container">
  <div class="location-list-header">
    <h1>{{ 'WAREHOUSE.LOCATION.LIST' | translate }}</h1>
    <div class="location-list-actions">
      <button mat-button color="primary" (click)="onAddLocation()">
        <mat-icon>add</mat-icon>
        {{ 'WAREHOUSE.LOCATION.ADD' | translate }}
      </button>
      <button mat-button color="accent" [disabled]="!selectedLocation()" (click)="onEditLocation()">
        <mat-icon>edit</mat-icon>
        {{ 'WAREHOUSE.LOCATION.EDIT' | translate }}
      </button>
      <button mat-button color="warn" [disabled]="!selectedLocation()" (click)="onDeleteLocation()">
        <mat-icon>delete</mat-icon>
        {{ 'WAREHOUSE.LOCATION.DELETE' | translate }}
      </button>
    </div>
  </div>

  <div class="location-list-content">
    <app-location-tree
      (selectLocation)="onSelectLocation($event)">
    </app-location-tree>
  </div>

  <div class="location-details" *ngIf="selectedLocation() && selectedLocation()?.data">
    <h2>{{ 'WAREHOUSE.LOCATION.DETAILS_LABEL' | translate }}</h2>
    <div class="detail-item">
      <span class="label">{{ 'WAREHOUSE.LOCATION.FORM.NAME' | translate }}:</span>
      <span class="value">{{ selectedLocation()?.data?.name || '-' }}</span>
    </div>
    <div class="detail-item">
      <span class="label">{{ 'WAREHOUSE.LOCATION.FORM.CODE' | translate }}:</span>
      <span class="value">{{ selectedLocation()?.data?.code || '-' }}</span>
    </div>
    <div class="detail-item">
      <span class="label">{{ 'WAREHOUSE.LOCATION.FORM.TYPE' | translate }}:</span>
      <span class="value">{{ 'WAREHOUSE.LOCATION.TYPE.' + (selectedLocation()?.data?.type || '') | translate }}</span>
    </div>
    <div class="detail-item">
      <span class="label">{{ 'WAREHOUSE.LOCATION.FORM.CAPACITY' | translate }}:</span>
      <span class="value">{{ selectedLocation()?.data?.capacity || 0 }} kg</span>
    </div>
    <div class="detail-item">
      <span class="label">{{ 'WAREHOUSE.LOCATION.FORM.DIMENSIONS' | translate }}:</span>
      <ng-container *ngIf="selectedLocation()?.data?.dimensions; else noDimensions">
        <span class="value">
          {{ selectedLocation()?.data?.dimensions?.length || 0 }} x
          {{ selectedLocation()?.data?.dimensions?.width || 0 }} x
          {{ selectedLocation()?.data?.dimensions?.height || 0 }} x
          {{ selectedLocation()?.data?.dimensions?.depth || 0 }} m
        </span>
      </ng-container>
      <ng-template #noDimensions>
        <span class="value">-</span>
      </ng-template>
    </div>
    <div class="detail-item">
      <span class="label">{{ 'WAREHOUSE.LOCATION.FORM.STATUS' | translate }}:</span>
      <span class="value">{{ 'WAREHOUSE.LOCATION.STATUS.' + (selectedLocation()?.data?.status || '') | translate }}</span>
    </div>
  </div>
</div>
