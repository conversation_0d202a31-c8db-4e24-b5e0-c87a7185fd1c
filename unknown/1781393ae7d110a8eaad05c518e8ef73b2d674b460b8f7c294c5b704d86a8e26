import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ImportAdditionalCost } from '../../models/api/goods-receipt.dto';

@Injectable()
export class OtherCostsService {
  constructor() {}

  /**
   * Tính tổng chi phí nhập khác
   * @param costs Danh sách chi phí
   * @returns Tổng chi phí
   */
  calculateTotalCost(costs: ImportAdditionalCost[]): number {
    return costs.reduce((total, cost) => {
      if (cost.costValue.type === 'fixed') {
        return total + (cost.costValue?.value || 0);
      } else {
        // Nếu là phần trăm, giá trị thực tế sẽ được tính trong component
        return total + (cost.costValue?.value || 0);
      }
    }, 0);
  }

  /**
   * Lấy danh sách tất cả chi phí có thể chọn
   * @returns Danh sách chi phí
   */
  getAllCosts(): Observable<ImportAdditionalCost[]> {
    // Mock data - trong thực tế sẽ gọi API
    const mockCosts: ImportAdditionalCost[] = [
      {
        _id: '4',
        name: 'Phí kiểm đếm',
        costValue: {
          type: 'fixed',
          value: 30000
        },
        paidToSupplier: false,
        allocateToItems: true,
        isActive: true,
        autoAddToPurchaseOrder: false,
        refundOnReturn: false
      },
      {
        _id: '5',
        name: 'Phí lưu kho',
        costValue: {
          type: 'fixed',
          value: 20000
        },
        paidToSupplier: false,
        allocateToItems: false,
        isActive: true,
        autoAddToPurchaseOrder: false,
        refundOnReturn: false
      },
      {
        _id: '6',
        name: 'Phí quản lý',
        costValue: {
          type: 'percentage',
          value: 0.5
        },
        paidToSupplier: false,
        allocateToItems: false,
        isActive: true,
        autoAddToPurchaseOrder: false,
        refundOnReturn: false,
        tax: {
          rate: 10,
          amount: 0
        }
      }
    ];

    return of(mockCosts);
  }
}
