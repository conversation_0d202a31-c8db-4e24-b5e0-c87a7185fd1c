import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute, Router, convertToParamMap } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';

import { LocationCreateComponent } from './location-create.component';
import { LocationService } from '../../services/location.service';
import { LocationType, LocationStatus, LocationFormData } from '../../models/view/location-view.model';

describe('LocationCreateComponent', () => {
  let component: LocationCreateComponent;
  let fixture: ComponentFixture<LocationCreateComponent>;
  let locationServiceSpy: jasmine.SpyObj<LocationService>;
  let routerSpy: jasmine.SpyObj<Router>;

  const mockLocation = {
    id: '1',
    name: 'Test Location',
    code: 'TEST-01',
    type: LocationType.Warehouse,
    level: 1,
    parentId: null,
    capacity: 1000,
    dimensions: { length: 10, width: 10, height: 5 },
    status: LocationStatus.Active,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockFormData: LocationFormData = {
    name: 'Test Location',
    code: 'TEST-01',
    type: LocationType.Warehouse,
    parentId: null,
    capacity: 1000,
    dimensions: { length: 10, width: 10, height: 5 },
    status: LocationStatus.Active,
    autoGenerateCode: false,
    quantity: 1
  };

  beforeEach(async () => {
    const locationServiceMock = jasmine.createSpyObj('LocationService', [
      'getLocationById',
      'createLocation',
      'updateLocation'
    ]);

    locationServiceMock.getLocationById.and.returnValue(of(mockLocation));
    locationServiceMock.createLocation.and.returnValue(of(mockLocation));
    locationServiceMock.updateLocation.and.returnValue(of(mockLocation));

    const routerMock = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        NoopAnimationsModule,
        MatCardModule,
        TranslateModule.forRoot(),
        LocationCreateComponent
      ],
      providers: [
        { provide: LocationService, useValue: locationServiceMock },
        { provide: Router, useValue: routerMock },
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of(convertToParamMap({ id: null })),
            queryParamMap: of(convertToParamMap({ parentId: null, level: null }))
          }
        }
      ]
    }).compileComponents();

    locationServiceSpy = TestBed.inject(LocationService) as jasmine.SpyObj<LocationService>;
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    fixture = TestBed.createComponent(LocationCreateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize in create mode when no id is provided', () => {
    expect(component.editMode).toBeFalse();
    expect(component.locationId).toBeNull();
  });

  it('should call createLocation when onFormSubmit is called in create mode', () => {
    component.onFormSubmit(mockFormData);
    expect(locationServiceSpy.createLocation).toHaveBeenCalledWith(mockFormData);
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/warehouse/location/list']);
  });

  it('should call updateLocation when onFormSubmit is called in edit mode', () => {
    component.editMode = true;
    component.locationId = '1';
    component.onFormSubmit(mockFormData);
    expect(locationServiceSpy.updateLocation).toHaveBeenCalledWith('1', mockFormData);
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/warehouse/location/list']);
  });

  it('should navigate to locations list when onFormCancel is called', () => {
    component.onFormCancel();
    expect(routerSpy.navigate).toHaveBeenCalledWith(['/warehouse/location/list']);
  });

  it('should load location details when in edit mode', () => {
    // Create a new instance with edit mode params
    TestBed.resetTestingModule();

    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        NoopAnimationsModule,
        MatCardModule,
        TranslateModule.forRoot(),
        LocationCreateComponent
      ],
      providers: [
        { provide: LocationService, useValue: locationServiceSpy },
        { provide: Router, useValue: routerSpy },
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of(convertToParamMap({ id: '1' })),
            queryParamMap: of(convertToParamMap({}))
          }
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LocationCreateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    expect(component.editMode).toBeTrue();
    expect(component.locationId).toBe('1');
    expect(locationServiceSpy.getLocationById).toHaveBeenCalledWith('1');
  });
});
