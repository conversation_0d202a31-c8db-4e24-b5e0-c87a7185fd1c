<h2 mat-dialog-title>{{ data.title | translate }}</h2>
<mat-dialog-content>
  <mat-form-field appearance="outline" class="w-100">
    <mat-label>{{ 'COMMON.NOTE' | translate }}</mat-label>
    <textarea matInput [(ngModel)]="note" rows="5"></textarea>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">
    {{ 'COMMON.CANCEL' | translate }}
  </button>
  <button mat-raised-button color="primary" (click)="onConfirm()">
    {{ 'COMMON.CONFIRM' | translate }}
  </button>
</mat-dialog-actions>
