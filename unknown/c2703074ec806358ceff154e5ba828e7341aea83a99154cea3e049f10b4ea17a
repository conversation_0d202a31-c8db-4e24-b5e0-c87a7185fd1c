import { signal, WritableSignal } from '@angular/core';

/**
 * computed không update khi signal thay đổi,
 * signal là 1 object = {x: {y: boolean}},
 * computed thay đổi theo object.x.y,
 * khi cập nhật signal chỉ cập nhật object.x.y
 * chứ không thay đổi tham chiếu của object
 *
 * có thể sau này sẽ có hàm mutate nhưng hiện tại chưa có
 *
 * https://chatgpt.com/c/67b09ef9-6740-8004-8f52-43fd662e5d8d
 */

export function createNotifiableSignal<T>(initialValue: T) {
  const _signal = signal(initialValue);
  const _trigger = signal(0); // Signal ẩn để ép computed cập nhật

  function wrapper() {
    _trigger(); // Đ<PERSON>m bảo computed theo dõi khi gọi obj()
    return _signal();
  }

  wrapper.set = (value: T) => {
    _signal.set(value);
    _trigger.set(_trigger() + 1); // Ép computed cập nhật
  };

  wrapper.update = (fn: (value: T) => T) => {
    _signal.update(fn);
    _trigger.set(_trigger() + 1);
  };

  wrapper.mutate = (fn: (value: T) => void) => {
    _signal.update(value => {
      fn(value);
      return value;
    });
    _trigger.set(_trigger() + 1);
  };

  return wrapper as WritableSignal<T> & {
    mutate: (fn: (value: T) => void) => void;
  };
}
