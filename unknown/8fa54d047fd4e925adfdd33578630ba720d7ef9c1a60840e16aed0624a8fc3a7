<h2 mat-dialog-title>{{ dialogTitle }}</h2>

<form [formGroup]="brandForm" (ngSubmit)="onSubmit()">
  <div mat-dialog-content>
    <div class="row">
      <!-- Tên thương hiệu -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Tên thương hiệu</mat-label>
          <input matInput formControlName="name" required>
          <mat-error *ngIf="brandForm.get('name')?.hasError('required')">
            Tê<PERSON> thương hiệu là bắt buộc
          </mat-error>
        </mat-form-field>
      </div>

      <!-- M<PERSON> thương hiệu -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>M<PERSON> thương hiệu</mat-label>
          <input matInput formControlName="code" required>
          <mat-error *ngIf="brandForm.get('code')?.hasError('required')">
            <PERSON><PERSON> thương hiệu là bắt buộc
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Quốc gia -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Quốc gia</mat-label>
          <input matInput formControlName="country">
        </mat-form-field>
      </div>

      <!-- Website -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Website</mat-label>
          <input matInput formControlName="website">
          <mat-icon matSuffix>language</mat-icon>
        </mat-form-field>
      </div>

      <!-- Mô tả -->
      <div class="col-md-12 mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Mô tả</mat-label>
          <textarea matInput formControlName="description" rows="3"></textarea>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div mat-dialog-actions align="end">
    <button type="button" mat-button (click)="onNoClick()">Hủy</button>
    <button type="submit" mat-raised-button color="primary">Lưu</button>
  </div>
</form>
