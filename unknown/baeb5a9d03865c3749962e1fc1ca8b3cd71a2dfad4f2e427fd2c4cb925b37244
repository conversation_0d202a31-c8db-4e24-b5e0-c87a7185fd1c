import { ChangeDetectionStrategy, Component, EventEmitter, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { TranslateModule } from '@ngx-translate/core';
import { Observable, Subject, debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs';

import { ProductSearchService } from './product-search.service';

/**
 * Shared component tìm kiếm sản phẩm
 */
@Component({
  selector: 'app-shared-product-search',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    TranslateModule
  ],
  templateUrl: './product-search.component.html',
  styleUrls: ['./product-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SharedProductSearchComponent implements OnInit, OnDestroy {
  /**
   * Sự kiện khi chọn sản phẩm
   */
  @Output() productSelected = new EventEmitter<any>();

  /**
   * Form control cho ô tìm kiếm
   */
  searchControl = new FormControl('');

  /**
   * Danh sách sản phẩm đã lọc
   */
  filteredProducts$!: Observable<any[]>;

  /**
   * Subject để hủy các subscription khi component bị hủy
   */
  private destroy$ = new Subject<void>();

  constructor(private productSearchService: ProductSearchService) { }

  ngOnInit(): void {
    // Thiết lập tìm kiếm sản phẩm với debounce
    this.setupProductSearch();
  }

  /**
   * Thiết lập tìm kiếm sản phẩm với debounce
   */
  private setupProductSearch(): void {
    this.filteredProducts$ = this.searchControl.valueChanges.pipe(
      takeUntil(this.destroy$),
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(value => {
        const keyword = typeof value === 'string' ? value : '';
        return this.productSearchService.searchProducts(keyword);
      })
    );
  }

  /**
   * Xử lý khi chọn sản phẩm từ autocomplete
   */
  onProductSelected(product: any): void {
    this.productSelected.emit(product);
    this.searchControl.setValue('');
  }

  /**
   * Hiển thị tên sản phẩm trong autocomplete
   */
  displayProductFn(product: any): string {
    return product && product.name ? product.name : '';
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
