import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

import { ShippingInfoService } from './shipping-info.service';
import { TransportInfo } from '../../models/api/goods-receipt.dto';

/**
 * Component thông tin vận chuyển
 */
@Component({
  selector: 'app-shipping-info',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    TranslateModule
  ],
  templateUrl: './shipping-info.component.html',
  styleUrls: ['./shipping-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ShippingInfoComponent implements OnInit, OnDestroy {
  /**
   * Dữ liệu thông tin vận chuyển ban đầu
   */
  @Input() initialData?: Partial<TransportInfo>;

  /**
   * Sự kiện khi dữ liệu thay đổi
   */
  @Output() dataChange = new EventEmitter<Partial<TransportInfo>>();

  /**
   * Form thông tin vận chuyển
   */
  shippingForm!: FormGroup;

  /**
   * Subject để hủy các subscription khi component bị hủy
   */
  private destroy$ = new Subject<void>();

  constructor(private shippingInfoService: ShippingInfoService) { }

  ngOnInit(): void {
    // Khởi tạo form
    this.initForm();

    // Theo dõi thay đổi form
    this.watchFormChanges();

    // Cập nhật form với dữ liệu ban đầu nếu có
    if (this.initialData) {
      this.shippingInfoService.patchFormData(this.shippingForm, this.initialData);
    }
  }

  /**
   * Khởi tạo form
   */
  private initForm(): void {
    this.shippingForm = this.shippingInfoService.createShippingForm();
  }

  /**
   * Theo dõi thay đổi form
   */
  private watchFormChanges(): void {
    this.shippingForm.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(value => {
      this.dataChange.emit(value);
    });
  }

  /**
   * Lấy dữ liệu form
   */
  getFormData(): Partial<TransportInfo> {
    return this.shippingInfoService.getFormData(this.shippingForm);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
