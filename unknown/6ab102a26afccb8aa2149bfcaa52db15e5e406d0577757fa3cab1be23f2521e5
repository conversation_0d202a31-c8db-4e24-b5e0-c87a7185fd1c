.location-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;

  .location-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h1 {
      margin: 0;
      font-size: 1.5rem;
    }

    .location-list-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .location-list-content {
    flex: 1;
    min-height: 300px;
    margin-bottom: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: auto;
  }

  .location-details {
    padding: 1rem;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 1rem;

    h2 {
      font-size: 1.2rem;
      margin-top: 0;
      margin-bottom: 1rem;
    }

    .detail-item {
      display: flex;
      margin-bottom: 0.5rem;

      .label {
        width: 150px;
        font-weight: 500;
      }

      .value {
        flex: 1;
      }
    }
  }
}

// Responsive styles
@media screen and (max-width: 768px) {
  .location-list-container {
    .location-list-header {
      flex-direction: column;
      align-items: flex-start;

      h1 {
        margin-bottom: 1rem;
      }

      .location-list-actions {
        width: 100%;
        justify-content: space-between;
      }
    }

    .location-details {
      .detail-item {
        flex-direction: column;

        .label {
          width: 100%;
          margin-bottom: 0.25rem;
        }
      }
    }
  }
}
