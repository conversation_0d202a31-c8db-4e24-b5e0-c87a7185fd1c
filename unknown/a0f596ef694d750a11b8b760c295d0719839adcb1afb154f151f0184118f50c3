<div class="product-filter-dialog">
  <h2 mat-dialog-title>{{ 'WAREHOUSE.PRODUCT_FILTER_DIALOG.TITLE' | translate }}</h2>

  <div class="modal-content">
    <div class="filter-section">
      <h3>{{ 'WAREHOUSE.PRODUCT_FILTER_DIALOG.CATEGORIES' | translate }}</h3>
      <div class="categories-list">
        <mat-checkbox
          *ngFor="let category of categories"
          [checked]="isCategorySelected(category._id)"
          (change)="onCategoryChange(category._id, $event.checked)"
          class="category-checkbox">
          {{ category.name }}
        </mat-checkbox>
      </div>
    </div>

    <div class="filter-section">
      <h3>{{ 'WAREHOUSE.PRODUCT_FILTER_DIALOG.WAREHOUSE_LOCATION' | translate }}</h3>
      <mat-form-field class="location-select">
        <mat-label>{{ 'WAREHOUSE.PRODUCT_FILTER_DIALOG.SELECT_LOCATION' | translate }}</mat-label>
        <mat-select [(ngModel)]="selectedWarehouseLocation">
          <mat-option [value]="null">{{ 'WAREHOUSE.PRODUCT_FILTER_DIALOG.ALL_LOCATIONS' | translate }}</mat-option>
          <mat-option *ngFor="let location of filteredLocations" [value]="location">
            {{ location.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="filter-section">
      <h3>{{ 'WAREHOUSE.PRODUCT_FILTER_DIALOG.OPTIONS' | translate }}</h3>
      <div class="options-list">
        <mat-checkbox [(ngModel)]="onlyInStock" class="option-checkbox">
          {{ 'WAREHOUSE.PRODUCT_FILTER_DIALOG.ONLY_IN_STOCK' | translate }}
        </mat-checkbox>

        <mat-checkbox [(ngModel)]="onlyActive" class="option-checkbox">
          {{ 'WAREHOUSE.PRODUCT_FILTER_DIALOG.ONLY_ACTIVE' | translate }}
        </mat-checkbox>
      </div>
    </div>
  </div>

  <div class="modal-actions">
    <button mat-button (click)="onCancel()">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" (click)="onConfirm()">
      {{ 'COMMON.CONFIRM' | translate }}
    </button>
  </div>
</div>
