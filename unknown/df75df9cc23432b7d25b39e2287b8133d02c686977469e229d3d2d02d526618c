
ul, ol {
  list-style: none;
  margin: 0;
  padding: 0;

  li {
    display: block;
  }
}


.bg-danger {
  background-color: #ffd8d8 !important;
}
.bg-warning {
  background-color: #ffeebb !important;
}
.text-info {
  color: rgb(5 180 215) !important;
}

.badge-warning {
  background: #ff9800;
}
.badge-primary {
  background: #2196f3;
}
.badge-success {
  background: #4caf50;
}
.badge-danger {
  background: #ff1100;
}

.material-icons {
  font-family: 'Material Icons' !important;
}

input-float {
  display: block;
  width: 100%;
  height: 100%;

  input {
    width: 100%;
    height: 100%;
    border: 0;
    outline: 0;
  }
}

.rating-star {
  --star-color:#ccc;
  margin:1em auto;
  font-size: 12px;
  position: relative;
  display: block;
  width: 0px;
  height: 0px;
  border-right: 1em solid transparent;
  border-bottom: 0.7em solid var(--star-color);
  border-left: 1em solid transparent;
  transform: rotate(35deg);

  &:before {
    border-bottom: 0.8em solid var(--star-color);
    border-left: 0.3em solid transparent;
    border-right: 0.3em solid transparent;
    position: absolute;
    height: 0;
    width: 0;
    top: -0.45em;
    left: -0.65em;
    display: block;
    content:"";
    transform: rotate(-35deg);
   }
   &:after {
    position: absolute;
    display: block;
    top: 0.03em;
    left: -1.05em;
    width: 0;
    height: 0;
    border-right: 1em solid transparent;
    border-bottom: 0.7em solid var(--star-color);
    border-left: 1em solid transparent;
    transform: rotate(-70deg);
    content:"";
  }

  &.fill {
    --star-color-fill:orange;
    border-bottom: 0.7em solid var(--star-color-fill);

    &:before {
      border-bottom: 0.8em solid var(--star-color-fill);
    }
    &:after {
      border-bottom: 0.8em solid var(--star-color-fill);
    }
  }
}

.btn-shadow {
  display: inline-block;
  position: relative;
  margin: 0 0 0.875em 0;
  border-width: 0.125em;
  border-style: solid;
  border-radius: 0.5em;
  border-color: #000;
  box-shadow: 0 0.375em 0 #000;
  -webkit-user-select: none;
  user-select: none;
  background: #fff;
  cursor: pointer;
  padding: 10px;
  vertical-align: middle;
  text-align: center;
  text-decoration: none;
  -webkit-text-decoration-color: unset;
  text-decoration-color: unset;
  color: #183153;
  font-weight: 600;
}
