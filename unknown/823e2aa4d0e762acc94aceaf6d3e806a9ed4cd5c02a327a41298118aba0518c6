export function getObjectValues(obj: any) {
  const allowedTypes = ['[object String]', '[object Object]', '[object Array]', '[object Function]'];
  const objType = Object.prototype.toString.call(obj);

  if(obj === null || typeof obj === 'undefined') {
    throw new TypeError('Cannot convert undefined or null to object');
    // eslint-disable-next-line
  } else if(!~allowedTypes.indexOf(objType)) {
    return [];
  } else {
    // if ES6 is supported
    if (Object.keys) {
      return Object.keys(obj).map((key) => obj[key]);
    }

    const result = [];
    for (const prop in obj) {
      // eslint-disable-next-line
      if (obj.hasOwnProperty(prop)) {
        result.push(obj[prop]);
      }
    }

    return result;
  }
};
export function deepEquals(x: any, y: any) {
  if (x === y) {
    return true; // if both x and y are null or undefined and exactly the same
  } if (!(x instanceof Object) || !(y instanceof Object)) {
    return false; // if they are not strictly equal, they both need to be Objects
  } if (x.constructor !== y.constructor) {
    // they must have the exact same prototype chain, the closest we can do is
    // test their constructor.
    return false;
  }
  for (const p in x) {
    // eslint-disable-next-line
    if (!x.hasOwnProperty(p)) {
      // eslint-disable-next-line
      continue; // other properties were tested using x.constructor === y.constructor
    }
    // eslint-disable-next-line
    if (!y.hasOwnProperty(p)) {
      return false; // allows to compare x[ p ] and y[ p ] when set to undefined
    }
    if (x[p] === y[p]) {
      // eslint-disable-next-line
      continue; // if they have the same strict value or identity then they are equal
    }
    if (typeof (x[p]) !== 'object') {
      return false; // Numbers, Strings, Functions, Booleans must be strictly equal
    }
    if (!deepEquals(x[p], y[p])) {
      return false;
    }
  }
  for (const p in y) {
    // eslint-disable-next-line
    if (y.hasOwnProperty(p) && !x.hasOwnProperty(p)) {
      return false;
    }
  }
  return true;
}
function isObject(item: any) {
  return (item && typeof item === 'object' && !Array.isArray(item));
}

export function mergeDeep(target: any, ...sources: any): any {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!target[key]) {
          Object.assign(target, {
            [key]: {}
          });
        }
        mergeDeep(target[key], source[key]);
      } else {
        Object.assign(target, {
          [key]: source[key]
        });
      }
    });
  }

  return mergeDeep(target, ...sources);
}
export function insertItemAtIndex(arr: Array<any>, index: number, item: any) {
  if (index < 0 || index > arr.length) {
    // Index out of bounds,
    // return the original array
    return arr;
  }

  arr.splice(index, 0, item);
  return arr;
  // return arr.slice(0, index).concat(item, arr.slice(index));
}
