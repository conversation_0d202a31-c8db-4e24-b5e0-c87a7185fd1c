export function isPhoneNumber(str: string) {
  const num = String(parseInt(str, 10));
  return num === str || str === `0${num}`;
}
export function formatPhoneNumber(str: string) {
  str = insertStr(str, 4, '.');
  str = insertStr(str, 8, '.');
  return str;
}
function insertStr(str: string, index: number, value: string) {
  return str.substring(0, index) + value + str.substring(index);
}
export function isValidVietnamesePhoneNumber(str: string) {
  if(!str) {
    return false;
  }

  const r = /(03|05|07|08|09|01[2|6|8|9])+([0-9]{8})\b/;
  return r.test(str);
}
export function padNumber(num: number) {
  if(num < 10) {
    return `0${num}`;
  }

  return num.toString();
}
export function normalizeVietnameseStr(str: string | undefined) {
  if(!str) {
    return str;
  }

  try {
    const AccentsMap = [
      'aàảãáạăằẳẵắặâầẩẫấậ',
      'AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬ',
      'dđ', 'DĐ',
      'eèẻẽéẹêềểễếệ',
      'EÈẺẼÉẸÊỀỂỄẾỆ',
      'iìỉĩíị',
      'IÌỈĨÍỊ',
      'oòỏõóọôồổỗốộơờởỡớợ',
      'OÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢ',
      'uùủũúụưừửữứự',
      'UÙỦŨÚỤƯỪỬỮỨỰ',
      'yỳỷỹýỵ',
      'YỲỶỸÝỴ'
    ];

    for (let i = 0; i < AccentsMap.length; i++) {
      const re = new RegExp(`[${AccentsMap[i].substring(1)}]`, 'g');
      const char = AccentsMap[i][0];
      str = str.replace(re, char);
    }
    return str;
  } catch(e) {
    console.log(str);
    throw e;
  }
}
export function formatMoney(value: string | number | undefined) {
  if(!value) {
    return value as string;
  }

  return Number(Number(value).toFixed(2)).toLocaleString('de-DE', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
}
export function isNumber(x: any) {
  return typeof x === 'number' && !Number.isNaN(x);
}
