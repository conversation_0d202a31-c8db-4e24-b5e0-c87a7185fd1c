import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { GoodsReceiptItem, EmbeddedProduct, EmbeddedWarehouseLocation, ProductBatchItem } from '../../models/api/goods-receipt.dto';

/**
 * Service xử lý logic danh sách sản phẩm
 */
@Injectable({
  providedIn: 'root'
})
export class ProductListService {
  /**
   * BehaviorSubject lưu trữ danh sách sản phẩm
   */
  private itemsSubject = new BehaviorSubject<GoodsReceiptItem[]>([]);

  /**
   * Observable danh sách sản phẩm
   */
  public items$ = this.itemsSubject.asObservable();

  constructor() { }

  /**
   * Thêm sản phẩm vào danh sách
   * @param product Sản phẩm cần thêm
   * @param price Giá sản phẩm
   * @param quantity Số lượng
   */
  addItem(product: EmbeddedProduct, price: number, quantity: number): void {
    const currentItems = this.itemsSubject.value;

    // Kiểm tra xem sản phẩm đã tồn tại chưa
    const existingItemIndex = currentItems.findIndex(
      existingItem => existingItem.product.productId === product.productId
    );

    if (existingItemIndex >= 0) {
      // Nếu sản phẩm đã tồn tại, cập nhật số lượng
      const updatedItems = [...currentItems];
      updatedItems[existingItemIndex].quantityReceived += quantity;
      // Cập nhật số lượng chấp nhận bằng số lượng nhận
      updatedItems[existingItemIndex].quantityAccepted = updatedItems[existingItemIndex].quantityReceived;
      updatedItems[existingItemIndex].subTotal = this.calculateSubTotal(updatedItems[existingItemIndex]);
      updatedItems[existingItemIndex].total = this.calculateItemTotal(updatedItems[existingItemIndex]);
      this.itemsSubject.next(updatedItems);
    } else {
      // Nếu sản phẩm chưa tồn tại, thêm mới
      const newItem: GoodsReceiptItem = {
        _id: 'temp-' + Date.now(),
        product: product,
        quantityReceived: quantity,
        quantityAccepted: quantity, // Mặc định số lượng chấp nhận bằng số lượng nhận
        price: price,
        subTotal: quantity * price,
        total: quantity * price,
        inventoryTransactionId: 'temp-' + Date.now()
      };
      this.itemsSubject.next([...currentItems, newItem]);
    }
  }

  /**
   * Cập nhật sản phẩm trong danh sách
   * @param index Vị trí sản phẩm cần cập nhật
   * @param updates Thông tin cần cập nhật
   */
  updateItem(index: number, updates: {
    quantityReceived?: number;
    quantityAccepted?: number;
    price?: number;
    discount?: number;
    warehouseLocation?: EmbeddedWarehouseLocation;
    product?: EmbeddedProduct;
    batches?: ProductBatchItem[];
  }): void {
    const currentItems = this.itemsSubject.value;
    if (index < 0 || index >= currentItems.length) return;

    const updatedItems = [...currentItems];
    const item = updatedItems[index];

    // Cập nhật các trường
    if (updates.quantityReceived !== undefined) {
      item.quantityReceived = updates.quantityReceived;
      // Nếu số lượng nhận thay đổi, cập nhật số lượng chấp nhận
      if (item.quantityAccepted === undefined || item.quantityAccepted > updates.quantityReceived) {
        item.quantityAccepted = updates.quantityReceived;
      }
    }

    if (updates.quantityAccepted !== undefined) {
      // Đảm bảo số lượng chấp nhận không vượt quá số lượng nhận
      item.quantityAccepted = Math.min(updates.quantityAccepted, item.quantityReceived);
    }

    if (updates.price !== undefined) {
      item.price = updates.price;
    }

    if (updates.discount !== undefined) {
      item.discount = updates.discount;
    }

    if (updates.warehouseLocation !== undefined) {
      item.warehouseLocation = updates.warehouseLocation;
    }

    if (updates.product !== undefined) {
      item.product = updates.product;
    }

    if (updates.batches !== undefined) {
      item.batches = updates.batches;

      // Cập nhật số lượng nhận dựa trên tổng số lượng từ các lô
      if (item.batches && item.batches.length > 0) {
        const totalBatchQuantity = item.batches.reduce((sum, batch) => sum + batch.quantity, 0);
        item.quantityReceived = totalBatchQuantity;

        // Cập nhật số lượng chấp nhận bằng số lượng nhận
        item.quantityAccepted = totalBatchQuantity;
      }
    }

    // Tính lại giá trị
    item.subTotal = this.calculateSubTotal(item);
    item.total = this.calculateItemTotal(item);

    this.itemsSubject.next(updatedItems);
  }

  /**
   * Xóa sản phẩm khỏi danh sách
   * @param index Vị trí sản phẩm cần xóa
   */
  removeItem(index: number): void {
    const currentItems = this.itemsSubject.value;
    if (index < 0 || index >= currentItems.length) return;

    const updatedItems = [...currentItems];
    updatedItems.splice(index, 1);

    this.itemsSubject.next(updatedItems);
  }

  /**
   * Lấy danh sách sản phẩm hiện tại
   * @returns Danh sách sản phẩm
   */
  getItems(): GoodsReceiptItem[] {
    return this.itemsSubject.value;
  }

  /**
   * Đặt danh sách sản phẩm mới
   * @param items Danh sách sản phẩm mới
   */
  setItems(items: GoodsReceiptItem[]): void {
    this.itemsSubject.next(items);
  }

  /**
   * Tính tổng tiền trước chiết khấu
   * @param item Sản phẩm cần tính
   * @returns Tổng tiền trước chiết khấu
   */
  private calculateSubTotal(item: GoodsReceiptItem): number {
    // Sử dụng số lượng chấp nhận nếu có, nếu không thì dùng số lượng nhận
    const quantity = item.quantityAccepted !== undefined ? item.quantityAccepted : item.quantityReceived;
    return quantity * item.price;
  }

  /**
   * Thêm lô hàng mới vào sản phẩm
   * @param index Vị trí sản phẩm cần thêm lô
   * @param batch Thông tin lô hàng
   */
  addBatch(index: number, batch: ProductBatchItem): void {
    const currentItems = this.itemsSubject.value;
    if (index < 0 || index >= currentItems.length) return;

    const updatedItems = [...currentItems];
    const item = updatedItems[index];

    // Khởi tạo mảng batches nếu chưa có
    if (!item.batches) {
      item.batches = [];
    }

    // Thêm lô mới vào danh sách
    item.batches.push(batch);

    // Cập nhật số lượng nhận và chấp nhận
    const totalBatchQuantity = item.batches.reduce((sum, b) => sum + b.quantity, 0);
    item.quantityReceived = totalBatchQuantity;
    item.quantityAccepted = totalBatchQuantity;

    // Tính lại giá trị
    item.subTotal = this.calculateSubTotal(item);
    item.total = this.calculateItemTotal(item);

    this.itemsSubject.next(updatedItems);
  }

  /**
   * Cập nhật lô hàng
   * @param itemIndex Vị trí sản phẩm
   * @param batchIndex Vị trí lô hàng
   * @param updatedBatch Thông tin lô hàng đã cập nhật
   */
  updateBatch(itemIndex: number, batchIndex: number, updatedBatch: ProductBatchItem): void {
    const currentItems = this.itemsSubject.value;
    if (itemIndex < 0 || itemIndex >= currentItems.length) return;

    const item = currentItems[itemIndex];
    if (!item.batches || batchIndex < 0 || batchIndex >= item.batches.length) return;

    const updatedItems = [...currentItems];
    const updatedItem = updatedItems[itemIndex];

    // Cập nhật lô hàng
    updatedItem.batches![batchIndex] = updatedBatch;

    // Cập nhật số lượng nhận và chấp nhận
    const totalBatchQuantity = updatedItem.batches!.reduce((sum, b) => sum + b.quantity, 0);
    updatedItem.quantityReceived = totalBatchQuantity;
    updatedItem.quantityAccepted = totalBatchQuantity;

    // Tính lại giá trị
    updatedItem.subTotal = this.calculateSubTotal(updatedItem);
    updatedItem.total = this.calculateItemTotal(updatedItem);

    this.itemsSubject.next(updatedItems);
  }

  /**
   * Xóa lô hàng
   * @param itemIndex Vị trí sản phẩm
   * @param batchIndex Vị trí lô hàng
   */
  removeBatch(itemIndex: number, batchIndex: number): void {
    const currentItems = this.itemsSubject.value;
    if (itemIndex < 0 || itemIndex >= currentItems.length) return;

    const item = currentItems[itemIndex];
    if (!item.batches || batchIndex < 0 || batchIndex >= item.batches.length) return;

    const updatedItems = [...currentItems];
    const updatedItem = updatedItems[itemIndex];

    // Xóa lô hàng
    updatedItem.batches!.splice(batchIndex, 1);

    // Cập nhật số lượng nhận và chấp nhận
    const totalBatchQuantity = updatedItem.batches!.reduce((sum, b) => sum + b.quantity, 0);
    updatedItem.quantityReceived = totalBatchQuantity;
    updatedItem.quantityAccepted = totalBatchQuantity;

    // Tính lại giá trị
    updatedItem.subTotal = this.calculateSubTotal(updatedItem);
    updatedItem.total = this.calculateItemTotal(updatedItem);

    this.itemsSubject.next(updatedItems);
  }

  /**
   * Tính tổng tiền của một sản phẩm
   * @param item Sản phẩm cần tính tổng tiền
   * @returns Tổng tiền của sản phẩm
   */
  private calculateItemTotal(item: GoodsReceiptItem): number {
    const subtotal = item.subTotal || (item.quantityReceived * item.price);
    const discount = item.discount || 0;
    return subtotal - discount;
  }

  /**
   * Tính tổng tiền của tất cả sản phẩm
   * @returns Tổng tiền của tất cả sản phẩm
   */
  getTotalAmount(): number {
    return this.itemsSubject.value.reduce((total, item) => total + item.total, 0);
  }

  /**
   * Tính tổng chiết khấu
   * @returns Tổng chiết khấu
   */
  calculateTotalDiscount(): number {
    return this.itemsSubject.value.reduce((total, item) => {
      return total + (item.discount || 0);
    }, 0);
  }
}
