<div class="product-modifiers-sheet container-fluid py-2">
  <!-- Header -->
  <div class="header d-flex justify-content-between align-items-center mb-3">
    <h5 class="mb-0" *ngIf="orderItem()?.product">{{ 'SALES.PRODUCT_MODIFIERS.CHOOSE_MORE' | translate }}: {{ orderItem()?.product?.name }}</h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="close()"></button>
  </div>

  <!-- Filter tabs -->
  <div class="filters d-flex mb-3 overflow-auto">
    <button *ngFor="let option of productOptionList()"
            class="btn me-2"
            [class.btn-primary]="activeFilter() === option.name"
            [class.btn-outline-primary]="activeFilter() !== option.name"
            (click)="setActiveFilter(option.name)">
      {{ option.name }}
    </button>
  </div>

  <!-- Product items list -->
  <div class="product-list mb-4">
    <ng-container *ngFor="let option of productOptionList()">
      <div class="product-group" *ngIf="activeFilter() === option.name">
        <div class="row g-3">
          <div class="col-md-4 mb-3" *ngFor="let item of option.items">
            <div class="card product-item h-100"
                 [class.selected]="isItemSelected(item.productId)"
                 (click)="selectItem(item.productId)">
              <div class="card-body d-flex">
                <!-- Product image -->
                <div class="product-image me-3">
                  <img [src]="item.image" [alt]="item.name" class="img-fluid rounded" style="width: 60px; height: 60px; object-fit: cover;">
                </div>

                <!-- Product info -->
                <div class="product-info flex-grow-1">
                  <h6 class="card-title mb-1">{{ item.name }}</h6>
                  <p class="card-text text-primary mb-2">{{ formatCurrency(item.price) }}</p>

                  <!-- Quantity controls -->
                  <div class="quantity-controls d-flex align-items-center" *ngIf="isItemSelected(item.productId)">
                    <button class="btn btn-sm btn-outline-secondary" (click)="decreaseItemQuantity(item.productId); $event.stopPropagation()">
                      <mat-icon>remove</mat-icon>
                    </button>
                    <span class="mx-2">{{ getItemQuantity(item.productId) }}</span>
                    <button class="btn btn-sm btn-outline-secondary" (click)="increaseItemQuantity(item.productId); $event.stopPropagation()">
                      <mat-icon>add</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </div>

  <!-- Footer actions -->
  <div class="footer-actions d-flex justify-content-between align-items-center border-top pt-3">
    <button class="btn btn-outline-secondary" (click)="resetSelections()">
      {{ 'SALES.PRODUCT_MODIFIERS.RESET' | translate }}
    </button>
    <button class="btn btn-primary" [disabled]="totalPrice() === 0" (click)="addToOrder()">
      {{ 'SALES.PRODUCT_MODIFIERS.ADD_TO_ORDER' | translate }} ({{ formatCurrency(totalPrice()) }})
    </button>
  </div>
</div>
