
@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Black.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Black.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Black.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Black.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Black.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Black.svg#GoogleSans-Black') format('svg');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Regular.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Regular.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Regular.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Regular.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Regular.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Regular.svg#GoogleSans-Regular') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Light.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Light.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Light.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Light.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Light.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Light.svg#GoogleSans-Light') format('svg');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Bold.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Bold.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Bold.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Bold.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Bold.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Bold.svg#GoogleSans-Bold') format('svg');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Italic.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Italic.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Italic.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Italic.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Italic.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Italic.svg#GoogleSans-Italic') format('svg');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-BlackItalic.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-BlackItalic.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-BlackItalic.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-BlackItalic.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-BlackItalic.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-BlackItalic.svg#GoogleSans-BlackItalic') format('svg');
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-MediumItalic.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-MediumItalic.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-MediumItalic.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-MediumItalic.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-MediumItalic.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-MediumItalic.svg#GoogleSans-MediumItalic') format('svg');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-BoldItalic.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-BoldItalic.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-BoldItalic.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-BoldItalic.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-BoldItalic.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-BoldItalic.svg#GoogleSans-BoldItalic') format('svg');
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-LightItalic.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-LightItalic.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-LightItalic.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-LightItalic.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-LightItalic.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-LightItalic.svg#GoogleSans-LightItalic') format('svg');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-ThinItalic.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-ThinItalic.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-ThinItalic.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-ThinItalic.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-ThinItalic.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-ThinItalic.svg#GoogleSans-ThinItalic') format('svg');
  font-weight: 100;
  font-style: italic;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Medium.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Medium.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Medium.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Medium.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Medium.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Medium.svg#GoogleSans-Medium') format('svg');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Google Sans';
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Thin.eot');
  src: url('../../../../../public/assets/fonts/googlesans/GoogleSans-Thin.eot?#iefix') format('embedded-opentype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Thin.woff2') format('woff2'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Thin.woff') format('woff'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Thin.ttf') format('truetype'),
      url('../../../../../public/assets/fonts/googlesans/GoogleSans-Thin.svg#GoogleSans-Thin') format('svg');
  font-weight: 100;
  font-style: normal;
}


/* fallback */
@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url('../../../../../public/assets/fonts/material-icons/mat-icons.woff2') format('woff2');
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* fallback */
@font-face {
  font-family: 'Material Symbols Outlined';
  font-style: normal;
  font-weight: 100 700;
  src: url('../../../../../public/assets/fonts/material-icons/mat-icons-outline.woff2') format('woff2');
}

.material-symbols-outlined {
  font-family: 'Material Symbols Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

