import type { ImportAdditionalCost } from 'salehub_shared_contracts/entities/scm/import_additional_cost';


export interface CategoryProductModalData {
  warehouseId?: string;
  categoryIds?: string[];
  excludeProductIds?: string[];
}

/**
 * Interface cho dữ liệu truyền vào modal chi phí bổ sung
 */
export interface AdditionalCostModalData {
  cost?: ImportAdditionalCost; // Dữ liệu chi phí nếu là chỉnh sửa
  subTotal?: number; // Tổng tiền hàng để tính thuế tự động cho chi phí phần trăm
}

