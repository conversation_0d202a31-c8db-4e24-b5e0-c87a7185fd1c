<h2 mat-dialog-title>{{ 'SIMPLE_NOTE_DIALOG.TITLE' | translate }}</h2>
<mat-dialog-content>
  <mat-form-field class="w-100">
    <mat-label>{{ 'SIMPLE_NOTE_DIALOG.NOTE_CONTENT' | translate }}</mat-label>
    <textarea
      matInput
      [(ngModel)]="noteContent"
      rows="5"
      placeholder="{{ 'SIMPLE_NOTE_DIALOG.ENTER_NOTE' | translate }}">
    </textarea>
  </mat-form-field>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button
    mat-button
    (click)="onCancel()">
    {{ 'COMMON.CANCEL' | translate }}
  </button>
  <button
    mat-raised-button
    color="primary"
    (click)="onSave()">
    {{ 'COMMON.SAVE' | translate }}
  </button>
</mat-dialog-actions>
