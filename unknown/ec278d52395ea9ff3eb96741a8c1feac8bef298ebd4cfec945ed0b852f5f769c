<div class="child-form" [formGroup]="formGroup">
  <div class="form-header">
    <h4>{{ 'WAREHOUSE.LOCATION.FORM.CHILD_LEVEL' | translate }} {{ level }}</h4>
    <button mat-icon-button type="button" color="warn" (click)="onRemoveChild()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div class="form-row">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.NAME' | translate }}</mat-label>
      <input matInput formControlName="name">
    </mat-form-field>
  </div>

  <div class="form-row code-row">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.CODE' | translate }}</mat-label>
      <input matInput formControlName="code">
    </mat-form-field>
    <mat-checkbox formControlName="autoGenerateCode" class="auto-code-checkbox">
      {{ 'WAREHOUSE.LOCATION.FORM.AUTO_GENERATE' | translate }}
    </mat-checkbox>
  </div>

  <div class="form-row">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.TYPE' | translate }}</mat-label>
      <mat-select formControlName="type">
        <mat-option *ngFor="let type of locationTypes" [value]="type">
          {{ getLocationTypeLabel(type) | translate }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div class="form-row">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.CAPACITY' | translate }} (kg)</mat-label>
      <input matInput type="number" formControlName="capacity" min="0">
    </mat-form-field>
  </div>

  <div class="form-row dimensions-row" formGroupName="dimensions">
    <h4>{{ 'WAREHOUSE.LOCATION.FORM.DIMENSIONS' | translate }}</h4>
    <div class="dimensions-inputs">
      <mat-form-field appearance="outline">
        <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.LENGTH' | translate }} (m)</mat-label>
        <input matInput type="number" formControlName="length" min="0" step="0.1">
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.WIDTH' | translate }} (m)</mat-label>
        <input matInput type="number" formControlName="width" min="0" step="0.1">
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.HEIGHT' | translate }} (m)</mat-label>
        <input matInput type="number" formControlName="height" min="0" step="0.1">
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.DEPTH' | translate }} (m)</mat-label>
        <input matInput type="number" formControlName="depth" min="0" step="0.1">
      </mat-form-field>
    </div>
  </div>

  <div class="form-row">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.STATUS' | translate }}</mat-label>
      <mat-select formControlName="status">
        <mat-option *ngFor="let status of locationStatuses" [value]="status">
          {{ getLocationStatusLabel(status) | translate }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div class="form-row">
    <mat-form-field appearance="outline">
      <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.QUANTITY' | translate }}</mat-label>
      <input matInput type="number" formControlName="quantity" min="1">
    </mat-form-field>
  </div>

  <!-- Nút tạo con nếu chưa đạt giới hạn 5 cấp -->
  <div class="create-child-container" *ngIf="level < 5">
    <button mat-stroked-button type="button" color="primary" (click)="addChild()">
      <mat-icon>add</mat-icon>
      {{ 'WAREHOUSE.LOCATION.FORM.CREATE_CHILD' | translate }}
    </button>
  </div>

  <!-- Hiển thị các form con nếu có -->
  <div class="children-container" *ngIf="showChildren && childrenFormArray">
    <ng-container *ngFor="let i of [0, 1, 2, 3, 4]; let idx = index">
      <div *ngIf="childrenFormArray && idx < childrenFormArray.length">
        <app-location-child-form
          [formGroup]="getChildFormGroup(idx)!"
          [level]="level + 1"
          [parentFormGroup]="formGroup"
          (removeChild)="removeChildForm(idx)">
        </app-location-child-form>
      </div>
    </ng-container>
  </div>
</div>
