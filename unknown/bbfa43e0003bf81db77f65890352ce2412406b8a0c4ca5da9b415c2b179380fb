/** bottom sheet */

// với các bottom sheet mà bên trong component cần position absolute giữ bên trên hoặc bên dưới
.bottom-sheet-overflow-visible {
  .mat-bottom-sheet-container {
    overflow: visible;
  }
}

// với các bottom sheet cần chiếm gần hết màn hình
.bottom-sheet-wide-screen {
  .mat-bottom-sheet-container {
    min-height: 90vh;
    max-height: 90vh;
  }
}

.bottom-sheet-no-padding {
  .mat-bottom-sheet-container {
    padding: 0;
  }
}

/* Style cho product-modifiers-bottom-sheet */
.product-modifiers-bottom-sheet {
  .mat-bottom-sheet-container {
    padding: 16px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    max-height: 80vh;
    overflow-y: auto;
  }
}


@media screen and (min-width: 500px) {
  .full-screen-bottom-sheet  {
    .mat-bottom-sheet-container {
      width: 500px;
      padding: 0;
    }
  }

  .full-screen-modal{
    width: 500px;
  }

  // .mat-mdc-dialog-component-host {
  //   width: 460px;
  // }
}
