<div class="inventory-check-container">
  <div class="row">
    <!-- Bên trái: <PERSON><PERSON> sách sản phẩm kiểm kho -->
    <div class="col-md-8 left-panel">
      <div class="header-section">
        <div class="row">
          <div class="col-md-4">
            <!-- Dropdown chọn kho -->
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>{{ 'INVENTORY_CHECK.SELECT_WAREHOUSE' | translate }}</mat-label>
              <mat-select [(ngModel)]="selectedWarehouse" (selectionChange)="onWarehouseChange($event.value)" [disabled]="hasProducts">
                <mat-option *ngFor="let warehouse of warehouses" [value]="warehouse._id">
                  {{ warehouse.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="col-md-6">
            <!-- Ô tìm kiếm sản phẩm -->
            <app-shared-product-search
              *ngIf="warehouseSelected"
              (productSelected)="onProductSelected($event)">
            </app-shared-product-search>
          </div>
          <div class="col-md-2 d-flex justify-content-end align-items-center">
            <!-- Button filter và icon máy in -->
            <button mat-icon-button [disabled]="!warehouseSelected" (click)="openFilterDialog()">
              <mat-icon>filter_list</mat-icon>
            </button>
            <button mat-icon-button [disabled]="!hasProducts">
              <mat-icon>print</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <mat-tab-group (selectedTabChange)="onTabChange($event)">
        <mat-tab [label]="'INVENTORY_CHECK.TAB_ALL' | translate | tabWithCount: (inventoryItems$ | async)?.length">
          <!-- Danh sách tất cả sản phẩm -->
          <div class="product-list">
            <div *ngIf="(inventoryItems$ | async)?.length === 0" class="empty-state">
              <p>{{ 'INVENTORY_CHECK.NO_PRODUCTS' | translate }}</p>
            </div>

            <!-- Header của danh sách sản phẩm -->
            <div *ngIf="(inventoryItems$ | async) && (inventoryItems$ | async)!.length > 0" class="product-list-header">
              <div class="header-actions">{{ 'INVENTORY_CHECK.PRODUCT.ACTIONS' | translate }}</div>
              <div class="header-info">{{ 'INVENTORY_CHECK.PRODUCT.NAME' | translate }}</div>
              <div class="header-unit">{{ 'INVENTORY_CHECK.PRODUCT.UNIT' | translate }}</div>
              <div class="header-stock">{{ 'INVENTORY_CHECK.PRODUCT.STOCK' | translate }}</div>
              <div class="header-actual">{{ 'INVENTORY_CHECK.PRODUCT.ACTUAL' | translate }}</div>
              <div class="header-difference">{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE' | translate }}</div>
              <div class="header-difference-value">{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE_VALUE' | translate }}</div>
            </div>

            <!-- Danh sách sản phẩm -->
            <app-product-row
              *ngFor="let item of inventoryItems$ | async; trackBy: trackByProductId"
              [item]="item"
              (updateActualQuantity)="inventoryCheckService.updateActualQuantity($event.productId, $event.quantity)"
              (removeProduct)="inventoryCheckService.removeProductFromInventory($event)"
              (addNote)="openNoteDialog($event)"
              (openSerialDialog)="openSerialDialog($event)"
              (selectVariant)="updateProductVariant($event)"
              (updateBatchQuantity)="updateBatchQuantity($event)"
              (addBatch)="openBatchDialog($event)">
            </app-product-row>
          </div>
        </mat-tab>
        <mat-tab [label]="'INVENTORY_CHECK.TAB_MATCHED' | translate | tabWithCount: (matchedItems$ | async)?.length">
          <!-- Danh sách sản phẩm khớp -->
          <div class="product-list">
            <div *ngIf="(matchedItems$ | async)?.length === 0" class="empty-state">
              <p>{{ 'INVENTORY_CHECK.NO_MATCHED_PRODUCTS' | translate }}</p>
            </div>

            <!-- Header của danh sách sản phẩm -->
            <div *ngIf="(matchedItems$ | async) && (matchedItems$ | async)!.length > 0" class="product-list-header">
              <div class="header-actions">{{ 'INVENTORY_CHECK.PRODUCT.ACTIONS' | translate }}</div>
              <div class="header-info">{{ 'INVENTORY_CHECK.PRODUCT.NAME' | translate }}</div>
              <div class="header-unit">{{ 'INVENTORY_CHECK.PRODUCT.UNIT' | translate }}</div>
              <div class="header-stock">{{ 'INVENTORY_CHECK.PRODUCT.STOCK' | translate }}</div>
              <div class="header-actual">{{ 'INVENTORY_CHECK.PRODUCT.ACTUAL' | translate }}</div>
              <div class="header-difference">{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE' | translate }}</div>
              <div class="header-difference-value">{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE_VALUE' | translate }}</div>
            </div>

            <!-- Danh sách sản phẩm -->
            <app-product-row
              *ngFor="let item of matchedItems$ | async; trackBy: trackByProductId"
              [item]="item"
              (updateActualQuantity)="inventoryCheckService.updateActualQuantity($event.productId, $event.quantity)"
              (removeProduct)="inventoryCheckService.removeProductFromInventory($event)"
              (addNote)="openNoteDialog($event)"
              (openSerialDialog)="openSerialDialog($event)"
              (selectVariant)="updateProductVariant($event)"
              (updateBatchQuantity)="updateBatchQuantity($event)"
              (addBatch)="openBatchDialog($event)">
            </app-product-row>
          </div>
        </mat-tab>
        <mat-tab [label]="'INVENTORY_CHECK.TAB_DIFFERENT' | translate | tabWithCount: (differentItems$ | async)?.length">
          <!-- Danh sách sản phẩm lệch -->
          <div class="product-list">
            <div *ngIf="(differentItems$ | async)?.length === 0" class="empty-state">
              <p>{{ 'INVENTORY_CHECK.NO_DIFFERENT_PRODUCTS' | translate }}</p>
            </div>

            <!-- Header của danh sách sản phẩm -->
            <div *ngIf="(differentItems$ | async) && (differentItems$ | async)!.length > 0" class="product-list-header">
              <div class="header-actions">{{ 'INVENTORY_CHECK.PRODUCT.ACTIONS' | translate }}</div>
              <div class="header-info">{{ 'INVENTORY_CHECK.PRODUCT.NAME' | translate }}</div>
              <div class="header-unit">{{ 'INVENTORY_CHECK.PRODUCT.UNIT' | translate }}</div>
              <div class="header-stock">{{ 'INVENTORY_CHECK.PRODUCT.STOCK' | translate }}</div>
              <div class="header-actual">{{ 'INVENTORY_CHECK.PRODUCT.ACTUAL' | translate }}</div>
              <div class="header-difference">{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE' | translate }}</div>
              <div class="header-difference-value">{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE_VALUE' | translate }}</div>
            </div>

            <!-- Danh sách sản phẩm -->
            <app-product-row
              *ngFor="let item of differentItems$ | async; trackBy: trackByProductId"
              [item]="item"
              (updateActualQuantity)="inventoryCheckService.updateActualQuantity($event.productId, $event.quantity)"
              (removeProduct)="inventoryCheckService.removeProductFromInventory($event)"
              (addNote)="openNoteDialog($event)"
              (openSerialDialog)="openSerialDialog($event)"
              (selectVariant)="updateProductVariant($event)"
              (updateBatchQuantity)="updateBatchQuantity($event)"
              (addBatch)="openBatchDialog($event)">
            </app-product-row>
          </div>
        </mat-tab>
        <mat-tab [label]="'INVENTORY_CHECK.TAB_UNCHECKED' | translate | tabWithCount: (uncheckedItems$ | async)?.length">
          <!-- Danh sách sản phẩm chưa kiểm -->
          <div class="product-list">
            <div *ngIf="(uncheckedItems$ | async)?.length === 0" class="empty-state">
              <p>{{ 'INVENTORY_CHECK.NO_UNCHECKED_PRODUCTS' | translate }}</p>
            </div>

            <!-- Header của danh sách sản phẩm -->
            <div *ngIf="(uncheckedItems$ | async) && (uncheckedItems$ | async)!.length > 0" class="product-list-header">
              <div class="header-actions">{{ 'INVENTORY_CHECK.PRODUCT.ACTIONS' | translate }}</div>
              <div class="header-info">{{ 'INVENTORY_CHECK.PRODUCT.NAME' | translate }}</div>
              <div class="header-unit">{{ 'INVENTORY_CHECK.PRODUCT.UNIT' | translate }}</div>
              <div class="header-stock">{{ 'INVENTORY_CHECK.PRODUCT.STOCK' | translate }}</div>
              <div class="header-actual">{{ 'INVENTORY_CHECK.PRODUCT.ACTUAL' | translate }}</div>
              <div class="header-difference">{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE' | translate }}</div>
              <div class="header-difference-value">{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE_VALUE' | translate }}</div>
            </div>

            <!-- Danh sách sản phẩm -->
            <app-product-row
              *ngFor="let item of uncheckedItems$ | async; trackBy: trackByProductId"
              [item]="item"
              (updateActualQuantity)="inventoryCheckService.updateActualQuantity($event.productId, $event.quantity)"
              (removeProduct)="inventoryCheckService.removeProductFromInventory($event)"
              (addNote)="openNoteDialog($event)"
              (openSerialDialog)="openSerialDialog($event)"
              (selectVariant)="updateProductVariant($event)"
              (updateBatchQuantity)="updateBatchQuantity($event)"
              (addBatch)="openBatchDialog($event)">
            </app-product-row>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>

    <!-- Bên phải: Thông tin tổng quan và hành động -->
    <div class="col-md-4 right-panel">
      <div class="card">
        <div class="card-body">
          <!-- Thông tin nhân viên và thời gian -->
          <div class="row mb-3">
            <div class="col-md-6">
              <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{ 'INVENTORY_CHECK.EMPLOYEE' | translate }}</mat-label>
                <mat-select [disabled]="!warehouseSelected" [(ngModel)]="selectedEmployee" (selectionChange)="onEmployeeChange($event.value)">
                  <mat-option *ngFor="let employee of employees" [value]="employee._id">
                    {{ employee.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{ 'INVENTORY_CHECK.DATE' | translate }}</mat-label>
                <input matInput [matDatepicker]="picker" [disabled]="!warehouseSelected" [(ngModel)]="inventoryDate" (dateChange)="onDateChange($event.value)">
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
              </mat-form-field>
            </div>
          </div>

          <!-- Ghi chú -->
          <div class="row mb-3">
            <div class="col-12">
              <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{ 'INVENTORY_CHECK.NOTE' | translate }}</mat-label>
                <textarea matInput rows="3" [disabled]="!warehouseSelected" [(ngModel)]="inventoryNote" (ngModelChange)="onNoteChange($event)"></textarea>
              </mat-form-field>
            </div>
          </div>

          <!-- Thông tin tổng hợp -->
          <app-inventory-check-summary [inventoryCheck]="inventoryCheck$ | async"></app-inventory-check-summary>

          <!-- Buttons -->
          <div class="action-buttons">
            <button mat-stroked-button class="me-2" [disabled]="!hasProducts" (click)="saveDraft()">
              {{ 'INVENTORY_CHECK.SAVE_DRAFT' | translate }}
            </button>
            <button mat-raised-button color="primary" [disabled]="!hasProducts" (click)="completeInventoryCheck()">
              {{ 'INVENTORY_CHECK.COMPLETE' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
