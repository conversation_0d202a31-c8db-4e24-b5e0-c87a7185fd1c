import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LocationListComponent } from './location-list.component';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { LocationService } from '../services/location.service';
import { of, throwError } from 'rxjs';
import { LocationNode, LocationType, LocationStatus } from '../models/view/location-view.model';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

describe('LocationListComponent', () => {
  let component: LocationListComponent;
  let fixture: ComponentFixture<LocationListComponent>;
  let router: Router;
  let snackBar: MatSnackBar;
  let dialog: MatDialog;
  let locationService: LocationService;
  let translateService: TranslateService;

  const mockLocation: LocationNode = {
    key: '1',
    label: 'Warehouse 1 (WH-001)',
    data: {
      id: '1',
      name: 'Warehouse 1',
      code: 'WH-001',
      type: LocationType.Warehouse,
      parentId: null,
      level: 1,
      capacity: 1000,
      status: LocationStatus.Active,
      dimensions: {
        length: 10,
        width: 10,
        height: 5,
        depth: 20
      },
      createdAt: new Date(),
      updatedAt: new Date()
    },
    icon: 'pi pi-home',
    children: [],
    expanded: false,
    leaf: false,
    selectable: true
  };

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigateByUrl']);
    const snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
    const locationServiceSpy = jasmine.createSpyObj('LocationService', [
      'getLocations',
      'deleteLocation'
    ]);

    locationServiceSpy.getLocations.and.returnValue(of([mockLocation]));
    locationServiceSpy.deleteLocation.and.returnValue(of(void 0));

    await TestBed.configureTestingModule({
      imports: [
        LocationListComponent,
        TranslateModule.forRoot()
      ],
      providers: [
        provideNoopAnimations(),
        { provide: Router, useValue: routerSpy },
        { provide: MatSnackBar, useValue: snackBarSpy },
        { provide: MatDialog, useValue: dialogSpy },
        { provide: LocationService, useValue: locationServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LocationListComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    snackBar = TestBed.inject(MatSnackBar);
    dialog = TestBed.inject(MatDialog);
    locationService = TestBed.inject(LocationService);
    translateService = TestBed.inject(TranslateService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load locations on init', () => {
    expect(locationService.getLocations).toHaveBeenCalled();
    expect(component.locations).toBeDefined();
  });

  it('should select a location', () => {
    component.onSelectLocation(mockLocation);
    expect(component.selectedLocation()).toEqual(mockLocation);
  });

  it('should navigate to create page when adding a new location', () => {
    component.onAddLocation();
    expect(router['navigateByUrl']).toHaveBeenCalledWith('/warehouse/locations/create');
  });

  it('should navigate to edit page when editing a location', () => {
    component.onSelectLocation(mockLocation);
    component.onEditLocation();
    expect(router['navigateByUrl']).toHaveBeenCalledWith(`/warehouse/locations/edit/${mockLocation.data.id}`);
  });

  it('should show error snackbar when trying to edit without selection', () => {
    spyOn(window, 'confirm').and.returnValue(true);
    spyOn(translateService, 'instant').and.returnValue('Select a location first');

    component.onEditLocation();

    expect(snackBar['open']).toHaveBeenCalledWith(
      'Select a location first',
      'Close',
      { duration: 3000 }
    );
  });

  it('should show confirmation dialog and delete location when confirmed', () => {
    spyOn(window, 'confirm').and.returnValue(true);
    spyOn(translateService, 'instant').and.returnValue('Location deleted successfully');

    component.onSelectLocation(mockLocation);
    component.onDeleteLocation();

    expect(window.confirm).toHaveBeenCalled();
    expect(locationService['deleteLocation']).toHaveBeenCalledWith(mockLocation.data.id);
    expect(snackBar['open']).toHaveBeenCalledWith(
      'Location deleted successfully',
      'Close',
      { duration: 3000 }
    );
  });

  it('should not delete location when confirmation is cancelled', () => {
    spyOn(window, 'confirm').and.returnValue(false);

    component.onSelectLocation(mockLocation);
    component.onDeleteLocation();

    expect(window.confirm).toHaveBeenCalled();
    expect(locationService['deleteLocation']).not.toHaveBeenCalled();
  });

  it('should show error snackbar when trying to delete without selection', () => {
    spyOn(translateService, 'instant').and.returnValue('Select a location first');

    component.onDeleteLocation();

    expect(snackBar['open']).toHaveBeenCalledWith(
      'Select a location first',
      'Close',
      { duration: 3000 }
    );
  });

  it('should handle error when deleting location fails', () => {
    spyOn(window, 'confirm').and.returnValue(true);
    locationService['deleteLocation'].and.returnValue(throwError(() => new Error('Delete failed')));
    spyOn(translateService, 'instant').and.returnValue('Error deleting location');
    spyOn(console, 'error');

    component.onSelectLocation(mockLocation);
    component.onDeleteLocation();

    expect(locationService['deleteLocation']).toHaveBeenCalledWith(mockLocation.data.id);
    expect(console.error).toHaveBeenCalled();
    expect(snackBar['open']).toHaveBeenCalledWith(
      'Error deleting location',
      'Close',
      { duration: 3000 }
    );
  });
});
