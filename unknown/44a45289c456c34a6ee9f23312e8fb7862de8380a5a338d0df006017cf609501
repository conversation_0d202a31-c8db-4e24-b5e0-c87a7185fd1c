.taxes-container {
  margin-bottom: 1rem;
}

.clickable-row {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.toggle-icon {
  transition: transform 0.3s;
}

.taxes-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
  background-color: #f8f9fa;
  border-radius: 0 0 4px 4px;

  &.expanded {
    max-height: 500px;
    padding: 8px 12px;
    margin-bottom: 8px;
  }
}

.tax-item {
  border-bottom: 1px solid #e9ecef;
  padding: 4px 0;

  &:last-child {
    border-bottom: none;
  }
}

.tax-type {
  font-weight: 500;
}

.tax-rate {
  font-size: 0.9rem;
}

.tax-actions {
  display: flex;
  align-items: center;
}

.small-button {
  width: 28px;
  height: 28px;
  line-height: 28px;
}

.small-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

.badge {
  font-size: 0.7rem;
  padding: 3px 6px;
  border-radius: 10px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .clickable-row {
    flex-direction: column;
    align-items: flex-start !important;

    > div:first-child {
      margin-bottom: 0.5rem;
    }

    > div:last-child {
      width: 100%;
      justify-content: space-between;
    }
  }

  .tax-item {
    .d-flex {
      flex-direction: column;

      .tax-info {
        margin-bottom: 0.5rem;
      }

      .tax-actions {
        margin-top: 0.5rem;
        align-self: flex-end;
      }
    }
  }
}
