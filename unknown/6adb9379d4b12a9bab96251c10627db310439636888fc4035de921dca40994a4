.product-filter-dialog {
  min-width: 400px;
  max-width: 600px;
  padding: 0;
}

.modal-content {
  padding: 0 16px 16px;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 8px 16px 16px;
}

.filter-section {
  margin-bottom: 20px;

  h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: #333;
  }
}

.categories-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
  padding: 5px;
}

.category-checkbox {
  flex: 0 0 calc(50% - 10px);
  margin-bottom: 5px;
}

.location-select {
  width: 100%;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-checkbox {
  margin-bottom: 5px;
}

/* Responsive styles */
@media (max-width: 600px) {
  .product-filter-dialog {
    min-width: 300px;
  }

  .category-checkbox {
    flex: 0 0 100%;
  }

  .modal-content {
    max-height: 60vh;
  }
}
