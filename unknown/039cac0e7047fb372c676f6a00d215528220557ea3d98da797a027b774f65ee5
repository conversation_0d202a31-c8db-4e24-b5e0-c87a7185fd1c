import { padNumber } from './string.util';

export function waitMsTillTheTime(date: Date, hour: number, minute: number, seconds?: number) {
  date.setHours(hour);
  date.setMinutes(minute);
  date.setSeconds(seconds ?? 0);

  const minTime = date.getTime();
  const now = Date.now();
  let waitTime = minTime - now;
  if(waitTime < 0) {
    waitTime = 0;
  }

  return {
    waitTime: minTime - now,
    minTime
  };
}
export function getXDaysAfter(xDays: number, fromDate?: Date): Date {
  if(!fromDate) {
    fromDate = new Date();
  }
  return new Date(fromDate.getTime() + xDays * 24 * 60 * 60000);
}

export function wait(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export function isValidDate(d: Date) {
  return d instanceof Date && !Number.isNaN(d as any);
}

export function formatDateTimeLocalValue(date: Date) {
  return `${date.getFullYear()}-${padNumber(date.getMonth() + 1)}-${padNumber(date.getDate())}T${padNumber(date.getHours())}:${padNumber(date.getMinutes())}`;
}

export function getMidnightTime(d?: Date) {
  d = d || getHanoiTime();
  d.setHours(0, 0, 0, 0);
  return d;
}

export function getHanoiTime(_d?: string | number | Date) {
  const d = _d ? new Date(_d) : new Date();
  const offset = 7; // gmt +7
  const utc = d.getTime() + (d.getTimezoneOffset() * 60000);

  // create new Date object for different city
  // using supplied offset
  const nd = new Date(utc + (3600000 * offset));
  return nd;
}
