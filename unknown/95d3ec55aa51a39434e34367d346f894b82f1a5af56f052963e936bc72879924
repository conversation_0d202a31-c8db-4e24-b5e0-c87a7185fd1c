:host {
  display: block;
}

.new-cost-button {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.table-container {
  position: relative;
  max-height: 400px;
  overflow: auto;
  border-radius: 4px;
}

table {
  width: 100%;
}

.mat-mdc-header-cell {
  font-weight: 600;
  color: rgba(0, 0, 0, 0.7);
}

.mat-mdc-column-select {
  width: 60px;
  text-align: center;
}

.mat-mdc-column-name {
  min-width: 200px;
}

.mat-mdc-column-type {
  width: 100px;
}

.mat-mdc-column-defaultValue,
.mat-mdc-column-customValue {
  width: 130px;
}

.mat-mdc-column-paidToSupplier,
.mat-mdc-column-allocateToItems {
  width: 100px;
  text-align: center;
}

.mat-mdc-column-tax {
  width: 150px;
}

.custom-value-field {
  width: 120px;
  margin-bottom: 0;
}

mat-dialog-content {
  min-width: 800px;
  max-width: 100%;
  max-height: 600px;
  margin-top: -16px;
}

@media (max-width: 992px) {
  mat-dialog-content {
    min-width: 100%;
  }

  .table-container {
    max-width: 100%;
    overflow-x: auto;
  }
}

.text-gray-600 {
  color: #6b7280;
}

.text-sm {
  font-size: 0.875rem;
}

.mt-3 {
  margin-top: 1rem;
}

.notice {
  font-style: italic;
  margin-top: 4px;
}

::ng-deep .mat-mdc-form-field-subscript-wrapper {
  height: 12px !important;
}

::ng-deep .mat-mdc-form-field-bottom-align::before {
  height: 0px !important;
}
