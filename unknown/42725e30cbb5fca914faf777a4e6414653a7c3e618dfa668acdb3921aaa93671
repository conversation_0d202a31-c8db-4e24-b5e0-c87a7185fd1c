<div class="warehouse-section" [formGroup]="warehouseForm">
  <div class="row">
    <!-- Chọ<PERSON> kho hàng -->
    <div class="col-md-3 mb-3">
      <mat-form-field class="w-100" appearance="outline">
        <mat-label>{{ 'COMMON.WAREHOUSE' | translate }}</mat-label>
        <mat-select formControlName="warehouseId">
          <mat-option *ngFor="let warehouse of warehouses" [value]="warehouse._id">
            {{ warehouse.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="warehouseForm.get('warehouseId')?.hasError('required')">
          {{ 'VALIDATION.REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Chọn nhà cung cấp -->
    <div class="col-md-3 mb-3 position-relative">
      <div>
        <mat-form-field class="w-100" appearance="outline">
          <mat-label>{{ 'COMMON.SUPPLIER' | translate }}</mat-label>
          <mat-select formControlName="supplierId">
            <mat-option *ngFor="let supplier of suppliers" [value]="supplier._id">
              {{ supplier.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div>
        <button type="button" class="add-supplier-btn" mat-icon-button color="primary"
              matTooltip="{{ 'PRODUCT.PRODUCT_FORM.ADD_SUPPLIER' | translate }}"
              (click)="onAddSupplier()">
          <mat-icon>add_circle</mat-icon>
        </button>
      </div>
    </div>

    <!-- Chọn vị trí trong kho - sử dụng modal -->
    <div class="col-md-3 mb-3">
      <mat-form-field class="w-100" appearance="outline">
        <mat-label>{{ 'PRODUCT.PRODUCT_FORM.LOCATION' | translate }}</mat-label>
        <input matInput
               readonly
               [placeholder]="'WAREHOUSE.SELECT_LOCATION' | translate"
               [value]="getLocationPath()"
               [disabled]="!warehouseForm.get('warehouseId')?.value"
               (click)="openLocationPickerModal()">
        <button *ngIf="warehouseForm.get('locationId')?.value"
                matSuffix
                mat-icon-button
                type="button"
                (click)="$event.stopPropagation(); warehouseForm.get('locationId')?.setValue(null)">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>
    </div>

    <!-- Số lượng tồn kho -->
    <div class="col-md-3 mb-3">
      <mat-form-field class="w-100" appearance="outline">
        <mat-label>{{ 'PRODUCT.PRODUCT_FORM.QUANTITY' | translate }}</mat-label>
        <input matInput type="number" formControlName="quantity" min="0">
        <mat-error *ngIf="warehouseForm.get('quantity')?.hasError('required')">
          {{ 'VALIDATION.REQUIRED' | translate }}
        </mat-error>
        <mat-error *ngIf="warehouseForm.get('quantity')?.hasError('min')">
          {{ 'VALIDATION.MIN' | translate: { value: 0 } }}
        </mat-error>
      </mat-form-field>
    </div>
  </div>
</div>
