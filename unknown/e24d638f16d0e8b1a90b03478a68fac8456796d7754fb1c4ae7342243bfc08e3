.preview-container {
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

  h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e0e0e0;
    text-align: center;
  }

  .preview-tree {
    flex: 1;
    overflow-y: auto;
    min-height: 500px;
    max-height: calc(100vh - 200px);
    padding-right: 8px;

    ::ng-deep {
      .location-preview-tree {
        .p-treenode-content {
          padding: 0.5rem;
          border-radius: 4px;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: rgba(0, 0, 0, 0.04);
          }
        }

        .p-treenode-icon {
          margin-right: 0.5rem;
        }

        .p-tree-container {
          padding: 0.5rem;
        }
      }
    }
  }

  .preview-empty {
    padding: 2rem;
    text-align: center;
    color: rgba(0, 0, 0, 0.5);
    font-style: italic;
    border: 1px dashed #e0e0e0;
    border-radius: 4px;
    margin: 1rem;
  }
}

// Responsive styles
@media screen and (max-width: 992px) {
  .preview-container {
    .preview-tree {
      min-height: 300px;
      max-height: 400px;
    }
  }
}

@media screen and (max-width: 768px) {
  .preview-container {
    .preview-tree {
      min-height: 200px;
      max-height: 300px;
    }
  }
}
