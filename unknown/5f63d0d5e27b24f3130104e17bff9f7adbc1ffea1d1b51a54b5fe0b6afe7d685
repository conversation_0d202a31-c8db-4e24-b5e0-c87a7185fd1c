<div class="product-row">
  <!-- Cột 1: Actions -->
  <div class="product-actions">
    <button mat-icon-button color="warn" (click)="onRemove()" matTooltip="{{ 'INVENTORY_CHECK.PRODUCT.REMOVE' | translate }}">
      <mat-icon>delete</mat-icon>
    </button>
    <button mat-icon-button (click)="onAddNote()" matTooltip="{{ 'INVENTORY_CHECK.PRODUCT.ADD_NOTE' | translate }}">
      <mat-icon>note_add</mat-icon>
    </button>
    <button *ngIf="hasSerials()" mat-icon-button (click)="onOpenSerialDialog()" matTooltip="{{ 'INVENTORY_CHECK.PRODUCT.MANAGE_SERIALS' | translate }}">
      <mat-icon>list</mat-icon>
      <span *ngIf="getSerialCount() > 0" class="serial-count">{{ getSerialCount() }}</span>
    </button>
  </div>

  <!-- Cột 2: Thông tin sản phẩm -->
  <div class="product-info">
    <div class="product-name">{{ item.product.name }}</div>
    <div class="product-sku">{{ item.product.sku }}</div>
  </div>

  <!-- Cột 3: Đơn vị tính -->
  <div class="product-unit" attr.data-label="{{ 'INVENTORY_CHECK.PRODUCT.UNIT' | translate }}">
    {{ item.product.unit }}
  </div>

  <!-- Cột 4: Tồn kho -->
  <div class="product-stock" attr.data-label="{{ 'INVENTORY_CHECK.PRODUCT.STOCK' | translate }}">
    {{ item.stockQuantity }}
  </div>

  <!-- Cột 5: Thực tế -->
  <div class="product-actual" attr.data-label="{{ 'INVENTORY_CHECK.PRODUCT.ACTUAL' | translate }}">
    <mat-form-field appearance="outline" class="quantity-input">
      <input matInput type="number" [ngModel]="item.actualQuantity"
        (ngModelChange)="onActualQuantityChange($event)"
        [disabled]="hasSerials()"
        min="0">
    </mat-form-field>
  </div>

  <!-- Cột 6: Lệch -->
  <div class="product-difference" attr.data-label="{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE' | translate }}" [ngClass]="{'positive': item.differenceQuantity > 0, 'negative': item.differenceQuantity < 0}">
    {{ item.differenceQuantity }}
  </div>

  <!-- Cột 7: Giá trị lệch -->
  <div class="product-difference-value" attr.data-label="{{ 'INVENTORY_CHECK.PRODUCT.DIFFERENCE_VALUE' | translate }}" [ngClass]="{'positive': item.differenceValue > 0, 'negative': item.differenceValue < 0}">
    {{ item.differenceValue | number }} VND
  </div>
</div>

<!-- Hiển thị ghi chú nếu có -->
<div *ngIf="hasNote()" class="note-row">
  <div class="note-label">
    <mat-icon>note</mat-icon>
    {{ 'COMMON.NOTE' | translate }}:
  </div>
  <div class="note-content">
    {{ (itemSignal() || item).note }}
  </div>
</div>

<!-- Hiển thị thông tin variant nếu có -->
<div *ngIf="hasVariants()" class="variant-row">
  <div class="variant-label">{{ 'INVENTORY_CHECK.PRODUCT.VARIANT' | translate }}:</div>
  <div class="variant-value">
    <span *ngFor="let variant of item.product.variant | keyvalue">
      {{ variant.key }}: {{ variant.value }}
    </span>
    <button mat-icon-button color="primary" (click)="openVariantSelector()" matTooltip="{{ 'INVENTORY_CHECK.PRODUCT.CHANGE_VARIANT' | translate }}">
      <mat-icon>edit</mat-icon>
    </button>
  </div>
</div>

<!-- Hiển thị thông tin lô nếu có -->
<div *ngIf="hasBatches() && item.batchDetails && item.batchDetails.length > 0" class="batch-container">
  <div *ngFor="let batch of item.batchDetails" class="batch-row">
    <div class="batch-info">
      <div class="batch-code">{{ batch.batch.batchCode }}</div>
      <div class="batch-expiry">{{ 'INVENTORY_CHECK.PRODUCT.EXPIRY_DATE' | translate }}: {{ batch.batch.expiryDate | date }}</div>
    </div>
    <div class="batch-stock" attr.data-label="{{ 'INVENTORY_CHECK.PRODUCT.STOCK' | translate }}">{{ batch.stockQuantity }}</div>
    <div class="batch-actual" attr.data-label="{{ 'INVENTORY_CHECK.PRODUCT.ACTUAL' | translate }}">
      <mat-form-field appearance="outline" class="quantity-input">
        <input matInput type="number" [ngModel]="batch.actualQuantity"
          (ngModelChange)="onBatchQuantityChange(batch.batch._id, $event)"
          min="0">
      </mat-form-field>
    </div>
  </div>
  <button mat-button color="primary" class="add-batch-button" (click)="onAddBatch()">
    <mat-icon>add</mat-icon> {{ 'INVENTORY_CHECK.PRODUCT.ADD_BATCH' | translate }}
  </button>
</div>
