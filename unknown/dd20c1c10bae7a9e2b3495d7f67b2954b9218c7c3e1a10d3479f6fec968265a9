<div class="variant-selector-container">
  <h2 class="title">{{ 'VARIANT_SELECTOR.TITLE' | translate }}</h2>

  <!-- <PERSON><PERSON><PERSON> thuộc tính -->
  <div class="attributes-section" *ngIf="data.variants.length > 0">
    <h3 class="section-title">{{ 'VARIANT_SELECTOR.VARIANT' | translate }}</h3>
    <p class="section-description" *ngIf="getAttributeGroupsCount() > 0">
      {{ 'VARIANT_SELECTOR.SELECT_VARIANT' | translate }}
    </p>
    <p class="no-data-message" *ngIf="getAttributeGroupsCount() === 0">
      {{ 'VARIANT_SELECTOR.NO_VARIANTS' | translate }}
    </p>
    <div class="attribute-group" *ngFor="let group of attributeGroups | keyvalue">
      <h4 class="group-title">{{ group.key }}</h4>
      <div class="attribute-values">
        <button
          mat-stroked-button
          *ngFor="let value of group.value"
          [class.selected]="isAttributeSelected(group.key, value)"
          (click)="selectAttribute(group.key, value)"
          class="attribute-button">
          {{ value }}
        </button>
      </div>
    </div>
  </div>

  <!-- Phần đơn vị tính -->
  <div class="units-section" *ngIf="data.units.length > 0">
    <h3 class="section-title">{{ 'VARIANT_SELECTOR.UNIT' | translate }}</h3>
    <p class="section-description">
      {{ 'VARIANT_SELECTOR.SELECT_UNIT' | translate }}
    </p>
    <div class="unit-values">
      <mat-radio-group [value]="selectedUnit()" (change)="selectUnit($event.value)" class="unit-radio-group">
        <mat-radio-button
          *ngFor="let unit of data.units"
          [value]="unit"
          class="unit-radio-button">
          {{ unit.unitName }}
          <span *ngIf="unit.conversionRate">
            ({{ unit.conversionRate | number }})
          </span>
        </mat-radio-button>
      </mat-radio-group>
    </div>
  </div>
  <p class="no-data-message" *ngIf="data.units.length === 0">
    {{ 'VARIANT_SELECTOR.NO_UNITS' | translate }}
  </p>

  <!-- Phần nút điều khiển -->
  <div class="action-buttons">
    <button mat-stroked-button (click)="cancel()">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button
      mat-raised-button
      color="primary"
      [disabled]="!isConfirmEnabled()"
      (click)="confirm()">
      {{ 'COMMON.CONFIRM' | translate }}
    </button>
  </div>
</div>
