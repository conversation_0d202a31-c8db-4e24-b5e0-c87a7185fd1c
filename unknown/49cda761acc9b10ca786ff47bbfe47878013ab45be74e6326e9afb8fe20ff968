import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { Place, Province, District, Ward } from 'salehub_shared_contracts';
import { AddressManualSelectorComponent } from '@shared/components/input/input-place/components/address-manual-selector/address-manual-selector.component';

@Component({
  selector: 'app-edit-place-details-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule,
    AddressManualSelectorComponent
  ],
  template: `
    <h2 mat-dialog-title>{{ 'ADDRESS.EDIT_DETAILS' | translate }}</h2>
    <mat-dialog-content>
      <!-- Component chọn địa chỉ -->
      <app-address-manual-selector
        #addressSelector
        [defaultValue]="place"
        (selectedPlace)="onPlaceChanged($event)">
      </app-address-manual-selector>

      <!-- Hướng dẫn -->
      <mat-form-field class="w-full mb-3">
        <mat-label>{{ 'ADDRESS.INSTRUCTION' | translate }}</mat-label>
        <input matInput [(ngModel)]="place.instruction">
      </mat-form-field>
    </mat-dialog-content>



    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">
        {{ 'COMMON.CANCEL' | translate }}
      </button>
      <button mat-raised-button color="primary" (click)="onSave()">
        {{ 'COMMON.SAVE' | translate }}
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    :host {
      display: block;
      min-width: 500px;
    }
    mat-dialog-content {
      padding-top: 20px;
    }
  `]
})
export class EditPlaceDetailsDialogComponent {
  place: Place;

  constructor(
    public dialogRef: MatDialogRef<EditPlaceDetailsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { place: Place }
  ) {
    // Clone place để không ảnh hưởng đến data gốc khi chưa save
    this.place = JSON.parse(JSON.stringify(data.place));
  }

  onPlaceChanged(place: Place) {
    this.place = place;
  }

  onSave(): void {
    this.dialogRef.close(this.place);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
