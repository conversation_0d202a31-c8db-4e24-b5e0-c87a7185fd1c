import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { ActionButtonsService } from './action-buttons.service';
import { GoodsReceipt } from '../../models/api/goods-receipt.dto';

@Component({
  selector: 'app-action-buttons',
  templateUrl: './action-buttons.component.html',
  styleUrls: ['./action-buttons.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  providers: [ActionButtonsService],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ActionButtonsComponent {
  /**
   * Trạng thái form c<PERSON> hợp lệ không
   */
  @Input() isValid: boolean = false;

  /**
   * D<PERSON> liệu phiếu nhập kho
   */
  @Input() formData!: GoodsReceipt;

  /**
   * Sự kiện khi nhấn nút lưu nháp
   */
  @Output() saveDraft = new EventEmitter<GoodsReceipt>();

  /**
   * Sự kiện khi nhấn nút hoàn thành
   */
  @Output() complete = new EventEmitter<GoodsReceipt>();

  constructor(private actionButtonsService: ActionButtonsService) {}

  /**
   * Xử lý khi nhấn nút lưu nháp
   */
  onSaveDraft(): void {
    // Kiểm tra validation cơ bản
    // if (!this.actionButtonsService.validateBasicInfo(this.formData)) {
    //   console.warn('Thông tin cơ bản không hợp lệ');
    //   return;
    // }

    // Đặt trạng thái là nháp
    const draftData = this.actionButtonsService.prepareDataForSave(this.formData, 'draft');

    // Log dữ liệu vào console
    console.log('Lưu nháp:', draftData);

    // Emit sự kiện lưu nháp
    this.saveDraft.emit(draftData);
  }

  /**
   * Xử lý khi nhấn nút hoàn thành
   */
  onComplete(): void {
    // Kiểm tra validation đầy đủ
    if (!this.isValid) {
      console.warn('Dữ liệu không hợp lệ');
      return;
    }

    // Kiểm tra validation chi tiết
    const validationResult = this.actionButtonsService.validateCompleteData(this.formData);
    if (!validationResult.isValid) {
      console.warn('Lỗi validation:', validationResult.errors);
      return;
    }

    // Đặt trạng thái là hoàn thành
    const completeData = this.actionButtonsService.prepareDataForSave(this.formData, 'completed');

    // Log dữ liệu vào console
    console.log('Hoàn thành:', completeData);

    // Emit sự kiện hoàn thành
    this.complete.emit(completeData);
  }
}
