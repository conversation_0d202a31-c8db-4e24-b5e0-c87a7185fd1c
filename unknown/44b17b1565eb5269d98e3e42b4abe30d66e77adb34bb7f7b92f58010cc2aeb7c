import { Component, Input, OnChanges, SimpleChanges, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TreeModule } from 'primeng/tree';
import { TreeNode } from 'primeng/api';
import { TranslateModule } from '@ngx-translate/core';
import { LocationFormData, LocationType, LocationStatus } from '../../models/api/location.dto';

@Component({
  selector: 'app-location-form-preview',
  standalone: true,
  imports: [CommonModule, TreeModule, TranslateModule],
  templateUrl: './location-form-preview.component.html',
  styleUrls: ['./location-form-preview.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LocationFormPreviewComponent implements OnChanges {
  @Input() formData?: LocationFormData;
  @Input() currentLevel = 1;

  previewData: TreeNode[] = [];

  constructor() {
    // Constructor dependency injection
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.formData) {
      this.updatePreview();
    }
  }

  private updatePreview(): void {
    if (!this.formData) return;

    // Create a node for the location being created/edited
    const node: TreeNode = {
      key: 'preview',
      label: `${this.formData.name} (${this.formData.code})`,
      data: this.formData,
      icon: this.getIconForLocationType(this.formData.type),
      children: [],
      expanded: true,
      leaf: this.formData.quantity <= 1 && (!this.formData.children || this.formData.children.length === 0),
      selectable: false
    };

    // If creating multiple locations, add children nodes for quantity
    if (this.formData.quantity > 1) {
      for (let i = 1; i <= this.formData.quantity; i++) {
        const childCode = `${this.formData.code}-${i.toString().padStart(2, '0')}`;
        node.children?.push({
          key: `preview-${i}`,
          label: `${this.formData.name} ${i} (${childCode})`,
          data: { ...this.formData, code: childCode },
          icon: this.getIconForLocationType(this.formData.type),
          children: [],
          expanded: false,
          leaf: true,
          selectable: false
        });
      }
    }

    // Nếu có form con (children), thêm các node con tương ứng
    if (this.formData.children && this.formData.children.length > 0) {
      // Nếu quantity > 1, thêm children vào mỗi node con của quantity
      if (this.formData.quantity > 1) {
        // Thêm children vào mỗi node cha (khi quantity > 1)
        node.children?.forEach((quantityNode, qIndex) => {
          quantityNode.leaf = false;
          quantityNode.expanded = true;
          quantityNode.children = this.formData?.children?.map((child, cIndex) => {
            const childCode = `${quantityNode.data.code}-${this.getChildPrefix(child.type)}${cIndex + 1}`;
            return this.createChildNode(child, childCode, `child-${qIndex}-${cIndex}`);
          });
        });
      } else {
        // Thêm children trực tiếp vào node cha (khi quantity = 1)
        node.children = this.formData.children.map((child, index) => {
          const childCode = `${this.formData?.code}-${this.getChildPrefix(child.type)}${index + 1}`;
          return this.createChildNode(child, childCode, `child-${index}`);
        });
      }
    }

    this.previewData = [node];
  }

  private createChildNode(child: LocationFormData, code: string, key: string): TreeNode {
    const childNode: TreeNode = {
      key,
      label: `${child.name} (${code})`,
      data: { ...child, code },
      icon: this.getIconForLocationType(child.type),
      children: [],
      expanded: true,
      leaf: child.quantity <= 1 && (!child.children || child.children.length === 0),
      selectable: false
    };

    // Nếu child có quantity > 1, tạo các node con cho child
    if (child.quantity > 1) {
      for (let i = 1; i <= child.quantity; i++) {
        const quantityChildCode = `${code}-${i.toString().padStart(2, '0')}`;
        childNode.children?.push({
          key: `${key}-quantity-${i}`,
          label: `${child.name} ${i} (${quantityChildCode})`,
          data: { ...child, code: quantityChildCode },
          icon: this.getIconForLocationType(child.type),
          children: [],
          expanded: false,
          leaf: true,
          selectable: false
        });
      }
    }

    // Xử lý đệ quy nếu child có children
    if (child.children && child.children.length > 0) {
      if (child.quantity > 1) {
        // Thêm children vào mỗi node con của quantity
        childNode.children?.forEach((quantityNode, qIndex) => {
          quantityNode.leaf = false;
          quantityNode.expanded = true;
          quantityNode.children = child.children?.map((grandChild, gcIndex) => {
            const grandChildCode = `${quantityNode.data.code}-${this.getChildPrefix(grandChild.type)}${gcIndex + 1}`;
            return this.createChildNode(grandChild, grandChildCode, `${key}-${qIndex}-grandchild-${gcIndex}`);
          });
        });
      } else {
        // Thêm children trực tiếp vào node cha (khi quantity = 1)
        childNode.children = child.children.map((grandChild, index) => {
          const grandChildCode = `${code}-${this.getChildPrefix(grandChild.type)}${index + 1}`;
          return this.createChildNode(grandChild, grandChildCode, `${key}-grandchild-${index}`);
        });
      }
    }

    return childNode;
  }

  private getChildPrefix(type: LocationType): string {
    switch (type) {
    case LocationType.Zone: return 'Z';
    case LocationType.Rack: return 'R';
    case LocationType.Shelf: return 'S';
    case LocationType.Bin: return 'B';
    default: return '';
    }
  }

  private getIconForLocationType(type: LocationType): string {
    switch (type) {
    case LocationType.Warehouse:
      return 'pi pi-home';
    case LocationType.Zone:
      return 'pi pi-th-large';
    case LocationType.Rack:
      return 'pi pi-server';
    case LocationType.Shelf:
      return 'pi pi-list';
    case LocationType.Bin:
      return 'pi pi-inbox';
    default:
      return 'pi pi-folder';
    }
  }
}
