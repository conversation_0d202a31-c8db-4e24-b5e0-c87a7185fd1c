<div class="product-search-container">
  <mat-form-field appearance="outline" class="w-100">
    <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.SEARCH_PRODUCT' | translate }}</mat-label>
    <input matInput
           [formControl]="searchControl"
           [matAutocomplete]="auto"
           placeholder="{{ 'PRODUCT_SELECTION.PRODUCT_SEARCH_PLACEHOLDER' | translate }}">
    <mat-autocomplete #auto="matAutocomplete"
                      [displayWith]="displayProductFn"
                      (optionSelected)="onProductSelected($event.option.value)">
      <mat-option *ngFor="let product of filteredProducts$ | async" [value]="product">
        <div>{{ product.name }}</div>
        <small *ngIf="product.sku" class="text-muted">{{ product.sku }}</small>
      </mat-option>
    </mat-autocomplete>
  </mat-form-field>
</div>
