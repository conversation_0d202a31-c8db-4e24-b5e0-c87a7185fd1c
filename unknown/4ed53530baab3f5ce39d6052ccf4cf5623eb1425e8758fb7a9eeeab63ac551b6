:host {
  .location-form-container {
    display: flex;
    flex-direction: row;
    gap: 24px;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 16px;

    @media screen and (max-width: 992px) {
      flex-direction: column;
      padding: 12px;
    }

    @media screen and (max-width: 576px) {
      padding: 8px;
    }

    .location-form {
      flex: 1;
      min-width: 0;
    }

    .preview-column {
      flex: 0 0 40%;
      min-width: 0;
      max-width: 450px;
      position: sticky;
      top: 24px;
      align-self: flex-start;
      height: calc(100vh - 100px);
      overflow-y: auto;

      @media screen and (max-width: 992px) {
        max-width: 100%;
        margin-top: 24px;
        position: static;
        height: auto;
        overflow-y: visible;
      }
    }
  }

  .location-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    margin: 0;

    .form-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    /* Override fix lỗi gạch dọc trong form fields */
    ::ng-deep {
      .mat-mdc-text-field-wrapper {
        .mdc-notched-outline {
          .mdc-notched-outline__leading {
            border-right-style: none !important;
            border-color: rgba(0, 0, 0, 0.12) !important;
          }

          .mdc-notched-outline__notch {
            border-left-style: none !important;
            border-right-style: none !important;
            border-color: rgba(0, 0, 0, 0.12) !important;
          }

          .mdc-notched-outline__trailing {
            border-left-style: none !important;
            border-color: rgba(0, 0, 0, 0.12) !important;
          }
        }
      }

      /* Khi focus */
      .mat-mdc-form-field.mat-focused .mdc-text-field-wrapper {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #3f51b5 !important;
          border-width: 2px !important;
        }
      }
    }
  }

  .form-row {
    display: flex;
    flex-direction: column;

    &.code-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 16px;

      mat-form-field {
        flex: 1;
      }

      .auto-code-checkbox {
        margin-top: -20px;
      }
    }
  }

  .dimensions-row {
    .dimensions-inputs {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
    }
  }

  .level-info {
    margin: 8px 0;
    color: rgba(0, 0, 0, 0.6);
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 16px;
  }
}

.location-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;

  .form-row {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    @media (min-width: 768px) {
      flex-direction: row;
      align-items: flex-start;
      margin-bottom: 1rem;

      mat-form-field {
        flex: 1;
      }
    }

    &.code-row {
      align-items: center;
      flex-wrap: wrap;

      mat-form-field {
        flex: 2;
        min-width: 200px;
      }

      .auto-code-checkbox {
        flex: 1;
        margin-left: 1rem;
        min-width: 150px;
      }
    }
  }

  .dimensions-row {
    flex-direction: column;
    border: 1px solid #eaeaea;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #fafafa;

    h4 {
      margin-top: 0;
      margin-bottom: 1rem;
      font-size: 1rem;
      font-weight: 500;
    }

    .dimensions-inputs {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
    }
  }

  .level-info {
    margin: 1rem 0;
    padding: 0.5rem;
    background-color: #f5f5f5;
    border-radius: 4px;
    text-align: center;
    font-weight: 500;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1rem;

    button {
      min-width: 120px;
    }
  }

  .create-child-container {
    margin: 1.5rem 0;
    display: flex;
    justify-content: center;

    button {
      padding: 0.5rem 1rem;
      font-weight: 500;

      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }

  .children-container {
    margin: 1.5rem 0;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;

    > div {
      margin-bottom: 1.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Responsive styles
@media screen and (max-width: 768px) {
  .location-form {
    .dimensions-row {
      .dimensions-inputs {
        flex-direction: column;
        gap: 0;
      }
    }

    .children-container {
      margin-left: 1rem;
      padding-left: 0.5rem;
    }
  }
}
