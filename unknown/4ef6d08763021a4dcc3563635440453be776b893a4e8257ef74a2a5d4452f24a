<div class="batch-modal-container">
  <h2>{{ 'WAREHOUSE.BATCH_DIALOG.TITLE' | translate }}</h2>
  <div class="batch-content">
    <form [formGroup]="batchForm" class="batch-form">
      <!-- <PERSON><PERSON> lô -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'WAREHOUSE.BATCH_DIALOG.BATCH_NUMBER' | translate }}</mat-label>
        <input matInput formControlName="batchNumber" placeholder="{{ 'WAREHOUSE.BATCH_DIALOG.BATCH_NUMBER_PLACEHOLDER' | translate }}">
        <mat-error *ngIf="batchForm.get('batchNumber')?.hasError('required')">
          {{ 'WAREHOUSE.BATCH_DIALOG.BATCH_NUMBER_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- <PERSON><PERSON><PERSON> sản xuất -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'WAREHOUSE.BATCH_DIALOG.MANUFACTURING_DATE' | translate }}</mat-label>
        <input matInput [matDatepicker]="manufacturingPicker" formControlName="manufacturingDate">
        <mat-hint>{{ 'WAREHOUSE.BATCH_DIALOG.DATE_FORMAT' | translate }}</mat-hint>
        <mat-datepicker-toggle matIconSuffix [for]="manufacturingPicker"></mat-datepicker-toggle>
        <mat-datepicker #manufacturingPicker></mat-datepicker>
      </mat-form-field>

      <!-- Hạn sử dụng -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'WAREHOUSE.BATCH_DIALOG.EXPIRY_DATE' | translate }}</mat-label>
        <input matInput [matDatepicker]="expiryPicker" formControlName="expiryDate">
        <mat-hint>{{ 'WAREHOUSE.BATCH_DIALOG.DATE_FORMAT' | translate }}</mat-hint>
        <mat-datepicker-toggle matIconSuffix [for]="expiryPicker"></mat-datepicker-toggle>
        <mat-datepicker #expiryPicker></mat-datepicker>
        <mat-error *ngIf="batchForm.get('expiryDate')?.hasError('required')">
          {{ 'WAREHOUSE.BATCH_DIALOG.EXPIRY_DATE_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- Số lượng -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'WAREHOUSE.BATCH_DIALOG.QUANTITY' | translate }}</mat-label>
        <input matInput type="number" min="1" formControlName="quantity" placeholder="{{ 'WAREHOUSE.BATCH_DIALOG.QUANTITY_PLACEHOLDER' | translate }}">
        <mat-error *ngIf="batchForm.get('quantity')?.hasError('required')">
          {{ 'WAREHOUSE.BATCH_DIALOG.QUANTITY_REQUIRED' | translate }}
        </mat-error>
        <mat-error *ngIf="batchForm.get('quantity')?.hasError('min')">
          {{ 'WAREHOUSE.BATCH_DIALOG.QUANTITY_MIN' | translate }}
        </mat-error>
      </mat-form-field>
    </form>
  </div>

  <div class="batch-actions">
    <button mat-button (click)="cancel()">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" [disabled]="batchForm.invalid" (click)="save()">
      {{ 'COMMON.SAVE' | translate }}
    </button>
  </div>
</div>
