/**
 * DTO (Data Transfer Object) cho Inventory Check
 * Chứa các interface liên quan đến dữ liệu giao tiếp với API
 */

import type { EmbeddedWarehouseLocation } from 'salehub_shared_contracts/entities/wms/warehouse_location';
import type { 
  InventoryCheck, 
  InventoryCheckItemUnit, 
  InventoryCheckItemBatch, 
  InventoryCheckItem, 
  InventoryCheckSummary 
} from 'salehub_shared_contracts/entities/ims/inventory/inventory_check';
import type { EmbeddedProduct } from 'salehub_shared_contracts/entities/ims/product/embedded_product';
import type { EmbeddedProductSerial } from 'salehub_shared_contracts/entities/ims/product/product_serial';
import type { EmbeddedProductBatch } from 'salehub_shared_contracts/entities/ims/product/product_batch';
import type { ProductListItem, ProductList } from 'salehub_shared_contracts/requests/shared/product';
import type { ListItem } from 'salehub_shared_contracts/requests/shared/list';

// Re-export các interface từ contracts
export {
  EmbeddedWarehouseLocation,
  InventoryCheck,
  EmbeddedProductSerial,
  ProductListItem,
  ProductList,
  InventoryCheckItemUnit,
  InventoryCheckItemBatch,
  InventoryCheckItem,
  ListItem as Warehouse,
  ListItem as Employee,
  EmbeddedProduct,
  EmbeddedProductBatch,
  InventoryCheckSummary
};

/**
 * Interface cho dữ liệu đầu vào của modal lọc sản phẩm
 */
export interface ProductFilterModalData {
  current: {
    category: string[];
    warehouseLocation: EmbeddedWarehouseLocation | null;
  };
  warehouseId: string;
}

/**
 * Interface cho dữ liệu đầu vào của modal quản lý số serial
 */
export interface SerialNumberModalData {
  product: ProductListItem;
  serials: EmbeddedProductSerial[];
}

/**
 * Interface cho dữ liệu lô hàng
 */
export interface BatchData {
  batchNumber: string;
  expiryDate: Date;
  quantity: number;
}

/**
 * Interface cho dữ liệu phiếu điều chỉnh kho
 */
export interface StockAdjustment {
  _id: string;
  warehouseId: string;
  warehouseName: string;
  referenceId: string;
  referenceType: string;
  items: StockAdjustmentItem[];
  createdBy: {
    _id: string;
    name: string;
  };
  createdAt: Date;
  status: string;
}

/**
 * Interface cho item trong phiếu điều chỉnh kho
 */
export interface StockAdjustmentItem {
  productId: string;
  productName: string;
  sku: string;
  quantity: number;
  cost: number;
  value: number;
  reason: string;
}

/**
 * Interface cho kết quả cập nhật tồn kho
 */
export interface StockUpdateResult {
  success: boolean;
  stockAdjustment?: StockAdjustment;
  message: string;
}

/**
 * Interface cho kết quả kiểm tra dữ liệu
 */
export interface ValidationResult {
  isValid: boolean;
  message: string;
}

/**
 * Interface cho kết quả lưu phiếu kiểm kho
 */
export interface SaveInventoryCheckResult {
  _id: string;
  createdAt?: Date;
  updatedAt?: Date;
  stockAdjustment?: StockAdjustment;
}
