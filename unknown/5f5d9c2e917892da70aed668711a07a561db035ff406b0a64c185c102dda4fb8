<mat-form-field
  appearance="outline"
  floatLabel="always"
  class="w-100"
  [class.disabled]="isDisabling()"
  >
  <mat-label>{{ placeholder }}</mat-label>

  <input
    #addressInput
    type="text"
    matInput
    [matAutocomplete]="autoAddress"
    [ngModel]="inputAddress()"
    (ngModelChange)="inputAddress.set($event); onAddressChange($event)"
    required
    >
  <div matSuffix>
    @if(!isDisabling()) {
      @if(isSearchingAddress()) {
        <span class="loader me-3" style="width: 24px"></span>
      } @else {
        @if(inputAddress()) {
          <i class="fa-regular fa-pen-to-square" (click)="editPlaceDetails($event)"></i>
        }

        <i class="fa-regular fa-xmark" (click)="clearAddress($event)"></i>
      }
    }
  </div>

  <mat-error class="mb-4">Đ<PERSON>a chỉ không được để trống.</mat-error>


  <mat-autocomplete
    #autoAddress="matAutocomplete"
    [displayWith]="display"
    (optionSelected)="select($event)"
    >
    @for (option of places(); track option) {
      <mat-option
        [value]="option"
        class="py-2"
        >
        <div>
          <b>
            {{ option.mainAddress ?? option.fullAddress }}
          </b>
        </div>

        @if(option.mainAddress || option.secondaryAddress) {
          <div>
            <small>{{ option.secondaryAddress ?? option.fullAddress }}</small>
          </div>
        }

        @if(option.instruction) {
          <div>
            <small>{{ option.instruction }}</small>
          </div>
        }
      </mat-option>
    }
  </mat-autocomplete>

</mat-form-field>

