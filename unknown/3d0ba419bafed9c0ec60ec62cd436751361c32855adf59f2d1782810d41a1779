<div class="preview-container">
  <h4>{{ 'WAREHOUSE.LOCATION.FORM.PREVIEW' | translate }}</h4>
  <div class="preview-tree">
    <p-tree *ngIf="previewData && previewData.length > 0; else emptyPreview" [value]="previewData" styleClass="location-preview-tree"></p-tree>
    <ng-template #emptyPreview>
      <div class="preview-empty">
        {{ 'WAREHOUSE.LOCATION.FORM.EMPTY_PREVIEW' | translate }}
      </div>
    </ng-template>
  </div>
</div>
