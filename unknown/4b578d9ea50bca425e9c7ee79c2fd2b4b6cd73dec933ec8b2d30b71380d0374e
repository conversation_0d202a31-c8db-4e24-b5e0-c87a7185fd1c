.product-modifiers-sheet {
  .filters {
    scrollbar-width: thin;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      height: 5px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 10px;
    }
  }

  .product-list {
    max-height: 60vh;
    overflow-y: auto;
    scrollbar-width: thin;

    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 10px;
    }
  }

  .product-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;

    &:hover {
      border-color: var(--bs-primary);
      box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075);
    }

    &.selected {
      border-color: var(--bs-primary);
      background-color: rgba(var(--bs-primary-rgb), 0.05);
    }

    .quantity-controls {
      button {
        width: 28px;
        height: 28px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }
  }

  .footer-actions {
    position: sticky;
    bottom: 0;
    background-color: white;
    z-index: 10;
  }
}
