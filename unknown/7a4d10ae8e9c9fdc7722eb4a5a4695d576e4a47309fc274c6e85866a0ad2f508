@forward 'swiper/css';
@forward 'swiper/css/grid';
@forward 'swiper/css/navigation';

/* <PERSON><PERSON><PERSON><PERSON> màu */
:host {
  --primary-color: #3f51b5;
  --primary-light: #c5cae9;
  --accent-color: #ff4081;
  --border-color: #e0e0e0;
  --text-color: #333;
  --disabled-color: #9e9e9e;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --background-light: #f5f5f5;
}

.product-selection {
  overflow: hidden; // Ngăn tràn nội dung

  // Styles cho thanh tìm kiếm
  .search-bar {
    padding: 16px 16px 0;

    .search-container {
      position: relative;
      width: 100%;
      margin-bottom: 16px;

      .search-input {
        width: 100%;
        padding: 10px 40px;
        border: 1px solid #ddd;
        border-radius: 24px;
        outline: none;
        font-size: 14px;
        transition: all 0.3s ease;

        &:focus {
          border-color: #1976d2;
          box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }
      }

      .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        font-size: 20px;
      }

      .clear-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        font-size: 20px;
        cursor: pointer;

        &:hover {
          color: #333;
        }
      }
    }
  }

  .navigation-row {
    position: relative;
    padding: 0 40px; // Để chừa chỗ cho nút prev/next

    .swiper-container {
      width: 100%;
      padding: 16px 0;
      overflow: hidden; // Ngăn tràn nội dung của swiper

      .swiper-wrapper {
        display: flex;

        .swiper-slide {
          height: auto !important; // Override swiper's default height
          width: 100% !important; // Đảm bảo slide chiếm toàn bộ chiều rộng
        }
      }

      .swiper-button-prev,
      .swiper-button-next {
        color: #000;
        &::after {
          font-size: 24px;
        }
        &.swiper-button-disabled {
          opacity: 0.35;
          cursor: not-allowed;
        }
      }

      .pagination-indicator {
        text-align: center;
        margin-top: 10px;
        font-size: 14px;
        color: #666;
      }
    }
  }

  .product-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px;

    @media (min-width: 1200px) {
      .product-item {
        width: calc((100% - 32px) / 3); // 3 items per row with 16px gap
      }
    }

    @media (min-width: 768px) and (max-width: 1199px) {
      .product-item {
        width: calc((100% - 16px) / 2); // 2 items per row with 16px gap
      }
    }

    @media (max-width: 767px) {
      .product-item {
        width: 100%; // 1 item per row
      }
    }
  }

  .product-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #fff;
    transition: all 0.3s ease;
    height: 100%;
    cursor: pointer; // Thêm con trỏ pointer để thể hiện có thể click

    &:hover {
      border-color: #1976d2;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border-color: #1976d2;
      background-color: #e3f2fd;
    }

    .product-image {
      width: 80px;
      height: 80px;
      margin-right: 12px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }
    }

    .product-info {
      flex: 1;
      min-width: 0; // Để text có thể ellipsis

      .product-name {
        font-weight: 500;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .product-variant {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .product-unit {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .product-price {
        color: #1976d2;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .inventory-quantity {
        font-size: 12px;
        color: #888;
      }
    }

    .product-controls {
      margin-left: 12px;
      display: flex;
      align-items: center;

      .quantity-controls {
        display: flex;
        align-items: center;

        button {
          min-width: 36px;
          height: 36px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .quantity {
          margin: 0 8px;
          min-width: 24px;
          text-align: center;
          font-weight: 500;
        }
      }
    }
  }
}

// Responsive styles
@media (max-width: 1199px) {
  .product-selection {
    .product-item {
      .product-image {
        width: 60px;
        height: 60px;
      }
    }
  }
}

@media (max-width: 767px) {
  .product-selection {
    .product-item {
      padding: 8px;

      .product-image {
        width: 50px;
        height: 50px;
        margin-right: 8px;
      }

      .product-controls {
        margin-left: 8px;

        .quantity-controls {
          button {
            min-width: 30px;
            height: 30px;
          }

          .quantity {
            margin: 0 4px;
          }
        }
      }
    }
  }
}

.product-selection-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  max-height: 100%;
  overflow: hidden;
}

/* Thanh tìm kiếm */
.search-container {
  padding: 8px 16px;
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: white;
  border-bottom: 1px solid var(--border-color);
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--background-light);
  border-radius: 24px;
  padding: 0 16px;
  height: 44px;
  position: relative;
}

.search-icon {
  color: var(--disabled-color);
  margin-right: 8px;
}

.search-input {
  flex: 1;
  border: none;
  background-color: transparent;
  outline: none;
  font-size: 14px;
  color: var(--text-color);
}

.clear-icon {
  color: var(--disabled-color);
  cursor: pointer;
  font-size: 20px;
}

/* Container cho danh sách sản phẩm */
.product-list-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Swiper */
.swiper {
  height: 100%;
  padding: 0;
}

.swiper-slide {
  height: auto;
}

/* Lưới sản phẩm */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 12px;
  padding: 16px;
}

/* Item sản phẩm */
.product-item {
  background-color: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 260px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }

  &.out-of-stock {
    opacity: 0.6;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.1);
      z-index: 1;
    }
  }
}

.product-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-image {
  position: relative;
  height: 120px;
  overflow: hidden;
  background-color: var(--background-light);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .out-of-stock-badge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    background-color: var(--danger-color);
    color: white;
    padding: 4px 12px;
    font-weight: bold;
    font-size: 14px;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
  }
}

.quantity-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 8px 8px 0 0;

  .quantity-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 4px 10px;
    border-radius: 20px;
    margin-bottom: 8px;

    .quantity-value {
      min-width: 24px;
      text-align: center;
      font-weight: bold;
      font-size: 16px;
    }

    .quantity-btn {
      width: 30px;
      height: 30px;
      line-height: 30px;

      .material-icons {
        font-size: 18px;
      }
    }
  }

  // Add Options button
  .add-options-btn {
    background-color: white;
    font-size: 12px;
    padding: 0 10px;
    height: 30px;
    line-height: 30px;

    .material-icons {
      font-size: 14px;
      margin-right: 4px;
    }
  }
}

.product-details {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px;
  color: var(--text-color);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 40px;
}

.product-variant {
  font-size: 12px;
  color: var(--disabled-color);
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-info {
  margin-top: auto;

  .price {
    font-weight: bold;
    color: var(--accent-color);
    font-size: 16px;
    margin-bottom: 4px;
  }

  .unit, .inventory {
    font-size: 12px;
    color: var(--disabled-color);
    margin-bottom: 2px;

    .label {
      font-weight: 500;
    }
  }

  .inventory {
    &.warning {
      color: var(--warning-color);
    }

    &.danger {
      color: var(--danger-color);
    }
  }
}

/* Nút điều hướng Swiper */
.swiper-button-next,
.swiper-button-prev {
  color: var(--primary-color);

  &::after {
    font-size: 24px;
  }

  &.swiper-button-disabled {
    opacity: 0.2;
  }
}

/* Empty state */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 24px;
}

.empty-message {
  text-align: center;
  color: var(--disabled-color);

  .material-icons {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    font-size: 16px;
    margin: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
    padding: 12px;
  }

  .product-item {
    min-height: 220px;
  }

  .product-image {
    height: 100px;
  }

  .product-name {
    font-size: 13px;
  }

  .price {
    font-size: 14px !important;
  }
}

@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
    padding: 8px;
  }

  .product-item {
    min-height: 200px;
  }
}

/* Header container */
.header-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: white;
  border-bottom: 1px solid var(--border-color);
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 16px 8px;
  gap: 12px;
}

.filter-field {
  flex: 1;
  min-width: 150px;
  max-width: 250px;
  margin-bottom: 0;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.reset-filter-btn, .add-product-btn {
  height: 40px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-product-btn {
  background-color: var(--primary-color);
}

/* Actions container */
.actions-container {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: white;
  border-top: 1px solid var(--border-color);
}

.reset-btn, .add-to-order-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
  height: 44px;
}

.add-to-order-btn {
  min-width: 180px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-field {
    max-width: 100%;
  }

  .reset-filter-btn {
    margin-left: 0;
    width: 100%;
  }

  .actions-container {
    flex-direction: column;
    gap: 8px;
  }

  .reset-btn, .add-to-order-btn {
    width: 100%;
  }
}
