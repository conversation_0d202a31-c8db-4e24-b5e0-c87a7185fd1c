.location-tree-container {
  width: 100%;
  height: 100%;
  overflow: auto;

  ::ng-deep .location-tree {
    width: 100%;

    .p-treenode-icon {
      margin-right: 0.5rem;
    }

    .p-treenode-content {
      padding: 0.5rem;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      &.p-highlight {
        background-color: #e3f2fd;
      }
    }
  }
}

// Responsive styles
@media screen and (max-width: 768px) {
  .location-tree-container {
    ::ng-deep .location-tree {
      .p-treenode-label {
        font-size: 0.9rem;
      }
    }
  }
}
