<h2 mat-dialog-title>{{ dialogTitle | translate }}</h2>

<form [formGroup]="form" (ngSubmit)="onSave()">
  <mat-dialog-content>
    <!-- <PERSON><PERSON><PERSON> thuế -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'TAX.TYPE' | translate }}</mat-label>
      <mat-select formControlName="type">
        <mat-option value="VAT">{{ 'TAX.VAT' | translate }}</mat-option>
        <mat-option value="import_tax">{{ 'TAX.IMPORT_TAX' | translate }}</mat-option>
        <mat-option value="other">{{ 'TAX.OTHER' | translate }}</mat-option>
      </mat-select>
      <mat-error *ngIf="form.get('type')?.errors?.['required']">
        {{ 'VALIDATION.REQUIRED' | translate }}
      </mat-error>
    </mat-form-field>

    <!-- Tỷ lệ thuế -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'TAX.RATE' | translate }}</mat-label>
      <input matInput type="number" formControlName="rate" min="0" max="100" step="0.1"
          matTooltip="{{ 'TAX.RATE_TOOLTIP' | translate }}">
      <span matSuffix>%</span>
      <mat-error *ngIf="form.get('rate')?.errors?.['min']">
        {{ 'VALIDATION.MIN_VALUE' | translate: { min: 0 } }}
      </mat-error>
      <mat-error *ngIf="form.get('rate')?.errors?.['max']">
        {{ 'VALIDATION.MAX_VALUE' | translate: { max: 100 } }}
      </mat-error>
    </mat-form-field>

    <!-- Số tiền thuế -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'TAX.AMOUNT' | translate }}</mat-label>
      <input matInput type="number" formControlName="amount" min="0" step="1">
      <span matSuffix>VND</span>
      <mat-error *ngIf="form.get('amount')?.errors?.['required']">
        {{ 'VALIDATION.REQUIRED' | translate }}
      </mat-error>
      <mat-error *ngIf="form.get('amount')?.errors?.['min']">
        {{ 'VALIDATION.MIN_VALUE' | translate: { min: 0 } }}
      </mat-error>
    </mat-form-field>

    <!-- Thông tin bổ sung -->
    <div *ngIf="data.subTotal" class="text-gray-600 text-sm mb-4">
      <p>{{ 'TAX.SUB_TOTAL_INFO' | translate: { value: data.subTotal } }}</p>

      <ng-container *ngIf="!manualTaxAmount && form.get('rate')?.value && data.subTotal">
        <p>{{ 'TAX.AUTO_TAX_INFO' | translate: { value: calculateTaxAmount() } }}</p>
      </ng-container>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" type="submit" [disabled]="form.invalid">
      {{ 'COMMON.SAVE' | translate }}
    </button>
  </mat-dialog-actions>
</form>
