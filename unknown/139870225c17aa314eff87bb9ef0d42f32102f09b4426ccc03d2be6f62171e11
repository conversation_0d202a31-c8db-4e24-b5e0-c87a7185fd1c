import { Injectable } from '@angular/core';
import { ModifierGroup, OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { BehaviorSubject, Observable, combineLatest, map } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { ProductList } from 'salehub_shared_contracts/requests/shared/product';

/**
 * Interface mở rộng cho ProductList đ<PERSON> bổ sung các thuộc tính cho UI
 */
export interface ProductItemExtended {
  selected?: boolean;
  image?: string;
  quantity?: number;
  nameAscii?: string; // Thêm trường nameAscii cho tìm kiếm
  sku?: string; // Thêm sku cho tìm kiếm
  barcode?: string; // Thêm barcode cho tìm kiếm
  inventoryQuantity?: number; // Thêm số lượng tồn kho
  trackInventory?: boolean; // Thêm flag theo dõi tồn kho
  isClone?: boolean; // Đ<PERSON>h dấu sản phẩm là clone để tránh clone tiếp
  originalName?: string; // Lưu tên gốc của sản phẩm
  originalPrice?: number; // Lưu giá gốc của sản phẩm
  variant?: {
    variantId: string;
    attributes: Array<{ name: string; value: string }>;
  };
  unit?: {
    unitName: string;
    conversionRate: number;
    isBaseUnit: boolean;
    price: number;
    cost?: number;
    barcode?: string;
  };
  modifierGroups?: ModifierGroup[];
}

/**
 * Định nghĩa cấu hình trang sản phẩm
 */
export interface PageConfig {
  itemsPerRow: number;
  rows: number;
}

/**
 * Định nghĩa cấu hình tồn kho
 */
export interface InventorySettings {
  allowSellWhenOutOfStock: boolean;
  warehouseId?: string;
}

/**
 * Service quản lý logic nghiệp vụ cho OrderProductPickerComponent
 */
@Injectable({
  providedIn: 'root'
})
export class ProductSelectionService {
  private productListSubject = new BehaviorSubject<(ProductList[number] & ProductItemExtended)[]>([]);
  public productList$: Observable<(ProductList[number] & ProductItemExtended)[]> = this.productListSubject.asObservable();

  private filteredProductListSubject = new BehaviorSubject<(ProductList[number] & ProductItemExtended)[]>([]);
  public filteredProductList$: Observable<(ProductList[number] & ProductItemExtended)[]> = this.filteredProductListSubject.asObservable();

  private searchTermSubject = new BehaviorSubject<string>('');
  public searchTerm$: Observable<string> = this.searchTermSubject.asObservable();

  private settingsSubject = new BehaviorSubject<InventorySettings>({ allowSellWhenOutOfStock: true });
  public settings$: Observable<InventorySettings> = this.settingsSubject.asObservable();

  // Kho đang được chọn để hiển thị tồn kho
  private selectedWarehouseIdSubject = new BehaviorSubject<string | null>(null);
  public selectedWarehouseId$: Observable<string | null> = this.selectedWarehouseIdSubject.asObservable();

  constructor(private translateService: TranslateService) {
    // Khởi tạo các giá trị mặc định
    this.productListSubject.next([]);
    this.filteredProductListSubject.next([]);
    this.searchTermSubject.next('');
  }

  /**
   * Cập nhật cài đặt cho tồn kho
   */
  updateSettings(settings: InventorySettings): void {
    this.settingsSubject.next(settings);
  }

  /**
   * Cập nhật kho đang được chọn để hiển thị tồn kho
   */
  selectWarehouse(warehouseId: string | null): void {
    this.selectedWarehouseIdSubject.next(warehouseId);
    // Cập nhật lại số lượng tồn kho cho các sản phẩm
    this.updateInventoryQuantities(warehouseId);
  }

  /**
   * Cập nhật số lượng tồn kho cho tất cả sản phẩm dựa trên kho được chọn
   */
  private updateInventoryQuantities(warehouseId: string | null): void {
    if (!warehouseId) return;

    const currentList = this.productListSubject.value;
    const updatedList = currentList.map(item => {
      const inventoryQuantity = this.getInventoryQuantityForWarehouse(item, warehouseId);
      return {
        ...item,
        inventoryQuantity
      };
    });

    this.productListSubject.next(updatedList);
    this.applySearchFilter(this.searchTermSubject.value);
  }

  /**
   * Lấy số lượng tồn kho của sản phẩm theo kho cụ thể
   */
  private getInventoryQuantityForWarehouse(product: ProductList[number] & ProductItemExtended, warehouseId: string): number {
    if (!product || !product.warehouseStock) {
      return 0;
    }

    return product.warehouseStock[warehouseId] || 0;
  }

  /**
   * Khởi tạo danh sách sản phẩm
   */
  initProductList(productList: ProductList, orderItems: OrderItemBaseDetails[] = []): void {
    // Thêm thuộc tính selected, quantity và nameAscii vào danh sách sản phẩm
    const extendedList = productList.map(item => ({
      ...item,
      selected: false,
      quantity: 0,
      nameAscii: this.convertToAscii(item.name),
      inventoryQuantity: this.getInventoryQuantity(item),
      trackInventory: !!item.trackInventory
    }));

    this.productListSubject.next(extendedList);
    this.filteredProductListSubject.next(extendedList);

    // Xử lý sản phẩm đã chọn từ orderItems
    if (orderItems.length > 0) {
      this.processOrderItems(orderItems);
    }
  }

  /**
   * Xử lý sản phẩm đã chọn từ OrderItemBaseDetails
   */
  processOrderItems(orderItems: OrderItemBaseDetails[]): void {
    const currentList = this.productListSubject.value;
    const updatedList = [...currentList];

    console.log(orderItems);

    // Duyệt qua danh sách OrderItemBaseDetails
    orderItems.forEach(orderItem => {
      if (!orderItem.product || !orderItem.product.productId) {
        return;
      }

      // Tìm sản phẩm trong danh sách
      const productIndex = updatedList.findIndex(
        item => item.productId === orderItem.product?.productId
      );

      if (productIndex === -1) {
        return;
      }

      const originalProduct = updatedList[productIndex];
      // Lưu tên gốc của sản phẩm
      const originalName = originalProduct.originalName || originalProduct.name;

      // Kiểm tra xem name hoặc price có bị chỉnh sửa bên ngoài không
      const nameModified = orderItem.product?.userOverride?.name && orderItem.product?.userOverride?.name !== originalName;
      const priceModified = orderItem.product?.userOverride?.price && orderItem.product?.userOverride?.price !== originalProduct.price;

      // Đánh dấu sản phẩm là đã chọn
      originalProduct.selected = true;
      originalProduct.quantity = orderItem.quantity;

      // Lưu giá gốc nếu chưa có và sẽ bị thay đổi
      if (priceModified && originalProduct.originalPrice === undefined) {
        originalProduct.originalPrice = originalProduct.price;
      }

      // Kiểm tra nếu có modifierGroups
      if (orderItem.modifierGroups && orderItem.modifierGroups.length > 0) {
        // Tính tổng số modifier đã chọn
        let totalModifiers = 0;
        orderItem.modifierGroups.forEach(group => {
          totalModifiers += group.modifiers?.length || 0;
        });

        // Cập nhật modifierGroups cho sản phẩm gốc
        originalProduct.modifierGroups = orderItem.modifierGroups;

        // Lưu tên gốc nếu chưa có
        if (!originalProduct.originalName) {
          originalProduct.originalName = originalName;
        }

        // Cập nhật tên hiển thị với format sử dụng i18n
        if (totalModifiers > 0) {
          originalProduct.name = this.translateService.instant('PRODUCT_SELECTION.WITH_OTHER_PRODUCTS', {
            name: originalName,
            count: totalModifiers
          });
        }

        // Kiểm tra xem đã có clone của sản phẩm này chưa
        const existingCloneIndex = updatedList.findIndex(item =>
          item.productId === originalProduct.productId &&
          item.isClone &&
          !item.variant &&
          !item.unit &&
          !item.modifierGroups
        );

        // Nếu chưa có clone, thì tạo mới
        if (existingCloneIndex === -1) {
          // Tạo bản sao của sản phẩm gốc không có modifierGroups
          const clonedProduct = this.cloneProductItem(originalProduct, true); // Giữ lại variants và units
          clonedProduct.name = originalName; // Khôi phục tên gốc
          clonedProduct.modifierGroups = undefined; // Xóa modifierGroups
          clonedProduct.selected = false; // Không chọn
          clonedProduct.isClone = true; // Đánh dấu là clone
          clonedProduct.variant = undefined; // Xóa variant đã chọn
          clonedProduct.unit = undefined; // Xóa unit đã chọn

          // Thêm bản sao vào danh sách ngay sau sản phẩm gốc
          updatedList.splice(productIndex + 1, 0, clonedProduct);
        }
      } else if (
        (orderItem.product.variant && orderItem.product.variant.variantId) ||
        orderItem.product.unit ||
        nameModified ||
        priceModified
      ) {
        // Cập nhật tên và giá nếu có thay đổi từ bên ngoài
        if (nameModified) {
          // Lưu tên gốc nếu chưa có
          if (!originalProduct.originalName) {
            originalProduct.originalName = originalName;
          }
          // Cập nhật tên mới
          originalProduct.name = orderItem.product?.userOverride?.name!;
        }

        if (priceModified) {
          // Cập nhật giá mới
          originalProduct.price = orderItem.product?.userOverride?.price!;
        }

        // Cập nhật variant nếu có
        if (orderItem.product.variant && orderItem.product.variant.variantId) {
          originalProduct.variant = orderItem.product.variant;

          // Tìm variant đầy đủ từ originalProduct.variants nếu có
          const matchedVariant = originalProduct.variants?.find(
            v => v.variantId === orderItem.product?.variant?.variantId
          );

          if (matchedVariant) {
            originalProduct.variant = matchedVariant;
          }
        }

        // Cập nhật unit nếu có
        if (orderItem.product.unit) {
          originalProduct.unit = orderItem.product.unit;

          // Cập nhật giá nếu có unit
          if (orderItem.product.unit.price) {
            originalProduct.price = orderItem.product.unit.price;
          }
        }

        // Lưu tên gốc nếu chưa có
        if (!originalProduct.originalName) {
          originalProduct.originalName = originalName;
        }

        // Kiểm tra xem đã có clone của sản phẩm này chưa
        const existingCloneIndex = updatedList.findIndex(item =>
          item.productId === originalProduct.productId &&
          item.isClone &&
          !item.variant &&
          !item.unit &&
          !item.modifierGroups
        );

        // Nếu chưa có clone, thì tạo mới
        if (existingCloneIndex === -1) {
          // Tạo bản sao của sản phẩm gốc
          const clonedProduct = this.cloneProductItem(originalProduct, true); // Giữ lại variants và units
          // Đặt lại thông tin cơ bản
          clonedProduct.variant = undefined;
          clonedProduct.unit = undefined;
          clonedProduct.name = originalName;
          clonedProduct.price = originalProduct.originalPrice !== undefined ? originalProduct.originalPrice : originalProduct.price; // Sử dụng giá gốc nếu có, nếu không thì dùng giá hiện tại
          clonedProduct.selected = false;
          clonedProduct.isClone = true;

          // Thêm sản phẩm clone vào list ngay sau sản phẩm gốc
          updatedList.splice(productIndex + 1, 0, clonedProduct);
        }
      }
    });

    this.productListSubject.next(updatedList);
    this.applySearchFilter(this.searchTermSubject.value);
  }

  /**
   * Thêm sản phẩm mới vào danh sách (cho clone)
   * Clone mới luôn ở bên phải sản phẩm được clone, đẩy các clone cũ xuống dưới
   */
  addProduct(product: ProductList[number] & ProductItemExtended): void {
    const currentList = this.productListSubject.value;
    const updatedList = [...currentList];

    // Tìm sản phẩm gốc trong danh sách hiện tại
    // Sản phẩm gốc sẽ có cùng productId nhưng không phải chính sản phẩm đang thêm vào
    const originalProductIndex = updatedList.findIndex(item =>
      item.productId === product.productId && item !== product && !item.isClone
    );

    if (originalProductIndex !== -1) {
      // Tìm vị trí cuối cùng của chuỗi clone từ sản phẩm gốc này
      let lastCloneIndex = originalProductIndex;
      for (let i = originalProductIndex + 1; i < updatedList.length; i++) {
        if (updatedList[i].productId === product.productId && updatedList[i].isClone) {
          lastCloneIndex = i;
        } else {
          break;
        }
      }

      // Thêm vào ngay sau clone cuối cùng (hoặc sau sản phẩm gốc nếu không có clone)
      updatedList.splice(lastCloneIndex + 1, 0, product);
    } else {
      // Nếu không tìm thấy sản phẩm gốc, thêm vào cuối
      updatedList.push(product);
    }

    this.productListSubject.next(updatedList);
    this.applySearchFilter(this.searchTermSubject.value);
  }

  /**
   * Tăng số lượng sản phẩm
   */
  increaseQuantity(productId: string, variantId?: string, unitName?: string): void {
    const currentList = this.productListSubject.value;
    const settings = this.settingsSubject.value;

    const updatedList = currentList.map(item => {
      // Tìm đúng sản phẩm dựa vào productId, variantId và unitName
      if (
        item.productId === productId &&
        (!variantId || item.variant?.variantId === variantId) &&
        (!unitName || item.unit?.unitName === unitName)
      ) {
        if (!item.quantity) {
          item.quantity = 0;
        }

        // Kiểm tra tồn kho trước khi tăng số lượng
        if (item.trackInventory && !settings.allowSellWhenOutOfStock) {
          const currentInventory = item.inventoryQuantity !== undefined
            ? item.inventoryQuantity
            : this.getInventoryQuantity(item);

          // Nếu số lượng hiện tại đã bằng hoặc vượt quá tồn kho và không cho phép bán khi hết hàng
          if (item.quantity >= currentInventory) {
            return item; // Không tăng số lượng
          }
        }

        return { ...item, quantity: item.quantity + 1, selected: true };
      }
      return item;
    });

    this.productListSubject.next(updatedList);
    this.applySearchFilter(this.searchTermSubject.value);
  }

  /**
   * Giảm số lượng sản phẩm
   */
  decreaseQuantity(productId: string, variantId?: string, unitName?: string): void {
    const currentList = this.productListSubject.value;
    const updatedList = currentList.map(item => {
      // Tìm đúng sản phẩm dựa vào productId, variantId và unitName
      if (
        item.productId === productId &&
        (!variantId || item.variant?.variantId === variantId) &&
        (!unitName || item.unit?.unitName === unitName)
      ) {
        if (!item.quantity || item.quantity <= 0) {
          return { ...item, quantity: 0, selected: false };
        }

        const newQuantity = item.quantity - 1;
        return {
          ...item,
          quantity: newQuantity,
          selected: newQuantity > 0
        };
      }
      return item;
    });

    this.productListSubject.next(updatedList);
    this.applySearchFilter(this.searchTermSubject.value);
  }

  /**
   * Lấy số lượng tồn kho của sản phẩm
   */
  getInventoryQuantity(product: ProductList[number] & ProductItemExtended): number {
    if (!product || !product.warehouseStock) {
      return 0;
    }

    const selectedWarehouseId = this.selectedWarehouseIdSubject.value;

    // Nếu có kho được chọn cụ thể
    if (selectedWarehouseId && product.warehouseStock[selectedWarehouseId] !== undefined) {
      return product.warehouseStock[selectedWarehouseId];
    }

    // Nếu không có kho được chọn, lấy kho đầu tiên
    const warehouseIds = Object.keys(product.warehouseStock);
    if (warehouseIds.length === 0) {
      return 0;
    }

    return product.warehouseStock[warehouseIds[0]] || 0;
  }

  /**
   * Kiểm tra xem có thể tăng số lượng cho sản phẩm không
   */
  canIncreaseQuantity(product: ProductList[number] & ProductItemExtended): boolean {
    const settings = this.settingsSubject.value;

    // Nếu cho phép bán khi hết hàng, luôn trả về true
    if (settings.allowSellWhenOutOfStock) {
      return true;
    }

    // Nếu không theo dõi tồn kho, luôn trả về true
    if (!product.trackInventory) {
      return true;
    }

    // Lấy số lượng tồn kho
    const inventoryQuantity = product.inventoryQuantity !== undefined
      ? product.inventoryQuantity
      : this.getInventoryQuantity(product);

    // Kiểm tra số lượng hiện tại và tồn kho
    return (product.quantity || 0) < inventoryQuantity;
  }

  /**
   * Clone một sản phẩm
   * @param product Sản phẩm cần clone
   * @param preserveVariantsAndUnits Có giữ lại variants và units trong bản clone không (mặc định là true)
   */
  cloneProductItem(product: ProductList[number] & ProductItemExtended, preserveVariantsAndUnits: boolean = true): ProductList[number] & ProductItemExtended {
    const clonedProduct = {
      ...JSON.parse(JSON.stringify(product)), // Clone sâu
      selected: false,
      quantity: 0,
      nameAscii: product.nameAscii || this.convertToAscii(product.name),
      // Giữ lại thông tin tồn kho
      trackInventory: product.trackInventory,
      inventoryQuantity: product.inventoryQuantity,
      isClone: true,
      originalName: product.name
    };

    // Xóa variant và unit của sản phẩm clone nếu không muốn giữ lại
    if (!preserveVariantsAndUnits) {
      clonedProduct.variant = undefined;
      clonedProduct.unit = undefined;
    }

    return clonedProduct;
  }

  /**
   * Thiết lập từ khóa tìm kiếm và lọc danh sách
   */
  setSearchTerm(term: string): void {
    this.searchTermSubject.next(term);
    this.applySearchFilter(term);
  }

  /**
   * Áp dụng bộ lọc tìm kiếm vào danh sách sản phẩm
   */
  private applySearchFilter(term: string): void {
    if (!term || term.trim() === '') {
      // Nếu không có từ khóa tìm kiếm, hiển thị tất cả
      this.filteredProductListSubject.next(this.productListSubject.value);
      return;
    }

    // Chuẩn hóa từ khóa tìm kiếm
    const normalizedTerm = this.convertToAscii(term.toLowerCase().trim());

    // Lọc sản phẩm dựa trên từ khóa
    const filteredList = this.productListSubject.value.filter(product => {
      // Tìm trong tên sản phẩm
      if (product.nameAscii && product.nameAscii.includes(normalizedTerm)) {
        return true;
      }

      // Tìm trong mã sản phẩm
      if (product.sku && product.sku.toLowerCase().includes(normalizedTerm)) {
        return true;
      }

      // Tìm trong mã vạch
      if (product.barcode && product.barcode.toLowerCase().includes(normalizedTerm)) {
        return true;
      }

      // Tìm trong variant nếu có
      if (product.variant && product.variant.attributes) {
        return product.variant.attributes.some(attr =>
          this.convertToAscii(attr.value.toLowerCase()).includes(normalizedTerm)
        );
      }

      return false;
    });

    this.filteredProductListSubject.next(filteredList);
  }

  /**
   * Chuyển đổi chuỗi Unicode thành ASCII (loại bỏ dấu tiếng Việt)
   */
  public convertToAscii(text: string): string {
    if (!text) return '';

    return text.normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Loại bỏ dấu
      .replace(/[đĐ]/g, d => d === 'đ' ? 'd' : 'D') // Thay thế đ/Đ
      .toLowerCase();
  }

  /**
   * Lấy danh sách sản phẩm đã chọn
   */
  getSelectedProducts(): (ProductList[number] & ProductItemExtended)[] {
    return this.productListSubject.value.filter(item => item.selected && (item.quantity || 0) > 0);
  }

  /**
   * Áp dụng các bộ lọc cho danh sách sản phẩm
   */
  applyFilters(filters: ProductFilters | null): void {
    const currentList = this.productListSubject.value;

    if (!filters) {
      // Nếu không có bộ lọc, hiển thị toàn bộ danh sách
      this.applySearchFilter(this.searchTermSubject.value);
      return;
    }

    const { warehouseId, categoryId, brandId } = filters;

    // Lọc danh sách sản phẩm dựa trên các bộ lọc
    let filteredList = currentList;

    // Lọc theo kho
    if (warehouseId) {
      filteredList = filteredList.filter(product =>
        product.warehouseIds && product.warehouseIds.includes(warehouseId)
      );
    }

    // Lọc theo danh mục
    if (categoryId) {
      filteredList = filteredList.filter(product =>
        product.categoryIds && product.categoryIds.includes(categoryId)
      );
    }

    // Lọc theo thương hiệu
    if (brandId) {
      filteredList = filteredList.filter(product =>
        product.brandIds && product.brandIds.includes(brandId)
      );
    }

    // Áp dụng kết quả lọc và áp dụng tìm kiếm nếu có
    this.filteredProductListSubject.next(filteredList);

    // Nếu đang có search term, áp dụng tìm kiếm trên danh sách đã lọc
    if (this.searchTermSubject.value) {
      this.applySearchFilter(this.searchTermSubject.value);
    }
  }

  /**
   * Đặt lại tất cả các lựa chọn sản phẩm
   */
  resetAllSelections(): void {
    const currentList = this.productListSubject.value;
    // Đặt lại trạng thái selected và quantity cho tất cả sản phẩm
    const resetList = currentList.map(item => ({
      ...item,
      selected: false,
      quantity: 0
    }));

    // Cập nhật danh sách sản phẩm với các giá trị đã đặt lại
    this.productListSubject.next(resetList);
    this.applySearchFilter(this.searchTermSubject.value);
  }

  /**
   * Lấy danh sách sản phẩm hiện tại từ BehaviorSubject
   * @returns Mảng sản phẩm hiện tại
   */
  getProductList(): ProductItemExtended[] {
    return this.productListSubject.getValue();
  }
}

/**
 * Interface cho các bộ lọc
 */
export interface ProductFilters {
  warehouseId?: string | null;
  categoryId?: string | null;
  brandId?: string | null;
}
