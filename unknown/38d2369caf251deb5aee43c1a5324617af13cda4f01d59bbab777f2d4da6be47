<div class="taxes-container">
  <!-- Row chính -->
  <div class="d-flex justify-content-between align-items-center mb-2 clickable-row" (click)="toggleExpanded()">
    <div class="d-flex align-items-center">
      <span>{{ 'WAREHOUSE.GOODS_RECEIPT.TAXES.TITLE' | translate }}</span>
      <button mat-icon-button color="primary" class="small-button" 
              matTooltip="{{ 'WAREHOUSE.GOODS_RECEIPT.TAXES.ADD_TAX' | translate }}"
              (click)="openAddTaxDialog(); stopPropagation($event)">
        <mat-icon class="small-icon">add_circle</mat-icon>
      </button>
      <span *ngIf="taxes.length > 0" class="ms-2 badge bg-info">{{ taxes.length }}</span>
    </div>
    <div class="d-flex align-items-center">
      <strong>{{ totalTax | number }} VND</strong>
      <mat-icon class="toggle-icon ms-2">{{ isExpanded ? 'expand_less' : 'expand_more' }}</mat-icon>
    </div>
  </div>

  <!-- Row phụ - hiển thị chi tiết -->
  <div class="taxes-details" [class.expanded]="isExpanded">
    <div *ngIf="taxes.length === 0" class="text-muted py-2">
      {{ 'WAREHOUSE.GOODS_RECEIPT.TAXES.NO_TAXES' | translate }}
    </div>

    <div *ngFor="let tax of taxes; let i = index" class="tax-item">
      <div class="d-flex justify-content-between align-items-center py-1">
        <div class="tax-info">
          <div class="tax-type">{{ getTaxTypeName(tax.type) | translate }}</div>
          <div class="tax-rate text-muted">{{ tax.rate }}% ({{ formatNumber(tax.amount) }} VND)</div>
        </div>
        <div class="tax-actions">
          <button mat-icon-button color="primary" class="small-button" 
                  matTooltip="{{ 'WAREHOUSE.GOODS_RECEIPT.TAXES.EDIT_TAX' | translate }}"
                  (click)="openEditTaxDialog(tax, i); stopPropagation($event)">
            <mat-icon class="small-icon">edit</mat-icon>
          </button>
          <button mat-icon-button color="warn" class="small-button" 
                  matTooltip="{{ 'WAREHOUSE.GOODS_RECEIPT.TAXES.DELETE_TAX' | translate }}"
                  (click)="deleteTax(i); stopPropagation($event)">
            <mat-icon class="small-icon">delete</mat-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
