.location-dialog {
  min-width: 400px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .location-tree-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 4px;
    margin: 10px 0;

    .mat-tree {
      background-color: transparent;
    }
  }

  .mat-tree-node {
    min-height: 36px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: #e3f2fd;
      font-weight: 500;
    }

    mat-icon {
      margin-right: 5px;
    }
  }

  mat-dialog-content {
    flex: 1;
    overflow: auto;
    padding: 10px 0;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.node-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.location-icon {
  margin-right: 8px;
  color: #555;
}

.location-name {
  line-height: 24px;
}

mat-tree-node.selected {
  background-color: rgba(0, 0, 0, 0.08);

  .node-wrapper {
    font-weight: 500;
  }
}
