<div class="modal-container">
  <h2 class="modal-title">
    {{ 'WAREHOUSE.CATEGORY_PRODUCT_MODAL.TITLE' | translate }}
  </h2>

  <div class="modal-content">
    <div class="category-selection">
      <h3>{{ 'WAREHOUSE.CATEGORY_PRODUCT_MODAL.SELECT_CATEGORIES' | translate }}</h3>

      <!-- Sử dụng selectionChange trực tiếp không thông qua template reference variable -->
      <mat-selection-list (selectionChange)="onSelectionChange($event)" color="primary">
        <mat-list-option *ngFor="let category of categoryList" [value]="category._id">
          {{ category.name }}
        </mat-list-option>
      </mat-selection-list>
    </div>
  </div>

  <div class="modal-actions">
    <button mat-stroked-button (click)="onCancel()">
      {{ 'WAREHOUSE.CATEGORY_PRODUCT_MODAL.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" (click)="onAdd()" [disabled]="selectedCategoryIds().length === 0">
      {{ 'WAREHOUSE.CATEGORY_PRODUCT_MODAL.ADD' | translate }}
    </button>
  </div>
</div>
