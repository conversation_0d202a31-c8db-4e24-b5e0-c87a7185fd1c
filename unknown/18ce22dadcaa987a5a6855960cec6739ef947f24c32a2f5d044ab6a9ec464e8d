import { ProductUnit } from 'salehub_shared_contracts';
import { ProductVariants } from 'salehub_shared_contracts/requests/shared/product';

/**
 * Dữ liệu đầu vào cho OrderItemVariantUnitSelectionModal
 */
export interface OrderItemVariantUnitSelectionModalData {
  variants: ProductVariants;
  currentValue: {
    variant: ProductVariants[number];
    unit: ProductUnit;
  };
  units: ProductUnit[];
}

/**
 * Kết quả trả về từ OrderItemVariantUnitSelectionModal
 */
export interface OrderItemVariantUnitSelectionResult {
  variant: {
    variantId: string;
    attributes: Array<{ name: string; value: string }>;
  } | null;
  unit: ProductUnit | null;
}
