<mat-expansion-panel class="mb-3" (opened)="onPanelToggle(true)" (closed)="onPanelToggle(false)">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <mat-icon class="me-2">local_shipping</mat-icon>
      {{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.TITLE' | translate }}
      <span *ngIf="hasTransportInfo()" class="status-badge ms-2">
        {{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.FILLED' | translate }}
      </span>
    </mat-panel-title>
  </mat-expansion-panel-header>

  <div class="transport-info-content" [formGroup]="transportForm">
    <!-- Form thông tin vận chuyển -->
    <div class="row mb-3">
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.TRANSPORT_METHOD' | translate }}</mat-label>
          <mat-select formControlName="transportMethod">
            <mat-option value="road">{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.METHODS.ROAD' | translate }}</mat-option>
            <mat-option value="sea">{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.METHODS.SEA' | translate }}</mat-option>
            <mat-option value="air">{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.METHODS.AIR' | translate }}</mat-option>
            <mat-option value="other">{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.METHODS.OTHER' | translate }}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.CARRIER' | translate }}</mat-label>
          <input matInput formControlName="carrier" placeholder="{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.CARRIER_PLACEHOLDER' | translate }}">
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.TRACKING_NUMBER' | translate }}</mat-label>
          <input matInput formControlName="trackingNumber" placeholder="{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.TRACKING_NUMBER_PLACEHOLDER' | translate }}">
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.ESTIMATED_DELIVERY_DATE' | translate }}</mat-label>
          <input matInput [matDatepicker]="estimatedDatePicker" formControlName="estimatedDeliveryDate">
          <mat-datepicker-toggle matSuffix [for]="estimatedDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #estimatedDatePicker></mat-datepicker>
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.TRANSPORT_INFO.ACTUAL_DELIVERY_DATE' | translate }}</mat-label>
          <input matInput [matDatepicker]="actualDatePicker" formControlName="actualDeliveryDate">
          <mat-datepicker-toggle matSuffix [for]="actualDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #actualDatePicker></mat-datepicker>
        </mat-form-field>
      </div>
    </div>

    <!-- Nút lưu -->
    <div class="d-flex justify-content-end">
      <button mat-raised-button color="primary" (click)="saveTransportInfo()" [disabled]="!hasChanges">
        <mat-icon>save</mat-icon>
        {{ 'COMMON.ACTIONS.SAVE' | translate }}
      </button>
    </div>
  </div>
</mat-expansion-panel>
