import { Pipe, PipeTransform } from '@angular/core';

/**
 * <PERSON>pe để hiển thị tên tab kèm theo số lượng items
 * Ví dụ: "Tất cả (10)"
 */
@Pipe({
  name: 'tabWithCount',
  standalone: true
})
export class TabWithCountPipe implements PipeTransform {
  transform(label: string, count: number | null | undefined): string {
    if (count === null || count === undefined) {
      return label;
    }
    return `${label} (${count})`;
  }
}
