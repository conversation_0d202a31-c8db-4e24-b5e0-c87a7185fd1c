<div class="product-search-wrapper d-flex align-items-center">
  <div class="flex-grow-1 me-2">
    <app-shared-product-search
      (productSelected)="onProductSelected($event)">
    </app-shared-product-search>
  </div>
  <div class="action-buttons">
    <button mat-raised-button color="primary" class="me-2" (click)="onAddFromCategory()">
      <mat-icon>add_circle</mat-icon>
      {{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.ADD_FROM_CATEGORY' | translate }}
    </button>
    <button mat-raised-button color="basic" (click)="onPrintList()">
      <mat-icon>print</mat-icon>
      {{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.PRINT' | translate }}
    </button>
  </div>
</div>
