import { ChangeDetectionStrategy, Component, computed, EventEmitter, Input, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { Place, Province, District, Ward } from 'salehub_shared_contracts';
import { InputAddressComponent } from './components/input-address/input-address.component';
import { AddressManualSelectorComponent } from './components/address-manual-selector/address-manual-selector.component';

interface PlaceSettings {
  useManual: boolean;
}

@Component({
  selector: 'input-place',
  templateUrl: './input-place.component.html',
  styleUrls: ['./input-place.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    InputAddressComponent,
    AddressManualSelectorComponent,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class InputPlaceComponent {
  @Input() settings: PlaceSettings = { useManual: false };
  @Input() placeholder = 'Địa chỉ';
  @Input() oldPlaces?: Place[];
  @Input() defaultValue?: Place;

  @Output() selectedPlace = new EventEmitter<Place>();
  @Output() changingPlace = new EventEmitter<boolean>();

  // Signals
  isChanging = signal(false);
  value = signal<Place | undefined>(undefined);

  // Computed values
  hasMainAddress = computed(() => {
    const currentValue = this.value();
    return currentValue?.mainAddress || currentValue?.secondaryAddress;
  });

  hasLatitude = computed(() => this.value()?.lat);
  hasInstruction = computed(() => this.value()?.instruction);

  ngOnInit() {
    // Khởi tạo giá trị từ latestUsedAddress nếu có
    if (this.defaultValue) {
      this.value.set(this.defaultValue);
    }
  }

  /**
   * Chuyển đổi giữa chế độ nhập thủ công và tự động
   */
  toggleInputMode() {
    this.settings = { useManual: !this.settings.useManual };
    this.changingPlace.emit(false);
    this.isChanging.set(false);
  }

  buildResolvedAddress(place: Place) {
    // Kiểm tra xem có đủ các thành phần của địa chỉ không
    if (place.streetAddress && place.province && place.district && place.ward) {
      // Nếu có đủ, build địa chỉ từ các thành phần
      const parts = [
        place.streetAddress,
        place.ward.name,
        place.district.name,
        place.province.name
      ];
      place.resolvedAddress = parts.join(', ');
    }

    // Nếu không đủ, ưu tiên mainAddress, nếu không có thì dùng fullAddress
    if(!place.resolvedAddress) {
      place.resolvedAddress = place.mainAddress || place.fullAddress || '';
    }
  }

  private buildFullAddress(formValue: any): string {
    const parts = [];
    if (formValue.streetAddress) parts.push(formValue.streetAddress);
    if (formValue.ward?.name) parts.push(formValue.ward.name);
    if (formValue.district?.name) parts.push(formValue.district.name);
    if (formValue.province?.name) parts.push(formValue.province.name);
    return parts.join(', ');
  }

  /**
   * Xử lý khi địa chỉ được chọn
   */
  onSelectedPlace(place: Place) {
    this.buildResolvedAddress(place);
    if(!place.fullAddress) {
      place.fullAddress = this.buildFullAddress(place);
    }
    this.value.set(place);
    this.selectedPlace.emit(place);
  }

  /**
   * Xử lý khi đang thay đổi địa chỉ
   */
  onChangingPlace(isChanging: boolean) {
    this.isChanging.set(isChanging);
    this.changingPlace.emit(isChanging);
  }
}
