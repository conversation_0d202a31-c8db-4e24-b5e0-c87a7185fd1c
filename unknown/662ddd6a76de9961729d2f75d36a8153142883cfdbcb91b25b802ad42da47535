<form [formGroup]="addressForm" class="address-form">
  <!-- <PERSON><PERSON><PERSON> chỉ đường phố -->
  <mat-form-field class="w-full">
    <mat-label>{{ 'ADDRESS.STREET' | translate }}</mat-label>
    <input matInput
           formControlName="streetAddress"
           required>
    <mat-error *ngIf="addressForm.get('streetAddress')?.errors?.['required']">
      {{ 'ADDRESS.STREET_REQUIRED' | translate }}
    </mat-error>
  </mat-form-field>

  <!-- Tỉnh/Thành phố -->
  <mat-form-field class="w-full">
    <mat-label>{{ 'ADDRESS.PROVINCE' | translate }}</mat-label>
    <input matInput
           type="text"
           formControlName="province"
           [matAutocomplete]="provinceAuto"
           (input)="filterProvinces($any($event.target).value)"
           required>
    <mat-autocomplete #provinceAuto="matAutocomplete" [displayWith]="displayFn">
      <mat-option *ngFor="let province of filteredProvinces()" [value]="province">
        {{province.name}}
      </mat-option>
    </mat-autocomplete>
    <mat-error *ngIf="addressForm.get('province')?.errors?.['required']">
      {{ 'ADDRESS.PROVINCE_REQUIRED' | translate }}
    </mat-error>
  </mat-form-field>

  <!-- Quận/Huyện -->
  <mat-form-field class="w-full">
    <mat-label>{{ 'ADDRESS.DISTRICT' | translate }}</mat-label>
    <input matInput
           type="text"
           formControlName="district"
           [matAutocomplete]="districtAuto"
           (input)="filterDistricts($any($event.target).value)"
           required>
    <mat-autocomplete #districtAuto="matAutocomplete" [displayWith]="displayFn">
      <mat-option *ngFor="let district of filteredDistricts()" [value]="district">
        {{district.name}}
      </mat-option>
    </mat-autocomplete>
    <mat-error *ngIf="addressForm.get('district')?.errors?.['required']">
      {{ 'ADDRESS.DISTRICT_REQUIRED' | translate }}
    </mat-error>
  </mat-form-field>

  <!-- Phường/Xã -->
  <mat-form-field class="w-full">
    <mat-label>{{ 'ADDRESS.WARD' | translate }}</mat-label>
    <input matInput
           type="text"
           formControlName="ward"
           [matAutocomplete]="wardAuto"
           (input)="filterWards($any($event.target).value)"
           required>
    <mat-autocomplete #wardAuto="matAutocomplete" [displayWith]="displayFn">
      <mat-option *ngFor="let ward of filteredWards()" [value]="ward">
        {{ward.name}}
      </mat-option>
    </mat-autocomplete>
    <mat-error *ngIf="addressForm.get('ward')?.errors?.['required']">
      {{ 'ADDRESS.WARD_REQUIRED' | translate }}
    </mat-error>
  </mat-form-field>
</form>
