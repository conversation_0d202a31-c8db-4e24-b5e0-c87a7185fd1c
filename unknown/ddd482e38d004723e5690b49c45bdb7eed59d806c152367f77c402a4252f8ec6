import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'input-float',
  standalone: true,
  imports: [
    FormsModule
  ],
  templateUrl: './input-float.component.html'
})
export class InputFloatComponent {
  @Input() value!: string | number;
  @Output() inputChange = new EventEmitter<number | null>();

  onChange(text: string) {
    const value: number = parseFloat(text.replace(/,/g, '.'));

    if(!Number.isNaN(value)) {
      this.inputChange.emit(value);
    } else {
      this.inputChange.emit(null);
    }
  }
}
