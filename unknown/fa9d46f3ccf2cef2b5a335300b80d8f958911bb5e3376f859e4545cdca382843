<div class="container-fluid">
  <mat-card class="variant-form-card">
    <mat-card-header>
      <mat-card-title>
        <h3>{{ editId ? ('PRODUCT.PRODUCT_FORM.EDIT_VARIANT' | translate) : ('PRODUCT.PRODUCT_FORM.CREATE_VARIANT' | translate) }}</h3>
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="variantForm" (ngSubmit)="saveVariant()">
        <!-- Tên thuộc tính -->
        <mat-form-field class="w-100" appearance="outline">
          <mat-label>{{ 'PRODUCT.PRODUCT_FORM.VARIANT_NAME' | translate }}</mat-label>
          <input matInput formControlName="name" required>
          <mat-error *ngIf="variantForm.get('name')?.hasError('required')">
            {{ 'VALIDATION.REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>

        <!-- Gi<PERSON> trị thuộc tính -->
        <app-chip-form-field
          [label]="'PRODUCT.PRODUCT_FORM.VARIANT_VALUES' | translate"
          [placeholder]="'PRODUCT.PRODUCT_FORM.ENTER_VALUE' | translate"
          [suggestions]="variantValueSuggestions"
          formControlName="values"
          [errorMsg]="variantForm.get('values')?.hasError('required') ? ('VALIDATION.REQUIRED' | translate) : ''">
        </app-chip-form-field>

        <!-- Trạng thái kích hoạt -->
        <mat-checkbox formControlName="isActive" color="primary">
          {{ 'PRODUCT.PRODUCT_FORM.ACTIVE' | translate }}
        </mat-checkbox>

        <!-- Nút bấm -->
        <div class="form-actions">
          <button type="button" mat-button (click)="cancel()">
            {{ 'COMMON.CANCEL' | translate }}
          </button>
          <button type="submit" mat-raised-button color="primary" [disabled]="variantForm.invalid">
            {{ 'COMMON.SAVE' | translate }}
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
