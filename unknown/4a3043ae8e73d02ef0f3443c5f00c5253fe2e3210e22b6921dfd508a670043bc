<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Thu ngân</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link rel="manifest" href="manifest.webmanifest">
  <meta name="theme-color" content="#fff">

  <!-- iOS -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <link rel="mask-icon" href="./assets/icons/icon.svg" color="#ffffff">
  <link rel="apple-touch-icon" href="./assets/icons/icon-512.png"/>

  <!-- Android -->
  <meta name="mobile-web-app-capable" content="yes">
  <link rel="icon" href="./assets/icons/icon.svg" type="image/svg+xml">
  <link rel="icon" href="./assets/icons/icon-512.png"/>

  <style>
    .app-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #f1f5f9;
      z-index: 99999;
    }

    .app-loading-inner {
      position: fixed;
      width: 300px;
      height: 78px;
      top: 50%;
      left: 50%;
      margin-left: -150px;
      margin-top: -39px;
      z-index: 99999;
    }
  </style>
</head>
<body cds-theme="light">
  <app-root></app-root>
  <!-- <div class="app-loading">
    <div class="app-loading-inner">
      <img src="assets/images/mock/logoipsum.svg" width="300"/>
    </div>
  </div> -->

  <noscript>Please enable JavaScript to continue using this application.</noscript>
</body>
</html>
