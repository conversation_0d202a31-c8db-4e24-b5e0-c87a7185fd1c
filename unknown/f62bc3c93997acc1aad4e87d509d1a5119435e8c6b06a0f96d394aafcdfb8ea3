<mat-expansion-panel class="mb-3" (opened)="onPanelToggle(true)" (closed)="onPanelToggle(false)">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <mat-icon class="me-2">verified</mat-icon>
      {{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.TITLE' | translate }}
      <span [class]="getStatusBadgeClass()" class="ms-2">
        {{ getStatusText() | translate }}
      </span>
    </mat-panel-title>
  </mat-expansion-panel-header>

  <div class="quality-check-content" [formGroup]="qualityCheckForm">
    <!-- Form kiểm tra chất lượng -->
    <div class="row mb-3">
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.STATUS.LABEL' | translate }}</mat-label>
          <mat-select formControlName="status">
            <mat-option value="pending">{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.STATUS.PENDING' | translate }}</mat-option>
            <mat-option value="passed">{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.STATUS.PASSED' | translate }}</mat-option>
            <mat-option value="failed">{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.STATUS.FAILED' | translate }}</mat-option>
            <mat-option value="partial">{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.STATUS.PARTIAL' | translate }}</mat-option>
          </mat-select>
          <mat-error *ngIf="qualityCheckForm.get('status')?.hasError('required')">
            {{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.ERRORS.STATUS_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.CHECKED_BY' | translate }}</mat-label>
          <mat-select formControlName="checkedBy">
            <mat-option *ngFor="let employee of employeeList" [value]="employee._id">{{ employee.name }}</mat-option>
          </mat-select>
          <mat-error *ngIf="qualityCheckForm.get('checkedBy')?.hasError('required')">
            {{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.ERRORS.CHECKED_BY_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.CHECKED_AT' | translate }}</mat-label>
          <input matInput [matDatepicker]="checkedAtPicker" formControlName="checkedAt">
          <mat-datepicker-toggle matSuffix [for]="checkedAtPicker"></mat-datepicker-toggle>
          <mat-datepicker #checkedAtPicker></mat-datepicker>
          <mat-error *ngIf="qualityCheckForm.get('checkedAt')?.hasError('required')">
            {{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.ERRORS.CHECKED_AT_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.NOTES' | translate }}</mat-label>
          <textarea matInput rows="2" formControlName="notes"></textarea>
        </mat-form-field>
      </div>
    </div>

    <!-- Bảng sản phẩm bị từ chối -->
    <div class="rejected-items-section mb-3">
      <div class="d-flex justify-content-between align-items-center mb-2">
        <h5 class="mb-0">{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.REJECTED_ITEMS.TITLE' | translate }}</h5>
        <button mat-raised-button color="primary" (click)="openAddRejectedItemDialog()">
          <mat-icon>add</mat-icon>
          {{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.REJECTED_ITEMS.ADD_BUTTON' | translate }}
        </button>
      </div>

      <div class="table-responsive">
        <table mat-table [dataSource]="rejectedItems" class="w-100">
          <!-- Sản phẩm -->
          <ng-container matColumnDef="product">
            <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.REJECTED_ITEMS.COLUMNS.PRODUCT' | translate }}</th>
            <td mat-cell *matCellDef="let item">{{ item.name }}</td>
          </ng-container>

          <!-- Số lượng -->
          <ng-container matColumnDef="quantity">
            <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.REJECTED_ITEMS.COLUMNS.QUANTITY' | translate }}</th>
            <td mat-cell *matCellDef="let item">{{ item.quantity }}</td>
          </ng-container>

          <!-- Lý do -->
          <ng-container matColumnDef="reason">
            <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.REJECTED_ITEMS.COLUMNS.REASON' | translate }}</th>
            <td mat-cell *matCellDef="let item" [matTooltip]="item.reason">
              {{ truncateText(item.reason, 50) }}
            </td>
          </ng-container>

          <!-- Hành động -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.REJECTED_ITEMS.COLUMNS.ACTIONS' | translate }}</th>
            <td mat-cell *matCellDef="let item">
              <button mat-icon-button color="primary" (click)="openEditRejectedItemDialog(item)" matTooltip="{{ 'COMMON.ACTIONS.EDIT' | translate }}">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="removeRejectedItem(item)" matTooltip="{{ 'COMMON.ACTIONS.DELETE' | translate }}">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

          <!-- Trạng thái trống -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell text-center py-4" [attr.colspan]="displayedColumns.length">
              {{ 'WAREHOUSE.GOODS_RECEIPT.QUALITY_CHECK.REJECTED_ITEMS.NO_ITEMS' | translate }}
            </td>
          </tr>
        </table>
      </div>
    </div>

    <!-- Nút lưu -->
    <div class="d-flex justify-content-end">
      <button mat-raised-button color="primary" (click)="saveQualityCheck()" [disabled]="!hasChanges || !qualityCheckForm.valid">
        <mat-icon>save</mat-icon>
        {{ 'COMMON.ACTIONS.SAVE' | translate }}
      </button>
    </div>
  </div>
</mat-expansion-panel>
