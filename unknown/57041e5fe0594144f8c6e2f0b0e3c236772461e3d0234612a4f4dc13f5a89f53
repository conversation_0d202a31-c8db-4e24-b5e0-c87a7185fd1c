/**
 * View Model cho Inventory Check
 * Chứa các interface liên quan đến hiển thị dữ liệu trên UI
 */

import { InventoryCheckSummary, InventoryCheck } from '../api/inventory-check.dto';
import { EmbeddedWarehouseLocation, ProductListItem } from '../api/inventory-check.dto';

/**
 * Interface mở rộng InventoryCheckSummary để hiển thị thêm thông tin thống kê
 */
export interface InventoryCheckSummaryExtended extends InventoryCheckSummary {
  stats?: {
    checkedItemsCount: number;
    uncheckedItemsCount: number;
    totalItemsCount: number;
    completionPercentage: number;
  };
}

/**
 * Mở rộng interface InventoryCheck để sử dụng InventoryCheckSummaryExtended
 */
export interface InventoryCheckExtended extends Omit<InventoryCheck, 'summary'> {
  summary: InventoryCheckSummaryExtended;
}

/**
 * Interface cho kết quả lọc sản phẩm từ ProductFilterDialog
 */
export interface ProductFilterResult {
  products: ProductListItem[];
  filters: {
    categories: string[];
    warehouseLocation: EmbeddedWarehouseLocation | null;
    onlyInStock: boolean;
    onlyActive: boolean;
  };
}

/**
 * Interface cho event cập nhật số lượng thực tế
 */
export interface ActualQuantityUpdateEvent {
  productId: string;
  quantity: number;
}

/**
 * Interface cho event cập nhật số lượng lô
 */
export interface BatchQuantityUpdateEvent {
  productId: string;
  batchId: string;
  quantity: number;
}

/**
 * Interface cho thuộc tính variant của sản phẩm
 */
export interface ProductVariantAttribute {
  name: string;
  value: string;
}

/**
 * Interface cho variant được chọn
 */
export interface SelectedVariant {
  variantId: string;
  attributes: ProductVariantAttribute[];
  price?: number;
}

/**
 * Interface cho đơn vị tính được chọn
 */
export interface SelectedUnit {
  unitId: string;
  name: string;
  conversionFactor: number;
  price?: number;
}

/**
 * Interface cho kết quả chọn variant
 */
export interface VariantSelectionResult {
  variant?: SelectedVariant;
  unit?: SelectedUnit;
}
