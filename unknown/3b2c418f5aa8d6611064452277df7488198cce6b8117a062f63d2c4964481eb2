.container-fluid {
  padding: 1rem;

  .card {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);

    .card-header {
      background-color: #f8f9fa;
      padding: 1rem;

      h3 {
        margin-bottom: 0;
        font-size: 1.5rem;
      }
    }

    .card-body {
      padding: 1.5rem;
    }
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .container-fluid {
    padding: 0.75rem;

    .card {
      .card-header {
        padding: 0.75rem;

        h3 {
          font-size: 1.25rem;
        }
      }

      .card-body {
        padding: 1rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .container-fluid {
    padding: 0.5rem;

    .card {
      .card-header {
        padding: 0.75rem;

        h3 {
          font-size: 1.1rem;
        }
      }

      .card-body {
        padding: 0.75rem;
      }
    }
  }
}
