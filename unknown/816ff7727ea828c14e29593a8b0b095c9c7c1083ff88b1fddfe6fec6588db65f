<div class="product-selection-container">
  <!-- Header with search and filters -->
  <div class="header-container">
    <!-- Search bar -->
    <div class="search-container">
      <div class="search-bar">
        <span class="search-icon material-icons">search</span>
        <input class="search-input"
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearchChange($event)"
              placeholder="{{ 'PRODUCT_SELECTION.PRODUCT_SEARCH_PLACEHOLDER' | translate }}" />
        <span *ngIf="searchTerm"
              class="clear-icon material-icons"
              (click)="clearSearch()">cancel</span>
      </div>
    </div>


    <!-- Filters -->
    <div class="filters-container">
      <!-- Warehouse Filter -->
      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ 'PRODUCT_SELECTION.WAREHOUSE_FILTER' | translate }}</mat-label>
        <mat-select [formControl]="selectedWarehouseFilter">
          <mat-option>{{ 'PRODUCT_SELECTION.ALL_WAREHOUSES' | translate }}</mat-option>
          <mat-option>
            <ngx-mat-select-search
              [formControl]="warehouseFilterCtrl"
              placeholderLabel="{{ 'PRODUCT_SELECTION.SEARCH_WAREHOUSE' | translate }}"
              noEntriesFoundLabel="{{ 'PRODUCT_SELECTION.NO_MATCHING_WAREHOUSE' | translate }}">
            </ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let warehouse of filteredWarehouseList()" [value]="warehouse._id">
            {{ warehouse.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <!-- Category Filter -->
      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ 'PRODUCT_SELECTION.CATEGORY_FILTER' | translate }}</mat-label>
        <mat-select [formControl]="selectedCategoryFilter">
          <mat-option>{{ 'PRODUCT_SELECTION.ALL_CATEGORIES' | translate }}</mat-option>
          <mat-option>
            <ngx-mat-select-search
              [formControl]="categoryFilterCtrl"
              placeholderLabel="{{ 'PRODUCT_SELECTION.SEARCH_CATEGORY' | translate }}"
              noEntriesFoundLabel="{{ 'PRODUCT_SELECTION.NO_MATCHING_CATEGORY' | translate }}">
            </ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let category of filteredCategoryList()" [value]="category._id">
            {{ category.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <!-- Brand Filter -->
      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>{{ 'PRODUCT_SELECTION.BRAND_FILTER' | translate }}</mat-label>
        <mat-select [formControl]="selectedBrandFilter">
          <mat-option>{{ 'PRODUCT_SELECTION.ALL_BRANDS' | translate }}</mat-option>
          <mat-option>
            <ngx-mat-select-search
              [formControl]="brandFilterCtrl"
              placeholderLabel="{{ 'PRODUCT_SELECTION.SEARCH_BRAND' | translate }}"
              noEntriesFoundLabel="{{ 'PRODUCT_SELECTION.NO_MATCHING_BRAND' | translate }}">
            </ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let brand of filteredBrandList()" [value]="brand._id">
            {{ brand.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <!-- Buttons -->
      <div class="filter-actions">
        <button mat-stroked-button color="warn" class="reset-filter-btn" (click)="resetFilters()">
          <span class="material-icons">filter_alt_off</span>
          {{ 'PRODUCT_SELECTION.RESET_FILTERS' | translate }}
        </button>

        <button mat-raised-button color="primary" class="add-product-btn" (click)="openProductForm()">
          <span class="material-icons">add</span>
          {{ 'PRODUCT_SELECTION.ADD_NEW_PRODUCT' | translate }}
        </button>
      </div>
    </div>
  </div>

  <!-- Product list container -->
  <div class="product-list-container">
    <div #swiperContainer class="swiper">
      <div class="swiper-wrapper">
        <!-- Page loop -->
        <div *ngFor="let page of displayedPages()" class="swiper-slide">
          <div class="product-grid">
            <!-- Product item loop -->
            <div *ngFor="let item of page"
                 [class.selected]="item.selected"
                 [class.out-of-stock]="item.trackInventory && getInventoryQuantity(item) <= 0 && !settings.allowSellWhenOutOfStock"
                 class="product-item"
                 (click)="onSelectItem(item)">
              <div class="product-content">
                <div class="product-image">
                  <img [src]="item.image || 'assets/images/default-product.png'" alt="{{ item.name }}">
                  <!-- Hiển thị thông báo hết hàng -->
                  <div *ngIf="item.trackInventory && getInventoryQuantity(item) <= 0 && !settings.allowSellWhenOutOfStock"
                       class="out-of-stock-badge">
                    {{ 'PRODUCT_SELECTION.OUT_OF_STOCK' | translate }}
                  </div>
                  <!-- Quantity counter -->
                  <div *ngIf="item.selected && item.quantity" class="quantity-overlay">
                    <div class="quantity-controls">
                      <button mat-icon-button class="quantity-btn" (click)="decreaseQuantity(item); $event.stopPropagation()">
                        <span class="material-icons">remove</span>
                      </button>
                      <span class="quantity-value">{{item.quantity}}</span>
                      <button mat-icon-button class="quantity-btn"
                              [disabled]="!canIncreaseQuantity(item)"
                              (click)="increaseQuantity(item); $event.stopPropagation()">
                        <span class="material-icons">add</span>
                      </button>
                    </div>
                    <!-- Add Options button for items with modifiers -->
                    <button *ngIf="item.linkedModifierGroupIds && item.linkedModifierGroupIds.length > 0"
                            mat-stroked-button
                            class="add-options-btn"
                            (click)="openModifiersSheet(item); $event.stopPropagation()">
                      <span class="material-icons">tune</span>
                      {{ 'PRODUCT_SELECTION.ADD_OPTIONS' | translate }}
                    </button>
                  </div>
                </div>
                <div class="product-details">
                  <h3 class="product-name">{{ item.name }}</h3>
                  <div *ngIf="item.variant" class="product-variant">
                    <span *ngFor="let attr of item.variant.attributes; let last = last">
                      {{ attr.name }}: {{ attr.value }}{{ !last ? ', ' : '' }}
                    </span>
                  </div>
                  <div class="product-info">
                    <div class="price">{{ item.price | number }} ₫</div>
                    <div *ngIf="item.unit" class="unit">
                      <span class="label">{{ 'PRODUCT_SELECTION.UNIT' | translate }}:</span> {{ item.unit.unitName }}
                    </div>
                    <div *ngIf="item.trackInventory" class="inventory"
                         [class.warning]="getInventoryQuantity(item) <= 5 && getInventoryQuantity(item) > 0"
                         [class.danger]="getInventoryQuantity(item) <= 0">
                      <span class="label">{{ 'PRODUCT_SELECTION.INVENTORY' | translate }}:</span> {{ getInventoryQuantity(item) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Navigation buttons -->
      <div class="swiper-button-next"></div>
      <div class="swiper-button-prev"></div>
    </div>

    <!-- Empty state -->
    <div *ngIf="!displayedPages().length" class="empty-state">
      <div class="empty-message">
        <span class="material-icons">search_off</span>
        <p>{{ 'PRODUCT_SELECTION.NO_PRODUCTS_FOUND' | translate }}</p>
      </div>
    </div>
  </div>

  <!-- Actions row -->
  <div class="actions-container">
    <button mat-raised-button color="warn" class="reset-btn" (click)="resetSelection()">
      <span class="material-icons">restart_alt</span>
      {{ 'PRODUCT_SELECTION.RESET_SELECTION' | translate }}
    </button>
    <button mat-raised-button color="primary" class="add-to-order-btn" (click)="addToOrder()">
      <span class="material-icons">add_shopping_cart</span>
      {{ 'PRODUCT_SELECTION.ADD_TO_ORDER_WITH_TOTAL' | translate: { total: getFormattedTotal() } }}
    </button>
  </div>
</div>
