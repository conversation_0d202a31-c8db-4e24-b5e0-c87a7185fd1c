/**
 * Interface cho thuộc tính sản phẩm
 */
export interface ProductVariant {
  _id: string;
  name: string;
  values: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Dữ liệu đầu vào cho VariantFormModal
 */
export interface VariantFormModalData {
  /**
   * ID của thuộc tính đang chỉnh sửa (nếu có)
   */
  editId?: string;
  
  /**
   * Danh sách gợi ý cho giá trị thuộc tính
   */
  valueSuggestions?: string[];
}

/**
 * Kết quả trả về từ VariantFormModal
 */
export type VariantFormModalResult = ProductVariant | null;
