import { Provider } from '@angular/core';
import { InventoryCheckRepository } from '../../../../../domain/repositories/warehouse/inventory-check.repository';
import { InventoryCheckRepositoryImpl } from '../services/inventory-check-repository.impl';
import { InventoryCheckUseCase } from '../../../../../application/use-cases/warehouse/inventory-check/inventory-check.usecase';

/**
 * Provider cho Inventory Check
 * Cung cấp các dependency injection cho component
 */
export const INVENTORY_CHECK_PROVIDERS: Provider[] = [
  {
    provide: InventoryCheckRepository,
    useClass: InventoryCheckRepositoryImpl
  },
  InventoryCheckUseCase
];
