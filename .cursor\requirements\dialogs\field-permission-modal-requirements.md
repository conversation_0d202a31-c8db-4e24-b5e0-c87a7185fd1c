**Modal Set Field Permission** để thiết lập quyền truy cập cho từng **Custom Field** trong `DynamicLayoutBuilderComponent`

---
<PERSON><PERSON><PERSON> yêu cầu cơ bản:  
- <PERSON>ôn tuân thủ rules của [text](../../rules/angular.mdc) và [text](../../rules/requirements.mdc)
- Sử dụng MCP server  khi cần thiết để debug lỗi và view trên browser.  
- Trước khi tự động chạy, nếu có gì cần làm rõ thì hỏi lại để rõ ràng các ý chính trước khi tự động chạy, không phải sửa đi sửa lại nhiều lần vì không hiểu ý.  

--


View Model:
- [text](../../../src/infra/shared/models/view/field-permission.model.ts)

---

Vi<PERSON>t tất cả vào [text](../../../src/infra/shared/modals/field-permission/*)

---

### 1. <PERSON><PERSON> tả chi tiết Modal Set Field Permission

**Mục tiêu**:  
Modal này cho phép người dùng (thường là admin hoặc quản lý) thiết lập quyền truy cập cho từng **Custom Field** trong một section của layout. Quyền được gán cho các **profile** (vai trò hoặc nhóm người dùng), đảm bảo kiểm soát chi tiết (**field-level security**) tương tự SAP, Odoo, Zoho.

**Tính năng chính**:
- **Input**: Nhận danh sách profiles dưới dạng mảng:
  ```typescript
  Array<{
    id: string;
    name: string;
    permission: 'read_write' | 'read' | 'none';
  }>
  ```
- **Header**: Hiển thị tiêu đề dạng "Set Permission - [Field Name]" (Field Name lấy từ `CustomField.label`).
- **Table**:
  - **Header**:
    - Cột 1: "Profiles" (tên profile).
    - Cột 2: "Read and Write All" (quyền đọc và ghi) với một **button** "All".
    - Cột 3: "Read Only All" (quyền chỉ đọc) với một **button** "All".
    - Cột 4: "Don't Show All" (không hiển thị) với một **button** "All".
  - **Rows**:
    - Mỗi hàng tương ứng với một profile từ input `profiles`.
    - Cột 1: Hiển thị `profile.name`.
    - Cột 2-4: Một nhóm **radio buttons** cho các quyền (`read_write`, `read`, `none`). Radio button tương ứng với `profile.permission` được chọn mặc định.
  - **Hành động trên header**:
    - Nhấn button "All" trong cột "Read and Write All" sẽ đặt `permission: 'read_write'` cho tất cả profiles.
    - Nhấn button "All" trong cột "Read Only All" sẽ đặt `permission: 'read'` cho tất cả profiles.
    - Nhấn button "All" trong cột "Don't Show All" sẽ đặt `permission: 'none'` cho tất cả profiles.
- **Hành động trên modal**:
  - Nút **Save** lưu thay đổi và emit danh sách profiles đã cập nhật.
  - Nút **Cancel** đóng modal mà không lưu.
- **Output**: Emit danh sách profiles đã cập nhật:
  ```typescript
  Array<{
    id: string;
    name: string;
    permission: 'read_write' | 'read' | 'none';
  }>
  ```


---

### 2. Cấu trúc file và vị trí
Modal sẽ được đặt trong thư mục `src/infra/shared/modals/field-permission/`:
- Thêm i18n keys vào `src/infra/i18n/shared/modals/field-permission/` (`en.json`, `vi.json`).
