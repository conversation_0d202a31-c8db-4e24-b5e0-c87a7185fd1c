// Permission Selector Component Styles
.permission-selector-container {
  margin-bottom: 1rem;
  
  .mat-mdc-form-field {
    width: 100%;
    
    // Responsive: Thu nhỏ trên mobile
    @media (max-width: 576px) {
      .mat-mdc-form-field-infix {
        min-height: 40px;
      }
    }
  }
}

// Permission Option Styles
.permission-option {
  .permission-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
    
    // Permission colors
    &.text-success {
      color: #28a745;
    }
    
    &.text-warning {
      color: #ffc107;
    }
    
    &.text-danger {
      color: #dc3545;
    }
    
    &.text-muted {
      color: #6c757d;
    }
  }
  
  .profile-name {
    font-weight: 500;
    flex: 1;
  }
}

// Permission Badge Styles
.permission-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  text-transform: uppercase;
  
  &.badge-read_write {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  
  &.badge-read {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }
  
  &.badge-none {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .permission-selector-container {
    margin-bottom: 0.75rem;
    
    .permission-option {
      .permission-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
      }
    }
  }
}

@media (max-width: 576px) {
  .permission-selector-container {
    margin-bottom: 0.5rem;
    
    .permission-option {
      .profile-name {
        font-size: 0.9rem;
      }
      
      .permission-badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
      }
    }
  }
}
