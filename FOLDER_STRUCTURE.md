Dưới đây là cấu trúc folder chi tiết và cách tổ chức các file/folder cho dự án ERP, sử dụng **Angular 19** với **standalone components** và **Clean Architecture**. 

### Cấu trúc folder và cách tổ chức

project-root/
├── scripts/                             # Scripts xử lý build-time/watch
│   ├── merge-translations.js            # Script ESM: Gộp file dịch từ src/infra/i18n thành public/assets/i18n
├── src/                                 # Source code chính
│   ├── domain/                          # Tầng Domain: Entities, business rules, repository abstractions
│   │   ├── entities/                    # Entities và Value Objects
│   │   │   ├── product.ts               # Entity: Sản phẩm (id, name, price, stock)
│   │   │   ├── order.ts                 # Entity: Đ<PERSON>n hàng (id, items, totalPrice)
│   │   │   ├── money.ts                 # Value Object: Tiền tệ (amount, currency)
│   │   │   ├── size.ts                  # Value Object: Kích cỡ (S, M) cho Thời trang
│   │   │   ├── expiration-date.ts       # Value Object: <PERSON><PERSON><PERSON> hết hạn cho Đồ ăn
│   │   ├── rules/                       # Business rules độc lập
│   │   │   ├── pricing-rules.ts         # Quy tắc: Tính giá (chiết khấu, thuế)
│   │   │   ├── inventory-rules.ts       # Quy tắc: Kiểm tra tồn kho
│   │   ├── repositories/                 # Repository abstractions theo feature
│   │   │   ├── cashier/                 # POS
│   │   │   │   ├── order.repository.ts  # Abstract: Quản lý đơn hàng
│   │   │   ├── warehouse/               # WMS
│   │   │   │   ├── inventory.repository.ts # Abstract: Quản lý tồn kho
│   │   │   ├── ecommerce/               # ECOMMERCE
│   │   │   │   ├── sync-order.repository.ts # Abstract: Đồng bộ đơn TMĐT
│   │   │   ├── settings/                # Settings
│   │   │   │   ├── settings.repository.ts # Abstract: Quản lý cài đặt
│   ├── application/                     # Tầng Application: Use cases
│   │   ├── use-cases/                   # Use cases theo feature/sub-feature
│   │   │   ├── cashier/                 # POS
│   │   │   │   ├── create-order/        # Sub-feature: Tạo đơn
│   │   │   │   │   ├── create-order.usecase.ts
│   │   │   │   ├── order-history/       # Sub-feature: Lịch sử đơn
│   │   │   │   │   ├── get-order-history.usecase.ts
│   │   │   ├── warehouse/               # WMS
│   │   │   │   ├── inventory-check/     # Sub-feature: Kiểm kho
│   │   │   │   │   ├── inventory-check.usecase.ts
│   │   │   │   ├── stock-adjustment/    # Sub-feature: Điều chỉnh tồn kho
│   │   │   │   │   ├── stock-adjustment.usecase.ts
│   │   │   ├── ecommerce/               # ECOMMERCE
│   │   │   │   ├── sync-orders/         # Sub-feature: Đồng bộ đơn
│   │   │   │   │   ├── sync-order.usecase.ts
│   │   │   │   ├── order-management/    # Sub-feature: Quản lý đơn
│   │   │   │   │   ├── order-management.usecase.ts
│   │   │   ├── settings/                # Settings
│   │   │   │   ├── settings/            # Sub-feature: Cài đặt
│   │   │   │   │   ├── update-settings.usecase.ts
│   ├── infra/                           # Tầng Infrastructure: UI, services, cấu hình
│   │   ├── config/                      # Cấu hình hệ thống
│   │   │   ├── environment.ts           # Biến môi trường (API_URL, Shopee keys)
│   │   │   ├── config.service.ts        # Service: Quản lý cấu hình runtime
│   │   ├── core/                        # Services, repository impl toàn cục
│   │   │   ├── services/                # Services và repository impl dùng chung
│   │   │   │   ├── http.service.ts      # Service: Gọi API
│   │   │   │   ├── shopee-adapter.ts    # Adapter: Đồng bộ Shopee
│   │   │   │   ├── lazada-adapter.ts    # Adapter: Đồng bộ Lazada
│   │   │   │   ├── product-repository.impl.ts # Repository Impl: Sản phẩm
│   │   │   │   ├── translate-http.loader.ts # Loader: Tải file dịch
│   │   │   ├── interceptors/            # HTTP interceptors
│   │   │   │   ├── auth.interceptor.ts  # Thêm token vào request
│   │   │   ├── guards/                  # Route guards
│   │   │   │   ├── auth.guard.ts        # Kiểm tra quyền truy cập
│   │   ├── shared/                      # Thành phần dùng chung
│   │   │   ├── models/                  # DTO và View Models dùng chung
│   │   │   │   ├── api/                 # DTO
│   │   │   │   │   ├── product.dto.ts
│   │   │   │   │   ├── order.dto.ts
│   │   │   │   ├── view/                # View Models
│   │   │   │   │   ├── product-view.model.ts
│   │   │   │   │   ├── cart-item-view.model.ts
│   │   │   ├── components/              # Standalone components dùng chung
│   │   │   │   ├── button/
│   │   │   │   │   ├── button.component.ts
│   │   │   │   │   ├── button.component.html
│   │   │   │   │   ├── button.component.scss
│   │   │   │   ├── spinner/
│   │   │   │   │   ├── spinner.component.ts
│   │   │   │   │   ├── spinner.component.html
│   │   │   │   │   ├── spinner.component.scss
│   │   │   ├── pipes/
│   │   │   │   ├── currency.pipe.ts
│   │   │   ├── directives/
│   │   │   │   ├── highlight.directive.ts
│   │   │   ├── styles/
│   │   │   │   ├── styles.scss
│   │   ├── features/                    # Các feature của ứng dụng
│   │   │   ├── cashier/                 # POS
│   │   │   │   ├── create-order/
│   │   │   │   │   ├── components/
│   │   │   │   │   │   ├── create-order.component.ts
│   │   │   │   │   │   ├── create-order.component.html
│   │   │   │   │   │   ├── create-order.component.scss
│   │   │   │   │   │   ├── cart-item/
│   │   │   │   │   │   │   ├── cart-item.component.ts
│   │   │   │   │   │   │   ├── cart-item.component.html
│   │   │   │   │   │   │   ├── cart-item.component.scss
│   │   │   │   │   │   ├── payment-modal/
│   │   │   │   │   │   │   ├── payment-modal.component.ts
│   │   │   │   │   │   │   ├── payment-modal.component.html
│   │   │   │   │   │   │   ├── payment-modal.component.scss
│   │   │   │   │   ├── services/
│   │   │   │   │   │   ├── create-order.service.ts
│   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── api/
│   │   │   │   │   │   │   ├── create-order.dto.ts
│   │   │   │   │   │   ├── view/
│   │   │   │   │   │   │   ├── create-order-view.model.ts
│   │   │   │   │   ├── create-order.provider.ts
│   │   │   │   │   ├── create-order.resolver.ts
│   │   │   │   │   ├── create-order.routes.ts
│   │   │   │   │   ├── index.ts
│   │   │   │   ├── order-history/
│   │   │   │   │   ├── components/
│   │   │   │   │   │   ├── order-history.component.ts
│   │   │   │   │   │   ├── order-history.component.html
│   │   │   │   │   │   ├── order-history.component.scss
│   │   │   │   │   │   ├── order-list/
│   │   │   │   │   │   │   ├── order-list.component.ts
│   │   │   │   │   │   │   ├── order-list.component.html
│   │   │   │   │   │   │   ├── order-list.component.scss
│   │   │   │   │   ├── services/
│   │   │   │   │   │   ├── order-history.service.ts
│   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── api/
│   │   │   │   │   │   │   ├── order-history.dto.ts
│   │   │   │   │   │   ├── view/
│   │   │   │   │   │   │   ├── order-history-view.model.ts
│   │   │   │   │   ├── order-history.provider.ts
│   │   │   │   │   ├── order-history.resolver.ts
│   │   │   │   │   ├── order-history.routes.ts
│   │   │   │   │   ├── index.ts
│   │   │   │   ├── services/
│   │   │   │   │   ├── cashier.service.ts
│   │   │   │   ├── cashier.routes.ts
│   │   │   ├── warehouse/               # WMS
│   │   │   │   ├── inventory-check/
│   │   │   │   │   ├── components/
│   │   │   │   │   │   ├── inventory-check.component.ts
│   │   │   │   │   │   ├── inventory-check.component.html
│   │   │   │   │   │   ├── inventory-check.component.scss
│   │   │   │   │   │   ├── inventory-check-list/
│   │   │   │   │   │   │   ├── inventory-check-list.component.ts
│   │   │   │   │   │   │   ├── inventory-check-list.component.html
│   │   │   │   │   │   │   ├── inventory-check-list.component.scss
│   │   │   │   │   ├── services/
│   │   │   │   │   │   ├── inventory-check.service.ts
│   │   │   │   │   │   ├── inventory-check-repository.impl.ts
│   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── api/
│   │   │   │   │   │   │   ├── inventory-check.dto.ts
│   │   │   │   │   │   ├── view/
│   │   │   │   │   │   │   ├── inventory-check-view.model.ts
│   │   │   │   │   ├── inventory-check.provider.ts
│   │   │   │   │   ├── inventory-check.resolver.ts
│   │   │   │   │   ├── inventory-check.routes.ts
│   │   │   │   │   ├── index.ts
│   │   │   │   ├── stock-adjustment/
│   │   │   │   │   ├── components/
│   │   │   │   │   │   ├── stock-adjustment.component.ts
│   │   │   │   │   │   ├── stock-adjustment.component.html
│   │   │   │   │   │   ├── stock-adjustment.component.scss
│   │   │   │   │   ├── services/
│   │   │   │   │   │   ├── stock-adjustment.service.ts
│   │   │   │   │   │   ├── stock-adjustment-repository.impl.ts
│   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── api/
│   │   │   │   │   │   │   ├── stock-adjustment.dto.ts
│   │   │   │   │   │   ├── view/
│   │   │   │   │   │   │   ├── stock-adjustment-view.model.ts
│   │   │   │   │   ├── stock-adjustment.provider.ts
│   │   │   │   │   ├── stock-adjustment.resolver.ts
│   │   │   │   │   ├── stock-adjustment.routes.ts
│   │   │   │   │   ├── index.ts
│   │   │   │   ├── services/
│   │   │   │   │   ├── warehouse.service.ts
│   │   │   │   ├── warehouse.routes.ts
│   │   │   ├── ecommerce/               # ECOMMERCE
│   │   │   │   ├── sync-orders/
│   │   │   │   │   ├── components/
│   │   │   │   │   │   ├── sync-orders.component.ts
│   │   │   │   │   │   ├── sync-orders.component.html
│   │   │   │   │   │   ├── sync-orders.component.scss
│   │   │   │   │   │   ├── order-list/
│   │   │   │   │   │   │   ├── order-list.component.ts
│   │   │   │   │   │   │   ├── order-list.component.html
│   │   │   │   │   │   │   ├── order-list.component.scss
│   │   │   │   │   ├── services/
│   │   │   │   │   │   ├── sync-orders.service.ts
│   │   │   │   │   │   ├── sync-order-repository.impl.ts
│   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── api/
│   │   │   │   │   │   │   ├── sync-orders.dto.ts
│   │   │   │   │   │   │   ├── shopee-order.dto.ts
│   │   │   │   │   │   ├── view/
│   │   │   │   │   │   │   ├── sync-orders-view.model.ts
│   │   │   │   │   ├── sync-orders.provider.ts
│   │   │   │   │   ├── sync-orders.resolver.ts
│   │   │   │   │   ├── sync-orders.routes.ts
│   │   │   │   │   ├── index.ts
│   │   │   │   ├── order-management/
│   │   │   │   │   ├── components/
│   │   │   │   │   │   ├── order-management.component.ts
│   │   │   │   │   │   ├── order-management.component.html
│   │   │   │   │   │   ├── order-management.component.scss
│   │   │   │   │   ├── services/
│   │   │   │   │   │   ├── order-management.service.ts
│   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── api/
│   │   │   │   │   │   │   ├── order-management.dto.ts
│   │   │   │   │   │   ├── view/
│   │   │   │   │   │   │   ├── order-management-view.model.ts
│   │   │   │   │   ├── order-management.provider.ts
│   │   │   │   │   ├── order-management.resolver.ts
│   │   │   │   │   ├── order-management.routes.ts
│   │   │   │   │   ├── index.ts
│   │   │   │   ├── services/
│   │   │   │   │   ├── ecommerce.service.ts
│   │   │   │   ├── ecommerce.routes.ts
│   │   │   ├── settings/                # Settings
│   │   │   │   ├── components/
│   │   │   │   │   ├── settings.component.ts
│   │   │   │   │   ├── settings.component.html
│   │   │   │   │   ├── settings.component.scss
│   │   │   │   ├── services/
│   │   │   │   │   ├── settings.service.ts
│   │   │   │   │   ├── settings-repository.impl.ts
│   │   │   │   ├── models/
│   │   │   │   │   ├── api/
│   │   │   │   │   │   ├── settings.dto.ts
│   │   │   │   │   ├── view/
│   │   │   │   │   │   ├── settings-view.model.ts
│   │   │   │   ├── settings.provider.ts
│   │   │   │   ├── settings.resolver.ts
│   │   │   │   ├── settings.routes.ts
│   │   │   │   ├── index.ts
│   │   ├── i18n/                        # File dịch gốc (Infrastructure)
│   │   │   ├── global/                  # Bản dịch chung
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   │   ├── cashier/                 # POS
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   │   │   ├── create-order/
│   │   │   │   │   ├── en.json
│   │   │   │   │   ├── vi.json
│   │   │   │   ├── order-history/
│   │   │   │   │   ├── en.json
│   │   │   │   │   ├── vi.json
│   │   │   ├── warehouse/               # WMS
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   │   │   ├── inventory-check/
│   │   │   │   │   ├── en.json
│   │   │   │   │   ├── vi.json
│   │   │   │   ├── stock-adjustment/
│   │   │   │   │   ├── en.json
│   │   │   │   │   ├── vi.json
│   │   │   ├── ecommerce/               # ECOMMERCE
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   │   │   ├── sync-orders/
│   │   │   │   │   ├── en.json
│   │   │   │   │   ├── vi.json
│   │   │   │   ├── order-management/
│   │   │   │   │   ├── en.json
│   │   │   │   │   ├── vi.json
│   │   │   ├── settings/                # Settings
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   ├── app.component.ts           # Root component
│   │   ├── app.component.html
│   │   ├── app.component.scss
│   │   ├── app.routes.ts              # Main routes
│   ├── main.ts                          # Entry point
├── public/                              # Tài nguyên tĩnh
│   ├── assets/                          # Hình ảnh, fonts, file gộp i18n
│   │   ├── images/
│   │   │   ├── logo.png
│   │   ├── fonts/
│   │   │   ├── roboto-regular.ttf
├── angular.json                         # Cấu hình Angular
├── package.json                         # Dependencies và scripts
├── tsconfig.json                        # Cấu hình TypeScript
```

---

### Giải thích chi tiết từng thư mục và file

#### 1. `src/domain/` - Tầng Domain
- **Vai trò trong Clean Architecture**: Đây là tầng **trọng tâm** của Clean Architecture, chứa các **entities**, **value objects**, **business rules**, và **repository abstractions** đại diện cho mô hình kinh doanh. Tầng này **độc lập hoàn toàn**, không được import từ `application/` hoặc `infra/`, đảm bảo logic kinh doanh không phụ thuộc vào framework (Angular) hoặc hệ thống bên ngoài (API, database).
- **Mục đích**: Định nghĩa các đối tượng kinh doanh (như sản phẩm, đơn hàng) và quy tắc kinh doanh (tính giá, kiểm kho), dùng chung cho tất cả module ERP (`POS`, `WMS`, `ECOMMERCE`) và ngành hàng (Thời trang, Đồ ăn).
- **Cấu trúc**:
  - **`entities/`**:
    - **Mô tả**: Chứa các **entities** (đối tượng kinh doanh chính) và **value objects** (đối tượng phụ hỗ trợ). Mỗi file là một class TypeScript định nghĩa cấu trúc và hành vi cơ bản.
    - **Ví dụ**:
      - `product.ts`: Entity `Product` với các thuộc tính `id`, `name`, `price`, `stock`. Ví dụ:
        ```typescript
        export class Product {
          constructor(
            public readonly id: string,
            public readonly name: string,
            public readonly price: Money,
            public stock: number
          ) {}
        }
        ```
      - `order.ts`: Entity `Order` với `id`, `items`, `totalPrice`.
      - `money.ts`: Value Object `Money` với `amount` và `currency` (VND).
      - `size.ts`: Value Object `Size` cho ngành Thời trang (giá trị như 'S', 'M').
      - `expiration-date.ts`: Value Object `ExpirationDate` cho ngành Đồ ăn.
    - **Lưu ý cho AI Coding Agent**: Các file này chỉ chứa định nghĩa class, không có logic gọi API hoặc import từ `application/`/`infra/`. Không thêm bất kỳ phụ thuộc nào vào tầng ngoài.
  - **`rules/`**:
    - **Mô tả**: Chứa các **business rules** (quy tắc kinh doanh) độc lập, thường là các hàm hoặc class tĩnh xử lý logic kinh doanh cốt lõi (như tính giá, kiểm kho).
    - **Ví dụ**:
      - `pricing-rules.ts`: Hàm tính giá đơn hàng dựa trên chiết khấu, thuế. Ví dụ:
        ```typescript
        export class PricingRules {
          static calculateTotalPrice(items: { price: Money; quantity: number }[]): Money {
            // Logic tính giá với chiết khấu, thuế
            return new Money(totalAmount, 'VND');
          }
        }
        ```
      - `inventory-rules.ts`: Quy tắc kiểm tra tồn kho (stock >= quantity).
    - **Lưu ý cho AI Coding Agent**: Các file này chỉ sử dụng entities/value objects từ `entities/`, không import từ `application/` hoặc `infra/`. Logic phải thuần túy, không phụ thuộc vào API hoặc UI.
  - **`repositories/`**:
    - **Mô tả**: Chứa các **repository abstractions** (abstract classes/interfaces) định nghĩa contract cho việc truy xuất dữ liệu (như tạo đơn, kiểm kho). Được tổ chức theo feature (`cashier/`, `warehouse/`, `ecommerce/`, `settings/`) để dễ quản lý.
    - **Ví dụ**:
      - `cashier/order.repository.ts`: Abstract class cho quản lý đơn hàng.
        ```typescript
        import { Observable } from 'rxjs';
        import { Order } from '../../entities/order';

        export abstract class OrderRepository {
          abstract createOrder(order: Order): Observable<Order>;
          abstract getOrders(): Observable<Order[]>;
        }
        ```
      - `warehouse/inventory-check.repository.ts`: Contract cho kiểm kho.
      - `ecommerce/sync-order.repository.ts`: Contract cho đồng bộ đơn TMĐT.
      - `settings/settings.repository.ts`: Contract cho quản lý cài đặt.
    - **Lưu ý cho AI Coding Agent**: Chỉ định nghĩa phương thức trừu tượng, không chứa logic cụ thể (như gọi API). Không import từ `application/` hoặc `infra/`. Đặt file trong sub-folder tương ứng với feature (`cashier/`, `warehouse/`).

#### 2. `src/application/` - Tầng Application
- **Vai trò trong Clean Architecture**: Tầng này chứa **use cases**, điều phối logic kinh doanh bằng cách kết nối `domain/` (entities, rules, repositories) với tầng Infrastructure (thông qua abstractions). Tầng Application chỉ phụ thuộc vào `domain/`, không được import DTO/View Models từ `infra/`.
- **Mục đích**: Xử lý các kịch bản kinh doanh cụ thể (như tạo đơn, kiểm kho, đồng bộ đơn TMĐT) cho các module ERP, đảm bảo logic kinh doanh được gói gọn và tái sử dụng.
- **Cấu trúc**:
  - **`use-cases/`**:
    - **Mô tả**: Chứa các use case được tổ chức theo feature (`cashier/`, `warehouse/`, `ecommerce/`, `settings/`) và sub-feature (như `cashier-order/`, `inventory-check/`). Mỗi use case là một class TypeScript thực hiện một kịch bản kinh doanh.
    - **Ví dụ**:
      - `cashier/cashier-order/create-order.usecase.ts`: Tạo đơn hàng tại quầy.
        ```typescript
        import { Injectable } from '@angular/core';
        import { Observable } from 'rxjs';
        import { Order } from '../../../domain/entities/order';
        import { OrderRepository } from '../../../domain/repositories/cashier/order.repository';
        import { PricingRules } from '../../../domain/rules/pricing-rules';

        @Injectable({ providedIn: 'root' })
        export class CreateOrderUseCase {
          constructor(private orderRepository: OrderRepository) {}

          execute(items: { productId: string; quantity: number }[]): Observable<Order> {
            const totalPrice = PricingRules.calculateTotalPrice(items);
            const order = new Order(Date.now().toString(), items, totalPrice);
            return this.orderRepository.createOrder(order);
          }
        }
        ```
      - `warehouse/inventory-check/inventory-check.usecase.ts`: Kiểm kho.
      - `ecommerce/sync-orders/sync-order.usecase.ts`: Đồng bộ đơn từ Shopee/Lazada.
      - `settings/settings/get-settings.usecase.ts`: Lấy cài đặt.
    - **Lưu ý cho AI Coding Agent**: 
      - Chỉ import từ `domain/` (entities, rules, repositories).
      - Không import DTO/View Models từ `infra/shared/models/` hoặc `infra/features/`.
      - Use cases chỉ gọi repository abstractions (`OrderRepository`), không gọi implementations (`OrderRepositoryImpl`).
      - Đặt use case trong sub-folder tương ứng với feature/sub-feature (ví dụ, `cashier/cashier-order/`).

#### 3. `src/infra/` - Tầng Infrastructure
- **Vai trò trong Clean Architecture**: Tầng ngoài cùng, chứa **UI components**, **services**, **repository implementations**, **adapters**, và **cấu hình kỹ thuật** (routing, HTTP, DI). Tầng này phụ thuộc vào `domain/` và `application/`, triển khai các abstractions (như repository implementations) và giao tiếp với hệ thống bên ngoài (API, Shopee, Lazada).
- **Chi tiết**:
  - **`config/`**:
    - **Mô tả**: Chứa cấu hình môi trường và service quản lý cấu hình.
    - **Ví dụ**:
      - `environment.ts`: Biến môi trường (API_URL).
      - `config.service.ts`: Đọc cấu hình runtime.
    - **Tổ chức**: File `.ts`, không chứa logic kinh doanh.
  - **`core/`**:
    - **Mô tả**: Chứa services, repository implementations, interceptors, và guards dùng chung.
    - **Sub-folders**:
      - **`services/`**:
        - **Ví dụ**:
          - `http.service.ts`: Gọi API.
          - `shopee-adapter.ts`: Đồng bộ Shopee.
          - `translate-http.loader.ts`: Tải file dịch gộp.
        - **Tổ chức**: File `.ts`, chứa logic kỹ thuật hoặc repository implementations dùng chung.
      - **`interceptors/`**, **`guards/`**: Xử lý HTTP và điều hướng.
  - **`shared/`**:
    - **Mô tả**: Chứa DTO, View Models, components, pipes, và directives dùng chung.
    - **Sub-folders**:
      - **`models/api/`**, **`models/view/`**: DTO/View Models dùng chung.
      - **`components/`**: Standalone components (button, spinner).
      - **`pipes/`**, **`directives/`**, **`styles/`**: Pipes, directives, và styles toàn cục.
  - **`features/`**:
    - **Mô tả**: Chứa các feature (`cashier`, `warehouse`, `ecommerce`, `settings`), với UI components, services, repository implementations cụ thể, DTO/View Models, providers, và resolvers.
    - **Ví dụ**:
      - `cashier/create-order/`:
        - `components/create-order.component.ts`: UI tạo đơn.
        - `services/create-order.service.ts`: Gọi use case.
        - `create-order.provider.ts`: Cung cấp DI.
        - `create-order.routes.ts`: Lazy-load sub-feature.
      - `warehouse/inventory-check/`:
        - `services/inventory-check-repository.impl.ts`: Implementation cụ thể.
    - **Tổ chức**: Sub-folder theo feature/sub-feature, components là standalone.
  - **`i18n/`**:
    - **Mô tả**: Chứa **file dịch gốc** cho ứng dụng, thuộc tầng Infrastructure. Được tổ chức theo feature (`cashier/`, `warehouse/`) và sub-feature (`create-order/`, `sync-orders/`), hỗ trợ gộp thành `public/assets/i18n/en.json`, `public/assets/i18n/vi.json` bằng script `merge-translations.js`.
    - **Mục đích**: Cung cấp bản dịch cho 16 module ERP và 9 ngành hàng, tối ưu hiệu suất (1 request HTTP). File dịch được gộp để tránh nhiều request trong dev/production.
    - **Cấu trúc**:
      ```
      src/infra/i18n/
      ├── global/                          # Bản dịch chung (welcome, error)
      │   ├── en.json
      │   ├── vi.json
      ├── cashier/                         # POS
      │   ├── en.json                     # Bản dịch chung cho POS
      │   ├── vi.json
      │   ├── create-order/               # Sub-feature: Tạo đơn
      │   │   ├── en.json
      │   │   ├── vi.json
      │   ├── order-history/              # Sub-feature: Lịch sử đơn
      │   │   ├── en.json
      │   │   ├── vi.json
      ├── warehouse/                       # WMS
      │   ├── en.json
      │   ├── vi.json
      │   ├── inventory-check/            # Sub-feature: Kiểm kho
      │   │   ├── en.json
      │   │   ├── vi.json
      │   ├── stock-adjustment/           # Sub-feature: Điều chỉnh tồn kho
      │   │   ├── en.json
      │   │   ├── vi.json
      ├── ecommerce/                       # ECOMMERCE
      │   ├── en.json
      │   ├── vi.json
      │   ├── sync-orders/                # Sub-feature: Đồng bộ đơn
      │   │   ├── en.json
      │   │   ├── vi.json
      │   ├── order-management/           # Sub-feature: Quản lý đơn
      │   │   ├── en.json
      │   │   ├── vi.json
      ├── settings/                        # Settings
      │   ├── en.json
      │   ├── vi.json
      ```
    - **Ví dụ nội dung**:
      - `global/en.json`:
        ```json
        {
          "welcome": "Welcome to ERP System",
          "common.error": "An error occurred",
          "common.submit": "Submit"
        }
        ```
      - `cashier/create-order/en.json`:
        ```json
        {
          "title": "Create Order",
          "fashion.size": "Size",
          "food.expiry": "Expiry Date"
        }
        ```
      - `ecommerce/sync-orders/en.json`:
        ```json
        {
          "shopee.success": "Shopee sync successful"
        }
        ```
    - **Tổ chức**:
      - **Thư mục `global/`**: Chứa bản dịch chung, dùng trên toàn ứng dụng (như nút Submit, thông báo lỗi).
      - **Thư mục feature (`cashier/`, `warehouse/`)**: Chứa bản dịch chung cho feature (như tiêu đề module).
      - **Thư mục sub-feature (`create-order/`, `sync-orders/`)**: Chứa bản dịch cụ thể cho sub-feature, bao gồm key dành riêng cho ngành hàng (Thời trang: `fashion.size`, Đồ ăn: `food.expiry`).
      - **Ngôn ngữ**: Mỗi thư mục chứa file `en.json` (tiếng Anh) và `vi.json` (tiếng Việt).
    - **Cách gộp**:
      - Script `merge-translations.js` quét đệ quy `src/infra/i18n/`, gộp tất cả file `en.json` thành `public/assets/i18n/en.json` (tương tự cho `vi.json`).
      - Sử dụng hàm `mergeDeep` để hợp nhất các object JSON, đảm bảo không ghi đè key trùng lặp.
      - File gộp sử dụng key phẳng (flat), không thêm tiền tố (ví dụ: `cashier.create-order.title` không cần tiền tố, giữ nguyên `title`).
    - **Tích hợp**:
      - File gộp (`public/assets/i18n/en.json`) được tải bởi `ngx-translate` thông qua `CustomTranslateHttpLoader`.
      - Trong dev: Script watch (`merge-translations.js --watch`) tự động gộp khi file dịch thay đổi.
      - Trong production: Script gộp một lần khi build.
    - **Lưu ý cho AI Coding Agent**:
      - File dịch là JSON, không chứa logic.
      - Key dịch phải ngắn gọn, mô tả rõ ràng, và hỗ trợ ngành hàng (ví dụ: `fashion.size`, `food.expiry`).
      - Không đặt file dịch ngoài `src/infra/i18n/` hoặc `public/assets/i18n/`.
      - Đảm bảo key không trùng lặp giữa `global/`, feature, và sub-feature (hàm `mergeDeep` xử lý merge an toàn).
  - **`app.component.ts`**, **`app.routes.ts`**, **`main.ts`**:
    - **Mô tả**: Root component, routes chính, và entry point.
    - **Tổ chức**: Không chứa logic kinh doanh, chỉ định nghĩa layout và lazy-load features.


#### 4. `public/` - Tài nguyên tĩnh
- **Vai trò trong Clean Architecture**: Thuộc tầng Infrastructure, chứa tài nguyên tĩnh như hình ảnh, fonts.
- **Mục đích**: Hỗ trợ UI (logo, sản phẩm) và font tùy chỉnh.
- **Cấu trúc**:
  - **`assets/`**:
    - **`images/`**: Hình ảnh (logo.png).
    - **`fonts/`**: Font tùy chỉnh.
  - **Lưu ý cho AI Coding Agent**: Chỉ chứa file tĩnh, không chứa logic. Được tham chiếu trong `styles/` hoặc components.

#### 5. `src/main.ts` - Entry point
- **Vai trò trong Clean Architecture**: Thuộc tầng Infrastructure, khởi động ứng dụng Angular.
- **Mô tả**: Bootstrap `AppComponent`.
- **Lưu ý cho AI Coding Agent**: Không chứa logic kinh doanh, chỉ gọi `bootstrapApplication`.

### 6. `scripts/` - Scripts build-time/watch
- **Vai trò**: Thuộc Infrastructure, chứa scripts xử lý build-time/watch (như gộp file dịch).
- **Chi tiết**:
  - **`merge-translations.js`**:
    - **Mô tả**: Script ESM gộp file dịch từ `src/infra/i18n/` thành `public/assets/i18n/`.
    - **Chức năng**:
      - Dev: Watch thay đổi, gộp ngay lập tức.
      - Production: Gộp một lần khi build.
    - **Tổ chức**: File `.js`, sử dụng ESM (`import`/`export`).

---

### Hướng dẫn cụ thể cho AI Coding Agent

1. **Tuân thủ Clean Architecture**:
   - **Tầng Domain (`domain/`)**
     - Chỉ chứa entities (`entities/`), rules (`rules/`), và repository abstractions (`repositories/`).
     - Không import từ `application/` hoặc `infra/`.
     - Đặt repository abstractions trong `repositories/<feature>/` (như `cashier/order.repository.ts`).
   - **Tầng Application (`application/`)**
     - Chỉ chứa use cases trong `use-cases/<feature>/<sub-feature>/`.
     - Chỉ import từ `domain/` (entities, rules, repositories).
     - Không import DTO/View Models từ `infra/`.
   - **Tầng Infrastructure (`infra/`)**
     - Chứa UI (`features/`), services (`core/services/`, `features/<feature>/<sub-feature>/services/`), repository implementations, DTO/View Models.
     - Repository implementations dùng chung đặt trong `core/services/` (như `product-repository.impl.ts`).
     - Repository implementations cụ thể đặt trong `features/<feature>/<sub-feature>/services/` (như `inventory-check-repository.impl.ts`).
     - Resolvers chỉ gọi use cases từ `application/`.
     - Providers cung cấp abstractions (`OrderRepository`), không phải implementations.

2. **Tìm kiếm file**:
   - **Entities/Value Objects**: `domain/entities/<entity>.ts`.
   - **Business Rules**: `domain/rules/<rule>.ts`.
   - **Repository Abstractions**: `domain/repositories/<feature>/<repository>.repository.ts`.
   - **Use Cases**: `application/use-cases/<feature>/<sub-feature>/<usecase>.usecase.ts`.
   - **Repository Implementations**:
     - Dùng chung: `infra/core/services/<repository>-repository.impl.ts`.
     - Cụ thể: `infra/features/<feature>/<sub-feature>/services/<repository>-repository.impl.ts`.
   - **DTO/View Models**:
     - Dùng chung: `infra/shared/models/api/` hoặc `infra/shared/models/view/`.
     - Cụ thể: `infra/features/<feature>/<sub-feature>/models/api/` hoặc `view/`.
   - **Components/Services**: `infra/features/<feature>/<sub-feature>/components/` hoặc `services/`.

3. **Lazy Loading**:
   - Sử dụng `*.routes.ts` với `loadComponent` hoặc `loadChildren` trong `infra/features/<feature>/<sub-feature>/` và `infra/features/<feature>/`.

4. **Hỗ trợ ngành hàng và TMĐT**:
   - **Ngành hàng**:
     - Value Objects: `domain/entities/size.ts` (Thời trang), `expiration-date.ts` (Đồ ăn).
     - Sub-features: Tạo `fashion-store/` hoặc `food-store/` trong `infra/features/cashier/` nếu cần.
   - **TMĐT**:
     - Adapters: `infra/core/services/shopee-adapter.ts`, `lazada-adapter.ts`.
     - DTO: `infra/features/ecommerce/sync-orders/models/api/shopee-order.dto.ts`.

5. **Tích hợp i18n**:
   - File dịch gốc: `src/infra/i18n/<feature>/<sub-feature>/<lang>.json`.
   - File gộp: `public/assets/i18n/<lang>.json`.
   - Script: `scripts/merge-translations.js` (ESM).
   - Loader: `infra/core/services/translate-http.loader.ts`.
   - Tải file gộp bằng `ngx-translate` với **1 request HTTP**.

---

### Kết luận
Cấu trúc trên **tuân thủ chặt chẽ Clean Architecture**, với:
- **Tách biệt tầng**: `domain/` (độc lập), `application/` (use cases), `infra/` (UI, services).
- **Dependency Rule**: Tầng trong không phụ thuộc tầng ngoài.
- **Tổ chức feature-based**: Repository abstractions và use cases theo feature/sub-feature.
- **Hiệu suất**: Lazy loading, standalone components.
- **Hỗ trợ ERP**: Ngành hàng (Thời trang, Đồ ăn) và TMĐT (Shopee, Lazada) được tích hợp tốt.
