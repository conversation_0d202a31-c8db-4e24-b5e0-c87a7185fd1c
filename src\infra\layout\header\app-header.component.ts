
import { Component, ViewEncapsulation, ViewChild, ChangeDetectionStrategy, signal, TemplateRef, ViewContainerRef, ElementRef, AfterViewInit, OnDestroy, WritableSignal } from '@angular/core';
import { Router, RouterLink, RouterLinkActive, RoutesRecognized } from '@angular/router';
import { MatMenuModule } from '@angular/material/menu';
import { CdkMenu, CdkMenuItem, CdkMenuTrigger } from '@angular/cdk/menu';
import { Overlay, OverlayRef, OverlayModule } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';

import { animate, state, style, transition, trigger } from '@angular/animations';
import { NotificationComponent } from '../notification/notification.component';
import { AppNavigationDrawerService } from '../navigation/navigation-drawer/app-navigation-drawer.service';
import { SubNavigationComponent } from '../navigation/sub-navigation/sub-navigation.component';
import { UserMenuComponent } from '../user-menu/user-menu.component';
import { MatRippleModule } from '@angular/material/core';
import { ViewportService } from '@core/services/viewport.service';
import { CommonModule } from '@angular/common';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { AppHeaderService } from './app-header.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    NotificationComponent,
    UserMenuComponent,
    CdkMenu,
    CdkMenuTrigger,
    SubNavigationComponent,
    MatRippleModule,
    CommonModule
  ],
  templateUrl: './app-header.component.html',
  styleUrls: ['./app-header.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppHeaderComponent implements AfterViewInit, OnDestroy {
  @ViewChild('header') headerRef!: ElementRef;
  @ViewChild('subNavMobile') subNavMobileRef!: ElementRef;

  private lastClickX: number = 0;
  private lastClickY: number = 0;
  private observer!: IntersectionObserver;


  viewport$!: ViewportService['viewport$'];
  activeHeaderMenu: WritableSignal<{[menu in HeaderMenu]?: boolean}> = signal({});

  constructor(
    private navigationDrawerService: AppNavigationDrawerService,
    private viewportService: ViewportService,
    private headerService: AppHeaderService
  ) {
    this.viewport$ = viewportService.viewport$;

    viewportService.viewport$.subscribe(value => {
      if(!value.isDesktop) {
        this.observeSubnavMobile();
      } else {
        this.unobserveSubnavMobile();
      }
    });
  }

  ngAfterViewInit() {
    this.observeSubnavMobile();
    // setTimeout(() => this.openNavigationDrawer(), 100);
    // this.openBottomSheetMenu('user-menu');
  }

  ngOnDestroy() {
    this.unobserveSubnavMobile();
  }

  observeSubnavMobile() {
    if(this.headerRef?.nativeElement && this.subNavMobileRef?.nativeElement && !this.observer) {
      this.observer = new IntersectionObserver(
        ([entry]) => {
          const subNav = this.subNavMobileRef?.nativeElement;
          const className = 'app-sub-nav-fixed';
          if(subNav) {
            if (!entry.isIntersecting) {
              document.body.classList.add(className);
              // console.log('add class fixed')
            } else {
              document.body.classList.remove(className);
              // console.log('remove class fixed')
            }
          }
        },
        {
          threshold: 0
        }
      );

      this.observer.observe(this.headerRef.nativeElement);
    }
  }
  unobserveSubnavMobile() {
    this.observer?.disconnect();
    // @ts-ignore
    delete this.observer;
  }

  openNavigationDrawer() {
    this.navigationDrawerService.openDrawer();
  }

  onCdkMenuTriggerClick(event: MouseEvent, ) {
    // Lưu tọa độ click dựa trên viewport
    this.lastClickX = event.clientX;
    this.lastClickY = event.clientY;
  }

  onCdkMenuOpened(menu: HeaderMenu) {
    this.activeHeaderMenu.update(m => {
      // close thì xử lý hàm bên dưới rồi
      if(!m[menu]) {
        m[menu] = true;
      }
      return m;
    });

    // Dùng timeout nhỏ để đảm bảo overlay pane đã render
    setTimeout(() => {
      const overlayPane = this.getOverlayPane();
      if (overlayPane) {
        // Lấy vị trí thực tế của overlay pane
        const rect = overlayPane.getBoundingClientRect();

        // Tính transform-origin dựa trên tọa độ click so với overlay pane
        const originX = this.lastClickX - rect.left;
        const originY = this.lastClickY - rect.top;
        // Áp dụng transform-origin
        overlayPane.style.transformOrigin = `${originX}px ${originY}px`;
        overlayPane.classList.remove('closing');
      }
    }, 0); // Timeout 0ms để chạy sau khi DOM cập nhật
  }

  onCdkMenuClosed() {
    this.activeHeaderMenu.update(m => {
      Object.keys(m).forEach((key: string) => {
        m[key as HeaderMenu] = false;
      });
      return m;
    });
  }

  openBottomSheetMenu(menu: HeaderMenu) {
    this.headerService.openBottomSheetMenu(menu);
  }

  private getOverlayPane(): HTMLElement | null {
    return document.querySelector('.cdk-overlay-pane');
  }
}

type HeaderMenu = 'notification' | 'user-menu';
