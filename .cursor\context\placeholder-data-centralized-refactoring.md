# Placeholder Data Centralized Refactoring Task

## Tổng quan
Refactor placeholder data system để sử dụng centralized constant mapping approach, giảm code duplication và tăng maintainability.

## <PERSON><PERSON><PERSON> bước thực hiện

### 1. Tạo FIELD_PLACEHOLDER_DATA constant ✅
- **Location**: `src/infra/shared/components/dynamic-layout-builder/constants/field-types.const.ts`
- **Structure**: `{ [key in FieldType]: string }` type mapping
- **Content**: i18n translation keys cho từng field type
- **Example**:
  ```typescript
  export const FIELD_PLACEHOLDER_DATA: { [key in FieldType]: string } = {
    'text': 'DYNAMIC_LAYOUT_RENDERER.PLACEHOLDER_DATA.TEXT',
    'number': 'DYNAMIC_LAYOUT_RENDERER.PLACEHOLDER_DATA.NUMBER',
    'checkbox': 'DYNAMIC_LAYOUT_RENDERER.PLACEHOLDER_DATA.BOOLEAN',
    // ... for all field types
  };
  ```

### 2. Simplify generatePlaceHolderData function ✅
- **Location**: `src/infra/shared/components/dynamic-layout-builder/utils/field.utils.ts`
- **Changes**:
  - Import FIELD_PLACEHOLDER_DATA constant
  - Sử dụng mapping thay vì complex switch statement
  - Thêm getSimpleFallbackData() function cho fallback logic
  - Xóa complex logic trong generatePlaceHolderData()

### 3. Remove redundant functions ✅
- ✅ Deleted `generateArrayPlaceHolderData()` function
- ✅ Deleted `getFallbackPlaceHolderData()` function
- ✅ Updated all field components to use only `generatePlaceHolderData()`

### 4. Update field components ✅
- **select-field.component.ts**: Handle array generation within component
  - Generate base placeholder và tạo array với suffix " 1", " 2"
- **checkbox-field.component.ts**: Handle boolean logic within component
  - Hiển thị translated "Yes"/"Có" text trực tiếp

### 5. Maintain i18n support ✅
- ✅ Keep existing PLACEHOLDER_DATA section in vi.json and en.json
- ✅ All field types have corresponding translation keys
- ✅ Fallback logic works when translation missing

## Kết quả kiểm tra
- ✅ **ng build**: Thành công, không có lỗi compilation
- ✅ **Ứng dụng**: Hoạt động bình thường tại http://localhost:4200
- ✅ **Placeholder data**: Hiển thị đúng với i18n support

## Lợi ích của refactoring
1. **Centralized mapping**: Single source of truth cho placeholder data keys
2. **Reduced code duplication**: Xóa redundant functions
3. **Better maintainability**: Dễ dàng thêm/sửa field types mới
4. **Cleaner architecture**: Logic đơn giản hơn, dễ hiểu hơn
5. **Type safety**: Sử dụng TypeScript mapping với FieldType

## Status: HOÀN THÀNH ✅
