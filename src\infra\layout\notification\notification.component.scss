@use '../../shared/styles/_variable' as *;

$notificationHeaderHeight: 60;

.notification-block {
  height: 100%;
  padding-top: $notificationHeaderHeight+px;
}
.notification-block-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: $notificationHeaderHeight+px;
  z-index: 9;
}
.notification-block-scrollable {
  height: 100%;
  overflow-y: scroll;
  scrollbar-width: thin;
}

@media screen and (min-width: #{$breakpointMinHorizontalWidth}px) {
  .notification-block {
    height: 500px;
  }
}
