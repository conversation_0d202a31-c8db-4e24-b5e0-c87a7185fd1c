import { signal, computed } from '@angular/core';
import { Subject } from 'rxjs';
import { Field, FieldValue, FieldConstraints, FieldOption } from '@domain/entities/field.entity';
import { DynamicLayoutRendererFormData, FormDataManagementServiceInterface } from '../models/dynamic-layout-renderer.model';

/**
 * Interface định nghĩa validation result cho một field
 */
export interface FieldValidationResult {
  fieldId: string;
  isValid: boolean;
  errors: string[]; // Array of i18n keys for error messages
}

/**
 * Interface định nghĩa form state
 */
export interface FormState {
  isDirty: boolean; // Form đã được modify
  isValid: boolean; // Tất cả fields đều valid
  isLoading: boolean; // Đang trong quá trình save
  fieldValues: Record<string, FieldValue>; // Current field values
  fieldValidations: Record<string, FieldValidationResult>; // Validation results
}

/**
 * Interface định nghĩa field change event
 */
export interface FieldChangeEvent {
  fieldId: string;
  value: FieldValue;
  field: Field;
}

// ===== FIELD CONSTRAINTS TYPE GUARDS =====

/**
 * Type guard để kiểm tra text field constraints
 */
interface TextFieldConstraints {
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  maxDigits?: number; // For phone fields
}

/**
 * Type guard để kiểm tra number field constraints
 */
interface NumberFieldConstraints {
  maxDigits?: number;
  min?: number;
  max?: number;
  step?: number;
}

/**
 * Type guard để kiểm tra picklist field constraints
 */
interface PicklistFieldConstraints {
  picklistOptions?: FieldOption[];
  options?: FieldOption[]; // Alias for picklistOptions
  sortOrder?: 'input' | 'alphabetical';
  defaultValue?: string | string[];
}

/**
 * Service quản lý form state, validation, và data collection cho từng DynamicLayoutRendererComponent instance
 *
 * QUAN TRỌNG: Service này được thiết kế để sử dụng riêng biệt cho từng component instance,
 * KHÔNG được chia sẻ giữa các component khác nhau.
 *
 * Cách sử dụng:
 * - Mỗi DynamicLayoutRendererComponent tạo instance riêng bằng `new FormDataManagementService()`
 * - Service tự động cleanup khi component bị destroy thông qua DestroyRef
 * - Hỗ trợ multi-instance: nhiều DynamicLayoutRendererComponent có thể hoạt động độc lập
 *
 * Features:
 * - Track form dirty state per instance
 * - Validate fields theo constraints
 * - Collect form data để save
 * - Manage loading state
 * - Field change notifications
 * - Automatic cleanup để tránh memory leaks
 */
export class FormDataManagementService implements FormDataManagementServiceInterface {

  // ===== INSTANCE PROPERTIES =====

  // Instance ID để debug và tracking
  private readonly instanceId = `FormDataService_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  // Private signals cho internal state
  private _isDirty = signal(false);
  private _isValid = signal(true);
  private _isLoading = signal(false);
  private _fieldValues = signal<Record<string, FieldValue>>({});
  private _fieldValidations = signal<Record<string, FieldValidationResult>>({});

  // Field change notifications
  private _fieldChangeSubject = new Subject<FieldChangeEvent>();

  // Cleanup flag
  private _isDestroyed = false;

  // ===== PUBLIC READONLY SIGNALS =====

  // Public computed signals
  readonly isDirty = this._isDirty.asReadonly();
  readonly isValid = this._isValid.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  readonly fieldValues = this._fieldValues.asReadonly();
  readonly fieldValidations = this._fieldValidations.asReadonly();

  // Computed signal cho form state tổng hợp
  readonly formState = computed<FormState>(() => ({
    isDirty: this._isDirty(),
    isValid: this._isValid(),
    isLoading: this._isLoading(),
    fieldValues: this._fieldValues(),
    fieldValidations: this._fieldValidations()
  }));

  // Observable cho field change events
  readonly fieldChanges$ = this._fieldChangeSubject.asObservable();

  // ===== CONSTRUCTOR =====

  /**
   * Constructor - Khởi tạo service instance riêng biệt
   * KHÔNG sử dụng Angular DI, được tạo trực tiếp bởi DynamicLayoutRendererComponent
   */
  constructor() {
    console.log(`🔧 FormDataManagementService created: ${this.instanceId}`);
  }

  // ===== LIFECYCLE MANAGEMENT =====

  /**
   * Cleanup service khi component bị destroy
   * Phải được gọi từ DynamicLayoutRendererComponent.ngOnDestroy()
   */
  destroy(): void {
    if (this._isDestroyed) {
      return;
    }

    console.log(`🧹 FormDataManagementService destroyed: ${this.instanceId}`);

    // Complete và cleanup subjects
    this._fieldChangeSubject.complete();

    // Reset tất cả signals
    this._isDirty.set(false);
    this._isValid.set(true);
    this._isLoading.set(false);
    this._fieldValues.set({});
    this._fieldValidations.set({});

    // Mark as destroyed
    this._isDestroyed = true;
  }

  /**
   * Kiểm tra xem service đã bị destroy chưa
   */
  isDestroyed(): boolean {
    return this._isDestroyed;
  }

  // ===== PUBLIC API METHODS =====

  /**
   * Khởi tạo form với initial values
   * @param initialValues - Dữ liệu ban đầu từ DynamicLayoutRendererConfig.formValues
   * @param fields - Array of fields để validate initial values
   */
  initializeForm(initialValues: Record<string, FieldValue> = {}, fields: Field[] = []): void {
    if (this._isDestroyed) {
      console.warn(`⚠️ Attempted to initialize destroyed FormDataManagementService: ${this.instanceId}`);
      return;
    }

    console.log(`🔄 Initializing form: ${this.instanceId}`, { initialValues, fieldsCount: fields.length });

    this._fieldValues.set({ ...initialValues });
    this._isDirty.set(false);
    this._fieldValidations.set({});
    this._isLoading.set(false);

    // Validate tất cả fields với initial values
    this.validateAllFields(fields, initialValues);
  }

  /**
   * Cập nhật giá trị của một field
   * @param fieldId - ID của field (field._id)
   * @param value - Giá trị mới
   * @param field - Field object để validate
   */
  updateFieldValue(fieldId: string, value: FieldValue, field: Field): void {
    if (this._isDestroyed) {
      console.warn(`⚠️ Attempted to update field in destroyed FormDataManagementService: ${this.instanceId}`);
      return;
    }

    console.log(`📝 Updating field value: ${this.instanceId}`, { fieldId, value, fieldType: field.type });

    // Cập nhật field value
    const currentValues = this._fieldValues();
    const newValues = { ...currentValues, [fieldId]: value };
    this._fieldValues.set(newValues);

    // Mark form as dirty
    this._isDirty.set(true);

    // Validate field
    const validationResult = this.validateField(field, value);
    const currentValidations = this._fieldValidations();
    const newValidations = { ...currentValidations, [fieldId]: validationResult };
    this._fieldValidations.set(newValidations);

    // Update overall form validity
    this.updateFormValidity();

    // Emit field change event
    this._fieldChangeSubject.next({
      fieldId,
      value,
      field
    });
  }

  /**
   * Lấy giá trị của một field cụ thể
   * @param fieldId - ID của field
   * @returns Giá trị hiện tại của field
   */
  getFieldValue(fieldId: string): FieldValue {
    if (this._isDestroyed) {
      return null;
    }

    return this._fieldValues()[fieldId] || null;
  }

  /**
   * Kiểm tra xem form có field values nào không
   * @returns true nếu form có ít nhất 1 field value
   */
  hasFieldValues(): boolean {
    if (this._isDestroyed) {
      return false;
    }

    return Object.keys(this._fieldValues()).length > 0;
  }

  /**
   * Validate một field dựa trên constraints
   * @param field - Field object chứa constraints
   * @param value - Giá trị cần validate
   * @returns FieldValidationResult
   */
  private validateField(field: Field, value: FieldValue): FieldValidationResult {
    const errors: string[] = [];
    const fieldId = field._id || '';

    // Check required
    if ((field.isRequired || field.required) && this.isEmpty(value)) {
      errors.push('FORM_VALIDATION.FIELD_REQUIRED');
    }

    // Skip other validations if value is empty and not required
    if (this.isEmpty(value) && !(field.isRequired || field.required)) {
      return { fieldId, isValid: true, errors: [] };
    }

    // Type-specific validations
    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
        this.validateTextField(field, value as string, errors);
        break;
      case 'textarea':
        this.validateTextareaField(field, value as string, errors);
        break;
      case 'number':
      case 'decimal':
      case 'currency':
      case 'percent':
        this.validateNumberField(field, value as number, errors);
        break;
      case 'picklist':
        this.validatePicklistField(field, value as string, errors);
        break;
      case 'multi-picklist':
        this.validateMultiPicklistField(field, value as string[], errors);
        break;
      case 'file':
      case 'image':
        this.validateFileField(field, value, errors);
        break;
      // Add more field type validations as needed
    }

    return {
      fieldId,
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate text field (text, email, phone, url)
   */
  private validateTextField(field: Field, value: string, errors: string[]): void {
    if (!value) return;

    const constraints = field.constraints as TextFieldConstraints;
    if (!constraints) return;

    // Max length validation
    if (constraints.maxLength && value.length > constraints.maxLength) {
      errors.push('FORM_VALIDATION.TEXT_TOO_LONG');
    }

    // Email format validation
    if (field.type === 'email' && !this.isValidEmail(value)) {
      errors.push('FORM_VALIDATION.INVALID_EMAIL');
    }

    // Phone format validation
    if (field.type === 'phone' && constraints.maxDigits) {
      const digits = value.replace(/\D/g, '');
      if (digits.length > constraints.maxDigits) {
        errors.push('FORM_VALIDATION.PHONE_TOO_LONG');
      }
    }

    // URL format validation
    if (field.type === 'url' && !this.isValidUrl(value)) {
      errors.push('FORM_VALIDATION.INVALID_URL');
    }
  }

  /**
   * Validate textarea field
   */
  private validateTextareaField(field: Field, value: string, errors: string[]): void {
    if (!value) return;

    const constraints = field.constraints as TextFieldConstraints;
    if (!constraints) return;

    if (constraints.maxLength && value.length > constraints.maxLength) {
      errors.push('FORM_VALIDATION.TEXT_TOO_LONG');
    }
  }

  /**
   * Validate number field (number, decimal, currency, percent)
   */
  private validateNumberField(field: Field, value: number, errors: string[]): void {
    if (value === null || value === undefined) return;

    const constraints = field.constraints as NumberFieldConstraints;
    if (!constraints) return;

    // Max digits validation
    if (constraints.maxDigits) {
      const digits = Math.abs(value).toString().replace('.', '').length;
      if (digits > constraints.maxDigits) {
        errors.push('FORM_VALIDATION.NUMBER_TOO_LONG');
      }
    }
  }

  /**
   * Validate picklist field
   */
  private validatePicklistField(field: Field, value: string, errors: string[]): void {
    if (!value) return;

    const constraints = field.constraints as PicklistFieldConstraints;
    if (!constraints || !constraints.options) return;

    // Lấy danh sách các giá trị hợp lệ từ options
    const validValues = constraints.options.map((option: FieldOption) => option.value);
    if (!validValues.includes(value)) {
      errors.push('FORM_VALIDATION.INVALID_PICKLIST_VALUE');
    }
  }

  /**
   * Validate multi-picklist field
   */
  private validateMultiPicklistField(field: Field, value: string[], errors: string[]): void {
    if (!value || !Array.isArray(value)) return;

    const constraints = field.constraints as PicklistFieldConstraints;
    if (!constraints || !constraints.options) return;

    // Lấy danh sách các giá trị hợp lệ từ options
    const validValues = constraints.options.map((option: FieldOption) => option.value);
    const invalidValues = value.filter(v => !validValues.includes(v));
    if (invalidValues.length > 0) {
      errors.push('FORM_VALIDATION.INVALID_PICKLIST_VALUE');
    }
  }

  /**
   * Validate file field
   */
  private validateFileField(_field: Field, _value: FieldValue, _errors: string[]): void {
    // File validation logic - placeholder for now
    // Will be implemented based on file upload requirements
    // Parameters prefixed with _ to indicate they are intentionally unused
  }

  /**
   * Helper methods
   */
  private isEmpty(value: FieldValue): boolean {
    return value === null || value === undefined || value === '' || 
           (Array.isArray(value) && value.length === 0);
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate tất cả fields với current values
   * @param fields - Array of fields để validate
   * @param values - Current field values (optional, sử dụng current state nếu không có)
   */
  private validateAllFields(fields: Field[], values?: Record<string, FieldValue>): void {
    const currentValues = values || this._fieldValues();
    const validations: Record<string, FieldValidationResult> = {};

    // Validate từng field
    fields.forEach(field => {
      const fieldId = field._id || '';
      const fieldValue = currentValues[fieldId];
      const validationResult = this.validateField(field, fieldValue);
      validations[fieldId] = validationResult;
    });

    // Update validations
    this._fieldValidations.set(validations);

    // Update overall form validity
    this.updateFormValidity();
  }

  /**
   * Cập nhật overall form validity dựa trên tất cả field validations
   */
  private updateFormValidity(): void {
    const validations = this._fieldValidations();
    const isValid = Object.values(validations).every(v => v.isValid);
    this._isValid.set(isValid);
  }

  /**
   * Set loading state
   */
  setLoading(loading: boolean): void {
    if (this._isDestroyed) {
      console.warn(`⚠️ Attempted to set loading state in destroyed FormDataManagementService: ${this.instanceId}`);
      return;
    }

    console.log(`⏳ Setting loading state: ${this.instanceId}`, { loading });
    this._isLoading.set(loading);
  }

  /**
   * Reset form về trạng thái ban đầu (không dirty, không có validation errors)
   * Giữ nguyên field values
   */
  resetForm(): void {
    if (this._isDestroyed) {
      console.warn(`⚠️ Attempted to reset destroyed FormDataManagementService: ${this.instanceId}`);
      return;
    }

    console.log(`🔄 Resetting form: ${this.instanceId}`);

    this._isDirty.set(false);
    this._fieldValidations.set({});
    this.updateFormValidity();
  }

  /**
   * Clear tất cả form data và reset về trạng thái ban đầu
   */
  clearForm(): void {
    if (this._isDestroyed) {
      console.warn(`⚠️ Attempted to clear destroyed FormDataManagementService: ${this.instanceId}`);
      return;
    }

    console.log(`🧹 Clearing form: ${this.instanceId}`);

    this._fieldValues.set({});
    this._isDirty.set(false);
    this._fieldValidations.set({});
    this._isLoading.set(false);
    this.updateFormValidity();
  }

  /**
   * Build DynamicLayoutRendererFormData từ current form state
   * @param formId - Optional form ID
   * @returns DynamicLayoutRendererFormData
   */
  buildFormData(formId?: string): DynamicLayoutRendererFormData {
    if (this._isDestroyed) {
      console.warn(`⚠️ Attempted to build form data from destroyed FormDataManagementService: ${this.instanceId}`);
      return { formId, values: [] };
    }

    const fieldValues = this._fieldValues();
    const values = Object.entries(fieldValues).map(([fieldId, value]) => ({
      fieldId,
      value
    }));

    console.log(`📦 Building form data: ${this.instanceId}`, { formId, valuesCount: values.length });

    return {
      formId,
      values
    };
  }

  /**
   * Get validation errors cho một field cụ thể
   * @param fieldId - ID của field
   * @returns Array of error i18n keys
   */
  getFieldErrors(fieldId: string): string[] {
    if (this._isDestroyed) {
      return [];
    }

    const validation = this._fieldValidations()[fieldId];
    return validation ? validation.errors : [];
  }

  /**
   * Check if field có errors
   * @param fieldId - ID của field
   * @returns true nếu field có errors
   */
  hasFieldErrors(fieldId: string): boolean {
    if (this._isDestroyed) {
      return false;
    }

    const validation = this._fieldValidations()[fieldId];
    return validation ? !validation.isValid : false;
  }

  /**
   * Get validation result cho một field cụ thể
   * @param fieldId - ID của field
   * @returns FieldValidationResult hoặc null nếu không tìm thấy
   */
  getFieldValidation(fieldId: string): FieldValidationResult | null {
    if (this._isDestroyed) {
      return null;
    }

    return this._fieldValidations()[fieldId] || null;
  }

  /**
   * Get instance ID cho debugging
   * @returns Instance ID string
   */
  getInstanceId(): string {
    return this.instanceId;
  }

  /**
   * Get form statistics cho debugging
   * @returns Object chứa thống kê form
   */
  getFormStats(): {
    instanceId: string;
    fieldsCount: number;
    validFieldsCount: number;
    invalidFieldsCount: number;
    isDirty: boolean;
    isValid: boolean;
    isLoading: boolean;
    isDestroyed: boolean;
  } {
    if (this._isDestroyed) {
      return {
        instanceId: this.instanceId,
        fieldsCount: 0,
        validFieldsCount: 0,
        invalidFieldsCount: 0,
        isDirty: false,
        isValid: false,
        isLoading: false,
        isDestroyed: true
      };
    }

    const validations = this._fieldValidations();
    const validFields = Object.values(validations).filter(v => v.isValid);
    const invalidFields = Object.values(validations).filter(v => !v.isValid);

    return {
      instanceId: this.instanceId,
      fieldsCount: Object.keys(this._fieldValues()).length,
      validFieldsCount: validFields.length,
      invalidFieldsCount: invalidFields.length,
      isDirty: this._isDirty(),
      isValid: this._isValid(),
      isLoading: this._isLoading(),
      isDestroyed: this._isDestroyed
    };
  }
}
