{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.0", "scripts": {"merge-translations": "node scripts/merge-translations.js", "merge-translations:watch": "node scripts/merge-translations.js --watch", "ng": "ng", "start": "concurrently \"npm run merge-translations:watch\" \"ng serve\"", "build": "ng build  --deploy-url / --base-href /", "watch": "npm run merge-translations && ng build --watch --configuration development", "test": "ng test", "eslint": "eslint \"src/**/*\"", "dev": "node scripts/dev.js", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd", "module-translation-validator": "node scripts/translation-validator/module-final-check/index.js"}, "bin": {"compare-component-translation-keys": "./scripts/translation-validator/bin/bin-compare-component-translation-keys.js", "compare-translations": "./scripts/translation-validator/bin/bin-compare-translations.js", "find-unused-translations": "./scripts/translation-validator/bin/bin-find-unused-translations.js", "export-component-html-translation-keys": "./scripts/translation-validator/bin/bin-export-component-html-translation-keys.js"}, "private": true, "dependencies": {"@angular/animations": "^20.1.2", "@angular/cdk": "^20.1.2", "@angular/common": "^20.1.2", "@angular/compiler": "^20.1.2", "@angular/core": "^20.1.2", "@angular/fire": "^20.0.1", "@angular/forms": "^20.1.2", "@angular/material": "^20.1.2", "@angular/material-moment-adapter": "^20.1.2", "@angular/platform-browser": "^20.1.2", "@angular/platform-browser-dynamic": "^20.1.2", "@angular/router": "^20.1.2", "@angular/service-worker": "^20.1.2", "@anthropic-ai/sdk": "^0.57.0", "@cds/core": "^6.16.0", "@clr/angular": "^17.10.0", "@clr/ui": "^17.10.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@primeng/themes": "^20.0.0", "bootstrap": "^5.3.7", "chokidar": "^4.0.3", "embla-carousel-angular": "^20.0.0", "glob-promise": "^6.0.7", "html2canvas": "^1.4.1", "ngx-mask": "^19.0.7", "ngx-mat-select-search": "^8.0.2", "ngx-sonner": "^3.1.0", "ngx-toastr": "^19.0.0", "primeng": "^20.0.0", "rxjs": "~7.8.2", "salehub_shared_contracts": "git+https://oauth2:<EMAIL>/salehub/salehub_shared_variables.git", "socket.io-client": "^4.8.1", "swiper": "^11.2.10", "zone.js": "~0.15.1"}, "devDependencies": {"@angular/build": "^20.1.1", "@angular/cli": "^20.1.1", "@angular/compiler-cli": "^20.1.2", "@ngx-i18nsupport/ngx-i18nsupport": "^1.1.6", "@types/jasmine": "~5.1.8", "concurrently": "^9.2.0", "jasmine-core": "~5.9.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "nodex-eslint-rules": "git+https://oauth2:<EMAIL>/_nodex_/modules/nodex-eslint-rules", "ts-morph": "^26.0.0", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "type": "module"}