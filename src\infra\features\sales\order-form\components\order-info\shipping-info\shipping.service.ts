import { mockDeliveryCompanyList, mockPickupAddressList } from '@mock/shared/list.mock';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { OrderDelivery } from 'salehub_shared_contracts/entities/oms/order/order_components/order_delivery';

/**
 * Service xử lý thông tin vận chuyển
 */
@Injectable({
  providedIn: 'root'
})
export class ShippingService {
  /**
   * Lấy danh sách địa chỉ lấy hàng
   * @returns Danh sách địa chỉ lấy hàng
   */
  getPickupAddresses(): any[] {
    return mockPickupAddressList;
  }

  /**
   * Lấy danh sách đơn vị vận chuyển
   * @returns Observable danh sách đơn vị vận chuyển
   */
  getDeliveryCompanies(): Observable<any[]> {
    return of(mockDeliveryCompanyList);
  }

  /**
   * Cập nhật thông tin vận chuyển vào đơn hàng
   * @param order Đơn hàng cần cập nhật
   * @param deliveryInfo Thông tin vận chuyển
   */
  updateDeliveryInfo(order: any, deliveryInfo: OrderDelivery): void {
    order.delivery = deliveryInfo;
  }

  /**
   * Tính phí vận chuyển dựa trên thông tin giao hàng
   * @param deliveryInfo Thông tin giao hàng
   * @returns Phí vận chuyển
   */
  calculateShippingFee(deliveryInfo: any): number {
    const baseFee = 15000; // Phí cơ bản
    let distanceFee = 0;

    // Tính phí theo khoảng cách
    if (deliveryInfo.address?.distance?.distance) {
      const distance = deliveryInfo.address.distance.distance / 1000; // Chuyển đổi sang km
      if (distance <= 2) {
        distanceFee = 0; // Miễn phí dưới 2km
      } else if (distance <= 5) {
        distanceFee = 5000; // 5.000đ từ 2-5km
      } else if (distance <= 10) {
        distanceFee = 10000; // 10.000đ từ 5-10km
      } else {
        distanceFee = 10000 + Math.ceil((distance - 10) / 5) * 5000; // Mỗi 5km thêm sẽ tính thêm 5.000đ
      }
    }

    // Tính phí theo trọng lượng
    let weightFee = 0;
    if (deliveryInfo.weight) {
      if (deliveryInfo.weight > 1000) {
        weightFee = Math.ceil(deliveryInfo.weight / 1000) * 5000; // Mỗi kg tính 5.000đ
      }
    }

    // Tính phí theo kích thước
    let dimensionFee = 0;
    if (deliveryInfo.dimensions) {
      const { height, length, width } = deliveryInfo.dimensions;
      const volume = (height * length * width) / 1000000; // Tính thể tích (m3)
      if (volume > 0.1) {
        dimensionFee = Math.ceil(volume * 10) * 10000; // Mỗi 0.1m3 tính 10.000đ
      }
    }

    // Tổng phí = phí cơ bản + phí khoảng cách + phí trọng lượng + phí kích thước
    return baseFee + distanceFee + weightFee + dimensionFee;
  }
}
