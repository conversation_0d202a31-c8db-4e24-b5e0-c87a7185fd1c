import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { GoodsReceipt } from '@domain/entities/goods-receipt.entity';
import { GoodsReceiptRepository } from '@domain/repositories/warehouse/goods-receipt.repository';

/**
 * Use case xử lý logic nghiệp vụ cho Goods Receipt
 * Tuân thủ nguyên tắc Clean Architecture
 */
@Injectable()
export class GoodsReceiptUseCase {
  constructor(private goodsReceiptRepository: GoodsReceiptRepository) {}

  /**
   * Lấy thông tin phiếu nhập kho theo ID
   * @param id ID của phiếu nhập kho
   */
  getGoodsReceipt(id: string): Observable<GoodsReceipt | null> {
    return this.goodsReceiptRepository.getGoodsReceipt(id);
  }

  /**
   * Tạo mới phiếu nhập kho
   * @param goodsReceipt Thông tin phiếu nhập kho
   */
  createGoodsReceipt(goodsReceipt: GoodsReceipt): Observable<GoodsReceipt> {
    return this.goodsReceiptRepository.createGoodsReceipt(goodsReceipt);
  }

  /**
   * Cập nhật phiếu nhập kho
   * @param goodsReceipt Thông tin phiếu nhập kho cần cập nhật
   */
  updateGoodsReceipt(goodsReceipt: GoodsReceipt): Observable<GoodsReceipt> {
    return this.goodsReceiptRepository.updateGoodsReceipt(goodsReceipt);
  }

  /**
   * Tính toán tổng tiền hàng
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateSubTotal(goodsReceipt: GoodsReceipt): number {
    return this.goodsReceiptRepository.calculateSubTotal(goodsReceipt);
  }

  /**
   * Tính toán tổng số tiền cần thanh toán
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateTotalPayment(goodsReceipt: GoodsReceipt): number {
    return this.goodsReceiptRepository.calculateTotalPayment(goodsReceipt);
  }

  /**
   * Tính toán công nợ
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateDebt(goodsReceipt: GoodsReceipt): number {
    return this.goodsReceiptRepository.calculateDebt(goodsReceipt);
  }

  /**
   * Tính toán tổng chi phí
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateTotalAdditionalCost(goodsReceipt: GoodsReceipt): number {
    return this.goodsReceiptRepository.calculateTotalAdditionalCost(goodsReceipt);
  }

  /**
   * Tính toán tổng thuế
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateTotalTax(goodsReceipt: GoodsReceipt): number {
    return this.goodsReceiptRepository.calculateTotalTax(goodsReceipt);
  }
}
