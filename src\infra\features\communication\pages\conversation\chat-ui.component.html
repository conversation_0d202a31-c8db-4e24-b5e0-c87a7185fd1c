<div class="app-main-box">
  <div class="chat-container flex flex-col lg:flex-row h-full">
    <!-- Left Filter Column -->
    <div class="filter-sidebar flex-shrink-0 w-[60px] border-r border-gray-200 dark:border-gray-700 flex flex-col items-center py-4 bg-gray-100 dark:bg-gray-800">
      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn từ: Chat"
        >
        <i class="fa-solid fa-message-smile fs-4"></i>
      </button>
      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn từ: B<PERSON>nh luận"
        >
        <i class="fa-solid fa-comments fs-4"></i>
      </button>
      
      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn từ: SMS"
        >
        <i class="fa-light fa-message-sms fs-4"></i>
      </button>

      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="<PERSON>ọc tin nhắn từ: Đ<PERSON><PERSON> giá"
        >
        <i class="fa-light fa-star-exclamation fs-4"></i>
      </button>

      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn: Chưa đọc"
        >
        <i class="fa-light fa-eye-slash fs-4"></i>
      </button>

       
      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn theo: Kênh bán hàng"
        >
        <i class="fa-light fa-cabinet-filing fs-4"></i>
      </button>

      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn theo: Thẻ hội thoại"
        >
        <i class="fa-light fa-tags fs-4"></i>
      </button>


      
      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn có: Số điện thoại"
        >
        <i class="fa-light fa-mobile fs-4"></i>
      </button>

      
      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn có: Đơn hàng"
        >
        <i class="fa-light fa-cart-circle-check fs-4"></i>
      </button>

      
      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn theo: Nhân viên được chỉ định"
        >
        <i class="fa-light fa-users-between-lines fs-4"></i>
      </button>

      
      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn theo: Nguồn khách"
        >
        <i class="fa-light fa-file-contract fs-4"></i>
      </button>

      
      <button 
        class="filter-icon-btn mb-4 p-2"
        matTooltip="Lọc tin nhắn theo: Thời gian"
        >
        <i class="fa-light fa-calendar-range fs-4"></i>
      </button>
    </div>
    
    <!-- Sidebar / Contact List -->
    <div class="chat-sidebar flex-shrink-0 w-full lg:w-[400px] border-r border-gray-200 dark:border-gray-700 flex flex-col">
      <!-- Search Box -->
      <div class="search-box p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="relative">
          <input type="text" placeholder="Search..." class="form-control form-control-solid form-control-sm w-full pl-10">
          <i class="ki-solid ki-search text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2"></i>
        </div>
      </div>

      <!-- Tabs -->
      <div class="chat-tabs flex border-b border-gray-200 dark:border-gray-700">
        <button class="flex-1 py-3 text-center font-medium text-gray-900 dark:text-white border-b-2 border-primary">
          All
        </button>
        <button class="flex-1 py-3 text-center font-medium text-gray-500 dark:text-gray-400">
          Personal
        </button>
        <button class="flex-1 py-3 text-center font-medium text-gray-500 dark:text-gray-400">
          Groups
        </button>
      </div>

      <!-- Contact List -->
      <div class="contacts-list flex-grow overflow-y-auto">
        <div *ngFor="let group of groups" 
            [class.bg-gray-200]="selectedGroup.id === group.id"
            [class.dark:bg-gray-800]="selectedGroup.id === group.id"
            class="contact-item px-3 py-2 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
            (click)="selectGroup(group)">
          <div class="flex items-center">
            <div class="relative mr-3">
              <div 
                *ngIf="group.unreadCount > 0" 
                class="badge badge-sm badge-rounded badge-danger absolute count-unread"
                >
                {{ group.unreadCount }}
              </div>

              <div class="avatar h-10 w-10 rounded overflow-hidden">
                <img [src]="group.avatar" alt="Contact Avatar" class="h-full w-full object-cover">
              </div>
            </div>
            <div class="contact-info flex-grow-1">
              <div class="flex justify-between items-center">
                <div>
                  <h4 
                    class="text-base text-gray-900 dark:text-white trimmed-text"
                    [class.font-semibold]="group.unreadCount > 0"
                    >
                    {{ group.name }}
                  </h4>
                </div>
                <div 
                  class="flex -space-x-2 opacity-90"
                  matTooltip="Tin nhắn trong Zalo: Xôi Mộc"
                  >
                  <div class="avatar h-6 w-6 rounded-full overflow-hidden">
                    <img src="assets/images/socials/zalo.svg" alt="Member" class="h-full w-full object-cover">
                  </div>
                  <div class="avatar h-6 w-6 rounded-full overflow-hidden">
                    <img src="assets/images/avatars/harry-maguire.svg" alt="Member" class="h-full w-full object-cover">
                  </div>
                </div>
              </div>
              <div class="flex justify-between items-center mt-1">
                <p 
                  class="text-sm text-gray-500 dark:text-gray-400 trimmed-text"
                  [class.text-dark]="group.unreadCount > 0"
                  >
                  {{ group.lastMessage }}
                </p>
                <div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">{{ group.lastMessageTime }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Area -->
    <div class="chat-area flex-grow flex flex-col min-w-0 lg:min-w-[500px] w-full">
      <!-- Chat Header -->
      <div class="chat-header flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="avatar h-10 w-10 rounded-full overflow-hidden mr-3">
            <img [src]="selectedGroup.avatar" alt="Group Avatar" class="h-full w-full object-cover">
          </div>
          <div>
            <h3 class="text-base font-medium text-gray-900 dark:text-white">{{ selectedGroup.name }}</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              <i class="ki-solid ki-dot text-success text-xs mr-1"></i>
              Rashford is typing...
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <button class="btn btn-icon btn-sm btn-light-primary">
            <i class="ki-solid ki-phone fs-2"></i>
          </button>
          <button class="btn btn-icon btn-sm btn-light-primary">
            <i class="ki-solid ki-video fs-2"></i>
          </button>
          <button class="btn btn-icon btn-sm btn-light-primary">
            <i class="ki-solid ki-information-5 fs-2"></i>
          </button>
        </div>
      </div>

      <!-- Chat Messages -->
      <div class="chat-messages flex-grow p-4">
        <cdk-virtual-scroll-viewport class="h-full w-full" [itemSize]="100" minBufferPx="400" maxBufferPx="800">
          <div *cdkVirtualFor="let message of chatMessages" class="message-wrapper mb-4">
            <div class="flex" [ngClass]="{'justify-end': message.senderId === currentUser.id}">
              <div class="flex" [ngClass]="{'flex-row-reverse': message.senderId === currentUser.id}">
                <!-- Avatar (only show for others, not current user) -->
                <div *ngIf="message.senderId !== currentUser.id" class="avatar h-10 w-10 rounded-full overflow-hidden mr-3">
                  <img [src]="message.senderAvatar" alt="User Avatar" class="h-full w-full object-cover">
                </div>
                
                <!-- Message Content -->
                <div [ngClass]="{
                  'bg-primary text-white rounded-tl-lg rounded-tr-lg rounded-br-lg': message.senderId === currentUser.id,
                  'bg-gray-100 dark:bg-gray-700 dark:text-white rounded-tl-lg rounded-tr-lg rounded-bl-lg': message.senderId !== currentUser.id
                }" class="message-content p-3 max-w-xs md:max-w-md lg:max-w-lg">
                  <!-- Sender Name (only for others) -->
                  <div *ngIf="message.senderId !== currentUser.id" class="sender-name text-xs font-medium mb-1 text-gray-600 dark:text-gray-300">
                    {{ message.senderName }}
                  </div>
                  
                  <!-- Message Text -->
                  <p class="text-sm">{{ message.content }}</p>
                  
                  <!-- Attachments if any -->
                  <div *ngIf="message.attachments && message.attachments.length > 0" class="attachments mt-2 space-y-2">
                    <div *ngFor="let attachment of message.attachments" class="attachment">
                      <img *ngIf="attachment.type === 'image'" [src]="attachment.url" alt="Attachment" class="rounded-lg w-full h-32 object-cover">
                      <div *ngIf="attachment.type === 'document'" class="document flex items-center p-2 bg-gray-200 dark:bg-gray-600 rounded-lg">
                        <i class="ki-solid ki-file-document text-primary mr-2"></i>
                        <span class="text-sm">{{ attachment.name }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Audio Message -->
                  <div *ngIf="message.audioMessage" class="audio-message mt-2 flex items-center">
                    <button class="btn btn-icon btn-sm btn-light-primary rounded-full mr-2">
                      <i class="ki-solid ki-play fs-2"></i>
                    </button>
                    <div class="audio-waveform flex-grow h-6 bg-gray-200 dark:bg-gray-600 rounded-lg">
                      <div class="waveform-img h-full w-full">
                        <!-- Audio waveform visualization would go here -->
                      </div>
                    </div>
                    <span class="text-xs ml-2">{{ message.audioDuration }}</span>
                  </div>
                  
                  <!-- Timestamp and Read Status -->
                  <div class="message-meta flex justify-end items-center mt-1">
                    <span class="text-xs opacity-70">{{ message.timestamp }}</span>
                    <span *ngIf="message.senderId === currentUser.id" class="ml-1">
                      <i [class.text-success]="message.isRead" 
                        class="ki-solid ki-check-circle text-xs"></i>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </cdk-virtual-scroll-viewport>
      </div>

      <!-- Chat Input -->
      <div class="chat-input p-4 border-t border-gray-200 dark:border-gray-700 w-full">
        <div class="flex items-end w-full">
          <div class="flex-grow w-full">
            <div class="relative w-full">
              <textarea [(ngModel)]="newMessage" 
                        placeholder="Type a message..." 
                        class="form-control form-control-solid w-full min-h-[80px] pr-12 resize-none"
                        (keydown.enter)="$event.preventDefault(); sendMessage()"></textarea>
              <div class="absolute right-3 bottom-3 flex space-x-2">
                <button class="btn btn-icon btn-sm btn-light-primary rounded-full">
                  <i class="ki-solid ki-paperclip fs-2"></i>
                </button>
                <button class="btn btn-icon btn-sm btn-light-primary rounded-full">
                  <i class="ki-solid ki-microphone fs-2"></i>
                </button>
              </div>
            </div>
          </div>
          <button (click)="sendMessage()" class="btn btn-icon btn-primary rounded-full ml-3 h-10 w-10 flex-shrink-0">
            <i class="ki-solid ki-paper-plane fs-2"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Detail Panel -->
    <div class="detail-panel flex-shrink-0 w-full lg:w-[250px] border-l border-gray-200 dark:border-gray-700 flex flex-col">
      <!-- Detail Header -->
      <div class="detail-header p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 class="text-base font-medium text-gray-900 dark:text-white">Detail group</h3>
        <button class="btn btn-icon btn-sm btn-light">
          <i class="ki-solid ki-cross fs-2"></i>
        </button>
      </div>

      <!-- Group Info -->
      <div class="group-info p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-col items-center">
          <div class="avatar h-16 w-16 rounded-full overflow-hidden mb-3">
            <img [src]="selectedGroup.avatar" alt="Group Avatar" class="h-full w-full object-cover">
          </div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ selectedGroup.name }}</h3>
        </div>

        <!-- Description -->
        <div class="mt-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</h4>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ selectedGroup.description || 'Hey lads, tough game yesterday. Let\'s talk about what went wrong and how we can improve it. #GGMU🔴' }}
          </p>
        </div>

        <!-- Link -->
        <div class="mt-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Link group</h4>
          <div class="flex items-center">
            <a href="https://t.me/foo/team/" class="text-sm text-primary underline">https://t.me/foo/team/</a>
          </div>
        </div>
      </div>

      <!-- Members -->
      <div class="members p-4 border-b border-gray-200 dark:border-gray-700">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Member</h4>
        <div class="flex -space-x-2">
          <div class="avatar h-8 w-8 rounded-full overflow-hidden border-2 border-white dark:border-gray-800">
            <img src="assets/images/avatars/erik-tenhag.svg" alt="Member" class="h-full w-full object-cover">
          </div>
          <div class="avatar h-8 w-8 rounded-full overflow-hidden border-2 border-white dark:border-gray-800">
            <img src="assets/images/avatars/harry-maguire.svg" alt="Member" class="h-full w-full object-cover">
          </div>
          <div class="avatar h-8 w-8 rounded-full overflow-hidden border-2 border-white dark:border-gray-800">
            <img src="assets/images/avatars/bruno-fernandes.svg" alt="Member" class="h-full w-full object-cover">
          </div>
          <div class="avatar h-8 w-8 rounded-full overflow-hidden border-2 border-white dark:border-gray-800">
            <img src="assets/images/avatars/rasmus-hojlund.svg" alt="Member" class="h-full w-full object-cover">
          </div>
          <div class="avatar h-8 w-8 rounded-full overflow-hidden border-2 border-white dark:border-gray-800 flex items-center justify-center bg-gray-200 dark:bg-gray-700">
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">+3</span>
          </div>
        </div>
      </div>

      <!-- Media -->
      <div class="media p-4 flex-grow overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Media</h4>
          <button class="btn btn-icon btn-sm btn-light">
            <i class="ki-solid ki-cross fs-2"></i>
          </button>
        </div>

        <!-- Media Tabs -->
        <div class="media-tabs flex border-b border-gray-200 dark:border-gray-700 mb-4">
          <button class="flex-1 py-2 text-center text-sm font-medium text-gray-900 dark:text-white border-b-2 border-primary">
            Media
          </button>
          <button class="flex-1 py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">
            Link
          </button>
          <button class="flex-1 py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">
            Docs
          </button>
        </div>

        <!-- Media Grid -->
        <div class="media-grid grid grid-cols-3 gap-2">
          <div *ngFor="let media of mediaFiles" class="media-item">
            <img *ngIf="media.type === 'image'" [src]="media.url" alt="Media" class="rounded-lg w-full h-20 object-cover">
            <div *ngIf="media.type === 'document'" class="document flex flex-col items-center justify-center p-2 bg-gray-100 dark:bg-gray-800 rounded-lg h-20">
              <i class="ki-solid ki-file-document text-primary text-2xl mb-1"></i>
              <span class="text-xs text-gray-700 dark:text-gray-300 truncate w-full text-center">{{ media.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>