.sub-navigation-sheet {
  $headerHeight: 70;


  padding-top: $headerHeight+px;
  padding-bottom: 20px;
  height: 100%;

  .sheet-item {
    font-size: 1.1em;
    letter-spacing: -.5px;
    user-select: none;
  }
  .sheet-item-main {
    &:active,
    &:focus {
      background: #ddd;
    }

    &.active {
      color: #dcb215;
    }
  }
  .sheet-sub-item {
    .arrow {
      padding-left: 20px;
    }
  }
  .sub-navigation-sheet-module {
    color: var(--tw-gray-700);
    text-transform: uppercase;
    font-size: 0.7em;
    white-space: nowrap;
    overflow: hidden;
  }
  .sub-navigation-sheet-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: $headerHeight+px;
    z-index: 9;
  }
  .sub-navigation-sheet-list {
    height: 100%;
    overflow-y: auto;
    scrollbar-width: thin;
  }
}
