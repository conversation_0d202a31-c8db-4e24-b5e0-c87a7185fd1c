import { Injectable } from '@angular/core';
import { HttpContext } from '@angular/common/http';
import { PosInitUserData } from 'salehub_shared_contracts';
import { wait } from '@shared/utils';
import { AppLoaderService } from '../services/app_loader.service';
import { HttpService } from '../services/http.service';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';
import { InitService } from '../services/init.service';
import { FlashMessageService } from '../services/flash_message.service';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class AuthInitializer {
  constructor(
    private http: HttpService,
    private appLoader: AppLoaderService,
    private initService: InitService,
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {}

  async initialize(): Promise<void> {
    try {
      const user: PosInitUserData = await this.http.promisify(
        this.http.get('public', 'user_info', {
          isRelativeUrlEndpoint: true,
          context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
            hideError: true
          })
        })
      );

      this.initService.init(user);
    } catch(e: any) {
      // Thay thế console.log bằng thông báo có ý nghĩa cho người dùng
      if(e.status !== 403 && e.status !== 401) {
        // Sử dụng i18n key cho thông báo lỗi
        const errorMessage = this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED');
        const retryMessage = this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.PROCESSING');

        this.flashMessageService.error(
          `${errorMessage}: ${e.message}. ${retryMessage}`,
          {
            description: this.translateService.instant('FLASH_MESSAGES.ERRORS.API.SERVER_ERROR'),
            timeout: 8000
          }
        );

        await wait(5000);
        return this.initialize();
      }
    }

    this.appLoader.loadingOff();
    this.appLoader.removeLoader();
  }
}

export function initializeAuth(authInitializer: AuthInitializer) {
  return authInitializer.initialize();
}

// export function initializeApp(authService: AuthService) {
//   return (): Promise<any> => authService.init();
// }
