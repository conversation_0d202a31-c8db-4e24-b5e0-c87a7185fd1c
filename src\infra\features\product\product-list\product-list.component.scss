.price-range-filter {
  display: flex;
  gap: 8px;
  align-items: center;
  max-width: 300px;

  input[type="range"] {
    flex: 1;
  }
}

.actions-column {
  button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  button:nth-child(1) {
    background: #007bff;
    color: #fff;
  }

  button:nth-child(2) {
    background: #ffc107;
    color: #000;
  }

  button:nth-child(3) {
    background: #dc3545;
    color: #fff;
  }
}
