import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AppHeaderService } from '../header/app-header.service';

@Component({
  selector: 'app-notification',
  imports: [],
  templateUrl: './notification.component.html',
  styleUrl: './notification.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NotificationComponent {
  constructor(
    private headerService: AppHeaderService
  ) {}

  dismiss() {
    this.headerService.dismissBottomSheet();
  }
}
