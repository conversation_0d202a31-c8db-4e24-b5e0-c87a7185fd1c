{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**", "/media/*.(svg|cur|jpg|jpeg|png|apng|webp|avif|gif|otf|ttf|woff|woff2)"]}, "cacheQueryOptions": {"ignoreSearch": true}}], "dataGroups": [{"name": "api", "urls": ["/pos_ajax"], "cacheConfig": {"strategy": "freshness", "maxSize": 1000, "maxAge": "1d", "timeout": "30s"}}]}