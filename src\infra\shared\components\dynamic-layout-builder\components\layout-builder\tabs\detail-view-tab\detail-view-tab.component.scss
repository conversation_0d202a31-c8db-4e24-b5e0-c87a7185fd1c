// Detail View Tab Styles
.detail-view-tab-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;

  // Header
  .detail-view-header {
    flex-shrink: 0;
    border-bottom: 1px solid #dee2e6 !important;

    h4 {
      color: #495057;
      font-weight: 600;
    }

    .summary-info {
      .stat-item {
        text-align: center;
        min-width: 60px;

        .stat-value {
          font-weight: 700;
          line-height: 1.2;
        }

        .stat-label {
          font-size: 0.75rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  // Main Content
  .detail-view-content {
    flex: 1;
    overflow-y: auto;

    .sections-list {
      .section-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          // Removed transform: translateY(-2px) to prevent annoying jumping
        }

        .section-header {
          border-bottom: 1px solid #dee2e6 !important;
          border-radius: 8px 8px 0 0;

          h5 {
            color: #495057;
            font-weight: 600;
          }

          .badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
          }
        }

        .section-body {
          // Single-Line Widgets List with CDK Drop List
          .widgets-list {
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            overflow: hidden;
            min-height: 60px; // Minimum height for drop zone

            // CDK Drop List states
            &.cdk-drop-list-receiving {
              background: rgba(0, 123, 255, 0.05);
              border-color: #007bff;
            }

            &.cdk-drop-list-dragging {
              background: rgba(0, 123, 255, 0.02);
            }

            .widget-list-item {
              display: flex;
              align-items: center;
              padding: 0.75rem 1rem;
              border-bottom: 1px solid #f8f9fa;
              transition: background-color 0.2s ease;
              cursor: move;

              &:last-child {
                border-bottom: none;
              }

              // Subtle hover effect (no jumping/bouncing)
              &:hover {
                background: #f8f9fa;
              }

              // CDK Drag & Drop states
              &.cdk-drag-preview {
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                border-radius: 4px;
                background: white;
                border: 1px solid #dee2e6;
                opacity: 0.9;
                transform: rotate(5deg);
              }

              &.cdk-drag-placeholder {
                opacity: 0.3;
                background: #e9ecef;
                border: 2px dashed #6c757d;
              }

              &.cdk-drag-animating {
                transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
              }

              .drag-handle {
                margin-right: 0.75rem;
                color: #6c757d;
                cursor: grab;

                &:active {
                  cursor: grabbing;
                }

                .drag-icon {
                  font-size: 18px;
                  width: 18px;
                  height: 18px;
                }
              }

              .widget-icon {
                font-size: 20px;
                width: 20px;
                height: 20px;
                margin-right: 0.75rem;
                color: #6c757d;

                // Category-based colors
                &.text-primary { color: #007bff !important; }
                &.text-success { color: #28a745 !important; }
                &.text-info { color: #17a2b8 !important; }
                &.text-warning { color: #ffc107 !important; }
                &.text-danger { color: #dc3545 !important; }
                &.text-secondary { color: #6c757d !important; }
              }

              .widget-info {
                .widget-title-row {
                  display: flex;
                  align-items: center;
                  gap: 0.5rem;
                  margin-bottom: 0.25rem;

                  .widget-name {
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: #495057;
                  }

                  .widget-type {
                    font-size: 0.75rem;
                    color: #6c757d;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                  }
                }

                .widget-meta-row {
                  display: flex;
                  align-items: center;
                  gap: 1rem;

                  .widget-category {
                    font-size: 0.75rem;
                    color: #007bff;
                    background: rgba(0, 123, 255, 0.1);
                    padding: 0.125rem 0.375rem;
                    border-radius: 0.25rem;
                  }

                  .widget-order {
                    font-size: 0.75rem;
                    color: #6c757d;
                  }
                }
              }

              .widget-actions {
                margin-left: auto;

                .btn {
                  padding: 0.25rem 0.5rem;
                  font-size: 0.75rem;
                  opacity: 0;
                  transition: opacity 0.2s ease;

                  mat-icon {
                    font-size: 14px;
                    width: 14px;
                    height: 14px;
                  }
                }
              }

              &:hover .widget-actions .btn {
                opacity: 1;
              }
            }
          }



        }
      }
    }

    .empty-state {
      .empty-icon {
        opacity: 0.3;
      }

      h4, p {
        margin-bottom: 1rem;
      }
    }
  }

  // Footer
  .detail-view-footer {
    flex-shrink: 0;
    border-top: 1px solid #dee2e6 !important;
    background-color: #f8f9fa !important;

    mat-icon {
      font-size: 18px;
      height: 18px;
      width: 18px;
    }

    .small {
      font-size: 0.875rem;
      color: #6c757d;
    }
  }
}

// Widget category colors
.bg-primary {
  background-color: #007bff !important;
}

.bg-info {
  background-color: #17a2b8 !important;
}

.bg-success {
  background-color: #28a745 !important;
}

.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.bg-secondary {
  background-color: #6c757d !important;
}

// Responsive Design
@media (max-width: 768px) {
  .detail-view-tab-container {
    .detail-view-header {
      .summary-info {
        margin-top: 1rem;

        .d-flex {
          justify-content: center;
        }
      }
    }

    .detail-view-content {
      .sections-list {
        .section-card {
          .section-body {
            .widgets-grid {
              grid-template-columns: 1fr;
            }
          }
        }
      }
    }

    .detail-view-footer {
      .row {
        text-align: center;

        .col-md-4 {
          margin-top: 0.5rem;
        }
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .detail-view-tab-container {
    background: #1a1a1a;

    .detail-view-header {
      background: #2d2d2d !important;
      border-bottom-color: #404040 !important;

      h4 {
        color: #e9ecef;
      }
    }

    .detail-view-content {
      .sections-list {
        .section-card {
          background: #2d2d2d;
          border-color: #404040;

          .section-header {
            background: #343a40 !important;
            border-bottom-color: #404040 !important;

            h5 {
              color: #e9ecef;
            }
          }

          .section-body {
            .widgets-grid {
              .widget-item {
                background: #343a40;
                border-color: #495057;

                &:hover {
                  background: #495057;
                  border-color: #007bff;
                }

                .widget-content {
                  .widget-header {
                    .widget-name {
                      color: #e9ecef;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .detail-view-footer {
      background-color: #2d2d2d !important;
      border-top-color: #404040 !important;
    }
  }
}

// Keyframe animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// CDK Drag & Drop Enhancements
.detail-view-tab-container {
  // Drag preview styles
  .drag-preview {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    font-size: 0.875rem;
    color: #495057;

    .widget-icon {
      margin-right: 0.5rem;
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .widget-name {
      font-weight: 500;
    }
  }

  // Drop zone animations
  .cdk-drop-list-receiving {
    animation: pulse 1.5s ease-in-out infinite;
  }

  // Drag placeholder
  .cdk-drag-placeholder {
    opacity: 0.3;
    background: #e9ecef;
    border: 2px dashed #6c757d;
  }
}
