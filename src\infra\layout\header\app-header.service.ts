import { Injectable } from '@angular/core';
import { MatBottomSheet, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { Subject } from 'rxjs';
import { NotificationComponent } from '../notification/notification.component';
import { UserMenuComponent } from '../user-menu/user-menu.component';

@Injectable({
  providedIn: 'root'
})

export class AppHeaderService {
  private $update = new Subject<boolean>();
  private bottomSheetRef!: MatBottomSheetRef<any>;

  constructor(
    private bottomSheet: MatBottomSheet
  ) {
  }

  setDisplay(show: boolean) {
    this.$update.next(show);
  }

  getDisplay() {
    return this.$update;
  }

  openBottomSheetMenu(menu: 'notification' | 'user-menu') {
    this.bottomSheetRef = this.bottomSheet.open(
      (menu === 'notification' ? NotificationComponent : UserMenuComponent) as any,
      {
        panelClass: menu === 'notification' ? ['bottom-sheet-wide-screen', 'bottom-sheet-no-padding'] : ['bottom-sheet-no-padding']
      }
    );
  }

  dismissBottomSheet() {
    if(this.bottomSheetRef?.dismiss) {
      this.bottomSheetRef.dismiss();
    }
  }
}
