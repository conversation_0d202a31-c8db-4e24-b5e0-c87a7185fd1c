<!-- Detail View Tab Container -->
<div class="detail-view-tab-container">

  <!-- Header -->
  <div class="detail-view-header p-3 border-bottom bg-white">
    <div class="d-flex align-items-center justify-content-between">
      <div>
        <h4 class="mb-1">{{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.TITLE' | translate }}</h4>
        <p class="text-muted small mb-0">
          {{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.DESCRIPTION' | translate }}
        </p>
      </div>

      <!-- Summary Info -->
      <div class="summary-info text-end">
        <div class="d-flex gap-3">
          <div class="stat-item">
            <div class="stat-value h5 mb-0 text-primary">{{ sections().length }}</div>
            <div class="stat-label small text-muted">{{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.SECTIONS' | translate }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-value h5 mb-0 text-success">{{ totalWidgets() }}</div>
            <div class="stat-label small text-muted">{{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.WIDGETS' | translate }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="detail-view-content p-3">

    @if (sections().length > 0) {
      <!-- Sections List -->
      <div class="sections-list">
        @for (section of sections(); track trackBySection($index, section)) {
          <div class="section-card mb-4">

            <!-- Section Header -->
            <div class="section-header d-flex align-items-center justify-content-between p-3 bg-light border-bottom">
              <div class="d-flex align-items-center">
                <mat-icon class="text-primary me-2">folder_open</mat-icon>
                <div>
                  <h5 class="mb-0">{{ section.name }}</h5>
                  <small class="text-muted">
                    {{ section.widgets.length }} {{ getWidgetCountText(section.widgets.length) }}
                  </small>
                </div>
              </div>

              <!-- Section Info -->
              <div class="section-info">
                <span class="badge bg-secondary">{{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.READ_ONLY' | translate }}</span>
              </div>
            </div>

            <!-- Section Body - Widgets -->
            <div class="section-body p-3">
              @if (hasWidgets(section)) {
                <!-- CDK Drop List for Widgets -->
                <div
                  class="widgets-list section-widgets"
                  cdkDropList
                  [id]="'widgets-list-' + section._id"
                  [cdkDropListData]="section.widgets"
                  (cdkDropListDropped)="onWidgetDrop($event, section)"
                  [attr.data-section-id]="section._id">

                  @for (widget of section.widgets; track trackByWidget($index, widget)) {
                    <div
                      class="widget-list-item"
                      cdkDrag
                      [cdkDragData]="widget"
                      [attr.data-widget-id]="widget._id"
                      [attr.data-widget-index]="$index">

                      <!-- Drag Handle -->
                      <div class="drag-handle" cdkDragHandle>
                        <mat-icon class="drag-icon">drag_indicator</mat-icon>
                      </div>

                      <!-- Widget Icon (simplified) -->
                      <mat-icon class="widget-icon">
                        widgets
                      </mat-icon>

                      <!-- Widget Info (simplified for DTO) -->
                      <div class="widget-info flex-grow-1">
                        <div class="widget-title-row">
                          <span class="widget-name">{{ widget.name }}</span>
                        </div>
                        <div class="widget-meta-row">
                          <span class="widget-description">{{ widget.description }}</span>
                        </div>
                      </div>

                      <!-- Actions -->
                      <div class="widget-actions">
                        <button
                          type="button"
                          class="btn btn-sm btn-outline-danger"
                          (click)="onWidgetDelete(section._id, widget._id)"
                          [attr.aria-label]="'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.DELETE_WIDGET' | translate">
                          <mat-icon>close</mat-icon>
                        </button>
                      </div>

                      <!-- CDK Drag Preview -->
                      <div *cdkDragPreview class="drag-preview">
                        <mat-icon class="widget-icon">widgets</mat-icon>
                        <span class="widget-name">{{ widget.name }}</span>
                      </div>
                    </div>
                  }
                </div>
              } @else {
                <!-- Empty Section State -->
                <div class="empty-section text-center py-4">
                  <mat-icon class="empty-icon text-muted mb-2" style="font-size: 48px; height: 48px; width: 48px;">
                    widgets
                  </mat-icon>
                  <h6 class="text-muted">{{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.NO_WIDGETS' | translate }}</h6>
                  <p class="small text-muted mb-0">
                    {{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.NO_WIDGETS_DESCRIPTION' | translate }}
                  </p>
                </div>
              }
            </div>
          </div>
        }
      </div>
    } @else {
      <!-- Empty State -->
      <div class="empty-state text-center py-5">
        <mat-icon class="empty-icon text-muted mb-3" style="font-size: 64px; height: 64px; width: 64px;">
          dashboard
        </mat-icon>
        <h4 class="text-muted mb-2">{{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.NO_SECTIONS' | translate }}</h4>
        <p class="text-muted mb-0">
          {{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.NO_SECTIONS_DESCRIPTION' | translate }}
        </p>
      </div>
    }

  </div>

  <!-- Footer -->
  <div class="detail-view-footer p-3 border-top bg-light">
    <div class="row align-items-center">
      <div class="col-md-8">
        <div class="d-flex align-items-center">
          <mat-icon class="text-info me-2">info</mat-icon>
          <span class="small">
            {{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.FOOTER_INFO' | translate }}
          </span>
        </div>
      </div>
      <div class="col-md-4 text-end">
        <div class="small text-muted">
          {{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.LAST_UPDATED' | translate }}:
          <strong>{{ 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.NOW' | translate }}</strong>
        </div>
      </div>
    </div>
  </div>

</div>
