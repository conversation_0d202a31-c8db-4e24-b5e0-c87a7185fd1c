import { Component, computed, inject, ChangeDetectionStrategy,  ViewChild, ElementRef, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { DragDropModule, CdkDragDrop, moveItemInArray,  CdkDrag } from '@angular/cdk/drag-drop';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FlashMessageService } from '@core/services/flash_message.service';
import { QuickCreateTabService } from './quick-create-tab.service';
import { Section } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.dto';
import { Field, FieldType } from '@domain/entities/field.entity';
import { TabComponentReference } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.model';
import { DynamicLayoutConfigStateService } from '../../../../services/dynamic-layout-config-state.service';
import { getFieldIcon, getFieldIconColor, getFieldBackgroundColor } from '../../../../utils/field.utils';


/**
 * QuickCreateTabComponent - Tab tạo nhanh layout với 1 section cố định
 *
 * Tính năng:
 * - Tái sử dụng logic từ Create tab
 * - Chỉ cho phép 1 section duy nhất
 * - Hiển thị fields từ Create tab đã lưu
 * - Drag-drop functionality giống Create tab
 */
@Component({
  selector: 'app-quick-create-tab',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    DragDropModule,
    TranslateModule
  ],
  // REMOVED providers - QuickCreateTabService được provide bởi DynamicLayoutBuilderComponent
  templateUrl: './quick-create-tab.component.html',
  styleUrls: ['./quick-create-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class QuickCreateTabComponent implements OnInit, TabComponentReference {
  // ViewChild references for CDK Drag & Drop
  @ViewChild('fieldsListContainer', { static: false }) fieldsListContainer!: ElementRef;

  // Services
  private flashMessageService = inject(FlashMessageService);
  private translateService = inject(TranslateService);

  // ✅ NEW: Inject DynamicLayoutConfigStateService (public để truy cập từ template)
  public configStateService = inject(DynamicLayoutConfigStateService);

  // ✅ REFACTORED: Instance riêng của QuickCreateTabService để xử lý business logic
  private quickCreateTabService!: QuickCreateTabService;

  // Computed properties
  hasQuickCreateSection = computed(() => this.configStateService.quickCreateSection() !== null);

  // Connected drop lists for CDK Drag & Drop
  connectedDropLists = computed(() => {
    const sections = this.configStateService.quickCreateFieldSections();
    return sections.map(section => `section-${section._id}`);
  });


  fieldUsageMap = computed(() => {
    const section = this.configStateService.quickCreateSection();
    const currentSections = this.configStateService.quickCreateFieldSections();
    const usageMap: { [key: string]: boolean } = {};

    if (section) {
      // Get all fields from all sections
      const allFields = currentSections.flatMap((s: Section) => s.fields);

      allFields.forEach((field: Field) => {
        const isUsed = section.fields.some((f: Field) => {
          if(f._id && field._id) {
            return f._id === field._id;
          }

          return f.type === field.type && f.label === field.label;
        });

        usageMap[field._id || ''] = isUsed;
      });
    }

    return usageMap;
  });

  ngOnInit() {
    this.quickCreateTabService = new QuickCreateTabService(
      this.configStateService,
      this.flashMessageService,
      this.translateService
    );
  }


  private triggerAutoSave(): void {
    // const quickCreateSection = this.configStateService.getQuickCreateSection();

    // if (quickCreateSection) {
    //   // ✅ NEW: Check autoSave config trước khi auto-save
    //   const currentState = this.configStateService.getCurrentState();
    //   const autoSaveEnabled = currentState?.currentLayout?.autoSave ?? true;

    //   if (!autoSaveEnabled) {
    //     // console.log('⏸️ QuickCreateTab: Auto-save disabled, skipping auto-save');
    //     return;
    //   }

    //   // console.log('💾 QuickCreateTab: Triggering auto-save');

    //   // Cập nhật data trong DynamicLayoutConfigStateService
    //   this.configStateService.updateQuickCreateTabData(quickCreateSection);

    //   // ✅ MERGED: Save state logic từ autoSaveState() method
    //   // setTimeout(() => this.saveTabState(), 100);
    // }
  }


  isFieldAlreadyUsed(fieldId: string): boolean {
    return !!this.fieldUsageMap()[fieldId];
  }

  onFieldDelete(field: Field) {
    return this.quickCreateTabService.deleteField(field);
  }


  /**
   * Handle field addition from left panel to right panel using CDK Drag & Drop
   */
  onFieldDrop(event: CdkDragDrop<Field[]>): void {
    if (event.previousContainer === event.container) {
      // Reordering within the same container (Quick Create section)
      this.quickCreateTabService.reorderFieldsInSection(event.previousIndex, event.currentIndex);
    } else {
      this.quickCreateTabService.addFieldToSection(event.previousContainer.data[event.previousIndex]);
    }
  }


  /**
   * Handle drag start event - Enhanced visual feedback với nhiều hiệu ứng
   */
  onDragStart(event: any): void {
    console.log('🚀 Drag started:', event);

    // Lấy drag element và thêm enhanced animations
    const dragElement = event.source.element.nativeElement;
    if (dragElement) {
      // Thêm drag starting animation
      dragElement.classList.add('drag-starting', 'cdk-drag-dragging');


      // Remove drag starting class sau animation
      setTimeout(() => {
        dragElement.classList.remove('drag-starting');
      }, 200);
    }

    // Enhanced visual feedback cho drop zones
    const dropZones = document.querySelectorAll('.fields-list-container');
    dropZones.forEach((zone, index) => {
      // Staggered animation cho multiple drop zones
      setTimeout(() => {
        zone.classList.add('drop-zone-active');
        // Thêm pulse effect
        (zone as HTMLElement).style.animation = 'dropZoneReceiving 1.2s ease-in-out infinite alternate';
      }, index * 50);
    });

    // Thêm body class để style toàn trang khi drag
    document.body.classList.add('is-dragging');

    // Highlight tất cả draggable items khác
    const allDraggableItems = document.querySelectorAll('.field-type-item:not(.disabled), .field-list-item');
    allDraggableItems.forEach(item => {
      if (item !== dragElement) {
        item.classList.add('other-draggable-highlighted');
      }
    });

    // Thêm cursor style cho toàn trang
    document.body.style.cursor = 'grabbing';
  }

  /**
   * Handle drag end event - Enhanced cleanup với smooth transitions
   */
  onDragEnd(event: any): void {
    console.log('🏁 Drag ended:', event);

    // Enhanced cleanup cho drag element
    const dragElement = event.source.element.nativeElement;
    if (dragElement) {
      dragElement.classList.remove('cdk-drag-dragging');
      // Thêm return animation
      dragElement.classList.add('returning');
      setTimeout(() => {
        dragElement.classList.remove('returning');
      }, 400);
    }

    // Smooth removal của drop zone effects
    const dropZones = document.querySelectorAll('.fields-list-container');
    dropZones.forEach((zone, index) => {
      setTimeout(() => {
        zone.classList.remove('drop-zone-active');
        (zone as HTMLElement).style.animation = '';
      }, index * 30);
    });

    // Remove body classes và styles
    document.body.classList.remove('is-dragging');
    document.body.style.cursor = '';

    // Remove highlight từ other draggable items
    const allDraggableItems = document.querySelectorAll('.other-draggable-highlighted');
    allDraggableItems.forEach(item => {
      item.classList.remove('other-draggable-highlighted');
    });

    // Thêm success feedback nếu drop thành công
    if (event.isPointerOverContainer) {
      this.showDropSuccessFeedback();
    }
  }

 

  /**
   * Hiển thị feedback khi drop thành công
   */
  private showDropSuccessFeedback(): void {
    // Tạo success indicator
    const successIndicator = document.createElement('div');
    successIndicator.classList.add('drop-success-indicator');
    successIndicator.innerHTML = '<i class="material-icons">check_circle</i>';

    // Thêm vào drop zone
    const activeDropZone = document.querySelector('.cdk-drop-list-receiving');
    if (activeDropZone) {
      activeDropZone.appendChild(successIndicator);

      // Remove sau animation
      setTimeout(() => {
        if (successIndicator.parentNode) {
          successIndicator.parentNode.removeChild(successIndicator);
        }
      }, 1000);
    }
  }

  /**
   * Get icon for field type
   */
  getFieldIcon(fieldType: string): string {
    return getFieldIcon(fieldType as FieldType);
  }

  /**
   * Get icon color for field type
   */
  getFieldIconColor(fieldType: string): string {
    return getFieldIconColor(fieldType as FieldType);
  }

  /**
   * Get background color for field type
   */
  getFieldBackgroundColor(fieldType: string): string {
    return getFieldBackgroundColor(fieldType as FieldType);
  }

  /**
   * ✅ NEW: Public method để implement TabComponentReference
   */
  refreshData(): void {
    console.log('🔄 QuickCreateTabComponent: Refresh data');
  }
}
