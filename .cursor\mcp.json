{"mcpServers": {"magic-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@21st-dev/magic-mcp", "--config", "{\"TWENTY_FIRST_API_KEY\":\"8665b1a5df0cbf41a29e959f9bab480b0aaf98d3dae06ad024fbf45371fd16db\"}"]}, "server-sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--config", "{}"]}, "chrome-tools": {"command": "node", "args": ["I:\\mcp\\chrome-tools-MCP\\dist\\index.js"], "env": {"CHROME_DEBUG_URL": "http://localhost:9222", "CHROME_CONNECTION_TYPE": "direct", "CHROME_ERROR_HELP": "custom error message"}}, "browser-tools": {"command": "cmd", "args": ["/c", "npx", "@agentdeskai/browser-tools-mcp@1.2.0"]}, "playwright-mcp-server": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@executeautomation/playwright-mcp-server", "--config", "{}"]}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "MODEL": "claude-3-7-sonnet-20250219", "MAX_TOKENS": 64000, "TEMPERATURE": 0.2, "DEFAULT_SUBTASKS": 5, "DEFAULT_PRIORITY": "medium"}}}}