Cá<PERSON> yêu cầu cơ bản:
- <PERSON><PERSON><PERSON> tuân thủ rules của [angular.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/angular.mdc) [general.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/general.mdc)
- Sử dụng MCP server từ [mcp.json](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/mcp.json) khi cần thiết để debug lỗi và view trên browser.
- Trước khi tự động chạy, nếu có gì cần làm rõ thì hỏi lại để rõ ràng các ý chính trước khi tự động chạy, không phải sửa đi sửa lại nhiều lần vì không hiểu ý.

NAVIGATIONS: Navigation[]: [text](../../src/app/config/navigation.config.ts)


--
T<PERSON><PERSON> đang phát triển một ứng dụng Angular 19 và cần tạo hai thanh navigation cho phiên bản mobile: một thanh bottom và một thanh top. Dữ liệu navigation được cung cấp trong mảng `NAVIGATIONS: Navigation[]`. Dưới đây là yêu cầu chi tiết:

#### **Yêu cầu cho thanh bottom:**
- Hiển thị 4 icon chính tương ứng với 4 phần tử đầu tiên trong mảng `NAVIGATIONS` (ví dụ: Bán hàng, Đơn hàng, Sản phẩm, Kho hàng). Mỗi icon bao gồm:
  - Icon được lấy từ `icon.set` và `icon.code` (hiển thị phía trên).
  - Văn bản bên dưới icon, ưu tiên sử dụng `shortText` nếu có, nếu không thì dùng `text`.
- Icon thứ 5 là icon "more" (gợi ý: sử dụng icon như `'menu'` hoặc `'dots-vertical'` từ `keenicons-filled`). Khi click vào icon "more", mở một **mat-bottom-sheet** hiển thị toàn bộ cây URL từ `NAVIGATIONS`.
- Icon ở thanh bottom phải có trạng thái **active** nếu URL hiện tại thuộc về `block` tương ứng của navigation đó (dựa trên `routerLink` và các `items` con).

#### **Yêu cầu cho thanh top:**
- Khi một icon ở thanh bottom được chọn, thanh top sẽ hiển thị các URL con (`items`) của navigation tương ứng. Ví dụ:
  - Nếu chọn "Bán hàng" ở thanh bottom, thanh top hiển thị: "Tìm hóa đơn", "Hóa đơn nháp", "Bán lẻ", v.v.
- Sử dụng **SwiperJS** ở chế độ **free mode** (xem: [SwiperJS Free Mode API](https://swiperjs.com/swiper-api#free-mode)) để hiển thị danh sách các URL con trên một dòng, cho phép cuộn ngang nếu danh sách dài.
- URL nào ở thanh top đang **active** (dựa trên URL hiện tại) phải được highlight (ví dụ: đổi màu hoặc thêm viền) và danh sách phải tự động cuộn đến vị trí của URL active.
- Nếu một URL ở thanh top có các URL con (`items`), hiển thị một **icon mũi tên hướng lên** (gợi ý: `'chevron-up'` từ `keenicons-filled`) bên cạnh văn bản để phân biệt. Khi click vào URL này, mở một **mat-bottom-sheet** hiển thị các URL con của nó (ví dụ: "Khuyến mại" mở ra "Chiết khấu", "Tích điểm").

#### **Chi tiết kỹ thuật:**
- Sử dụng **Angular Router** để theo dõi URL hiện tại (`Router.url`) và xác định trạng thái **active** cho cả thanh bottom và top.
- Sử dụng **Angular Material** cho component `mat-bottom-sheet` để hiển thị cây URL hoặc các URL con.
- Tích hợp **SwiperJS** vào component Angular cho thanh top, đảm bảo hỗ trợ cuộn ngang và tự động cuộn đến URL active.
- Đảm bảo component có thể phản hồi các thay đổi trong URL (sử dụng `Router.events` hoặc `NavigationEnd`) để cập nhật giao diện tương ứng.

#### **Cấu trúc component:**
- Tạo một **component cha** (ví dụ: `MobileNavigationComponent`) quản lý cả hai thanh navigation.
- Tạo hai **component con**:
  - `BottomNavigationComponent`: Quản lý thanh bottom.
  - `TopNavigationComponent`: Quản lý thanh top.
- (Tùy chọn) Sử dụng một **service** (ví dụ: `NavigationService`) để chia sẻ trạng thái giữa các component, như navigation hiện tại đang được chọn.

#### **Mẫu code mong muốn:**
1. **Template HTML cho thanh bottom:**
   - Hiển thị 4 icon chính + 1 icon "more".
   - Sử dụng `ngClass` hoặc `[class.active]` để xử lý trạng thái active dựa trên URL hiện tại.
   - Gắn sự kiện `(click)` để mở `mat-bottom-sheet` khi nhấn icon "more".

2. **Template HTML cho thanh top:**
   - Sử dụng directive của SwiperJS (như `<swiper>`) để hiển thị danh sách URL con.
   - Áp dụng `ngClass` để highlight URL active.
   - Thêm icon mũi tên cho các URL có `items`, gắn sự kiện `(click)` để mở `mat-bottom-sheet`.

3. **Logic trong TypeScript:**
   - Xác định navigation hiện tại ở thanh bottom dựa trên URL (so sánh `routerLink` và `block`).
   - Cập nhật danh sách URL con ở thanh top khi chọn icon ở thanh bottom.
   - Xử lý logic cuộn SwiperJS đến URL active (sử dụng phương thức như `slideTo` của Swiper).
   - Mở `mat-bottom-sheet` với dữ liệu cây URL hoặc URL con khi cần.


#### **Yêu cầu bổ sung:**
- Code phải tuân theo best practices của Angular (component hóa, tái sử dụng, dễ bảo trì).
- Bao gồm comment trong code để giải thích các phần quan trọng.
- Đảm bảo giao diện responsive, phù hợp với mobile.

Hãy cung cấp mã nguồn chi tiết cho các component này, bao gồm cách tích hợp SwiperJS và Angular Material, cũng như logic xử lý URL và trạng thái active.
