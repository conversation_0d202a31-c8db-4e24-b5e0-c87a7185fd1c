import { Component, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '@core/services/auth.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-logout',
  standalone: true,
  imports: [],
  templateUrl: './logout.component.html'
})
export class LogoutComponent implements OnDestroy {
  // Memory management: Container để quản lý tất cả subscriptions
  private readonly subscriptions = new Subscription();

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnDestroy(): void {
    // Memory management: Unsubscribe tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }

  async ngOnInit() {
    const result = await this.authService.logout();
    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      result.subscribe({
        next: () => {
          this.router.navigate(['login'], { queryParams: { redirect: '/' } });
        }
      })
    );
  }
}
