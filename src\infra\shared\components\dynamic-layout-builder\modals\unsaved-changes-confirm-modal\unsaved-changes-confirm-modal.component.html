<!-- <PERSON><PERSON> Header -->
<div mat-dialog-title class="d-flex align-items-center">
  <mat-icon class="text-warning me-2">warning</mat-icon>
  <span>{{ 'DYNAMIC_LAYOUT_BUILDER.UNSAVED_CHANGES.TITLE' | translate }}</span>
</div>

<!-- Modal Content -->
<div mat-dialog-content class="py-3">
  <p class="mb-3">
    {{ 'DYNAMIC_LAYOUT_BUILDER.UNSAVED_CHANGES.MESSAGE' | translate }}
  </p>
  
  <!-- Layout Info -->
  <div class="bg-light p-3 rounded mb-3">
    <div class="row">
      <div class="col-6">
        <small class="text-muted">{{ 'DYNAMIC_LAYOUT_BUILDER.LAYOUT_SELECTOR.CURRENT' | translate }}:</small>
        <div class="fw-bold">{{ data.fromLayout.title }}</div>
      </div>
      <div class="col-6">
        <small class="text-muted">{{ 'DYNAMIC_LAYOUT_BUILDER.LAYOUT_SELECTOR.SWITCHING_TO' | translate }}:</small>
        <div class="fw-bold">{{ data.toLayout.title }}</div>
      </div>
    </div>
  </div>
</div>

<!-- Modal Actions -->
<div mat-dialog-actions class="d-flex justify-content-end gap-2 p-3">
  <!-- Cancel Button -->
  <button 
    mat-button 
    type="button"
    (click)="onCancel()"
    [disabled]="isSaving"
    class="btn-outline-secondary">
    <mat-icon>close</mat-icon>
    {{ 'DYNAMIC_LAYOUT_BUILDER.UNSAVED_CHANGES.CANCEL' | translate }}
  </button>
  
  <!-- Discard & Switch Button -->
  <button 
    mat-button 
    type="button"
    (click)="onDiscardAndSwitch()"
    [disabled]="isSaving"
    class="btn-outline-danger">
    <mat-icon>delete_outline</mat-icon>
    {{ 'DYNAMIC_LAYOUT_BUILDER.UNSAVED_CHANGES.DISCARD_AND_SWITCH' | translate }}
  </button>
  
  <!-- Save & Switch Button -->
  <button 
    mat-raised-button 
    color="primary"
    type="button"
    (click)="onSaveAndSwitch()"
    [disabled]="isSaving"
    class="btn-primary">
    
    <!-- Loading Spinner -->
    <mat-spinner 
      *ngIf="isSaving" 
      diameter="16" 
      class="me-2">
    </mat-spinner>
    
    <!-- Icon when not saving -->
    <mat-icon *ngIf="!isSaving">save</mat-icon>
    
    <!-- Button Text -->
    <span class="ms-1">
      {{ isSaving 
        ? ('DYNAMIC_LAYOUT_BUILDER.UNSAVED_CHANGES.SAVING' | translate)
        : ('DYNAMIC_LAYOUT_BUILDER.UNSAVED_CHANGES.SAVE_AND_SWITCH' | translate) 
      }}
    </span>
  </button>
</div>
