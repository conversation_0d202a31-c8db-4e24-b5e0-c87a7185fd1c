#!/usr/bin/env node
import { join, resolve } from 'path';
import { collectHtmlTranslationKeys, exportTranslationKeys } from '../export-component-html-translation-keys.js';
import { stat } from 'fs/promises';
import { compareTranslationKeys } from '../compare-component-translation-keys.js';
import { compareTranslations } from '../compare-translations.js';
import { findUnusedTranslations } from '../find-unused-translations.js';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { configs } from './configs.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const featurePath = __dirname + '/../../../src/infra/features';
const exportPath = __dirname + '/../exports';
const outputScannedTranslationKeyFile = resolve(join(exportPath, 'html_translation_keys.js'));
const outputMissingTranslationKeyFile = resolve(join(exportPath, 'missing_translation_keys.js'));
const outputUnunsedTranslationKeyFile = resolve(join(exportPath, 'unused_translation_keys.js'));
const mergedTranslationFile = resolve(join(__dirname, '../../../public/assets/i18n', 'en.json'));


/**
 * Quét translation keys trong toàn bộ component
 */
async function scanAllComponentTranslationKeys() {
  console.log(`Quét translation key trong các file .html trong: ${configs.componentPath}`);
  console.log(`File đầu ra: ${outputScannedTranslationKeyFile}`);

  // Kiểm tra folder tồn tại
  try {
    const stats = await stat(configs.componentPath);
    if (!stats.isDirectory()) {
      console.error(`Lỗi: Đường dẫn không phải thư mục: ${configs.componentPath}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`Lỗi: Thư mục không tồn tại: ${configs.componentPath}`);
    process.exit(1);
  }

  // Thu thập key
  const keys = await collectHtmlTranslationKeys(configs.componentPath);

  // Báo cáo kết quả
  if (keys.length === 0) {
    console.log('Không tìm thấy translation key nào trong các file .html.');
  } else {
    console.log(`Tìm thấy ${keys.length} translation key`);
  }

  // Xuất key ra file
  await exportTranslationKeys(keys, outputScannedTranslationKeyFile);
}

/**
 * So sánh các key quét translation keys trong toàn bộ component
 * với bản dịch hiện tại xem có thiếu key nào không
 */
async function compareCurrentTranslationKeys() {
  console.log(`So sánh translation key:`);
  console.log(`  File HTML keys: ${outputScannedTranslationKeyFile}`);
  console.log(`  File merged translations: ${mergedTranslationFile}`);
  console.log(`  File đầu ra: ${outputMissingTranslationKeyFile}`);

  // Kiểm tra file tồn tại
  try {
    await stat(outputScannedTranslationKeyFile);
    await stat(mergedTranslationFile);
  } catch (error) {
    console.error(`Lỗi: Một trong hai file không tồn tại: ${outputScannedTranslationKeyFile} hoặc ${mergedTranslationFile}`);
    process.exit(1);
  }

  return compareTranslationKeys(outputScannedTranslationKeyFile, mergedTranslationFile, outputMissingTranslationKeyFile);
}

/**
 * So sánh 2 bản dịch trong folder i18n component
 */
async function compareComponentTranslation() {
  console.log(`So sánh bản dịch trong thư mục: ${configs.i18nComponentPath}`);
  return compareTranslations(configs.i18nComponentPath);
}

/**
 * Tìm các keys thừa trong translation
 */
async function findComponentUnusedTranslations() {
  return findUnusedTranslations(configs.componentPath, configs.i18nComponentPath, outputUnunsedTranslationKeyFile, false);
}


// Hàm chính
async function main() {
  await scanAllComponentTranslationKeys();
  await compareCurrentTranslationKeys();
  await compareComponentTranslation();
  await findComponentUnusedTranslations();

  process.exit(0);
}

// Chạy script
main().catch(error => {
  console.error(`Lỗi không xác định: ${error.message}`);
  process.exit(1);
});