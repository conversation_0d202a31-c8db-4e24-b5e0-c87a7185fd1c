<div class="px-4 py-5 bg-white">
  <mat-form-field appearance="outline" floatLabel="always" class="w-100 mb-5">
    <mat-label>Tên đăng nhập</mat-label>

    <input
      type="text"
      matInput
      [(ngModel)]="input.email"
      (keydown)="resetError()"
      required
      >
    <mat-hint class="text-muted">
      Tên đăng nhập có thể là username, email hoặc số điện thoại
    </mat-hint>
  </mat-form-field>

  <mat-form-field appearance="outline" floatLabel="always" class="w-100">
    <mat-label>M<PERSON><PERSON> khẩu</mat-label>

    <input
      type="password"
      matInput
      [(ngModel)]="input.password"
      (keydown)="resetError()"
      required
      >
  </mat-form-field>

  @if (err) {
    <div class="alert alert-danger" role="alert">
      {{ err}}
    </div>
  }

  <div class="save">
    <button class="text-uppercase btn btn-primary" (click)="submit()">
      <PERSON><PERSON><PERSON> nhập
    </button>
  </div>
</div>
