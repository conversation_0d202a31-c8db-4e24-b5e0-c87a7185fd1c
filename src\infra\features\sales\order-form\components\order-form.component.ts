import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

import { CreateOrderRequest } from '../models/api/order-form.dto';
import { OrderTabsComponent } from './order-tabs/order-tabs.component';
import { OrderTabContentComponent } from './order-tabs/order-tab-content/order-tab-content.component';
import { OrderTabService } from './order-tabs/order-tab.service';

/**
 * Component quản lý trang chính đơn hàng
 */
@Component({
  selector: 'app-order-form',
  standalone: true,
  imports: [
    CommonModule,
    OrderTabsComponent,
    OrderTabContentComponent,
    TranslateModule
  ],
  templateUrl: './order-form.component.html',
  styleUrls: ['./order-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OrderFormComponent implements OnInit {
  /**
   * Service quản lý tab đơn hàng
   */
  private orderTabService = inject(OrderTabService);

  /**
   * Dữ liệu đơn hàng của tab đang active
   */
  activeOrder: CreateOrderRequest = {};

  /**
   * Khởi tạo component
   */
  ngOnInit(): void {
    // Lấy dữ liệu đơn hàng của tab đang active
    this.activeOrder = this.orderTabService.getActiveTab();
  }

  /**
   * Xử lý khi thêm tab mới
   */
  onAddTab(): void {
    // Cập nhật activeOrder khi thêm tab mới
    this.activeOrder = this.orderTabService.getActiveTab();
  }

  /**
   * Xử lý khi xóa tab
   */
  onRemoveTab(): void {
    // Cập nhật activeOrder khi xóa tab
    this.activeOrder = this.orderTabService.getActiveTab();
  }

  /**
   * Xử lý khi chuyển tab
   */
  onTabChange(): void {
    // Cập nhật activeOrder khi chuyển tab
    this.activeOrder = this.orderTabService.getActiveTab();
  }

  /**
   * Xử lý khi cập nhật đơn hàng
   * @param updatedOrder Dữ liệu đơn hàng đã cập nhật
   */
  onOrderUpdated(updatedOrder: CreateOrderRequest): void {
    this.activeOrder = updatedOrder;
    this.orderTabService.updateActiveTab(updatedOrder);
  }
}
