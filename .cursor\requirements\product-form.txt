Sau đây là các yêu cầu chi tiết:
Tôi cần tạo form thêm sản phẩm theo interface Product từ module salehub_shared_contracts. Interface Product  là một interface có sẵn trong TypeScript, được import bằng câu lệnh: import { Product } from 'salehub_shared_contracts'; Bạn không được phép tự định nghĩa interface Product, nếu bạn không thể tìm được interface Product thì phải dừng chương trình để hỏi lại.

Định nghĩa
- chip-form-field: một input text có thể nhận nhiều giá trị, hỗ trợ autocomplete với khả năng chọn sẵn các giá trị từ danh sách gợi ý, và cho phép tạo giá trị mới khi nhấn Enter nếu giá trị không có trong danh sách. Sử dụng Angular Material  component mat-autocomplete kết hợp với mat-chip-list
	- Nhận nhiều giá trị: Sử dụng mat-chip-list để hiển thị nhiều giá trị đã chọn dưới dạng "chip" (nhãn).
	- Autocomplete: mat-autocomplete cung cấp danh sách gợi ý khi người dùng nhập. (ví dụ người dùng đã từng nhập màu trắng, đen, thì khi khởi tạo sẽ có auto complete trắng đen luôn)
	- Chọn sẵn giá trị: Bạn có thể thiết lập các giá trị ban đầu bằng cách gán dữ liệu vào mảng chứa giá trị đã chọn.
	- Tạo giá trị mới khi nhấn Enter: Khi người dùng nhập một giá trị không có trong danh sách gợi ý và nhấn Enter, bạn có thể xử lý sự kiện để thêm giá trị mới vào danh sách. (ví dụ thuộc tính màu, người dùng thêm giá trị xanh chưa từng có trong database, nhấn enter sẽ tạo 1 value mới)
- autocomplete-with-new-value: angular material autocomplete (https://material.angular.io/components/autocomplete/overview). Nếu user chọn từ 1 autocomplate value sẽ có {_id: string, value: string}. Nếu không user không chọn sẽ chỉ có {value: string} và trên server sẽ tạo value mới.
- Section kho hàng gồm 1 row có 4 cột:
	- Chọn kho hàng (ví dụ kho tổng, kho A) - bắt buộc chọn
	- Chọn nhà cung cấp (danh sách nhà cung cấp từ mock Supplier), có 1 nút + bên phải nếu bấm vào sẽ ra 1 dialog hướng đến CreateSupplierComponent. Nếu user add supplier thành công, supplier đấy sẽ được thêm vào mockdata đã load. - không bắt buộc chọn
	- Chọn vị trí sản phẩm - tree node load ra warehouse location mock lấy từ src/app/mock-data/locations.ts - không bắt buộc chọn
	- Số lượng tồn kho: number - field bắt buộc
- Block Quản lý kho ban đầu
	1. Mỗi 1 row Là 1 Section kho hàng
	2. Nếu Block thuộc tính đã có dữ liệu, block Quản lý kho ban đầu sẽ bị disable. Lúc này quản lý kho ban đầu sẽ theo các sản phẩm thuộc tính
	3. Row cuối cùng là button "thêm tồn kho" để có thể lưu tồn kho ở nhiều kho hàng, nhấn sẽ ra 1 row mới


Trước tiên làm các form:
- Danh mục theo interface ProductCategory  từ module salehub_shared_contracts, path: /products/category/create, folder: src/app/features/product/pages/category-form
- Thương hiệu theo interface ProductBrand  từ module salehub_shared_contracts, path: /products/brand/create, folder: src/app/features/product/pages/brand-form
- Thuộc tính theo interface ProductVariant từ module salehub_shared_contracts, path: /products/variant/create, folder: src/app/features/product/pages/variant-form
- Tạo Nhà cung cấp theo interface Supplier từ module salehub_shared_contracts, path: /supply-chain/suppliers/create, folder: src/app/features/supply-chain/pages/supplier-form

Tạo các mock_data vào src/app/mock_data/product_form.ts để ghép vào ProductForm gồm:
- mock ProductCategory   theo ỉnterface ProductCategory  từ module salehub_shared_contracts
- mock ProductBrand  theo ỉnterface ProductBrand  từ module salehub_shared_contracts
- mock ProductVariant theo ỉnterface ProductVariant từ module salehub_shared_contracts
- mock Supplier theo ỉnterface Supplier từ module salehub_shared_contracts

Trong form product gồm 3 block chính:
1. Block các trường cơ bản như tên sản phẩm, mã hàng, trọng lượng, giá bán...Trong block này, có 2 form select chính: Nhóm hàng và thương hiệu. 2 form này được lấy data từ mock ProductCategory   và ProductBrand  , ở bên cạnh form field sẽ có 1 nút + để thêm category/brand ngay tại ProductForm. Khi nhấn vào nút này, sẽ load 1 dialog hướng đến ProductCategoryComponent hoặc ProductBrandComponent. Nếu user thêm category/brand từ đây, sau khi add thành công, thêm các category/brand đấy vào mockdata đã load. Mỗi 1 product có thể có nhiều category . Mỗi 1 product có thể 1 brand. ProductTag là 1 chip-form-field
2. Block Quản lý kho ban đầu
3. Block thuộc tính. Là 1 Expansion-panel: https://material.angular.io/components/expansion/overview, mặc định mở
	1. trong block sẽ có các row chính gồm:
		1. bên trái tên thuộc tính là select box load ra những thuộc tính đã được lưu trong ProductVariant ví dụ tên thuộc tính là màu
		2. bên phải là giá trị của thuộc tính là 1 chip-form-field
	2. dòng dưới cùng có thêm thuộc tính, khi người dùng click vào sẽ hiện ra 1 dialog hướng đến ProductVariantComponent. Nếu user add variant thành công, các variant đấy sẽ được thêm vào mockdata đã load.
4. Ngay khi user thêm các thuộc tính, 1 block mới được khởi tạo: Danh sách hàng hóa cùng loại. Block này đóng vai trò như là liệt kê các sản phẩm con (theo thuộc tính) theo sản phẩm cha (là FormProduct). Là 1 Expansion-panel: https://material.angular.io/components/expansion/overview, mặc định mở. Trong đó bao gồm:
	1. Row 1 chọn kho hàng mặc định (*)
		1. là 1 Section kho hàng nhưng không có số lượng tồn kho, nhằm nhanh chóng khgai báo tồn kho nếu các sản phẩm con cùng 1 kho
	2. Row 2: List các sản phẩm con theo thuộc tính
		1. Tên: ví dụ như trắng, S đen M theo thuộc tính size và màu (mỗi 1 thuộc tính sẽ sinh ra 1 record, ví dụ chỉ thêm thuộc tính màu và size. Màu xanh, trắng và size S thì sinh ra row Trắng S và Xanh S)
		2. Mã hàng (tự dộng hoặc user nhập)
		3. Giá vốn
		4. Giá bán
		5. Tồn kho: Có 1 form text number và 1 nút warehouse bên cạnh
			1. Nếu user nhập vào form text number thì mặc định kho là (*)
			2. Nếu user nhấn vào nút warehouse thì ra 1 dialog block  Quản lý kho ban đầu, nghĩa là sản phẩm này ở các kho và vị trí khác (*)
		6. Nút delete
5. Block Đơn vị tính (với loại hàng hóa có nhiều đơn vị tính như hộp, vỉ, lốc...). Là 1 Expansion-panel: https://material.angular.io/components/expansion/overview, mặc định mở. Có 3 row chính:
	1. Row 1 gôm:
		1. Đơn vị cơ bản: autocomplete-with-new-value (nghĩa là sản phẩm này dùng đơn vị nào để tính trước khi quy đổi, ví dụ hộp)
	2. Row 2 gồm các row được thêm khi nhấn buttom "thêm đơn vị". Bao gồm:
		1. Tên đơn vị: autocomplete-with-new-value (ví dụ lốc, vỉ..)
		2. Giá trị quy đổi: number (ví dụ 1 lốc = 12 hộp thì ở đây điền 12)
		3. Giá bán lẻ
		4. Giá bán buôn
		5. Giá vốn
		6. Mã hàng
	3. Row 3 có nút "thêm đơn vị" để sinh ra các row 2
6. Block Thành phần nguyên liệu (nhằm tính giá cost theo nguyên liệu). Nếu block này có values, field cost sẽ bị disable do sẽ tính cost theo block này. Các row:
	1. Row chính:
		1. Field textbox auto complete Sản phẩm: để tìm sản phẩm (đã lưu trong db, load từ mock product ra)
		2. Field number số lượng
		3. Field text giá vốn tự động sinh theo giá vốn của sản phẩm
		4. field thành tiền = số lượng * giá vốn
	2. Row 2 tổng giá vốn
	3. Row 3 button "thêm nguyên liệu"



Các links để tham khảo:
- https://support.haravan.com/support/solutions/articles/42000084598-t%E1%BA%A1o-s%E1%BA%A3n-ph%E1%BA%A9m-m%E1%BB%9Bi
- https://manual.nhanh.vn/pos/san-pham-v2/danh-sach-san-pham-v2/them-san-pham-v2
- https://www.youtube.com/watch?v=1zpetxlx06Y
- https://www.youtube.com/watch?v=OeoOB7G-3pk&ab_channel=H%C3%A0KiotViet
- https://www.youtube.com/watch?v=G0turnt4U2g&ab_channel=H%C3%A0KiotViet
- https://www.youtube.com/watch?v=DHVCH2MdEsA&ab_channel=MrQu%E1%BA%A3n



---


Sau đây là 1 bản lược bớt lại, nếu không hiểu bản trên bạn có thể đọc thêm bản này. Bản trên là bản chi tiết, PHẢI LÀM ĐÚNG THEO.


#### Yêu cầu chính

1. **Sử dụng interface có sẵn**:
   - Sử dụng interface `Product` từ module `salehub_shared_contracts`. Câu lệnh import là:
     ```typescript
     import { Product } from 'salehub_shared_contracts';
     ```
   - Không tự định nghĩa lại interface `Product`. Nếu bạn không biết cấu trúc của `Product` hoặc các interface liên quan như `ProductCategory`, `ProductBrand`, `ProductVariant`, `Supplier`, hãy dừng lại và hỏi tôi để tôi cung cấp.

2. **Các thành phần tùy chỉnh**:
   - **`chip-form-field`**: Một ô nhập văn bản cho phép nhập nhiều giá trị (multi-value), hỗ trợ:
     - Autocomplete với danh sách gợi ý (dùng `mat-autocomplete` từ Angular Material).
     - Hiển thị giá trị dưới dạng chip (dùng `mat-chip-list`).
     - Nếu người dùng nhập giá trị mới (không có trong danh sách gợi ý) và nhấn Enter, giá trị đó sẽ được thêm vào danh sách và lưu lại để gợi ý sau này.
       **Ví dụ**: Người dùng nhập "trắng", "đen", sau đó các giá trị này xuất hiện trong gợi ý. Nhập "xanh" và nhấn Enter thì "xanh" được thêm vào.
   - **`autocomplete-with-new-value`**: Một ô autocomplete (dùng `mat-autocomplete`):
     - Nếu chọn từ danh sách gợi ý, giá trị trả về dạng `{_id: string, value: string}`.
     - Nếu nhập giá trị mới (không có trong danh sách), trả về dạng `{value: string}` và server sẽ xử lý tạo mới.

3. **Section kho hàng**:
   - Một section gồm 4 cột:
     - **Chọn kho hàng**: Bắt buộc, dùng select box (mock data từ `src/app/mock-data/warehouses.ts`).
     - **Chọn nhà cung cấp**: Không bắt buộc, dùng select box với nút "+" để mở dialog thêm nhà cung cấp mới.
     - **Chọn vị trí sản phẩm**: Không bắt buộc, dùng tree node (mock data từ `src/app/mock-data/locations.ts`).
     - **Số lượng tồn kho**: Bắt buộc, ô nhập số.

4. **Các form cần tạo trước**:
   - **Danh mục sản phẩm**:
     - Dựa trên interface `ProductCategory` từ module `salehub_shared_contracts`.
     - Path: `/products/category/create`.
     - Folder: `src/app/features/product/pages/category-form`.
   - **Thương hiệu**:
     - Dựa trên interface `ProductBrand` từ module `salehub_shared_contracts`.
     - Path: `/products/brand/create`.
     - Folder: `src/app/features/product/pages/brand-form`.
   - **Thuộc tính**:
     - Dựa trên interface `ProductVariant` từ module `salehub_shared_contracts`.
     - Path: `/products/variant/create`.
     - Folder: `src/app/features/product/pages/variant-form`.
   - **Nhà cung cấp**:
     - Dựa trên interface `Supplier` từ module `salehub_shared_contracts`.
     - Path: `/supply-chain/suppliers/create`.
     - Folder: `src/app/features/supply-chain/pages/supplier-form`.

5. **Mock data**:
   - Tạo mock data trong file `src/app/mock_data/product_form.ts` cho các thành phần:
     - `ProductCategory`
     - `ProductBrand`
     - `ProductVariant`
     - `Supplier`
   - Đảm bảo dữ liệu đủ để thử nghiệm form chính.

6. **Form Product chính**:
   - **Block 1: Thông tin cơ bản**:
     - Các trường: Tên sản phẩm (text), mã hàng (text), trọng lượng (number), giá bán (number)...
     - Hai select box:
       - Nhóm hàng: Dùng mock `ProductCategory`, có nút "+" để mở dialog thêm mới.
       - Thương hiệu: Dùng mock `ProductBrand`, có nút "+" để mở dialog thêm mới.
     - `ProductTag`: Sử dụng `chip-form-field`.
   - **Block 2: Quản lý kho ban đầu**:
     - Mỗi hàng là một "Section kho hàng" (theo mục 3).
     - Nếu sản phẩm có thuộc tính (Block 3), block này sẽ bị vô hiệu hóa.
     - Có nút "Thêm tồn kho" để thêm kho hàng mới.
   - **Block 3: Thuộc tính**:
     - Dùng `mat-expansion-panel`, mặc định mở.
     - Mỗi row:
       - Select box chọn tên thuộc tính (mock `ProductVariant`).
       - Giá trị thuộc tính: Dùng `chip-form-field`.
     - Nút "Thêm thuộc tính" để mở dialog thêm mới.
   - **Block 4: Danh sách hàng hóa cùng loại** (chỉ hiển thị khi có thuộc tính):
     - Dùng `mat-expansion-panel`, mặc định mở.
     - Row 1: Chọn kho hàng mặc định (select box).
     - Row 2: Danh sách sản phẩm con theo thuộc tính, gồm các cột:
       - Tên, mã hàng, giá vốn, giá bán, tồn kho (có nút "warehouse" để mở dialog quản lý kho chi tiết).
   - **Block 5: Đơn vị tính**:
     - Dùng `mat-expansion-panel`, mặc định mở.
     - Row 1: Đơn vị cơ bản (dùng `autocomplete-with-new-value`).
     - Row 2: Danh sách đơn vị quy đổi, mỗi row gồm:
       - Tên đơn vị, giá trị quy đổi, giá bán lẻ, giá bán buôn, giá vốn, mã hàng.
     - Nút "Thêm đơn vị" để thêm row mới.
   - **Block 6: Thành phần nguyên liệu**:
     - Dùng `mat-expansion-panel`, tính giá vốn dựa trên nguyên liệu.
     - Row chính:
       - Tìm sản phẩm (autocomplete), số lượng, giá vốn (tự động tính), thành tiền.
     - Row tổng giá vốn.
     - Nút "Thêm nguyên liệu" để thêm row mới.

---

#### Hướng dẫn thực hiện từng bước

1. **Xác định cấu trúc interface**:
   - Kiểm tra và liệt kê cấu trúc của các interface: `Product`, `ProductCategory`, `ProductBrand`, `ProductVariant`, `Supplier`.
   - Nếu không biết cấu trúc, dừng lại và hỏi tôi để tôi cung cấp.

2. **Tạo các form phụ**:
   - Tạo 4 form: Danh mục sản phẩm, Thương hiệu, Thuộc tính, Nhà cung cấp theo path và folder đã chỉ định.
   - Mỗi form chỉ cần các trường cơ bản dựa trên interface tương ứng.

3. **Tạo mock data**:
   - Tạo file `src/app/mock_data/product_form.ts` và thêm dữ liệu mẫu cho `ProductCategory`, `ProductBrand`, `ProductVariant`, `Supplier`.

4. **Xây dựng form Product**:
   - Bắt đầu từ **Block 1: Thông tin cơ bản**, hoàn thành và kiểm tra.
   - Tiếp tục với các block còn lại (Block 2 → Block 6), từng bước một.

---

#### Lưu ý quan trọng
- Sau khi hoàn thành mỗi bước, hãy báo cáo kết quả (ví dụ: "Tôi đã tạo xong Block 1, đây là code...").
- Nếu bạn không hiểu rõ yêu cầu hoặc cần thêm thông tin (như mock data cụ thể, cấu trúc interface), hãy hỏi tôi ngay.
- Sử dụng Angular Material cho tất cả các thành phần UI (select, autocomplete, chip, expansion panel...).




Hãy bắt đầu bằng cách lập kế hoạch chi tiết cho các bước trên và gửi lại cho tôi để xác nhận trước khi thực hiện!
