<h2 mat-dialog-title>{{ data.title }}</h2>
<mat-dialog-content>
  <mat-form-field class="w-100" cdkTrapFocus cdkTrapFocusAutoCapture>
    <mat-label><PERSON><PERSON> tiền (VNĐ)</mat-label>
    <input
      type="number"
      pattern="[0-9]*"
      inputmode="numeric"
      [min]="1000"
      [max]="data.max"
      matInput
      [(ngModel)]="discount.amount"
      (keydown.enter)="close()"
      cdkFocusInitial
      >
    <mat-hint>
      Tối thiểu: <b>1000</b>; Tối đa: <b>{{ data.max  | formatString: 'money'}}</b>
    </mat-hint>
  </mat-form-field>

  <mat-form-field class="w-100 mt-4">
    <mat-label>Nội dung</mat-label>
    <input
      type="text"
      matInput
      [(ngModel)]="discount.name"
      (keydown.enter)="close()"
      >
  </mat-form-field>
</mat-dialog-content>

<mat-dialog-actions class="justify-content-center">
  <button
    mat-button
    mat-flat-button
    color="primary"
    cdkFocusInitial
    (click)="close()"
    >
    Ok
  </button>
</mat-dialog-actions>
