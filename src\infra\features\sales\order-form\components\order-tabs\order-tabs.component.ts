import { ChangeDetectionStrategy, Component, EventEmitter, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { CreateOrderRequest } from 'salehub_shared_contracts/requests/sales/create_order';
import { OrderTabService } from './order-tab.service';

/**
 * Component hiển thị và quản lý các tab đơn hàng
 */
@Component({
  selector: 'app-order-tabs',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './order-tabs.component.html',
  styleUrls: ['./order-tabs.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OrderTabsComponent {
  /**
   * Service quản lý tab đơn hàng
   */
  private orderTabService = inject(OrderTabService);

  /**
   * EventEmitter khi thêm tab mới
   */
  @Output() addTab = new EventEmitter<void>();

  /**
   * EventEmitter khi xóa tab
   */
  @Output() removeTab = new EventEmitter<number>();

  /**
   * EventEmitter khi chuyển tab
   */
  @Output() tabChange = new EventEmitter<number>();

  /**
   * Lấy danh sách tab từ service
   */
  get tabs() {
    return this.orderTabService.tabs;
  }

  /**
   * Lấy index tab đang active
   */
  get activeTabIndex() {
    return this.orderTabService.activeTabIndex;
  }

  /**
   * Thêm một tab mới
   */
  onAddTab(): void {
    this.orderTabService.addTab();
    this.addTab.emit();
  }

  /**
   * Xóa tab tại vị trí index
   * @param index Vị trí tab cần xóa
   * @param event Sự kiện click
   */
  onRemoveTab(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Ngăn sự kiện lan đến tab
    this.orderTabService.removeTab(index);
    this.removeTab.emit(index);
  }

  /**
   * Xử lý khi chuyển tab
   * @param index Vị trí tab đã chuyển đến
   */
  onTabChange(index: number): void {
    this.orderTabService.setActiveTab(index);
    this.tabChange.emit(index);
  }

  /**
   * Tạo tên hiển thị cho tab
   * @param order Dữ liệu đơn hàng của tab
   * @param index Vị trí của tab
   * @returns Tên hiển thị cho tab
   */
  getTabLabel(order: CreateOrderRequest, index: number): string {
    if (order?.customer?.name) {
      return order.customer.name;
    }
    return `Đơn hàng ${index + 1}`;
  }

  /**
   * Kiểm tra xem có thể thêm tab mới không
   * @returns true nếu không thể thêm tab mới
   */
  isAddTabDisabled(): boolean {
    return this.tabs().length >= 10;
  }
}
