<div class="organization-container">
  <h2 class="page-title">{{ 'ORGANIZATION.ORGANIZATION_FORM.TITLE' | translate }}</h2>

  <form [formGroup]="companyForm" (ngSubmit)="onSubmit()">
    <div class="row-flex">
      <!-- Tên doanh nghiệp -->
      <div class="col-half">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'ORGANIZATION.ORGANIZATION_FORM.BUSINESS_NAME' | translate }}</mat-label>
          <input matInput formControlName="businessName" [placeholder]="'ORGANIZATION.ORGANIZATION_FORM.BUSINESS_NAME_PLACEHOLDER' | translate">
          <mat-error *ngIf="companyForm.get('businessName')?.hasError('required')">
            {{ 'ORGANIZATION.ORGANIZATION_FORM.BUSINESS_NAME_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <!-- <PERSON><PERSON> số thuế -->
      <div class="col-half">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'ORGANIZATION.ORGANIZATION_FORM.TAX_CODE' | translate }}</mat-label>
          <input matInput formControlName="taxCode" [placeholder]="'ORGANIZATION.ORGANIZATION_FORM.TAX_CODE_PLACEHOLDER' | translate">
          <mat-error *ngIf="companyForm.get('taxCode')?.hasError('required')">
            {{ 'ORGANIZATION.ORGANIZATION_FORM.TAX_CODE_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <div class="row-flex">
      <!-- Chi cục thuế -->
      <div class="col-half">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'ORGANIZATION.ORGANIZATION_FORM.TAX_AUTHORITY' | translate }}</mat-label>
          <input matInput formControlName="taxAuthority" [placeholder]="'ORGANIZATION.ORGANIZATION_FORM.TAX_AUTHORITY_PLACEHOLDER' | translate">
        </mat-form-field>
      </div>

      <!-- Ngày thành lập -->
      <div class="col-half">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'ORGANIZATION.ORGANIZATION_FORM.ESTABLISHMENT_DATE' | translate }}</mat-label>
          <input matInput [matDatepicker]="establishmentDatePicker" formControlName="establishmentDate" placeholder="DD/MM/YYYY">
          <mat-datepicker-toggle matSuffix [for]="establishmentDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #establishmentDatePicker></mat-datepicker>
        </mat-form-field>
      </div>
    </div>

    <!-- Địa chỉ -->
    <div formGroupName="address" class="section">
      <h3 class="section-title">{{ 'ADDRESS.TITLE' | translate }}</h3>
      <div class="row-flex">
        <div class="col-full">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'ADDRESS.FULL_ADDRESS' | translate }}</mat-label>
            <input matInput formControlName="fullAddress" [placeholder]="'ADDRESS.FULL_ADDRESS_PLACEHOLDER' | translate">
            <mat-error *ngIf="companyForm.get('address.fullAddress')?.hasError('required')">
              {{ 'ADDRESS.FULL_ADDRESS_REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="row-flex">
        <div class="col-half">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'ADDRESS.STREET' | translate }}</mat-label>
            <input matInput formControlName="street" [placeholder]="'ADDRESS.STREET_PLACEHOLDER' | translate">
          </mat-form-field>
        </div>
        <div class="col-half">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'ADDRESS.CITY' | translate }}</mat-label>
            <input matInput formControlName="city" [placeholder]="'ADDRESS.CITY_PLACEHOLDER' | translate">
          </mat-form-field>
        </div>
      </div>

      <div class="row-flex">
        <div class="col-third">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'ADDRESS.PROVINCE' | translate }}</mat-label>
            <input matInput formControlName="province" [placeholder]="'ADDRESS.PROVINCE_PLACEHOLDER' | translate">
          </mat-form-field>
        </div>
        <div class="col-third">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'ADDRESS.COUNTRY' | translate }}</mat-label>
            <input matInput formControlName="country" [placeholder]="'ADDRESS.COUNTRY_PLACEHOLDER' | translate">
          </mat-form-field>
        </div>
        <div class="col-third">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'ADDRESS.POSTAL_CODE' | translate }}</mat-label>
            <input matInput formControlName="postalCode" [placeholder]="'ADDRESS.POSTAL_CODE_PLACEHOLDER' | translate">
          </mat-form-field>
        </div>
      </div>

      <div class="row-flex">
        <div class="col-full">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'ADDRESS.INSTRUCTION' | translate }}</mat-label>
            <textarea matInput formControlName="instruction" [placeholder]="'ADDRESS.INSTRUCTION_PLACEHOLDER' | translate"></textarea>
          </mat-form-field>
        </div>
      </div>
    </div>

    <!-- Số điện thoại -->
    <div class="section">
      <h3 class="section-title">{{ 'ORGANIZATION.ORGANIZATION_FORM.PHONES.TITLE' | translate }}</h3>
      <div formArrayName="workPhones">
        <div *ngFor="let phone of workPhonesArray.controls; let i = index" class="row-flex align-center margin-bottom">
          <div class="col-large">
            <mat-form-field appearance="outline" class="full-width" [formGroupName]="i">
              <mat-label>{{ 'ORGANIZATION.ORGANIZATION_FORM.PHONES.PHONE_NUMBER' | translate }}</mat-label>
              <input matInput formControlName="phoneNumber" [placeholder]="'ORGANIZATION.ORGANIZATION_FORM.PHONES.PHONE_NUMBER_PLACEHOLDER' | translate">
              <mat-error *ngIf="workPhonesArray.at(i).get('phoneNumber')?.hasError('required')">
                {{ 'ORGANIZATION.ORGANIZATION_FORM.PHONES.PHONE_NUMBER_REQUIRED' | translate }}
              </mat-error>
              <mat-error *ngIf="workPhonesArray.at(i).get('phoneNumber')?.hasError('pattern')">
                {{ 'ORGANIZATION.ORGANIZATION_FORM.PHONES.PHONE_NUMBER_PATTERN' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-small text-center">
            <button type="button" mat-icon-button color="warn" (click)="removePhone(i)" [disabled]="workPhonesArray.length === 1">
              <i class="fa-regular fa-trash-xmark"></i>
            </button>
          </div>
        </div>
        <div class="row-flex">
          <div class="col-full">
            <button type="button" mat-stroked-button color="primary" (click)="addPhone()" class="add-button">
              <i class="fa-regular fa-plus"></i>
              {{ 'ORGANIZATION.ORGANIZATION_FORM.PHONES.ADD_PHONE' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Email -->
    <div class="row-flex margin-top">
      <div class="col-half">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'ORGANIZATION.ORGANIZATION_FORM.WORK_EMAIL' | translate }}</mat-label>
          <input matInput formControlName="workEmail" [placeholder]="'ORGANIZATION.ORGANIZATION_FORM.WORK_EMAIL_PLACEHOLDER' | translate">
          <mat-error *ngIf="companyForm.get('workEmail')?.hasError('email')">
            {{ 'ORGANIZATION.ORGANIZATION_FORM.WORK_EMAIL_INVALID' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Mô tả -->
    <div class="row-flex">
      <div class="col-full">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>{{ 'ORGANIZATION.ORGANIZATION_FORM.DESCRIPTION' | translate }}</mat-label>
          <textarea matInput formControlName="description" [placeholder]="'ORGANIZATION.ORGANIZATION_FORM.DESCRIPTION_PLACEHOLDER' | translate" rows="3"></textarea>
        </mat-form-field>
      </div>
    </div>

    <!-- Thông tin chủ sở hữu -->
    <div formGroupName="owner" class="section">
      <h3 class="section-title">{{ 'ORGANIZATION.ORGANIZATION_FORM.OWNER.TITLE' | translate }}</h3>
      <div class="row-flex">
        <div class="col-half">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'ORGANIZATION.ORGANIZATION_FORM.OWNER.NAME' | translate }}</mat-label>
            <input matInput formControlName="name" [placeholder]="'ORGANIZATION.ORGANIZATION_FORM.OWNER.NAME_PLACEHOLDER' | translate">
            <mat-error *ngIf="companyForm.get('owner.name')?.hasError('required')">
              {{ 'ORGANIZATION.ORGANIZATION_FORM.OWNER.NAME_REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>

    <!-- Nút Submit -->
    <div class="form-actions">
      <button type="button" mat-stroked-button class="cancel-button">{{ 'ORGANIZATION.ORGANIZATION_FORM.ACTIONS.CANCEL' | translate }}</button>
      <button type="submit" mat-raised-button color="primary" class="submit-button">{{ 'ORGANIZATION.ORGANIZATION_FORM.ACTIONS.SAVE' | translate }}</button>
    </div>
  </form>
</div>
