<!-- Loading State -->
<div *ngIf="isLoading()" class="loading-container">
  <div class="text-center py-5">
    <mat-spinner diameter="60" class="mb-3"></mat-spinner>
    <h4 class="text-muted">{{ 'PRODUCT_LAYOUT_EDIT.LOADING' | translate }}</h4>
    <p class="text-muted">{{ 'PRODUCT_LAYOUT_EDIT.LOADING_DESC' | translate }}</p>
  </div>
</div>



<!-- Layout Builder Content -->
<ng-container *ngIf="hasLayoutConfigs() && !isLoading() && !error()">
  <app-dynamic-layout-builder
    #dynamicLayoutBuilder
    [layoutBuilderConfig]="layoutBuilderConfig()"
    class="d-block h-100">
  </app-dynamic-layout-builder>
</ng-container>
