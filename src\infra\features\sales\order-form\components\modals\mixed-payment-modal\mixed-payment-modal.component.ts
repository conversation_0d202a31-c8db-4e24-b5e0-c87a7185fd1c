import { Compo<PERSON>, On<PERSON>ni<PERSON>, On<PERSON><PERSON>roy, inject, signal, QueryList, ViewChildren, ElementRef, Optional, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatBottomSheetModule, MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatAutocompleteModule, MatAutocomplete } from '@angular/material/autocomplete';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatCardModule } from '@angular/material/card';
import { FlashMessageService } from '@core/services/flash_message.service';
import { OrderPayment } from 'salehub_shared_contracts/entities/oms/order/order_components/order_payment';
import { mockBankList } from '@mock/shared/list.mock';
import { StrictModalComponent } from '@shared/components/standard-dialog/interfaces/modal-component.interface';
import { Subscription } from 'rxjs';

/**
 * Interface cho dữ liệu đầu vào của MixedPaymentModalComponent
 */
export interface MixedPaymentModalData {
  totalAmount: number;
  payments?: any[];
  paidAmount?: number;
  remainingAmount?: number;
  paymentStatus?: string;
}

/**
 * Interface cho kết quả trả về từ MixedPaymentModalComponent
 */
export type MixedPaymentModalResult = OrderPayment;

@Component({
  selector: 'app-mixed-payment-modal',
  templateUrl: './mixed-payment-modal.component.html',
  styleUrls: ['./mixed-payment-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatBottomSheetModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatAutocompleteModule,
    TranslateModule,
    MatCardModule
  ]
})
export class MixedPaymentModalComponent implements OnInit, OnDestroy, StrictModalComponent<MixedPaymentModalData, MixedPaymentModalResult> {
  // ViewChildren để tham chiếu tới các input số tiền
  @ViewChildren('amountInput') amountInputs!: QueryList<ElementRef>;

  // Inject services
  private fb = inject(FormBuilder);

  // Dialog refs
  private dialogRef?: MatDialogRef<MixedPaymentModalComponent>;
  private bottomSheetRef?: MatBottomSheetRef<MixedPaymentModalComponent>;
  private data: MixedPaymentModalData;

  // Memory management: Container để quản lý tất cả subscriptions
  private readonly subscriptions = new Subscription();

  // Data
  bankAccounts = mockBankList;
  qrCodeImage = 'https://qrcg-free-editor.qr-code-generator.com/latest/assets/images/websiteQRCode_noFrame.png';

  // Form
  paymentForm!: FormGroup;

  // Reactive signals
  paidAmount = signal(0); // Tổng số tiền đã thanh toán
  remainingAmount = signal(0); // Số tiền còn lại

  // Phương thức thanh toán
  paymentMethods = [
    { value: 'cash', label: 'SALES.ORDER_FORM.PAYMENT_METHOD.CASH' },
    { value: 'bank_transfer', label: 'SALES.ORDER_FORM.PAYMENT_METHOD.BANK_TRANSFER' },
    { value: 'credit_card', label: 'SALES.ORDER_FORM.PAYMENT_METHOD.CREDIT_CARD' },
    { value: 'ewallet', label: 'SALES.ORDER_FORM.PAYMENT_METHOD.EWALLET' }
  ];

  // Mảng các giá trị tự động gợi ý (sẽ được cập nhật khi init component)
  quickAmountSuggestions: Map<number, number[][]> = new Map();

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: MixedPaymentModalData | null | undefined,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: MixedPaymentModalData | null | undefined,
    @Optional() dialogRefInject?: MatDialogRef<MixedPaymentModalComponent>,
    @Optional() bottomSheetRefInject?: MatBottomSheetRef<MixedPaymentModalComponent>,
    private flashMessageService: FlashMessageService = inject(FlashMessageService),
    private translateService: TranslateService = inject(TranslateService)
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {
      totalAmount: 0,
      payments: [],
      paidAmount: 0,
      remainingAmount: 0,
      paymentStatus: 'unpaid'
    };

    // Gán refs
    this.dialogRef = dialogRefInject;
    this.bottomSheetRef = bottomSheetRefInject;
  }

  ngOnInit(): void {
    this.initForm();
    this.calculateTotals();

    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      this.paymentsArray.valueChanges.subscribe(() => {
        this.calculateTotals();
        // Cập nhật lại các gợi ý giá trị nhanh khi có thay đổi
        this.paymentsArray.controls.forEach((control, index) => {
          this.setupQuickAmountSuggestionsForIndex(index);
        });
      })
    );
  }

  ngOnDestroy(): void {
    // Memory management: Unsubscribe tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }

  // Khởi tạo form
  initForm(): void {
    this.paymentForm = this.fb.group({
      totalAmount: [this.data?.totalAmount || 0, Validators.required],
      payments: this.fb.array([])
    });

    // Nếu đã có payments thì thêm vào form
    if (this.data?.payments?.length) {
      this.data.payments.forEach(payment => {
        this.addPayment(payment.amount, payment.paymentMethod, payment.details);
      });
    } else {
      // Nếu chưa có thì thêm một payment mặc định
      this.addPayment(null, 'cash');
    }
  }

  // Getter cho form controls
  get paymentsArray(): FormArray {
    return this.paymentForm.get('payments') as FormArray;
  }

  get totalAmount(): number {
    return this.paymentForm.get('totalAmount')?.value || 0;
  }

  // Tạo mới một payment form group
  createPaymentFormGroup(amount: number | null = null, method: string = 'cash', details: any = {}): FormGroup {
    return this.fb.group({
      amount: [amount, [Validators.required, Validators.min(0)]],
      paymentMethod: [method, Validators.required],
      details: this.fb.group({
        bankTransfer: this.fb.group({
          account: this.fb.group({
            accountNumber: [details?.bankTransfer?.account?.accountNumber || ''],
            accountHolder: [details?.bankTransfer?.account?.accountHolder || ''],
            bankName: [details?.bankTransfer?.account?.bankName || ''],
            branchName: [details?.bankTransfer?.account?.branchName || '']
          })
        }),
        ewalletProvider: [details?.ewalletProvider || '']
      }),
      paidAt: [new Date()],
      transactionId: [''],
      note: ['']
    });
  }

  // Thêm phương thức thanh toán
  addPayment(amount: number | null = null, method: string = 'cash', details: any = {}): void {
    const index = this.paymentsArray.length;
    this.paymentsArray.push(this.createPaymentFormGroup(amount, method, details));

    // Thiết lập các gợi ý giá trị nhanh cho phương thức thanh toán mới
    this.setupQuickAmountSuggestionsForIndex(index);

    // Focus vào ô input số tiền mới thêm sau khi view đã cập nhật
    setTimeout(() => {
      if (this.amountInputs && this.amountInputs.length > index) {
        const inputElement = this.amountInputs.get(index)?.nativeElement;
        inputElement.focus();
      }
    });
  }

  // Xóa phương thức thanh toán
  removePayment(index: number): void {
    this.paymentsArray.removeAt(index);
    this.quickAmountSuggestions.delete(index);
  }

  // Thiết lập các gợi ý giá trị nhanh cho một phương thức thanh toán cụ thể
  setupQuickAmountSuggestionsForIndex(index: number): void {
    // Tính toán số tiền còn lại cần thanh toán (trừ đi số tiền trong các phương thức thanh toán khác)
    const totalPaid = this.paymentsArray.value
      .filter((_: any, i: number) => i !== index)
      .reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);

    const remainingAmount = this.totalAmount - totalPaid;

    if (remainingAmount <= 0) {
      this.quickAmountSuggestions.set(index, [[], []]);
      return;
    }

    // Tạo các giá trị gợi ý dựa trên số tiền còn lại
    this.quickAmountSuggestions.set(index, [
      [
        remainingAmount, // Số tiền còn lại chính xác
        Math.ceil(remainingAmount / 10) * 10, // Làm tròn lên đến 10
        Math.ceil(remainingAmount / 50) * 50, // Làm tròn lên đến 50
        Math.ceil(remainingAmount / 100) * 100 // Làm tròn lên đến 100
      ],
      [
        // Các mệnh giá cao hơn
        Math.ceil(remainingAmount / 500) * 500, // Làm tròn lên đến 500
        Math.ceil(remainingAmount / 1000) * 1000, // Làm tròn lên đến 1000
        Math.ceil(remainingAmount / 2000) * 2000, // Làm tròn lên đến 2000
        Math.ceil(remainingAmount / 5000) * 5000 // Làm tròn lên đến 5000
      ]
    ]);
  }

  // Lấy các gợi ý giá trị nhanh cho một phương thức thanh toán
  getQuickAmountSuggestionsForIndex(index: number): number[][] {
    return this.quickAmountSuggestions.get(index) || [[], []];
  }

  // Mở autocomplete khi focus vào input
  openQuickAmountSuggestions(index: number, auto: MatAutocomplete): void {
    // Cập nhật giá trị gợi ý cho phương thức thanh toán này
    this.setupQuickAmountSuggestionsForIndex(index);

    // Mở autocomplete bằng cách trigger event programmatically
    setTimeout(() => {
      auto.opened.emit();

      // Tìm và điều chỉnh panel
      setTimeout(() => {
        // Loại bỏ class hidden khỏi autocomplete panel
        const panelContainer = document.querySelector('.cdk-overlay-container .mat-mdc-autocomplete-panel') as HTMLElement;
        const overlayPane = document.querySelector('.cdk-overlay-pane') as HTMLElement;

        if (panelContainer) {
          panelContainer.classList.remove('mat-mdc-autocomplete-hidden');
          panelContainer.style.visibility = 'visible';
          panelContainer.style.opacity = '1';
          panelContainer.style.display = 'block';
        }

        if (overlayPane) {
          // Tìm input element để lấy kích thước
          const inputEl = this.amountInputs.get(index)?.nativeElement as HTMLElement;
          if (inputEl) {
            // Lấy thông tin kích thước và vị trí
            const inputRect = inputEl.getBoundingClientRect();
            const formField = inputEl.closest('mat-form-field');

            // Đặt kích thước tối thiểu cho overlay pane
            overlayPane.style.minWidth = `${Math.max(300, inputRect.width)}px`;
            overlayPane.style.width = 'auto';

            // Điều chỉnh vị trí nếu cần
            if (formField) {
              const formFieldRect = formField.getBoundingClientRect();
              overlayPane.style.left = `${formFieldRect.left}px`;
            }
          }
        }
      }, 50); // Delay thêm để đảm bảo DOM đã được cập nhật
    }, 0);
  }

  // Function hiển thị cho autocomplete - để giữ nguyên giá trị đã nhập
  displayFn(value: number): string {
    return value !== null ? value.toString() : '';
  }

  // Tính toán tổng số tiền thanh toán và số tiền còn lại
  calculateTotals(): void {
    const payments = this.paymentsArray.value;
    const totalPaid = payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);

    this.paidAmount.set(totalPaid);
    this.remainingAmount.set(this.totalAmount - totalPaid);
  }

  // Chọn giá trị nhanh
  selectQuickAmount(amount: number, index: number): void {
    // Cập nhật giá trị của form control
    const paymentControl = this.paymentsArray.at(index);
    paymentControl.patchValue({ amount });

    // Không làm gì với autocomplete panel - để nó vẫn mở nếu cần
    // Tránh đóng dialog bằng cách không gọi blur() trên input
    // Để autocomplete vẫn hiển thị, không thêm class hidden nào vào panel

    // Nếu cần, có thể gọi lại hàm này để cập nhật các gợi ý
    setTimeout(() => {
      this.setupQuickAmountSuggestionsForIndex(index);
    }, 0);
  }

  // Lấy ngân hàng đã chọn
  getSelectedBank(index: number): any {
    const paymentControl = this.paymentsArray.at(index);
    const bankAccountId = paymentControl.get('details.bankTransfer.account.bankName')?.value;
    return this.bankAccounts.find(bank => bank.name === bankAccountId);
  }

  // Hủy dialog
  cancel(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss();
    }
  }

  // Lưu và đóng dialog
  save(): void {
    if (this.paymentForm.valid) {
      const result: OrderPayment = {
        totalAmount: this.totalAmount,
        payments: this.paymentsArray.value,
        paidAmount: this.paidAmount(),
        remainingAmount: this.remainingAmount(),
        paymentStatus: this.getPaymentStatus()
      };

      // Thêm success message khi payment được xử lý thành công
      this.flashMessageService.success(
        this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.SAVED')
      );

      if (this.dialogRef) {
        this.dialogRef.close(result);
      } else if (this.bottomSheetRef) {
        this.bottomSheetRef.dismiss(result);
      }
    } else {
      // Thêm error message khi form không valid
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.FORM.INVALID_DATA')
      );
    }
  }

  // Xác định trạng thái thanh toán
  private getPaymentStatus(): 'pending' | 'cancelled' | 'returned' | 'unpaid' | 'partially_paid' | 'partially_returned' | 'paid' {
    const totalPaid = this.paidAmount();

    if (totalPaid <= 0) {
      return 'unpaid';
    } if (totalPaid < this.totalAmount) {
      return 'partially_paid';
    }
    return 'paid';
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): MixedPaymentModalResult {
    if (!this.paymentForm.valid) {
      // Thay thế throw Error bằng FlashMessageService với i18n
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.FORM.INVALID_DATA'),
        {
          description: this.translateService.instant('FLASH_MESSAGES.ERRORS.FORM.MISSING_REQUIRED')
        }
      );
      throw new Error('Payment form is not valid');
    }

    return {
      totalAmount: this.totalAmount,
      payments: this.paymentsArray.value,
      paidAmount: this.paidAmount(),
      remainingAmount: this.remainingAmount(),
      paymentStatus: this.getPaymentStatus()
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.paymentForm.valid && this.paymentsArray.length > 0;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: MixedPaymentModalData): void {
    this.data = data;

    // Cập nhật form với dữ liệu mới
    this.paymentForm.patchValue({
      totalAmount: data.totalAmount || 0
    });

    // Clear existing payments và thêm payments mới
    while (this.paymentsArray.length !== 0) {
      this.paymentsArray.removeAt(0);
    }

    if (data.payments?.length) {
      data.payments.forEach(payment => {
        this.addPayment(payment.amount, payment.paymentMethod, payment.details);
      });
    } else {
      this.addPayment(null, 'cash');
    }

    this.calculateTotals();
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào field đầu tiên khi modal mở
    setTimeout(() => {
      if (this.amountInputs && this.amountInputs.length > 0) {
        const firstInput = this.amountInputs.first?.nativeElement;
        if (firstInput) {
          firstInput.focus();
        }
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Kiểm tra xem có thay đổi nào chưa được lưu không
    if (this.paymentForm.dirty && this.paymentForm.valid) {
      // Có thể thêm logic confirm nếu cần
      return true;
    }
    return true; // Luôn cho phép đóng modal
  }
}
