import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { PermissionSelectorConfig, PermissionProfileChangeEvent } from '../../../../models/dynamic-layout-renderer.model';
import { FieldPermissionProfile } from '@domain/entities/field.entity';

/**
 * Component dropdown để chọn permission profile
 * Hiển thị khi DynamicLayoutRendererConfig.showPermissionProfiles = true
 * 
 * Features:
 * - Dropdown hiển thị danh sách permission profiles
 * - Emit event khi thay đổi selection
 * - Hỗ trợ i18n và responsive design
 * - Icon hiển thị theo permission level
 */
@Component({
  selector: 'app-permission-selector',
  standalone: true,
  imports: [
    CommonModule,
    MatSelectModule,
    MatFormFieldModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './permission-selector.component.html',
  styleUrls: ['./permission-selector.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PermissionSelectorComponent implements OnInit {
  
  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   * Tuân thủ yêu cầu: component chỉ nhận 1 input object
   */
  @Input({ required: true }) config!: PermissionSelectorConfig;

  /**
   * Event emit khi thay đổi permission profile
   */
  @Output() permissionProfileChange = new EventEmitter<PermissionProfileChangeEvent>();

  // Signals để quản lý state
  selectedProfileId = signal<string>('');
  selectedProfile = signal<FieldPermissionProfile | null>(null);

  ngOnInit(): void {
    // Khởi tạo selected profile từ config
    this.selectedProfileId.set(this.config.currentPermissionProfileId);
    this.updateSelectedProfile();
  }

  /**
   * Xử lý khi user thay đổi selection
   * @param profileId ID của permission profile được chọn
   */
  onSelectionChange(profileId: string): void {
    this.selectedProfileId.set(profileId);
    this.updateSelectedProfile();
    
    const selectedProfile = this.selectedProfile();
    if (selectedProfile) {
      this.permissionProfileChange.emit({
        permissionProfileId: profileId,
        permissionProfile: selectedProfile
      });
    }
  }

  /**
   * Cập nhật selected profile object từ ID
   */
  private updateSelectedProfile(): void {
    const profile = this.config.permissionProfiles.find(
      p => p._id === this.selectedProfileId()
    );
    this.selectedProfile.set(profile || null);
  }

  /**
   * Lấy icon phù hợp với permission level
   * @param permission Permission level
   * @returns Tên icon Material
   */
  getPermissionIcon(permission: 'read_write' | 'read' | 'none'): string {
    switch (permission) {
      case 'read_write':
        return 'edit';
      case 'read':
        return 'visibility';
      case 'none':
        return 'visibility_off';
      default:
        return 'help';
    }
  }

  /**
   * Lấy màu sắc phù hợp với permission level
   * @param permission Permission level
   * @returns CSS class cho màu sắc
   */
  getPermissionColor(permission: 'read_write' | 'read' | 'none'): string {
    switch (permission) {
      case 'read_write':
        return 'text-success';
      case 'read':
        return 'text-warning';
      case 'none':
        return 'text-danger';
      default:
        return 'text-muted';
    }
  }
}
