.organization-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  .page-title {
    margin-bottom: 24px;
    color: #333;
    font-size: 24px;
  }

  .section {
    margin-bottom: 24px;

    .section-title {
      font-size: 18px;
      margin-bottom: 16px;
      color: #555;
      font-weight: 500;
    }
  }

  .row-flex {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;

    &.align-center {
      align-items: center;
    }

    &.margin-bottom {
      margin-bottom: 16px;
    }

    &.margin-top {
      margin-top: 16px;
    }

    .col-full {
      flex: 0 0 100%;
      padding: 0 10px;
      margin-bottom: 16px;
    }

    .col-half {
      flex: 0 0 calc(50% - 20px);
      padding: 0 10px;
      margin-bottom: 16px;

      @media (max-width: 768px) {
        flex: 0 0 100%;
      }
    }

    .col-third {
      flex: 0 0 calc(33.333% - 20px);
      padding: 0 10px;
      margin-bottom: 16px;

      @media (max-width: 992px) {
        flex: 0 0 calc(50% - 20px);
      }

      @media (max-width: 768px) {
        flex: 0 0 100%;
      }
    }

    .col-large {
      flex: 1;
      padding: 0 10px;
    }

    .col-small {
      flex: 0 0 60px;
      padding: 0 10px;

      &.text-center {
        text-align: center;
      }
    }
  }

  .full-width {
    width: 100%;
  }

  .add-button {
    margin-top: 8px;

    i.ki-solid {
      font-size: 16px;
      margin-right: 6px;
      vertical-align: middle;
    }
  }

  button[mat-icon-button] {
    i.ki-duotone {
      font-size: 20px;
    }
  }

  button[mat-stroked-button] {
    i.ki-solid {
      margin-right: 4px;
      vertical-align: text-bottom;
    }
  }

  // Keen Icons styles
  .ki-duotone, .ki-solid {
    display: inline-block;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .ki-trash {
    color: #f44336; // Màu đỏ tương tự với theme warn
  }

  .ki-plus {
    color: #3f51b5; // Màu tương tự với theme primary
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;

    .cancel-button {
      margin-right: 16px;
    }

    @media (max-width: 600px) {
      flex-direction: column;

      button {
        width: 100%;
        margin-bottom: 8px;
      }

      .cancel-button {
        margin-right: 0;
      }
    }
  }
}

/* Responsive styles for tablet */
@media (max-width: 991px) {
  .col-third {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .organization-container {
    padding: 16px;
  }

  .submit-button {
    min-width: 100px;
  }
}

/* Responsive styles for mobile */
@media (max-width: 767px) {
  .col-half,
  .col-third {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .col-large {
    flex: 0 0 80%;
    max-width: 80%;
  }

  .col-small {
    flex: 0 0 20%;
    max-width: 20%;
  }

  .page-title {
    font-size: 20px;
  }

  .section-title {
    font-size: 16px;
  }

  .organization-container {
    padding: 12px;
    border-radius: 4px;
  }

  .form-actions {
    flex-direction: column;
  }

  .cancel-button {
    margin-right: 0;
    margin-bottom: 12px;
    width: 100%;
  }

  .submit-button {
    width: 100%;
  }
}

/* Extra small devices */
@media (max-width: 479px) {
  .col-large {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-small {
    flex: 0 0 25%;
    max-width: 25%;
  }

  .organization-container {
    padding: 8px;
  }
}


