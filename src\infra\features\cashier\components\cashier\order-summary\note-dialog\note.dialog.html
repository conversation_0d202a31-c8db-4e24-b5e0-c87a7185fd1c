<h2 mat-dialog-title> {{ data.title }} </h2>


<mat-dialog-content>
  @if (!data.isItemNote) {
  <mat-form-field class="w-100">
    <mat-label><PERSON><PERSON><PERSON> ghi chú</mat-label>
    <mat-select required [(ngModel)]="noteType">
      <mat-option value="internalNote"><PERSON><PERSON> chú nội bộ</mat-option>
      <mat-option value="note"><PERSON><PERSON> chú toàn đơn</mat-option>
    </mat-select>
  </mat-form-field>

  @if (noteType === 'internalNote') {
  <mat-form-field class="w-100" cdkTrapFocus cdkTrapFocusAutoCapture>
    <input
      type="text"
      matInput
      [(ngModel)]="data.invoice.internalNote"
      (keydown.enter)="close()"
      cdkFocusInitial
      >
  </mat-form-field>
  } @else if (noteType === 'note') {
  <mat-form-field class="w-100" cdkTrapFocus cdkTrapFocusAutoCapture>
    <input
      type="text"
      matInput
      [(ngModel)]="data.invoice.note"
      (keydown.enter)="close()"
      cdkFocusInitial
      >
    <mat-hint class="text-danger">
      Ghi chú này sẽ hiện ra trong hóa đơn gửi khách hàng.
    </mat-hint>
  </mat-form-field>
  }
} @else {
  <mat-form-field class="w-100" cdkTrapFocus cdkTrapFocusAutoCapture>
    <input
      type="text"
      matInput
      [(ngModel)]="data.note"
      (keydown.enter)="close()"
      cdkFocusInitial
      >
  </mat-form-field>
}
</mat-dialog-content>

<mat-dialog-actions class="justify-content-center">
  <button
    mat-button
    mat-flat-button
    color="primary"
    cdkFocusInitial
    (click)="close()">
    Ok
  </button>
</mat-dialog-actions>
