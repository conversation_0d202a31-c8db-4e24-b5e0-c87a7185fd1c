import { DateFieldTypes, Field, FieldPermissionProfile, FieldType, FileFieldTypes, NumberFieldTypes, SelectFieldTypes, TextFieldTypes } from "@domain/entities/field.entity";
import { FieldComponentType } from "@domain/entities/field.entity";
import { DEFAULT_FIELD_TYPES, FIELD_ICON_MAPS, FIELD_TYPE_LABEL_KEYS, FIELD_ICON_COLORS, FIELD_BACKGROUND_COLORS } from "../constants/field-types.const";
import { TranslateService } from "@ngx-translate/core";
import { FieldComponentInterface } from "../components/layout-renderer/components/field-item/components/base/field-component.interface";

/**
 * <PERSON>ác định field type thuộc nhóm nào để điều phối đến component phù hợp
 */
export function getFieldComponentType(fieldType: FieldType): FieldComponentType {
  // Text-based fields
  const textTypes: TextFieldTypes[] = ['text', 'email', 'phone', 'url', 'search'];
  if (textTypes.includes(fieldType as TextFieldTypes)) {
    return 'text';
  }

  // Number-based fields
  const numberTypes: NumberFieldTypes[] = ['number', 'decimal', 'currency', 'percent'];
  if (numberTypes.includes(fieldType as NumberFieldTypes)) {
    return 'number';
  }

  // Select-based fields
  const selectTypes: SelectFieldTypes[] = ['picklist', 'multi-picklist', 'select', 'radio'];
  if (selectTypes.includes(fieldType as SelectFieldTypes)) {
    return 'select';
  }

  // Date-based fields
  const dateTypes: DateFieldTypes[] = ['date', 'datetime'];
  if (dateTypes.includes(fieldType as DateFieldTypes)) {
    return 'date';
  }

  // File-based fields
  const fileTypes: FileFieldTypes[] = ['file', 'image'];
  if (fileTypes.includes(fieldType as FileFieldTypes)) {
    return 'file';
  }

  // Specific field types
  if (fieldType === 'textarea') return 'textarea';
  if (fieldType === 'checkbox') return 'checkbox';
  if (fieldType === 'user') return 'user';

  // Unknown field type
  console.error(`FieldItemComponent: Unknown field type '${fieldType}'`);
  return 'unknown';
}


/**
 * Hàm tạo temporary ID cho field mới
 * Server sẽ dựa vào prefix 'temp-' để phân biệt field mới và field cũ
 */
export function generateTempFieldId(): string {
  const randomString = Math.random().toString(36).substring(2, 15);
  return `temp-${randomString}`;
}

export function createNewField(fieldType: FieldType, permissionProfiles: FieldPermissionProfile[], translateService: TranslateService) {
  const field: Field = DEFAULT_FIELD_TYPES.filter(f => f.type === fieldType)?.[0];

  if(field) {
    field._id = generateTempFieldId();
    field.label = getFieldTypeLabel(fieldType, translateService);
    field.permissionProfiles = permissionProfiles;
    return field;
  }
}


/**
 * Hàm lọc field types dựa trên danh sách supportedFieldTypes
 * @param supportedFieldTypes - Danh sách các FieldType được hỗ trợ
 * @returns Danh sách Field được filter
 */
export function getFilteredFieldTypes(supportedFieldTypes?: FieldType[]): Field[] {
  if (!supportedFieldTypes || supportedFieldTypes.length === 0) {
    return DEFAULT_FIELD_TYPES;
  }

  return DEFAULT_FIELD_TYPES.filter(fieldType =>
    supportedFieldTypes.includes(fieldType.type as FieldType)
  );
}

/**
 * Hàm lấy field type template dựa trên fieldType
 * @param fieldType - Type của field
 * @returns Field template hoặc null nếu không tìm thấy
 */
export function getFieldTypeById(fieldType: FieldType): Field | null {
  return DEFAULT_FIELD_TYPES.find(field => field.type === fieldType) || null;
}

export function getFieldTypeLabel(type: FieldType, translateService: TranslateService): string {
  return translateService.instant(FIELD_TYPE_LABEL_KEYS[type] || type);
}

export function getFieldIcon(fieldType: FieldType): string {
  return FIELD_ICON_MAPS[fieldType] || 'help_outline';
}

/**
 * Type guard để kiểm tra xem component có implement FieldComponentInterface không
 */
export function isFieldComponent(component: any): component is FieldComponentInterface {
  return component &&
         typeof component.initializeFormControl === 'function' &&
         typeof component.handleFieldValueChange === 'function' &&
         typeof component.getFieldValue === 'function' &&
         typeof component.getFieldIcon === 'function' &&
         component.config &&
         component.formControl &&
         component.valueChange;
}

// ===== PLACEHOLDER DATA FUNCTIONS =====

/**
 * Generate placeholder data cho field dựa trên field type
 * @param fieldType Loại field cần generate data
 * @param translateService Service để translate i18n keys
 * @returns Chuỗi dữ liệu mẫu đã được i18n
 */
export function generatePlaceHolderData(fieldType: FieldType, translateService: TranslateService): string {
  const placeHolderDataKey = `DYNAMIC_LAYOUT_RENDERER.PLACEHOLDER_DATA.${fieldType.toUpperCase()}`;

  // Fallback data nếu không có translation
  const fallbackData = getFallbackPlaceHolderData(fieldType, translateService);

  // Lấy translated data, nếu không có thì dùng fallback
  const translatedData = translateService.instant(placeHolderDataKey);

  // Nếu translation key không tồn tại, instant() sẽ trả về chính key đó
  return translatedData === placeHolderDataKey ? fallbackData : translatedData;
}

/**
 * Generate placeholder data cho array fields (multi-picklist, etc.)
 * @param fieldType Loại field
 * @param translateService Service để translate i18n keys
 * @param itemCount Số lượng items trong array (default: 3)
 * @returns Chuỗi các items được nối bằng dấu phẩy
 */
export function generateArrayPlaceHolderData(fieldType: FieldType, translateService: TranslateService, itemCount: number = 3): string {
  const items: string[] = [];

  for (let i = 1; i <= itemCount; i++) {
    const itemKey = `DYNAMIC_LAYOUT_RENDERER.PLACEHOLDER_DATA.${fieldType.toUpperCase()}_ITEM_${i}`;
    const fallbackItem = `${getFallbackPlaceHolderData(fieldType, translateService)} ${i}`;
    const translatedItem = translateService.instant(itemKey);

    items.push(translatedItem === itemKey ? fallbackItem : translatedItem);
  }

  return items.join(', ');
}


/**
 * Fallback placeholder data khi không có i18n
 * @param fieldType Loại field
 * @param translateService Service để translate i18n keys (cần cho recursive calls)
 * @returns Dữ liệu mẫu mặc định
 */
function getFallbackPlaceHolderData(fieldType: FieldType, translateService: TranslateService): string {
  switch (fieldType) {
    case 'text':
      return 'Lorem ipsum text';
    case 'number':
      return '123';
    case 'decimal':
      return '123.45';
    case 'currency':
      return '1,234,567 VND';
    case 'percent':
      return '85%';
    case 'email':
      return '<EMAIL>';
    case 'phone':
      return '+84 123 456 789';
    case 'url':
      return 'https://example.com';
    case 'textarea':
      return 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...';
    case 'date':
      return '15/12/2024';
    case 'datetime':
      return '15/12/2024 14:30';
    case 'file':
      return 'document.pdf';
    case 'image':
      return 'image.jpg';
    case 'checkbox':
      return 'Yes';
    case 'radio':
      return 'Tùy chọn 1';
    case 'select':
      return 'Giá trị được chọn';
    case 'picklist':
      return 'Mục danh sách';
    case 'multi-picklist':
      return generateArrayPlaceHolderData('picklist', translateService);
    case 'search':
      return 'Kết quả tìm kiếm';
    case 'user':
      return 'Nguyễn Văn A';
    default:
      return 'Dữ liệu mẫu';
  }
}

/**
 * Hàm lấy màu sắc icon cho field type
 * Thay thế việc sử dụng CSS classes để centralize color management
 * @param fieldType - Type của field
 * @returns Màu sắc icon dưới dạng hex string
 */
export function getFieldIconColor(fieldType: FieldType): string {
  return FIELD_ICON_COLORS[fieldType] || '#2196f3'; // Default blue color
}

/**
 * Hàm lấy màu sắc background cho field type
 * Thay thế việc sử dụng CSS classes để centralize color management
 * @param fieldType - Type của field
 * @returns Màu sắc background dưới dạng rgba string
 */
export function getFieldBackgroundColor(fieldType: FieldType): string {
  return FIELD_BACKGROUND_COLORS[fieldType] || 'rgba(33, 150, 243, 0.1)'; // Default blue background
}