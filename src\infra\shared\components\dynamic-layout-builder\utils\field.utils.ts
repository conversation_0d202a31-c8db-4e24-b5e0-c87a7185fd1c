import { DateFieldTypes, Field, FieldPermissionProfile, FieldType, FileFieldTypes, NumberFieldTypes, SelectFieldTypes, TextFieldTypes } from "@domain/entities/field.entity";
import { FieldComponentType } from "@domain/entities/field.entity";
import { DEFAULT_FIELD_TYPES, FIELD_ICON_MAPS, FIELD_TYPE_LABEL_KEYS, FIELD_ICON_COLORS, FIELD_BACKGROUND_COLORS, FIELD_PLACEHOLDER_DATA } from "../constants/field-types.const";
import { TranslateService } from "@ngx-translate/core";
import { FieldComponentInterface } from "../components/layout-renderer/components/field-item/components/base/field-component.interface";

/**
 * Xác định field type thuộc nhóm nào để điều phối đến component phù hợp
 */
export function getFieldComponentType(fieldType: FieldType): FieldComponentType {
  // Text-based fields
  const textTypes: TextFieldTypes[] = ['text', 'email', 'phone', 'url', 'search'];
  if (textTypes.includes(fieldType as TextFieldTypes)) {
    return 'text';
  }

  // Number-based fields
  const numberTypes: NumberFieldTypes[] = ['number', 'decimal', 'currency', 'percent'];
  if (numberTypes.includes(fieldType as NumberFieldTypes)) {
    return 'number';
  }

  // Select-based fields
  const selectTypes: SelectFieldTypes[] = ['picklist', 'multi-picklist', 'select', 'radio'];
  if (selectTypes.includes(fieldType as SelectFieldTypes)) {
    return 'select';
  }

  // Date-based fields
  const dateTypes: DateFieldTypes[] = ['date', 'datetime'];
  if (dateTypes.includes(fieldType as DateFieldTypes)) {
    return 'date';
  }

  // File-based fields
  const fileTypes: FileFieldTypes[] = ['file', 'image'];
  if (fileTypes.includes(fieldType as FileFieldTypes)) {
    return 'file';
  }

  // Specific field types
  if (fieldType === 'textarea') return 'textarea';
  if (fieldType === 'checkbox') return 'checkbox';
  if (fieldType === 'user') return 'user';

  // Unknown field type
  console.error(`FieldItemComponent: Unknown field type '${fieldType}'`);
  return 'unknown';
}


/**
 * Hàm tạo temporary ID cho field mới
 * Server sẽ dựa vào prefix 'temp-' để phân biệt field mới và field cũ
 */
export function generateTempFieldId(): string {
  const randomString = Math.random().toString(36).substring(2, 15);
  return `temp-${randomString}`;
}

export function createNewField(fieldType: FieldType, permissionProfiles: FieldPermissionProfile[], translateService: TranslateService) {
  const field: Field = DEFAULT_FIELD_TYPES.filter(f => f.type === fieldType)?.[0];

  if(field) {
    field._id = generateTempFieldId();
    field.label = getFieldTypeLabel(fieldType, translateService);
    field.permissionProfiles = permissionProfiles;
    return field;
  }
}


/**
 * Hàm lọc field types dựa trên danh sách supportedFieldTypes
 * @param supportedFieldTypes - Danh sách các FieldType được hỗ trợ
 * @returns Danh sách Field được filter
 */
export function getFilteredFieldTypes(supportedFieldTypes?: FieldType[]): Field[] {
  if (!supportedFieldTypes || supportedFieldTypes.length === 0) {
    return DEFAULT_FIELD_TYPES;
  }

  return DEFAULT_FIELD_TYPES.filter(fieldType =>
    supportedFieldTypes.includes(fieldType.type as FieldType)
  );
}

/**
 * Hàm lấy field type template dựa trên fieldType
 * @param fieldType - Type của field
 * @returns Field template hoặc null nếu không tìm thấy
 */
export function getFieldTypeById(fieldType: FieldType): Field | null {
  return DEFAULT_FIELD_TYPES.find(field => field.type === fieldType) || null;
}

export function getFieldTypeLabel(type: FieldType, translateService: TranslateService): string {
  return translateService.instant(FIELD_TYPE_LABEL_KEYS[type] || type);
}

export function getFieldIcon(fieldType: FieldType): string {
  return FIELD_ICON_MAPS[fieldType] || 'help_outline';
}

/**
 * Type guard để kiểm tra xem component có implement FieldComponentInterface không
 */
export function isFieldComponent(component: any): component is FieldComponentInterface {
  return component &&
         typeof component.initializeFormControl === 'function' &&
         typeof component.handleFieldValueChange === 'function' &&
         typeof component.getFieldValue === 'function' &&
         typeof component.getFieldIcon === 'function' &&
         component.config &&
         component.formControl &&
         component.valueChange;
}

// ===== PLACEHOLDER DATA FUNCTIONS =====

/**
 * Generate placeholder data cho field dựa trên field type
 * Sử dụng centralized constant mapping từ FIELD_PLACEHOLDER_DATA
 * @param fieldType Loại field cần generate data
 * @param translateService Service để translate i18n keys
 * @returns Chuỗi dữ liệu mẫu đã được i18n
 */
export function generatePlaceHolderData(fieldType: FieldType, translateService: TranslateService): string {
  // Lấy i18n key từ constant mapping
  const placeHolderDataKey = FIELD_PLACEHOLDER_DATA[fieldType];

  if (!placeHolderDataKey) {
    console.warn(`No placeholder data key found for field type: ${fieldType}`);
    return 'Sample data';
  }

  // Lấy translated data
  const translatedData = translateService.instant(placeHolderDataKey);

  // Nếu translation key không tồn tại, instant() sẽ trả về chính key đó
  // Trong trường hợp này, trả về fallback data đơn giản
  if (translatedData === placeHolderDataKey) {
    return getSimpleFallbackData(fieldType);
  }

  return translatedData;
}


/**
 * Simple fallback data khi không có i18n translation
 * @param fieldType Loại field
 * @returns Dữ liệu mẫu mặc định đơn giản
 */
function getSimpleFallbackData(fieldType: FieldType): string {
  switch (fieldType) {
    case 'text':
      return 'Sample text';
    case 'number':
      return '123';
    case 'decimal':
      return '123.45';
    case 'currency':
      return '$1,234.56';
    case 'percent':
      return '85%';
    case 'email':
      return '<EMAIL>';
    case 'phone':
      return '****** 456 789';
    case 'url':
      return 'https://example.com';
    case 'textarea':
      return 'Sample long text content...';
    case 'date':
      return '12/15/2024';
    case 'datetime':
      return '12/15/2024 2:30 PM';
    case 'file':
      return 'document.pdf';
    case 'image':
      return 'image.jpg';
    case 'checkbox':
      return 'Yes';
    case 'radio':
      return 'Option 1';
    case 'select':
      return 'Selected value';
    case 'picklist':
      return 'List item';
    case 'multi-picklist':
      return 'Item 1, Item 2';
    case 'search':
      return 'Search result';
    case 'user':
      return 'John Doe';
    default:
      return 'Sample data';
  }
}

/**
 * Hàm lấy màu sắc icon cho field type
 * Thay thế việc sử dụng CSS classes để centralize color management
 * @param fieldType - Type của field
 * @returns Màu sắc icon dưới dạng hex string
 */
export function getFieldIconColor(fieldType: FieldType): string {
  return FIELD_ICON_COLORS[fieldType] || '#2196f3'; // Default blue color
}

/**
 * Hàm lấy màu sắc background cho field type
 * Thay thế việc sử dụng CSS classes để centralize color management
 * @param fieldType - Type của field
 * @returns Màu sắc background dưới dạng rgba string
 */
export function getFieldBackgroundColor(fieldType: FieldType): string {
  return FIELD_BACKGROUND_COLORS[fieldType] || 'rgba(33, 150, 243, 0.1)'; // Default blue background
}