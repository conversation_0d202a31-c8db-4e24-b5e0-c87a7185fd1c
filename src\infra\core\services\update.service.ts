import { Injectable } from '@angular/core';
import { HttpContext } from '@angular/common/http';
import { Subject } from 'rxjs';
import { PosUpdateData } from 'salehub_shared_contracts';
import { HttpService } from './http.service';
import { AppChannelService } from './app_channel.service';
import { SocketService } from './socket.service';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';
import { UpdateDataStore } from '../store/update_data.store';
/**
 * https://www.angularjswiki.com/angular/progress-bar-in-angular-mat-progress-bar-examplematerial-design/
 * https://long2know.com/2017/01/angular2-http-interceptor-and-loading-indicator/
 */
@Injectable({
  providedIn: 'root'
})
export class AppUpdateService {
  private originTitle!: string;
  private itvUpdate!: ReturnType<typeof setTimeout>;

  private onConnIndex!: number;
  private disConnFunc!: () => void;
  private POS_UPDATE_ROOM!: string;

  constructor(
    private http: HttpService,
    private appChannel: AppChannelService,
    private socketService: SocketService,
    private updateDataStore: UpdateDataStore
  ) {
    this.updateOriginTitle();
  }

  public updateOriginTitle() {
    this.originTitle = document.title;
  }

  public updatePosData(d: PosUpdateData) {
    if(!d) {
      return;
    }

    // console.log('updateData', d);

    const data = this.updateDataStore.updatePosData(d);

    if(this.originTitle) {
      if(data?.countPendingOrders && data.countPendingOrders > 0) {
        document.title = `${this.originTitle} (${data.countPendingOrders})`;
      } else {
        document.title = this.originTitle;
      }
    }

    if(d?.appVersion) {
      this.appChannel.updateApp(d.appVersion);
    }
  }

  private clearListeners() {
    this.socketService.leave('cashier', this.POS_UPDATE_ROOM, () => {});
    this.socketService.getCashierSocket()?.off('UPDATED_POS_DATA');
    this.socketService.getCashierSocket()?.off('disconnect', this.disConnFunc);

    if(this.itvUpdate) {
      clearTimeout(this.itvUpdate);
    }
  }

  private reset() {
    this.clearListeners();
    this.socketService.removeOnConnectFunction('cashier', this.onConnIndex);
  }

  private internalUpdate() {
    this.socketService.join('cashier', this.POS_UPDATE_ROOM, (err?: Error) => {
      if(!err) {
        this.socketService.getCashierSocket()?.on(
          'UPDATED_POS_DATA',
          d => this.updatePosData(d)
        );
      } else {
        this.xhrUpdate(() => {
          this.reset();
          this.internalUpdate();
        });
      }
    });

    this.intervalManualUpdate();

    /**
     * khi socket bị disconnect
     * thì data trong khoảng thời gian
     * sau khi bị disconnect -> connect lại
     * và toàn bộ các sự kiện on trước khi bị disconnect
     * đều bị mất hết
     *
     * không listen vào on connect được
     * vì socketService.getSocket
     * giờ nó đã là 1 thằng khác với thằng socket ban đầu
     */
    this.disConnFunc = () => {
      console.log('received disconnect from updater');
      this.clearListeners();
    };

    this.onConnIndex = this.socketService.pushOnConnectFunction(
      'cashier',
      () => {
        this.manualUpdate();
        this.internalUpdate();
      }
    );
    this.socketService.getCashierSocket()?.on('disconnect', this.disConnFunc);
  }

  private manualUpdate(callback?: () => void) {
    this.socketService.getCashierSocket()
      ?.timeout(5000)
      ?.emit('pos_update', (e, data) => {
        if(e) {
          this.xhrUpdate(callback);
        } else if(data) {
          this.updatePosData(data);

          if(callback) {
            callback();
          }
        }
      });
  }

  /**
   * đề phòng lúc socket broadcast xuống có những socket không nhận đc
   * thì emit theo thời gian để lấy manual
   */
  private intervalManualUpdate() {
    if(this.itvUpdate) {
      clearTimeout(this.itvUpdate);
    }

    this.itvUpdate = setTimeout(() => {
      this.manualUpdate(() => this.intervalManualUpdate());
    }, 60000);
  }

  private xhrUpdate(callback?: () => void) {
    return this.http.get<{pos_updates: PosUpdateData}>(
      'cashier',
      'pos_updates',
      {
        timeout: 5000,
        context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
          hideError: true
        })
      }
    )
      .subscribe(
        {
          next: (d) => {
            if(d?.pos_updates) {
              this.updatePosData(d.pos_updates);
            }

            if(callback) {
              callback();
            }
          },
          error: () => {
            if(callback) {
              callback();
            }
          }
        }
      );
  }

  init() {
    this.POS_UPDATE_ROOM = 'POS_UPDATE';
    this.internalUpdate();
  }

  destroy() {
    this.reset();
  }
}
