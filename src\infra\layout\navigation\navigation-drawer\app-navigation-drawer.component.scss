@use '../../../shared/styles/_variable' as *;

app-navigation-drawer {
  display: block;
  position: relative;
  padding-top: #{$mobileHeaderHeight + 10}+px;
  height: 100%;


  $menuActiveColor: #edb304;

  .navigation-drawer-header {
    position: absolute;
    top: 0;
    // left: 0;
    height: $mobileHeaderHeight+px;
    width: 100%;

    .icon {
      font-size: 20px;
      color: #99a1b7;
    }
  }
  .navigation-drawer-content {
    display: block;
    height: 100%;


    &-inner {
      // quan trọng để tính offsetTop trong scrollToElement
      position: relative;
      height: 100%;
      overflow: auto;
      scrollbar-width: thin;
    }
  }

  .menu-heading {
    color: #696f81;
    padding: 12px;
    height: 50px;
    font-weight: 700;
    font-size: 13px;

    &-text {
      color: #252f4a;
    }

    &.active {
      .menu-heading-text {
        color: $menuActiveColor;
      }
    }
  }
  .menu-heading-icon {
    color: #99a1b7;
    transition: transform 0.4s ease;

    &.icon-animating {
      animation: iconPulse 0.4s ease;
    }
  }

  .sb-accordion {
    .p-accordionpanel,
    .p-accordionheader,
    .p-accordioncontent-content {
      background: none !important;
    }
    .p-accordionpanel {
      border: 0;
    }
    .p-accordionheader {
      padding: 0;

      &.disabled {
        opacity: .4;
      }
    }
    .p-accordioncontent-content {
      position: relative;
      padding: 0 0 0 8px;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 23px;
        width: 1px;
        height: 100%;
        background: #dbe1eb;
      }
    }
  }
  .sb-main-accordion {
    padding-right: 10px;
  }
  .sb-sub-accordion {
    .menu-heading  {
      margin-left: 27px;
    }
    p-accordion-content{
      margin-left: 20px;
    }
  }

  .navigation-drawer-menu-item {
    position: relative;
    padding: 10px 15px;
    color: #465975;
    margin-bottom: 8px;
    padding-left: 37px;
    border-radius: 5px;
    font-weight: 500;
    font-size: 1rem;
    letter-spacing: -.2px;

    &.disabled {
      &::before,
      span {
        opacity: 0.4;
      }
      i {
        opacity: .6;
      }

      &:hover {
        background: none;
        color: inherit;
      }
    }

    .icon {
      font-size: 18px;
      background: $bodyBackgroundColor;
    }

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 12px;
      width: 8px;
      height: 8px;
      margin-top: -4px;
      background: #ffc926;
      border-radius: 100%;
    }
    &:hover {
      background: rgb(221 221 221 / 68%);
      color: #465975;
    }

    &.active2,
    &.active2:hover {
      background: rgb(14 26 43 / 71%);
      color: #ffc926;

      .icon {
        background: none;
      }
    }
  }
  .p-accordionheader-toggle-icon {
    display: none !important;
  }
  /* Mặc định hiển thị ki-plus, ẩn ki-minus */
  .custom-toggle-icon .toggle-plus {
    display: inline-block;
  }
  .custom-toggle-icon .toggle-minus {
    display: none;
  }

  /* Khi tab mở (active), hiển thị ki-minus, ẩn ki-plus */
  p-accordiontab.p-accordionpanel-active  .custom-toggle-icon .toggle-plus {
    display: none;
  }
  p-accordiontab.p-accordionpanel-active  .custom-toggle-icon .toggle-minus {
    display: inline-block;
  }

  ng-scrollbar {
    --scrollbar-border-radius: 3px;
    --scrollbar-thumb-color: rgb(0 0 0 / 7%);
  }
  .ng-scroll-content {
    padding-right: 15px !important;
  }

  .drag-cursor {
    cursor: move;
  }

  .cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
      0 8px 10px 1px rgba(0, 0, 0, 0.14),
      0 3px 14px 2px rgba(0, 0, 0, 0.12);
    background-color: white;
  }

  .cdk-drag-placeholder {
    opacity: 0;
  }

  .cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .cdk-drop-list-dragging .mat-row:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
}




@media screen and (min-width: #{$breakpointMinHorizontalWidth}px) {
  app-navigation-drawer {
    height: 100vh;
    padding-top: #{$desktopHeaderHeight + 10}+px;

    .navigation-drawer-header {
      height: $desktopHeaderHeight+px;
    }
    .navigation-drawer-menu-item {
      font-size: 1rem;
    }
  }
}
