import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CreateOrderRequest } from 'salehub_shared_contracts/requests/sales/create_order';
import { OrderDetailsComponent } from '../../order-info/order-details/order-details.component';
import { CustomerInfoComponent } from '../../order-info/customer-info/customer-info.component';
import { ShippingInfoComponent } from '../../order-info/shipping-info/shipping-info.component';
import { OrderSummaryComponent } from '../../order-info/order-summary/order-summary.component';

/**
 * Component hiển thị nội dung tab đơn hàng
 */
@Component({
  selector: 'app-order-tab-content',
  standalone: true,
  imports: [
    CommonModule,
    OrderDetailsComponent,
    CustomerInfoComponent,
    ShippingInfoComponent,
    OrderSummaryComponent
  ],
  templateUrl: './order-tab-content.component.html',
  styleUrls: ['./order-tab-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OrderTabContentComponent {
  /**
   * Dữ liệu đơn hàng của tab hiện tại
   */
  @Input() order!: CreateOrderRequest;

  /**
   * EventEmitter khi dữ liệu đơn hàng thay đổi
   */
  @Output() orderUpdated = new EventEmitter<CreateOrderRequest>();

  /**
   * Cập nhật dữ liệu đơn hàng
   * @param updatedOrder Dữ liệu đơn hàng đã cập nhật
   */
  updateOrder(updatedOrder: CreateOrderRequest): void {
    this.orderUpdated.emit(updatedOrder);
  }
}
