<div class="bg-white w-100 px-3 mt-4">
  <div class="text-uppercase pb-5 pt-4"><b><PERSON><PERSON><PERSON> chuyển</b></div>

  <mat-form-field appearance="outline" floatLabel="always" class="w-100">
    <mat-label>Đơn vị vận chuyển</mat-label>

    <mat-select
      [(ngModel)]="inputDeliveryCompanyId"
      (ngModelChange)="changeDeliveryCompany()"
      required
      >

      @if (order().delivery.company.name && order().delivery.company.id) {
      <mat-select-trigger>
        {{order().delivery.company.name}}
        @if (order().delivery.company.id && deliveryFee()[order().delivery.company.id || '']) {
          - {{ deliveryFee()[order().delivery.company.id || ''] | formatString: 'money' }}
        }
      </mat-select-trigger>
      }

      @for (company of deliveryCompanies(); track company) {
        <mat-option
          [value]="company.id"
          class="py-2 px-3"
          >
          <div class="fw-bold">
            {{ company.name }}
            @if (deliveryFee()[company.id]) {
              - {{ deliveryFee()[company.id] | formatString: 'money' }}
            }

          </div>
          <div class="small">
            {{ company.desc }}
          </div>
        </mat-option>
      }

    </mat-select>
  </mat-form-field>

  <mat-form-field
    appearance="outline"
    floatLabel="always"
    class="w-100"
    [class.mat-form-field-invalid]=hasDeliveryFeeError()
    >
    <mat-label>Phí ship</mat-label>
    <input
      type="number"
      matInput
      pattern="[0-9]*"
      inputmode="numeric"
      [(ngModel)]="order().delivery.fee"
      >
      @if (hasDeliveryFeeError()) {
      <mat-hint class="mat-mdc-form-field-error">Vui lòng điền phí ship hoặc quãng đường.</mat-hint>
      }
  </mat-form-field>

  <mat-form-field appearance="outline" floatLabel="always" class="w-100">
    <mat-label>Quãng đường</mat-label>
    <input-float
      [value]="deliveryDistance()"
      (inputChange)="updateDeliveryFee($event)"
      >
    </input-float>
    <input matInput type="text" hidden>

    @if(googleMapUrl()) {
    <a
      matSuffix
      [href]="googleMapUrl()"
      target="_blank"
      class="text-dark me-3"
      >
      <span class="material-symbols-outlined">
        map
        </span>
    </a>
    }
  </mat-form-field>

  <!-- <mat-form-field appearance="outline" floatLabel="always" class="w-100">
    <mat-label>Ghi chú</mat-label>
    <input
      type="text"
      matInput
      [(ngModel)]="order.delivery.note"

      >
  </mat-form-field> -->

  <!-- <mat-form-field appearance="outline" floatLabel="always" class="w-100">
    <mat-label>Tên tài xế</mat-label>
    <input
      type="text"
      matInput
      [(ngModel)]="order.delivery.driver.name"

      >
  </mat-form-field>

  <mat-form-field appearance="outline" floatLabel="always" class="w-100">
    <mat-label>Số ĐT tài xế</mat-label>
    <input
      type="text"
      matInput
      [(ngModel)]="order.delivery.driver.phoneNumber"

      >
  </mat-form-field> -->
</div>
