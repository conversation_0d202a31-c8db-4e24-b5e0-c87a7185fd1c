<!-- Section Container -->
@if (hasVisibleFields()) {
  <mat-card class="section-card mb-4">
    
    <!-- Section Header -->
    @if (shouldShowTitle()) {
      <mat-card-header class="section-header">
        <mat-card-title class="section-title">
          <mat-icon class="section-icon me-2">folder</mat-icon>
          {{ config.section.title }}
        </mat-card-title>
      </mat-card-header>
    }

    <!-- Section Content -->
    <mat-card-content class="section-content">

      <!-- Dynamic CSS-based Column Layout -->
      <!-- CSS sẽ tự động xử lý việc chia 1 hoặc 2 cột dựa trên isDoubleColumn() và responsive breakpoints -->
      <div class="section-fields-container"
           [class.single-column]="!isDoubleColumn()"
           [class.double-column]="isDoubleColumn()">
        @for (field of visibleFields(); track field._id) {
          <app-field-item
            [config]="createFieldItemConfig(field)"
            (valueChange)="onFieldValueChange($event)"
            class="field-item">
          </app-field-item>
        }
      </div>
    </mat-card-content>
  </mat-card>
}
