

import {
  ProductModifierGroup,
  ProductList,
  ProductVariants,
} from 'salehub_shared_contracts/requests/shared/product';
import { EmbeddedProductBatch, ProductUnit } from 'salehub_shared_contracts';
import { mockBrandList, mockCategoryList, mockWarehouseList } from '../shared/list.mock';
import { EmbeddedProductSerial } from 'salehub_shared_contracts/entities/ims/product/product_serial';


export const mockVariantList: ProductVariants = [
  { variantId: 'v1', attributes: [{ name: 'size', value: 'L' }, { name: 'color', value: 'Xanh' }] },
  { variantId: 'v2', attributes: [{ name: 'size', value: 'L' }, { name: 'color', value: 'Đỏ' }] },
  { variantId: 'v3', attributes: [{ name: 'size', value: 'XL' }, { name: 'color', value: 'Xanh' }] },
  { variantId: 'v4', attributes: [{ name: 'size', value: 'XL' }, { name: 'color', value: 'Đỏ' }] },
  { variantId: 'v5', attributes: [{ name: 'size', value: 'XL' }, { name: 'color', value: 'Đen' }] }
];

export const mockProductModifierGroupList: ProductModifierGroup[] = [
  // Nhóm 1: Topping trà sữa
  {
    _id: 'mg1',
    name: 'Topping Trà Sữa',
    items: [
      { productId: 'p1', name: 'Trân châu đen', price: 10000, cost: 5000, image: 'https://picsum.photos/200/200?random=1', sku: 'T01' },
      { productId: 'p2', name: 'Trân châu trắng', price: 12000, cost: 6000, image: 'https://picsum.photos/200/200?random=2', sku: 'T02' },
      { productId: 'p3', name: 'Pudding', price: 15000, cost: 7000, image: 'https://picsum.photos/200/200?random=3', sku: 'T03' },
      { productId: 'p4', name: 'Thạch trái cây', price: 12000, cost: 5500, image: 'https://picsum.photos/200/200?random=4', sku: 'T04' },
      { productId: 'p5', name: 'Đậu đỏ', price: 10000, cost: 4500, image: 'https://picsum.photos/200/200?random=5', sku: 'T05' }
    ]
  },
  // Nhóm 2: Topping phở
  {
    _id: 'mg2',
    name: 'Topping Phở',
    items: [
      { productId: 'p6', name: 'Thịt bò tái', price: 20000, cost: 10000, image: 'https://picsum.photos/200/200?random=6', sku: 'P01' },
      { productId: 'p7', name: 'Thịt bò chín', price: 20000, cost: 9500, image: 'https://picsum.photos/200/200?random=7', sku: 'P02' },
      { productId: 'p8', name: 'Chả quế', price: 15000, cost: 7000, image: 'https://picsum.photos/200/200?random=8', sku: 'P03' },
      { productId: 'p9', name: 'Trứng luộc', price: 10000, cost: 4000, image: 'https://picsum.photos/200/200?random=9', sku: 'P04' },
      { productId: 'p10', name: 'Bánh phở thêm', price: 5000, cost: 2000, image: 'https://picsum.photos/200/200?random=10', sku: 'P05' }
    ]
  },
  // Nhóm 3: Topping bánh mì
  {
    _id: 'mg3',
    name: 'Topping Bánh Mì',
    items: [
      { productId: 'p11', name: 'Thịt nguội', price: 10000, cost: 5000, image: 'https://picsum.photos/200/200?random=11', sku: 'B01' },
      { productId: 'p12', name: 'Chả lụa', price: 10000, cost: 4500, image: 'https://picsum.photos/200/200?random=12', sku: 'B02' },
      { productId: 'p13', name: 'Trứng ốp la', price: 8000, cost: 3000, image: 'https://picsum.photos/200/200?random=13', sku: 'B03' },
      { productId: 'p14', name: 'Pate thêm', price: 5000, cost: 2000, image: 'https://picsum.photos/200/200?random=14', sku: 'B04' },
      { productId: 'p15', name: 'Phô mai', price: 12000, cost: 6000, image: 'https://picsum.photos/200/200?random=15', sku: 'B05' }
    ]
  },
  // Nhóm 4: Topping cà phê
  {
    _id: 'mg4',
    name: 'Topping Cà Phê',
    items: [
      { productId: 'p16', name: 'Sữa đặc', price: 5000, cost: 2000, image: 'https://picsum.photos/200/200?random=16', sku: 'C01' },
      { productId: 'p17', name: 'Kem tươi', price: 10000, cost: 4500, image: 'https://picsum.photos/200/200?random=17', sku: 'C02' },
      { productId: 'p18', name: 'Sốt caramel', price: 8000, cost: 3500, image: 'https://picsum.photos/200/200?random=18', sku: 'C03' },
      { productId: 'p19', name: 'Sốt socola', price: 8000, cost: 3500, image: 'https://picsum.photos/200/200?random=19', sku: 'C04' },
      { productId: 'p20', name: 'Hạt dẻ', price: 12000, cost: 6000, image: 'https://picsum.photos/200/200?random=20', sku: 'C05' }
    ]
  },
  // Nhóm 5: Topping sinh tố
  {
    _id: 'mg5',
    name: 'Topping Sinh Tố',
    items: [
      { productId: 'p21', name: 'Dâu tây tươi', price: 15000, cost: 7000, image: 'https://picsum.photos/200/200?random=21', sku: 'S01' },
      { productId: 'p22', name: 'Chuối lát', price: 10000, cost: 4000, image: 'https://picsum.photos/200/200?random=22', sku: 'S02' },
      { productId: 'p23', name: 'Hạt chia', price: 12000, cost: 5500, image: 'https://picsum.photos/200/200?random=23', sku: 'S03' },
      { productId: 'p24', name: 'Yogurt', price: 10000, cost: 4500, image: 'https://picsum.photos/200/200?random=24', sku: 'S04' },
      { productId: 'p25', name: 'Mật ong', price: 8000, cost: 3000, image: 'https://picsum.photos/200/200?random=25', sku: 'S05' }
    ]
  },
  // Nhóm 6: Topping đồ ăn vặt
  {
    _id: 'mg6',
    name: 'Topping Đồ Ăn Vặt',
    items: [
      { productId: 'p26', name: 'Phô mai que', price: 15000, cost: 7000, image: 'https://picsum.photos/200/200?random=26', sku: 'D01' },
      { productId: 'p27', name: 'Khoai tây chiên', price: 20000, cost: 9000, image: 'https://picsum.photos/200/200?random=27', sku: 'D02' },
      { productId: 'p28', name: 'Gà rán miếng', price: 25000, cost: 12000, image: 'https://picsum.photos/200/200?random=28', sku: 'D03' },
      { productId: 'p29', name: 'Sốt phô mai', price: 10000, cost: 4500, image: 'https://picsum.photos/200/200?random=29', sku: 'D04' },
      { productId: 'p30', name: 'Bắp rang bơ', price: 15000, cost: 6000, image: 'https://picsum.photos/200/200?random=30', sku: 'D05' }
    ]
  }
];
export const mockProductUnits: ProductUnit[] = [
  // Đơn vị tính cho thuốc (ví dụ: thuốc kháng sinh)
  {
    unitName: 'viên',
    conversionRate: 1,
    isBaseUnit: true,
    price: 5000,
    cost: 3000,
    barcode: '8931234567890'
  },
  {
    unitName: 'vỉ',
    conversionRate: 10, // 1 vỉ = 10 viên
    isBaseUnit: false,
    price: 45000, // Giá rẻ hơn nếu mua theo vỉ
    cost: 25000,
    barcode: '8931234567891'
  },
  {
    unitName: 'hộp',
    conversionRate: 100, // 1 hộp = 100 viên (10 vỉ)
    isBaseUnit: false,
    price: 400000, // Giá ưu đãi khi mua hộp
    cost: 220000,
    barcode: '8931234567892'
  },

  // Đơn vị tính cho thực phẩm (ví dụ: nước uống)
  {
    unitName: 'chai',
    conversionRate: 1,
    isBaseUnit: true,
    price: 10000,
    cost: 6000,
    barcode: '8939876543210'
  },
  {
    unitName: 'lốc',
    conversionRate: 6, // 1 lốc = 6 chai
    isBaseUnit: false,
    price: 55000, // Giá rẻ hơn khi mua lốc
    cost: 33000,
    barcode: '8939876543211'
  },
  {
    unitName: 'thùng',
    conversionRate: 24, // 1 thùng = 24 chai (4 lốc)
    isBaseUnit: false,
    price: 200000, // Giá ưu đãi khi mua thùng
    cost: 120000,
    barcode: '8939876543212'
  },

  // Đơn vị tính cho hàng hóa (ví dụ: bút bi)
  {
    unitName: 'cây',
    conversionRate: 1,
    isBaseUnit: true,
    price: 3000,
    cost: 1500,
    barcode: '8934567891234'
  },
  {
    unitName: 'hộp',
    conversionRate: 10, // 1 hộp = 10 cây
    isBaseUnit: false,
    price: 25000,
    cost: 12000,
    barcode: '8934567891235'
  },
  {
    unitName: 'lốc',
    conversionRate: 50, // 1 lốc = 50 cây (5 hộp)
    isBaseUnit: false,
    price: 120000,
    cost: 60000,
    barcode: '8934567891236'
  },

  // Đơn vị tính cho thực phẩm (ví dụ: bánh kẹo)
  {
    unitName: 'gói',
    conversionRate: 1,
    isBaseUnit: true,
    price: 15000,
    cost: 8000,
    barcode: '8936543219876'
  },
  {
    unitName: 'hộp',
    conversionRate: 12, // 1 hộp = 12 gói
    isBaseUnit: false,
    price: 165000, // Giá rẻ hơn khi mua hộp
    cost: 90000,
    barcode: '8936543219877'
  },
  {
    unitName: 'thùng',
    conversionRate: 48, // 1 thùng = 48 gói (4 hộp)
    isBaseUnit: false,
    price: 600000,
    cost: 320000,
    barcode: '8936543219878'
  }
];
export const mockBatches: EmbeddedProductBatch[] = [
  {
    _id: "prod_batch_001",
    batchCode: "BATCH2025-001",
    manufactureDate: new Date("2025-01-10"),
    expiryDate: new Date("2026-01-10"),
  },
  {
    _id: "prod_batch_002",
    batchCode: "BATCH2025-002",
    manufactureDate: new Date("2025-02-15"),
    expiryDate: new Date("2026-02-15"),
  },
  {
    _id: "prod_batch_003",
    batchCode: "BATCH2025-003",
    manufactureDate: new Date("2025-03-20"),
    expiryDate: new Date("2026-03-20"),
  },
];

const mockSerials: EmbeddedProductSerial[] = [
  { _id: 'serial1', serialNumber: 'SN001', status: 'in_stock' },
  { _id: 'serial2', serialNumber: 'SN002', status: 'assigned' },
  { _id: 'serial3', serialNumber: 'SN003', status: 'sold' }
];

// Hàm tạo tên sản phẩm ngẫu nhiên
const generateProductName = (index: number): string => {
  const prefixes = ['Áo', 'Quần', 'Giày', 'Túi', 'Đồng hồ', 'Mũ', 'Phụ kiện'];
  const suffixes = ['thời trang', 'cao cấp', 'thể thao', 'casual', 'trẻ em', 'unisex'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
  return `${prefix} ${suffix} ${index}`;
};
// Hàm hỗ trợ chọn ngẫu nhiên phần tử từ mảng
const getRandomItems = <T>(array: T[], min: number, max: number): T[] => {
  const count = Math.floor(Math.random() * (max - min + 1)) + min;
  const shuffled = array.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

// Tạo mockProductList
export const mockProductList: ProductList = Array.from({ length: 100 }, (_, index) => {
  const productId = `prod${index + 1}`;
  const trackByBatch = Math.random() > 0.2 || index === 0; // 50% sản phẩm có batches
  const trackBySerial = Math.random() > 0.2 || index === 0; // 50% sản phẩm có batches
  const hasVariants = Math.random() > 0.2 || index === 0; // 50% sản phẩm có variants
  const hasModifiers = Math.random() > 0.5 || index === 0; // 50% sản phẩm có linkedModifierGroupIds
  const hasMultipleUnits = Math.random() > 0.5 || index === 0; // 50% sản phẩm có linkedModifierGroupIds
  const warehouseIds = getRandomItems(mockWarehouseList, 1, 4).map((wh) => wh._id);

  return {
    sku: `SKU-${index + 1}`,
    productId,
    warehouseIds, // 1-4 kho
    warehouseStock: warehouseIds.reduce((acc, whId) => {
      acc[whId] = Math.floor(Math.random() * 1000) + 100;
      return acc;
    }, {} as Record<string, number>),
    name: generateProductName(index + 1),
    price: Math.floor(Math.random() * 500000) + 50000, // Giá từ 50k đến 550k
    cost: Math.floor(Math.random() * 300000) + 30000, // Chi phí từ 30k đến 330k
    image: `https://picsum.photos/200/200?random=${index + 1}`,
    categoryIds: getRandomItems(mockCategoryList, 1, 3).map((cat) => cat._id), // 1-3 danh mục
    brandIds: getRandomItems(mockBrandList, 1, 2).map((brand) => brand._id), // 1-2 thương hiệu
    linkedModifierGroupIds: hasModifiers
      ? getRandomItems(mockProductModifierGroupList, 1, 3).map((mg) => mg._id) // 1-3 nhóm topping
      : undefined,

    variants: hasVariants ? mockVariantList : undefined,
    units: hasMultipleUnits ? mockProductUnits : undefined,
    baseUnit: hasMultipleUnits ? mockProductUnits[0] : undefined,
    batches: trackByBatch ? mockBatches : undefined,
    serials: trackBySerial ? mockSerials : undefined,

    trackByBatch,
    trackBySerial,
    trackInventory: true
  };
});
