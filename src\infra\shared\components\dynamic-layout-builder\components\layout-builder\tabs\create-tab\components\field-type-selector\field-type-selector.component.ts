import { Component, EventEmitter, Input, Output, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DragData, TrackByFunction } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.model';
import { Field, FieldType } from '@domain/entities/field.entity';
import { getFieldIcon, getFieldTypeLabel, getFieldIconColor, getFieldBackgroundColor } from '@/shared/components/dynamic-layout-builder/utils/field.utils';

/**
 * Component để chọn loại field khi thêm field mới vào section
 * Hiển thị dropdown hoặc danh sách button để chọn loại field
 * Hỗ trợ drag & drop để kéo field type vào section
 */
@Component({
  selector: 'app-field-type-selector',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatListModule,
    MatDividerModule,
    TranslateModule
  ],
  templateUrl: './field-type-selector.component.html',
  styleUrls: ['./field-type-selector.component.scss']
})
export class FieldTypeSelectorComponent {
  private translateService = inject(TranslateService);

  /**
   * Danh sách field types có sẵn
   */
  @Input() availableFieldTypes: Field[] = [];

  /**
   * Event được emit khi người dùng chọn field type
   */
  @Output() fieldTypeSelected = new EventEmitter<Field>();

  /**
   * Signal cho basic field types (text, number, email, etc.)
   */
  fieldTypes = signal<Field[]>([]);


  /**
   * Signal để track drag state cho visual feedback
   */
  isDragging = signal<boolean>(false);

  /**
   * Field type hiện tại đang được drag
   */
  currentDraggedField = signal<Field | null>(null);

  ngOnInit(): void {
    this.categorizeFieldTypes();
  }

  ngOnChanges(): void {
    this.categorizeFieldTypes();
  }

  /**
   * Phân loại field types thành các nhóm
   */
  private categorizeFieldTypes(): void {
    this.fieldTypes.set(this.availableFieldTypes);
  }

  /**
   * Tạo drag data cho field type với clone behavior
   * Đảm bảo field type được clone thay vì move
   */
  createDragData(fieldType: Field): DragData {
    return {
      type: 'field-type',
      fieldType: { ...fieldType } // Clone field type để tránh reference issues
    };
  }

  /**
   * HTML5 Drag Start Event Handler
   */
  onDragStart(event: DragEvent, fieldType: Field): void {
    // Set drag data
    const dragData = {
      type: 'field-type-clone',
      fieldType: fieldType,
      sourceComponent: 'field-type-selector'
    };

    if (event.dataTransfer) {
      event.dataTransfer.setData('application/json', JSON.stringify(dragData));
      event.dataTransfer.effectAllowed = 'copy';

      // Set drag image (optional)
      const dragImage = event.target as HTMLElement;
      if (dragImage) {
        event.dataTransfer.setDragImage(dragImage, 0, 0);
      }
    }

    // Visual feedback
    this.isDragging.set(true);
    this.currentDraggedField.set(fieldType);
    document.body.classList.add('field-dragging');
  }

  /**
   * HTML5 Drag End Event Handler
   */
  onDragEnd(_event: DragEvent): void {
    // Reset visual state
    this.isDragging.set(false);
    this.currentDraggedField.set(null);
    document.body.classList.remove('field-dragging');
  }

  /**
   * Lấy icon cho field type
   */
  getFieldIcon(fieldType: FieldType): string {
    return getFieldIcon(fieldType);
  }

 
  getFieldTypeLabel(type: FieldType): string {
    return getFieldTypeLabel(type, this.translateService);
  }

  /**
   * Lấy màu sắc icon cho field type
   */
  getFieldIconColor(fieldType: FieldType): string {
    return getFieldIconColor(fieldType);
  }

  /**
   * Lấy màu sắc background cho field type
   */
  getFieldBackgroundColor(fieldType: FieldType): string {
    return getFieldBackgroundColor(fieldType);
  }

  /**
   * TrackBy function cho field types
   */
  trackByFieldType(_index: number, fieldType: Field): string | number {
    return fieldType._id || fieldType.type;
  }
}
