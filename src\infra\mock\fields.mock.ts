import { Field, FieldOption } from "@domain/entities/field.entity";

/**
 * Helper function để tạo FieldOption array từ string array
 * @param values Array các string values
 * @returns Array các FieldOption không có _id
 */
function createFieldOptions(values: string[]): FieldOption[] {
  return values.map((value, index) => ({
    label: value,
    value: value
  }));
}

export const mockCustomerFields: Field[] = [
  {
    _id: '1',
    label: 'Customer Name',
    type: 'text',
    value: '',
    isPublic: false,
    isRequired: true,
    tooltip: 'Enter customer full name',
    constraints: { maxLength: 100, unique: false },
    permissionProfiles: []
  },
  {
    _id: '2',
    label: 'Email Address',
    type: 'email',
    value: '',
    isPublic: true,
    isRequired: true,
    tooltip: 'Enter valid email address',
    constraints: { unique: true },
    permissionProfiles: []
  },
  {
    _id: '3',
    label: 'Phone Number',
    type: 'phone',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter phone number',
    constraints: {},
    permissionProfiles: []
  },
  {
    _id: '4',
    label: 'Age',
    type: 'number',
    value: 0,
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter age',
    constraints: { maxDigits: 3 },
    permissionProfiles: []
  },
  {
    _id: '5',
    label: 'Salary',
    type: 'currency',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter salary amount',
    constraints: { maxDigits: 10, decimalPlaces: 2 },
    permissionProfiles: []
  },
  {
    _id: '6',
    label: 'Birth Date',
    type: 'date',
    value: new Date(),
    isPublic: false,
    isRequired: false,
    tooltip: 'Select birth date',
    constraints: {},
    permissionProfiles: []
  },
  {
    _id: '7',
    label: 'Status',
    type: 'picklist',
    value: 'Active', // Giá trị phải là string cho picklist
    isPublic: true,
    isRequired: true,
    tooltip: 'Select status',
    constraints: {
      picklistOptions: createFieldOptions(['Active', 'Inactive', 'Pending', 'Suspended']),
      sortOrder: 'input',
      defaultValue: 'Active'
    },
    permissionProfiles: []
  },
  {
    _id: '8',
    label: 'Skills',
    type: 'multi-picklist',
    value: ['JavaScript', 'TypeScript'], // Giá trị phải là string[] cho multi-picklist
    isPublic: false,
    isRequired: false,
    tooltip: 'Select multiple skills',
    constraints: {
      picklistOptions: createFieldOptions(['JavaScript', 'TypeScript', 'Angular', 'React', 'Vue', 'Node.js', 'Python', 'Java']),
      sortOrder: 'alphabetical',
      defaultValue: ['JavaScript', 'TypeScript']
    },
    permissionProfiles: []
  },
  {
    _id: '9',
    label: 'Is Active',
    type: 'checkbox',
    value: false,
    isPublic: true,
    isRequired: false,
    tooltip: 'Check if active',
    constraints: {},
    permissionProfiles: []
  },
  {
    _id: '10',
    label: 'Description',
    type: 'textarea',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter description',
    constraints: { textType: 'large', maxLength: 500 },
    permissionProfiles: []
  },
  {
    _id: '11',
    label: 'Description 1',
    type: 'textarea',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter description',
    constraints: { textType: 'large', maxLength: 500 },
    permissionProfiles: []
  },
  {
    _id: '12',
    label: 'Description 2',
    type: 'textarea',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter description',
    constraints: { textType: 'large', maxLength: 500 },
    permissionProfiles: []
  }
];
