import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { NumberFieldTypes, FieldItemConfig, FieldValue } from '../../../../../../models/dynamic-layout-renderer.model';

/**
 * Component chuyên biệt xử lý các number-based fields
 * 
 * Supported field types:
 * - number: <PERSON><PERSON> nguyên
 * - decimal: <PERSON><PERSON> thập phân
 * - currency: Tiền tệ với formatting
 * - percent: <PERSON>ần trăm với % symbol
 * 
 * Features:
 * - View mode: <PERSON><PERSON><PERSON> thị mock data với formatting
 * - Form mode: Number input với validation
 * - Currency formatting (VND)
 * - Percent formatting với % symbol
 * - Min/max validation
 * - Step validation cho decimal
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-number-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './number-field.component.html',
  styleUrls: ['./number-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NumberFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  @Input({ required: true }) config!: FieldItemConfig;
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    const supportedTypes: NumberFieldTypes[] = ['number', 'decimal', 'currency', 'percent'];
    if (!supportedTypes.includes(this.config.field.type as NumberFieldTypes)) {
      console.warn(`NumberFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate mock value dựa trên field type
   */
  protected override generateMockValue(): void {
    const fieldType = this.config.field.type as any;
    const mockData = this.mockDataService.generateMockData(fieldType);

    // Format mock data based on field type
    if (fieldType === 'currency') {
      this.mockValue.set(this.formatDisplayValue(Number(mockData)));
    } else if (fieldType === 'percent') {
      this.mockValue.set(this.formatDisplayValue(Number(mockData)));
    } else {
      this.mockValue.set(String(mockData));
    }
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected override getValidators(): any[] {
    const validators = this.getCommonValidators();

    // Number fields có thể có min/max constraints
    const constraints = this.config.field.constraints as any;
    if (constraints?.min !== undefined) {
      // validators.push(Validators.min(constraints.min));
    }
    if (constraints?.max !== undefined) {
      // validators.push(Validators.max(constraints.max));
    }

    return validators;
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    const fieldType = this.config.field.type as any;
    switch (fieldType) {
      case 'currency':
        return 'attach_money';
      case 'percent':
        return 'percent';
      case 'decimal':
        return 'decimal_increase';
      case 'number':
      default:
        return 'numbers';
    }
  }

  // ===== IMPLEMENT INTERFACE METHODS =====

  /**
   * Handle khi field.value thay đổi từ bên ngoài
   */
  override handleFieldValueChange(): void {
    super.handleFieldValueChange();
  }

  /**
   * Lấy giá trị hiện tại của field
   */
  getFieldValue(): FieldValue {
    return this.getCurrentValue();
  }

  /**
   * Override onValueChange để emit event
   */
  override onValueChange(value: FieldValue): void {
    super.onValueChange!(value);

    // Emit change event để Form Management Service track
    const fieldId = this.config.field._id || '';
    this.valueChange.emit({ fieldId, value });
  }

  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Format giá trị để hiển thị dựa trên field type
   */
  formatDisplayValue(value: number): string {
    switch (this.config.field.type) {
      case 'currency':
        return new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(value);

      case 'percent':
        return `${value}%`;

      case 'decimal':
        return value.toFixed(2);

      case 'number':
      default:
        return value.toString();
    }
  }

}
