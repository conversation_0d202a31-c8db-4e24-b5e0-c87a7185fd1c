import { Routes } from '@angular/router';
import { INVENTORY_CHECK_PROVIDERS } from './inventory-check/providers/inventory-check.provider';
import { GOODS_RECEIPT_PROVIDERS } from './goods-receipt/providers/goods-receipt.provider';
import { ROUTER_LINKS } from 'salehub_shared_contracts';

export const WarehouseRoutes: Routes = [
  {
    path: ROUTER_LINKS.warehouse.inventory_check,
    loadComponent: () => import('./inventory-check/components/inventory-check.component')
      .then(c => c.InventoryCheckComponent),
    providers: INVENTORY_CHECK_PROVIDERS
    // canActivate: [canActivateCashier]
  },
  {
    path: ROUTER_LINKS.warehouse.goods_receipt,
    loadComponent: () => import('./goods-receipt/components/goods-receipt.component')
      .then(c => c.GoodsReceiptComponent),
    providers: GOODS_RECEIPT_PROVIDERS
    // canActivate: [canActivateCashier]
  },
  {
    path: ROUTER_LINKS.warehouse.product_location.location_list,
    loadComponent: () => import('./location/components/location-list.component')
      .then(c => c.LocationListComponent)
    // canActivate: [canActivateCashier]
  },
  // {
  //   path: 'warehouse/location/create',
  //   loadComponent: () => import('./pages/location-create/location-create.component')
  //     .then(c => c.LocationCreateComponent)
  //   // canActivate: [canActivateCashier]
  // },
  // {
  //   path: 'warehouse/location/edit/:id',
  //   loadComponent: () => import('./location/pages/location-create/location-create.component')
  //     .then(c => c.LocationCreateComponent)
  //   // canActivate: [canActivateCashier]
  // },
];
