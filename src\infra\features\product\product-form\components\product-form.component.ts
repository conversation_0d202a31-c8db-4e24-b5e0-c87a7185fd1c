import { ChangeDetectionStrategy, Component, OnD<PERSON>roy, OnInit, ViewChild, TemplateRef, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { FlashMessageService } from '@core/services/flash_message.service';
import { Subject, takeUntil } from 'rxjs';
import { MatAutocompleteSelectedEvent, MatAutocompleteModule } from '@angular/material/autocomplete';
import {
  Product,
  ProductBrand,
  ProductCategory,
  ProductVariant,
  Warehouse,
  mockProductBrands,
  mockProductCategories,
  mockProductVariants,
  mockWarehouses,
  mockProducts
} from '@mock/product_form';
import { WarehouseLocation } from '@shared/models/api/warehouse-entities.dto';
import { ChipFormFieldComponent } from '@/shared/components/input/chip-form-field/chip-form-field.component';
import { WarehouseAndLocationPickerComponent } from '@shared/components/warehouse-and-location-picker/warehouse-and-location-picker.component';
import { CategoryFormModalService } from '@shared/modals/product/category-form-modal/category-form-modal.service';
import { BrandFormModalService } from '@shared/modals/product/brand-form-modal/brand-form-modal.service';
import { VariantFormModalService } from '@shared/modals/product/variant-form-modal/variant-form-modal.service';
import { SupplierFormModalService } from '@shared/modals/supply-chain/supplier-form-modal/supplier-form-modal.service';
import { WarehouseLocationPickerModalService } from '@shared/modals/warehouse/warehouse-location-picker-modal';
import { Supplier } from 'salehub_shared_contracts/requests/shared/supplier';
import { mockSuppliers, mockWarehouseLocations } from '@mock/shared/list.mock';
import { VariantWarehousePickerModalService } from './variant-warehouse-picker-modal';

/**
 * Component ProductForm
 * Form tạo và chỉnh sửa sản phẩm với đầy đủ các block phức tạp
 */
@Component({
  selector: 'app-product-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatChipsModule,
    MatCardModule,
    MatIconModule,
    MatExpansionModule,
    MatTabsModule,
    MatDividerModule,
    MatTooltipModule,
    MatDialogModule,
    TranslateModule,
    ChipFormFieldComponent,
    WarehouseAndLocationPickerComponent,
    MatAutocompleteModule
  ],
  templateUrl: './product-form.component.html',
  styleUrls: ['./product-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductFormComponent implements OnInit, OnDestroy {
  /**
   * Form chính cho sản phẩm
   */
  productForm: FormGroup;

  /**
   * ID của sản phẩm đang chỉnh sửa (nếu có)
   */
  editId?: string;

  /**
   * Danh mục sản phẩm
   */
  categories: ProductCategory[] = [...mockProductCategories];

  /**
   * Thương hiệu sản phẩm
   */
  brands: ProductBrand[] = [...mockProductBrands];

  /**
   * Thuộc tính sản phẩm
   */
  variants: ProductVariant[] = [...mockProductVariants];

  /**
   * Nhà cung cấp
   */
  suppliers: Supplier[] = [...mockSuppliers];

  /**
   * Kho hàng
   */
  warehouses: Warehouse[] = [...mockWarehouses];

  /**
   * Vị trí trong kho
   */
  locations: WarehouseLocation[] = [...mockWarehouseLocations];

  /**
   * Gợi ý cho tags
   */
  tagSuggestions: string[] = ['Mới', 'Bán chạy', 'Giảm giá', 'Hết hàng'];

  /**
   * Flag kiểm tra xem có thuộc tính nào được chọn không
   */
  hasVariants = false;

  /**
   * Map lưu trữ thông tin kho hàng tùy chỉnh cho từng biến thể sản phẩm
   * Key: index của biến thể trong form array
   * Value: thông tin kho hàng
   */
  customWarehouseMap = new Map<number, {
    warehouseId: string;
    supplierId?: string;
    locationId?: string;
    quantity: number;
  }>();

  /**
   * Subject để quản lý unsubscribe
   */
  private destroy$ = new Subject<void>();

  // Danh sách sản phẩm để làm nguyên liệu
  products = mockProducts;

  /**
   * Constructor với FormBuilder, MatDialog và các service
   */
  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private supplierFormModalService: SupplierFormModalService,
    private brandFormModalService: BrandFormModalService,
    private categoryFormModalService: CategoryFormModalService,
    private variantFormModalService: VariantFormModalService,
    private warehouseLocationPickerModalService: WarehouseLocationPickerModalService,
    private variantWarehousePickerModalService: VariantWarehousePickerModalService,
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {
    this.productForm = this.createProductForm();
  }

  /**
   * Khởi tạo component
   */
  ngOnInit(): void {
    // Lắng nghe thay đổi của variants để kiểm tra có thuộc tính nào được chọn không
    this.variantsFormArray.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(variants => {
        this.hasVariants = variants && variants.length > 0;
        if (this.hasVariants) {
          // Nếu có thuộc tính, disable quản lý kho ban đầu và tạo danh sách sản phẩm
          this.warehousesFormArray.disable();
          this.generateVariantProducts();
        } else {
          // Nếu không có thuộc tính, enable quản lý kho ban đầu và xóa danh sách sản phẩm
          this.warehousesFormArray.enable();
          while (this.variantProductsFormArray.length > 0) {
            this.variantProductsFormArray.removeAt(0);
          }
        }
      });

    // Lắng nghe thay đổi của từng variant form để cập nhật danh sách sản phẩm
    this.variantsFormArray.controls.forEach((control, index) => {
      control.valueChanges
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          if (this.hasVariants) {
            this.generateVariantProducts();
          }
        });
    });

    // Lắng nghe thay đổi của ingredients để tính lại giá vốn
    this.ingredientsFormArray.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(ingredients => {
        if (ingredients && ingredients.length > 0) {
          this.calculateTotalCost();
          // Nếu có nguyên liệu, disable trường cost
          this.productForm.get('cost')?.disable();
        } else {
          // Nếu không có nguyên liệu, enable trường cost
          this.productForm.get('cost')?.enable();
        }
      });

    // Lắng nghe thay đổi của defaultWarehouseId để cập nhật các sản phẩm biến thể
    this.productForm.get('defaultWarehouseId')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(warehouseId => {
        if (warehouseId && this.hasVariants) {
          // Reset defaultLocationId khi thay đổi kho
          this.productForm.get('defaultLocationId')?.setValue('');
        }
      });

    // Thêm một warehouse mặc định khi khởi tạo
    if (this.warehousesFormArray.length === 0) {
      this.addWarehouse();
    }
  }

  /**
   * Dọn dẹp khi component bị hủy
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Tạo form chính cho sản phẩm
   */
  private createProductForm(): FormGroup {
    return this.fb.group({
      // Block thông tin cơ bản
      name: ['', Validators.required],
      sku: [''],
      description: [''],
      weight: [0],
      price: [0, [Validators.required, Validators.min(0)]],
      cost: [0, [Validators.required, Validators.min(0)]],
      wholesale_price: [0, [Validators.min(0)]],
      categoryId: ['', Validators.required],
      brandId: [''],
      tags: [[]],
      isActive: [true],

      // Block quản lý kho ban đầu
      warehouses: this.fb.array([]),

      // Block thuộc tính
      variants: this.fb.array([]),

      // Block danh sách sản phẩm con
      defaultWarehouseId: ['', Validators.required],
      defaultSupplierId: [''],
      defaultLocationId: [''],

      // Danh sách sản phẩm con sẽ được tạo dựa trên thuộc tính đã chọn
      variantProducts: this.fb.array([]),

      // Block đơn vị tính
      baseUnit: ['', Validators.required],
      units: this.fb.array([]),

      // Block thành phần nguyên liệu
      ingredients: this.fb.array([])
    });
  }

  /**
   * Getter cho form array warehouses
   */
  get warehousesFormArray(): FormArray {
    return this.productForm.get('warehouses') as FormArray;
  }

  /**
   * Getter cho form array variants
   */
  get variantsFormArray(): FormArray {
    return this.productForm.get('variants') as FormArray;
  }

  /**
   * Getter cho form array variantProducts
   */
  get variantProductsFormArray(): FormArray {
    return this.productForm.get('variantProducts') as FormArray;
  }

  /**
   * Getter cho form array units
   */
  get unitsFormArray(): FormArray {
    return this.productForm.get('units') as FormArray;
  }

  /**
   * Getter cho form array ingredients
   */
  get ingredientsFormArray(): FormArray {
    return this.productForm.get('ingredients') as FormArray;
  }

  /**
   * Thêm một phần tử kho hàng mới
   */
  addWarehouse(): void {
    const warehouseForm = this.fb.group({
      warehouseId: ['', Validators.required],
      supplierId: [''],
      locationId: [''],
      quantity: [0, [Validators.required, Validators.min(0)]]
    });
    this.warehousesFormArray.push(warehouseForm);
  }

  /**
   * Xóa một phần tử kho hàng
   */
  removeWarehouse(index: number): void {
    this.warehousesFormArray.removeAt(index);
  }

  /**
   * Thêm một phần tử thuộc tính mới
   */
  addVariant(): void {
    const variantForm = this.fb.group({
      variantId: ['', Validators.required],
      values: [[], Validators.required]
    });

    // Thêm lắng nghe sự thay đổi cho variant form mới
    variantForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.hasVariants) {
          this.generateVariantProducts();
        }
      });

    this.variantsFormArray.push(variantForm);
  }

  /**
   * Xóa một phần tử thuộc tính
   */
  removeVariant(index: number): void {
    this.variantsFormArray.removeAt(index);
    // Không cần gọi generateVariantProducts() vì đã có valueChanges của variantsFormArray
  }

  /**
   * Tạo danh sách sản phẩm con từ thuộc tính đã chọn
   */
  private generateVariantProducts(): void {
    // Xóa danh sách sản phẩm con hiện tại
    while (this.variantProductsFormArray.length > 0) {
      this.variantProductsFormArray.removeAt(0);
    }

    // Lấy các thuộc tính đã chọn
    const selectedVariants = this.variantsFormArray.value;
    if (!selectedVariants || selectedVariants.length === 0) {
      return;
    }

    // Tạo tất cả các kết hợp từ các giá trị thuộc tính
    const variantCombinations = this.generateVariantCombinations(selectedVariants);

    // Tạo form cho từng sản phẩm con
    variantCombinations.forEach((combination, index) => {
      const variantProductForm = this.fb.group({
        sku: [''],
        price: [this.productForm.get('price')?.value, [Validators.required, Validators.min(0)]],
        cost: [this.productForm.get('cost')?.value, [Validators.required, Validators.min(0)]],
        wholesale_price: [this.productForm.get('wholesale_price')?.value, [Validators.min(0)]],
        variant_options: [combination],
        quantity: [0, [Validators.required, Validators.min(0)]],
        // warehouseId, supplierId và locationId được quản lý bên ngoài form
        customWarehouse: [false] // Flag để biết sản phẩm này có dùng kho hàng tùy chỉnh không
      });
      this.variantProductsFormArray.push(variantProductForm);
    });
  }

  /**
   * Tạo tất cả các kết hợp từ các giá trị thuộc tính
   */
  private generateVariantCombinations(selectedVariants: any[]): any[] {
    // Mảng các tùy chọn thuộc tính
    const options: any[] = [];

    // Thêm các giá trị thuộc tính vào mảng options
    selectedVariants.forEach(variant => {
      const variantInfo = this.variants.find(v => v._id === variant.variantId);
      if (variantInfo) {
        // Chỉ lấy các giá trị đã chọn
        const selectedValues = variant.values.filter((value: string) =>
          variantInfo.values.includes(value)
        );

        options.push(selectedValues.map((value: string) => ({
          variantId: variant.variantId,
          variantName: variantInfo.name,
          value
        })));
      }
    });

    // Sinh ra tất cả các kết hợp
    const combinations: any[] = [];
    this.combineVariants(options, 0, [], combinations);

    return combinations;
  }

  /**
   * Hàm đệ quy để tạo tất cả các kết hợp từ các tùy chọn thuộc tính
   */
  private combineVariants(options: any[], index: number, current: any[], result: any[]): void {
    if (index === options.length) {
      result.push([...current]);
      return;
    }

    for (const option of options[index]) {
      current.push(option);
      this.combineVariants(options, index + 1, current, result);
      current.pop();
    }
  }

  /**
   * Thêm một đơn vị tính mới
   */
  addUnit(): void {
    const unitForm = this.fb.group({
      name: ['', Validators.required],
      conversionValue: [1, [Validators.required, Validators.min(0.01)]],
      retailPrice: [0, [Validators.min(0)]],
      wholesalePrice: [0, [Validators.min(0)]],
      cost: [0, [Validators.min(0)]],
      sku: ['']
    });
    this.unitsFormArray.push(unitForm);
  }

  /**
   * Xóa một đơn vị tính
   */
  removeUnit(index: number): void {
    this.unitsFormArray.removeAt(index);
  }

  /**
   * Thêm một nguyên liệu mới
   */
  addIngredient(): void {
    const ingredientForm = this.fb.group({
      productId: [''],
      productName: ['', Validators.required],
      quantity: [null, [Validators.required, Validators.min(0.01)]],
      cost: [{ value: 0, disabled: true }],
      total: [{ value: 0, disabled: true }]
    });

    this.ingredientsFormArray.push(ingredientForm);
  }

  /**
   * Xóa một nguyên liệu
   */
  removeIngredient(index: number): void {
    this.ingredientsFormArray.removeAt(index);
    this.calculateTotalCost();
  }

  /**
   * Tính tổng giá vốn từ tất cả các nguyên liệu
   */
  calculateTotalCost(): void {
    const totalCost = this.ingredientsFormArray.controls.reduce((total, control) => {
      const ingredientTotal = control.get('total')?.value || 0;
      return total + ingredientTotal;
    }, 0);

    // Cập nhật giá vốn
    this.productForm.patchValue({ cost: totalCost }, { emitEvent: false });
  }

  /**
   * Lưu sản phẩm
   */
  saveProduct(): void {
    if (this.productForm.invalid) {
      // Đánh dấu tất cả các trường là đã touched để hiển thị lỗi
      this.markFormGroupTouched(this.productForm);
      return;
    }

    const formValue = this.productForm.getRawValue(); // Lấy cả các trường disabled

    // Xử lý dữ liệu các sản phẩm biến thể với thông tin kho hàng
    if (this.hasVariants && formValue.variantProducts.length > 0) {
      // Thông tin kho hàng mặc định
      const { defaultWarehouseId } = formValue;
      const { defaultSupplierId } = formValue;
      const { defaultLocationId } = formValue;

      // Xử lý từng sản phẩm biến thể
      formValue.variantProducts = formValue.variantProducts.map((product: any, index: number) => {
        // Kiểm tra xem sản phẩm này có sử dụng kho hàng tùy chỉnh không
        const customWarehouse = this.customWarehouseMap.get(index);

        // Thông tin kho hàng
        const warehouse = customWarehouse || {
          warehouseId: defaultWarehouseId,
          supplierId: defaultSupplierId,
          locationId: defaultLocationId,
          quantity: product.quantity
        };

        // Trả về sản phẩm biến thể với thông tin kho hàng
        return {
          ...product,
          warehouse
        };
      });
    }

    // TODO: Xử lý lưu sản phẩm
    // Thay thế console.log bằng FlashMessageService với i18n
    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.SAVED')
    );
  }

  /**
   * Đánh dấu tất cả các trường trong form là đã touched
   */
  private markFormGroupTouched(formGroup: FormGroup | FormArray): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      } else {
        control?.markAsTouched();
      }
    });
  }

  /**
   * Mở modal thêm danh mục mới
   */
  async openCategoryDialog(): Promise<void> {
    try {
      // Mở modal danh mục sử dụng service
      const result = await this.categoryFormModalService.open({
        parentCategories: this.categories
      });

      if (result) {
        // Thêm danh mục mới vào danh sách
        this.categories.push(result);
        // Cập nhật form nếu cần
        if (this.productForm.get('categoryId')?.value === '') {
          this.productForm.get('categoryId')?.setValue(result._id);
        }
      }
    } catch (error: any) {
      // Thay thế console.error bằng FlashMessageService với i18n
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: error.message
        }
      );
    }
  }

  /**
   * Mở modal thêm thương hiệu mới
   */
  async openBrandDialog(): Promise<void> {
    try {
      // Mở modal thương hiệu sử dụng service
      const result = await this.brandFormModalService.open();

      if (result) {
        // Thêm thương hiệu mới vào danh sách
        this.brands.push(result);
        // Cập nhật form nếu cần
        if (this.productForm.get('brandId')?.value === '') {
          this.productForm.get('brandId')?.setValue(result._id);
        }
      }
    } catch (error: any) {
      // Thay thế console.error bằng FlashMessageService với i18n
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: error.message
        }
      );
    }
  }

  /**
   * Mở modal thêm thuộc tính mới
   */
  async openVariantDialog(): Promise<void> {
    try {
      // Mở modal thuộc tính sản phẩm sử dụng service
      const result = await this.variantFormModalService.open();

      if (result) {
        // Thêm thuộc tính mới vào danh sách
        this.variants.push(result);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thuộc tính sản phẩm:', error);
    }
  }

  /**
   * Mở modal thêm nhà cung cấp mới
   */
  async openSupplierDialog(): Promise<void> {
    try {
      // Mở modal nhà cung cấp sử dụng service
      const result = await this.supplierFormModalService.open();

      if (result) {
        // Thêm nhà cung cấp mới vào danh sách
        this.suppliers.push(result);
        // Cập nhật form nếu có warehouse đang được chọn
        const warehousesArray = this.productForm.get('warehouses') as FormArray;
        warehousesArray.controls.forEach(control => {
          if (control.get('supplierId')?.value === '') {
            control.get('supplierId')?.setValue(result._id);
          }
        });
      }
    } catch (error: any) {
      // Thay thế console.error bằng FlashMessageService với i18n
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: error.message
        }
      );
    }
  }

  /**
   * Lấy danh sách giá trị cho thuộc tính đã chọn
   */
  getVariantValues(variantForm: any): string[] {
    const variantId = variantForm.get('variantId')?.value;
    if (!variantId) return [];

    const variant = this.variants.find(v => v._id === variantId);
    return variant?.values || [];
  }

  /**
   * Lọc sản phẩm theo từ khóa tìm kiếm
   */
  filteredProducts(searchText: string | Product): Product[] {
    if (!searchText) return this.products;

    // Nếu searchText là object (Product), trả về danh sách gốc
    if (typeof searchText === 'object') return this.products;

    // Chuyển đổi searchText thành lowercase và tìm kiếm
    const search = searchText.toLowerCase();
    return this.products.filter(product =>
      product.name.toLowerCase().includes(search) ||
      product.sku.toLowerCase().includes(search)
    );
  }

  /**
   * Hiển thị tên sản phẩm trong autocomplete
   */
  displayProductFn(product: Product): string {
    return product ? product.name : '';
  }

  /**
   * Xử lý khi chọn sản phẩm từ autocomplete
   */
  onProductSelected(event: MatAutocompleteSelectedEvent, index: number) {
    const product = event.option.value as Product;
    const ingredientForm = this.ingredientsFormArray.at(index);

    // Cập nhật giá cost
    ingredientForm.patchValue({
      productId: product._id,
      cost: product.cost || 0
    });

    // Tính lại thành tiền
    this.updateTotal(index);
  }

  /**
   * Lấy đơn vị tính của sản phẩm đã chọn
   */
  getSelectedProductUnit(index: number): string {
    const ingredientForm = this.ingredientsFormArray.at(index);
    const productName = ingredientForm.get('productName')?.value;
    if (productName && typeof productName === 'object') {
      return (productName as Product).units?.baseUnit || '';
    }
    return '';
  }

  /**
   * Tính lại thành tiền của nguyên liệu
   */
  updateTotal(index: number) {
    const ingredientForm = this.ingredientsFormArray.at(index);
    const quantity = ingredientForm.get('quantity')?.value || 0;
    const cost = ingredientForm.get('cost')?.value || 0;

    ingredientForm.patchValue({
      total: quantity * cost
    }, { emitEvent: false });

    // Cập nhật tổng giá vốn
    this.calculateTotalCost();
  }

  /**
   * Tính tổng giá vốn từ nguyên liệu
   */
  getTotalCost(): number {
    return this.ingredientsFormArray.controls.reduce((total, control) => total + (control.get('total')?.value || 0), 0);
  }

  /**
   * Tính tổng số lượng thành phần
   */
  getTotalIngredients(): number {
    return this.ingredientsFormArray.controls.reduce((total, control) => {
      const quantity = control.get('quantity')?.value || 0;
      return total + quantity;
    }, 0);
  }

  /**
   * Lấy đơn vị cơ bản của sản phẩm
   */
  getBaseUnit(): string {
    return this.productForm.get('baseUnit')?.value || '';
  }

  /**
   * Cập nhật giá vốn của sản phẩm dựa trên tổng giá vốn nguyên liệu
   */
  private updateProductCost() {
    const totalCost = this.getTotalCost();
    if (totalCost > 0) {
      // Nếu có nguyên liệu, disable field cost và cập nhật giá trị
      this.productForm.get('cost')?.disable();
      this.productForm.get('cost')?.setValue(totalCost);
    } else {
      // Nếu không có nguyên liệu, enable field cost
      this.productForm.get('cost')?.enable();
    }
  }

  /**
   * Lấy đường dẫn vị trí mặc định
   */
  getDefaultLocationPath(): string {
    const locationId = this.productForm.get('defaultLocationId')?.value;
    if (!locationId) return '';

    const location = this.locations.find(loc => loc._id === locationId);
    if (!location) return '';

    return location.path.join(' > ');
  }

  /**
   * Mở modal chọn vị trí mặc định
   */
  async openLocationDialog(): Promise<void> {
    const warehouseId = this.productForm.get('defaultWarehouseId')?.value;
    if (!warehouseId) {
      // Hiển thị thông báo cần chọn kho hàng trước
      console.error('Vui lòng chọn kho hàng trước');
      return;
    }

    // Dữ liệu cho modal
    const data = {
      selectedLocationId: this.productForm.get('defaultLocationId')?.value,
      warehouseId,
      locations: this.locations
    };

    try {
      // Mở modal cho phép chọn vị trí từ tree
      const result = await this.warehouseLocationPickerModalService.open(data);

      if (result) {
        // Cập nhật defaultLocationId
        this.productForm.get('defaultLocationId')?.setValue(result);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chọn vị trí kho:', error);
    }
  }

  /**
   * Mở modal chọn kho hàng cho sản phẩm biến thể
   */
  async openWarehouseForVariant(variantIndex: number): Promise<void> {
    const defaultWarehouseId = this.productForm.get('defaultWarehouseId')?.value;
    const defaultSupplierId = this.productForm.get('defaultSupplierId')?.value;
    const defaultLocationId = this.productForm.get('defaultLocationId')?.value;

    // Lấy dữ liệu kho hàng tùy chỉnh nếu có
    let initialData = this.customWarehouseMap.get(variantIndex);

    if (!initialData) {
      // Nếu chưa có dữ liệu tùy chỉnh, sử dụng dữ liệu mặc định
      const variantQuantity = this.variantProductsFormArray.at(variantIndex).get('quantity')?.value || 0;
      initialData = {
        warehouseId: defaultWarehouseId || '',
        supplierId: defaultSupplierId || '',
        locationId: defaultLocationId || '',
        quantity: variantQuantity
      };
    }

    try {
      // Mở modal chọn kho hàng
      const result = await this.variantWarehousePickerModalService.open({
        warehouses: this.warehouses,
        suppliers: this.suppliers,
        locations: this.locations,
        initialData
      });

      if (result) {
        if (result.action === 'addSupplier') {
          // Nếu người dùng muốn thêm nhà cung cấp, mở modal thêm nhà cung cấp
          await this.openSupplierDialog();
          return;
        }

        // Lưu thông tin kho hàng tùy chỉnh
        this.customWarehouseMap.set(variantIndex, result);

        // Cập nhật số lượng trong form
        this.variantProductsFormArray.at(variantIndex).get('quantity')?.setValue(result.quantity);

        // Đánh dấu sản phẩm biến thể này sử dụng kho hàng tùy chỉnh
        this.variantProductsFormArray.at(variantIndex).get('customWarehouse')?.setValue(true);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chọn kho hàng:', error);
    }
  }

  /**
   * Mở dialog chi tiết sản phẩm biến thể
   */
  openVariantDetails(variantIndex: number): void {
    // TODO: tạo dialog chi tiết sản phẩm biến thể
    console.log('Mở dialog chi tiết sản phẩm biến thể', variantIndex);
  }
}
