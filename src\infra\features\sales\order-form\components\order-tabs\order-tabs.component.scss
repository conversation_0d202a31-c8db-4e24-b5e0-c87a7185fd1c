.order-tabs-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;

  .order-tabs {
    flex: 1;
    overflow-x: auto;

    ::ng-deep {
      .mat-mdc-tab-header {
        overflow-x: auto;
        scrollbar-width: thin;

        &::-webkit-scrollbar {
          height: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 4px;
        }
      }
    }
  }

  .tab-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 100px;
    max-width: 200px;

    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-right: 8px;
    }

    .tab-close-btn {
      width: 20px;
      height: 20px;
      line-height: 20px;

      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
        line-height: 16px;
      }
    }
  }

  .add-tab-btn {
    margin-left: 8px;
    width: 36px;
    height: 36px;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      line-height: 20px;
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .order-tabs-container {
    .tab-label {
      min-width: 80px;
      max-width: 120px;
    }
  }
}
