import { ChangeDetectionStrategy, Component, Inject } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { MatButtonModule } from "@angular/material/button";
import { MAT_DIALOG_DATA, MatDialogActions, MatDialogClose, MatDialogContent, MatDialogRef, MatDialogTitle } from "@angular/material/dialog";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { FormatStringPipe } from "@shared/pipes/format_str.pipe";

@Component({
  selector: 'invoice-discount-dialog',
  templateUrl: 'discount.dialog.html',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatButtonModule,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    FormatStringPipe
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DiscountDialog {
  discount: {
    amount: number,
    name?: string
  };

  constructor(
    public dialogRef: MatDialogRef<DiscountDialog>,
    @Inject(MAT_DIALOG_DATA) public data: {
      discount: {
        amount: number,
        name?: string
      },
      max: number,
      title: string
    }
  ) {
    this.discount = data.discount;

    if(!this.discount.amount) {
      // @ts-ignore
      this.discount.amount = null;
    }
  }

  close(): void {
    if(!this.discount.amount) {
      this.dialogRef.close();
      return;
    }

    if(!this.discount.name) {
      // @ts-ignore
      this.discount.name = null;
    }

    if(
      typeof this.discount.amount !== 'number' ||
      this.discount.amount < 1000 ||
      this.discount.amount > this.data.max
    ) {
      return;
    }

    this.dialogRef.close(this.discount);
  }
}
