<div class="field-item-container" [class.dragging]="isDragging()"
  [attr.title]="'DYNAMIC_LAYOUT_BUILDER.FIELD.DRAG_TO_REORDER' | translate">

  <!-- Field row với layout 1 dòng ngang -->
  <div class="field-row d-flex align-items-center">

    <!-- Drag handle -->
    <div class="drag-handle me-2" [attr.title]="'DYNAMIC_LAYOUT_BUILDER.FIELD.DRAG_TO_REORDER' | translate">
      <mat-icon class="drag-icon">drag_indicator</mat-icon>
    </div>

    <!-- Field content - flex grow để chiếm hết không gian còn lại -->
    <div class="field-content flex-grow-1 d-flex align-items-center justify-content-between">

      <!-- Left side: Label (inline editable) -->
      <div class="field-label-section">
        <!-- Display mode -->
        <div class="label-display d-flex align-items-center" *ngIf="!isEditingLabel()"
          [attr.title]="'DYNAMIC_LAYOUT_BUILDER.FIELD.CLICK_TO_EDIT' | translate">
          <span class="label-text" (click)="startEditLabel()">
            {{ field().label }}
          </span>
          <span class="required-indicator ms-1" *ngIf="field().isRequired">*</span>
          <mat-icon class="edit-icon ms-2" (click)="startEditLabel()">edit</mat-icon>
        </div>

        <!-- Edit mode -->
        <div class="label-editor" *ngIf="isEditingLabel()">
          <mat-form-field appearance="outline" class="label-input">
            <input matInput [(ngModel)]="editingLabel" (keydown.enter)="saveLabel()"
              (keydown.escape)="cancelEditLabel()" (blur)="saveLabel()" #labelInput
              [placeholder]="'DYNAMIC_LAYOUT_BUILDER.FIELD.LABEL_PLACEHOLDER' | translate">
          </mat-form-field>
        </div>
      </div>

      <!-- Right side: Type và Actions -->
      <div class="field-right-section d-flex align-items-center">

        <!-- Field type badge với icon và màu sắc đồng bộ với sidebar -->
        <div class="field-type-section me-3">
          <span class="type-badge" [style.backgroundColor]="getFieldBackgroundColor(field().type)">
            <mat-icon class="type-icon" [style.color]="getFieldIconColor(field().type)">
              {{ getFieldIcon(field().type) }}
            </mat-icon>
            {{ getFieldTypeLabel(field().type) }}
          </span>
        </div>

        <!-- Actions -->
        <div class="field-actions-section">
          <!-- Menu button -->
          <button mat-icon-button [matMenuTriggerFor]="fieldMenu" (click)="$event.preventDefault()" class="menu-btn"
            [attr.aria-label]="'DYNAMIC_LAYOUT_BUILDER.FIELD.ACTIONS' | translate">
            <mat-icon>more_vert</mat-icon>
          </button>

          <!-- Menu -->
          <mat-menu #fieldMenu="matMenu" class="field-menu">
            <!-- Mark as required -->
            <button mat-menu-item (click)="onToggleRequired()">
              <mat-icon>{{ field().isRequired ? 'radio_button_checked' :
                'radio_button_unchecked' }}</mat-icon>
              <span>{{ 'DYNAMIC_LAYOUT_BUILDER.FIELD.MARK_REQUIRED' | translate
                }}</span>
            </button>

            <!-- Edit properties -->
            <button mat-menu-item (click)="onEditProperties()">
              <mat-icon>edit</mat-icon>
              <span>{{ 'DYNAMIC_LAYOUT_BUILDER.FIELD.EDIT_PROPERTIES' |
                translate }}</span>
            </button>

            <!-- Set permission -->
            <button mat-menu-item (click)="onSetPermission()">
              <mat-icon>security</mat-icon>
              <span>{{ 'DYNAMIC_LAYOUT_BUILDER.FIELD.SET_PERMISSION' | translate
                }}</span>
            </button>

            <mat-divider></mat-divider>

            <!-- Delete field -->
            <button mat-menu-item (click)="onDeleteField()" class="delete-action">
              <mat-icon>delete</mat-icon>
              <span>{{ 'DYNAMIC_LAYOUT_BUILDER.FIELD.DELETE_FIELD' | translate
                }}</span>
            </button>
          </mat-menu>
        </div>
      </div>
    </div>
  </div>
</div>