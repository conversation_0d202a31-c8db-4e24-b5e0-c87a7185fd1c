


Tôi muốn bạn tạo một giao diện Angular 19 + TypeScript để quản lý cấu trúc kho. H<PERSON>y lập kế hoạch từng bư<PERSON><PERSON>, bao gồm cấu trúc thư mụ<PERSON>, các thành phần cần tạo, và cách triển khai. Dưới đây là yêu cầu cụ thể:

Bối cảnh hiện tại:
- Tôi đang dùng Angular Material và PrimeNG
- Tất cả global style đã được import vào index.html từ file src/styles/styles.scss bao gồm: Bootstrap, tailwind, angular material. Các icon font như: keen-icon, FontAwesome Pro, LineAwesome, Bootstrap icon, Material Icons.
- Tôi đang dùng theme metronic: https://keenthemes.com/metronic/tailwind/demo1/

Yêu cầu chung:
- Bạn phải đọc toàn bộ rules trong .cursor/rules/*
- Bạn phải đọc toàn bộ docs trong docs/*
- Bạn phải đọc FOLDER_STRUCTURE.md để hiểu rõ cấu trúc folder
- Kết nối với các MCP server hiện tại để tự động debug lỗi và xem preview.

Yêu cầu cụ thể:

1. **Giao diện chính**:
   - Sử dụng PrimeNG Tree (`p-tree`) để hiển thị cấu trúc kho phân cấp.
   - Có các nút: "Thêm" (`mat-button`), "Sửa" (`mat-button`), "Xóa" (`mat-button`) ở trên cùng.
   - Hiển thị tối đa 5 cấp (Kho tổng -> Khu vực -> Kệ -> Tầng -> Vị trí).
   - Path: warehouse/location/list

2. **Form "Thêm/Sửa vị trí"**:
   - Khi nhấn "Thêm" hoặc "Sửa", hiển thị Angular Material Dialog (`MatDialog`) với các trường:
     - Tên: `mat-form-field` với `input` (string).
     - Mã: `mat-form-field` với `input` (string).
     - Checkbox "Tự sinh mã" (`mat-checkbox`).
     - Loại vị trí: `mat-select` (enum: Warehouse, Zone, Rack, Shelf, Bin).
     - Vị trí cha: `mat-select` (chọn từ danh sách vị trí hiện có).
     - Sức chứa: `mat-form-field` với `input` type number (kg).
     - Kích thước: 3 `mat-form-field` với `input` type number (Length, Width, Height).
     - Trạng thái: `mat-select` (enum: Active, Inactive, Reserved).
     - Số lượng: `mat-form-field` với `input` type number (mặc định 1).
     - Nút "Tạo con": `mat-button`, khi nhấn, thêm form con lồng trong dialog (tối đa 5 cấp).
     - **Preview real-time**: Hiển thị cấu trúc cây tạm thời trong dialog bằng `p-tree` nhỏ, tự động cập nhật mỗi khi người dùng thay đổi dữ liệu (Tên, Số lượng, Tạo con, v.v.) mà không cần nút "Xem trước".
     - Nút "Thêm" và "Hủy bỏ" (`mat-button`).
   - Form con có các trường tương tự (trừ "Vị trí cha"), với nút "Tạo con" và được phản ánh ngay trong preview.
   - Hiển thị "Cấp độ: X/5" (`mat-label`) trong form con để kiểm soát giới hạn 5 cấp.
   - Có thể truy cập trực tiếp từ path: warehouse/location/create; hoặc truy cập thông qua Dialog từ warehouse/location/list

3. **Cấu trúc dữ liệu**:
   - Dùng interface TypeScript sau cho collection `locations`:
```typescript
export enum LocationType {
  Warehouse = "Warehouse",
  Zone = "Zone",
  Rack = "Rack",
  Shelf = "Shelf",
  Bin = "Bin",
}
export enum LocationStatus {
  Active = "Active",
  Inactive = "Inactive",
  Reserved = "Reserved",
}
export interface Dimensions {
  length: number;
  width: number;
  height: number;
}
export interface Location {
  id: string;
  name: string;
  code: string;
  type: LocationType;
  parentId: string | null;
  capacity: number;
  dimensions: Dimensions;
  status: LocationStatus;
  level: number;
  createdAt: Date;
  updatedAt: Date;
}
4. Yêu cầu kỹ thuật:
   Dùng Angular components với @Component decorator.
   Sử dụng Angular Material (@angular/material) cho form và dialog, PrimeNG (primeng/tree) cho Tree View.
   Quản lý trạng thái bằng Angular service (LocationService) và RxJS (Observable).
   Xử lý logic "Tạo nhanh" khi "Số lượng" > 1 (tạo nhiều vị trí cùng cấp).
   Giới hạn 5 cấp: Nếu cấp hiện tại là 5, vô hiệu hóa nút "Tạo con" (dùng [disabled]).
   Tự sinh mã nếu checkbox "Tự sinh mã" được chọn (ví dụ: Z1-R1-S1).
   Preview real-time: Dùng Angular reactive forms (FormGroup, FormArray) để theo dõi thay đổi và cập nhật p-tree ngay lập tức.

5. Mock Data:
   Tạo file src/app/mock-data/warehouse-locations.ts với dữ liệu mẫu (ít nhất 10 bản ghi, thể hiện phân cấp).

6. Thêm routing vào src/app/features/warehouse/warehouse-routing.ts
7. Quan trọng: Dùng ngx-translate với tất cả string
8. Giao diện phải responsive trên mobile và tablet.

Hãy tạo toàn bộ code trong thư mục src/app/features/warehouse/*, phù hợp với DDD và cấu trúc theo FOLDER_STRUCTURE.
Đảm bảo giao diện trực quan, dễ dùng, và khớp với yêu cầu.

---

### Mock Data (`src/app/mock-data/locations.ts`)
Dưới đây là mock data để lưu trong file `src/app/mock-data/locations.ts`. Dữ liệu này thể hiện cấu trúc phân cấp với tối đa 5 cấp.

```typescript
// src/app/mock-data/locations.ts
export enum LocationType {
  Warehouse = "Warehouse",
  Zone = "Zone",
  Rack = "Rack",
  Shelf = "Shelf",
  Bin = "Bin",
}

export enum LocationStatus {
  Active = "Active",
  Inactive = "Inactive",
  Reserved = "Reserved",
}

export interface Dimensions {
  length: number;
  width: number;
  height: number;
}

export interface Location {
  id: string;
  name: string;
  code: string;
  type: LocationType;
  parentId: string | null;
  capacity: number;
  dimensions: Dimensions;
  status: LocationStatus;
  level: number;
  createdAt: Date;
  updatedAt: Date;
}

export const mockLocations: Location[] = [
  {
    id: "loc_001",
    name: "Kho tổng",
    code: "WH01",
    type: LocationType.Warehouse,
    parentId: null,
    capacity: 5000,
    dimensions: { length: 10, width: 10, height: 5 },
    status: LocationStatus.Active,
    level: 1,
    createdAt: new Date("2025-03-24"),
    updatedAt: new Date("2025-03-24"),
  },
  {
    id: "loc_002",
    name: "Khu vực 1",
    code: "Z1",
    type: LocationType.Zone,
    parentId: "loc_001",
    capacity: 1000,
    dimensions: { length: 5, width: 3, height: 2 },
    status: LocationStatus.Active,
    level: 2,
    createdAt: new Date("2025-03-24"),
    updatedAt: new Date("2025-03-24"),
  },
  {
    id: "loc_003",
    name: "Kệ 1",
    code: "Z1-R1",
    type: LocationType.Rack,
    parentId: "loc_002",
    capacity: 250,
    dimensions: { length: 2, width: 1, height: 2 },
    status: LocationStatus.Active,
    level: 3,
    createdAt: new Date("2025-03-24"),
    updatedAt: new Date("2025-03-24"),
  },
  {
    id: "loc_004",
    name: "Tầng 1",
    code: "Z1-R1-S1",
    type: LocationType.Shelf,
    parentId: "loc_003",
    capacity: 80,
    dimensions: { length: 2, width: 1, height: 0.5 },
    status: LocationStatus.Active,
    level: 4,
    createdAt: new Date("2025-03-24"),
    updatedAt: new Date("2025-03-24"),
  },
  {
    id: "loc_005",
    name: "Vị trí 1",
    code: "Z1-R1-S1-B1",
    type: LocationType.Bin,
    parentId: "loc_004",
    capacity: 20,
    dimensions: { length: 0.5, width: 0.5, height: 0.5 },
    status: LocationStatus.Active,
    level: 5,
    createdAt: new Date("2025-03-24"),
    updatedAt: new Date("2025-03-24"),
  },
];

Mô phỏng Form với con (ASCII Art):
+-------------------------------------------+
| Thêm vị trí mới                           |
+-------------------------------------------+
| Tên:          [Khu vực 1                ] |
| Mã:           [Z1                      ]  |
|               [ ] Tự sinh mã             |
| Loại vị trí:  [Zone ▾                 ]  |
| Vị trí cha:   [WH01 - Kho tổng ▾      ]  |
| Sức chứa:     [1000 kg                ]  |
| Kích thước:   [L: 5m W: 3m H: 2m     ]  |
| Trạng thái:   [Active ▾              ]  |
| Số lượng:     [2                      ]  |
|                                           |
|   +--- Con:                              |
|   | Tên:      [Kệ                    ]  |
|   | Mã:       [R                     ]  |
|   |           [ ] Tự sinh mã            |
|   | Loại:     [Rack ▾               ]  |
|   | Số lượng: [4                    ]  |
|   |           [Tạo con]                |
|                                           |
|               [Thêm] [Hủy bỏ]           |
+-------------------------------------------+

Nếu nhấn "Tạo con" trong phần con, thêm một cấp nữa (cháu):
+-------------------------------------------+
| Thêm vị trí mới                           |
+-------------------------------------------+
| Tên:          [Khu vực 1                ] |
| Số lượng:     [2                      ]  |
|   +--- Con:                              |
|   | Tên:      [Kệ                    ]  |
|   | Số lượng: [4                    ]  |
|   |   +--- Con:                         |
|   |   | Tên:  [Tầng                 ]  |
|   |   | Mã:   [S                   ]  |
|   |   | Loại: [Shelf ▾            ]  |
|   |   | S.lượng: [3               ]  |
|   |   |         [Tạo con]              |
|               [Thêm] [Hủy bỏ]           |
+-------------------------------------------+


Lưu kế hoạch vào .cursor/plans/warehouse-location-plan.md để tôi xem và sửa trước khi thực hiện.


---

Trả lời tôi bằng tiếng việt.