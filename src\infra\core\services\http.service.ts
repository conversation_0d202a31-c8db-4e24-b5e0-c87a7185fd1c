import { Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpHeaders } from '@angular/common/http';
import { Observable, of, lastValueFrom } from 'rxjs';
import { map, timeout } from 'rxjs/operators';
import { ConnectionCheckService } from './connection.service';
/**
 * https://www.angularjswiki.com/angular/progress-bar-in-angular-mat-progress-bar-examplematerial-design/
 * https://long2know.com/2017/01/angular2-http-interceptor-and-loading-indicator/
 */
@Injectable({
  providedIn: 'root'
})
export class HttpService {
  constructor(
    private http: HttpClient,
    private connection: ConnectionCheckService
  ) { }

  #request(permission: REQUEST_PERMISSION, urlEndPoint: string, options: RequestOptions = {}) {
    if(!this.connection.isOnline()) {
      return of({}); ;
    }

    options ||= {};

    const opts: Parameters<HttpClient['request']>[2] = {
      withCredentials: true,
      ...options
    };

    let endpoint: string = '';
    switch(permission) {
    case 'preverified':
      endpoint = '/api/v1/preverified';
      break;
    case 'public':
      endpoint = '/api/v1/public';
      break;
    case 'cashier':
      endpoint = '/api/v1/cashier';
      break;
    case 'admin':
      endpoint = '/api/v1/admin';
      break;
    case 'manage':
      endpoint = '/api/v1/manage';
      break;
    }

    if(options?.isAbsoluteUrlEndpoint) {
      endpoint = '';
    } else if(options?.isRelativeUrlEndpoint) {
      endpoint += '/';
    } else {
      endpoint += '?q[]=';
    }

    const url = (endpoint + urlEndPoint).replace(/q\[\]=&/g, '&');

    let method = options?.method || 'GET';
    if(options.body) {
      method = 'POST';
    }

    return this.http.request(method, url, opts)
      .pipe(
        timeout(options?.timeout ? options.timeout : 10000),

        map(res => {
          const result = (res as any)?.data ?? res;
          return result;
        })
      );
  }

  get<T>(type: REQUEST_PERMISSION, apiEndPoint: string, options: RequestOptions = {}): Observable<T> {
    return this.#request(type, apiEndPoint, options);
  }

  /**
   * https://stackoverflow.com/questions/36208732/angular-2-http-post-is-not-sending-the-request
   * post mặc định phải có subscribe nó mới send
   */
  post<T>(type: REQUEST_PERMISSION, apiEndPoint: string, body: any, options: RequestOptions = {}): Observable<T> {
    return this.#request(type, apiEndPoint, {
      ...options,
      ...{
        body
      }
    });
  }

  delete<T>(type: REQUEST_PERMISSION, apiEndPoint: string, body: any, options: RequestOptions = {}): Observable<T> {
    return this.#request(type, apiEndPoint, {
      ...options,
      ...{
        body,
        method: 'DELETE'
      }
    });
  }

  public promisify(handler: any): Promise<any> {
    return lastValueFrom(handler);
  }
}
export type RequestOptions = {
  body?: any;
  method?: string;
  timeout?: number;
  isAbsoluteUrlEndpoint?: boolean;
  isRelativeUrlEndpoint?: boolean;
  headers?: HttpHeaders | {
    [header: string]: string | string[];
  },
  context?: HttpContext;
};

export type REQUEST_PERMISSION = 'public' | 'cashier' | 'manage' | 'admin' | 'preverified';
