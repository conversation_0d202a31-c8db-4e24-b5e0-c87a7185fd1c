import { Observable } from 'rxjs';
import { InventoryCheck } from '@domain/entities/inventory-check.entity';

/**
 * Repository abstraction cho Inventory Check
 * <PERSON><PERSON> thủ nguyên tắc Dependency Inversion của Clean Architecture
 */
export abstract class InventoryCheckRepository {
  /**
   * Khởi tạo một phiếu kiểm kho mới
   */
  abstract initInventoryCheck(): InventoryCheck;

  /**
   * Tải phiếu kiểm kho theo ID
   * @param id ID của phiếu kiểm kho
   */
  abstract loadInventoryCheck(id: string): Observable<InventoryCheck>;

  /**
   * L<PERSON>y danh sách phiếu kiểm kho
   * @param page Số trang
   * @param pageSize Số lượng item trên mỗi trang
   */
  abstract getInventoryCheckList(page: number, pageSize: number): Observable<{
    items: InventoryCheck[];
    total: number;
    page: number;
    pageSize: number;
  }>;

  /**
   * <PERSON><PERSON><PERSON> phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho cần lưu
   * @param status Trạng thái của phiếu kiểm kho
   */
  abstract saveInventoryCheck(
    inventoryCheck: InventoryCheck,
    status: 'draft' | 'completed'
  ): Observable<{
    _id: string;
    createdAt?: Date;
    updatedAt?: Date;
  }>;

  /**
   * Thêm sản phẩm vào phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho
   * @param productId ID của sản phẩm
   */
  abstract addProductToInventory(
    inventoryCheck: InventoryCheck,
    productId: string
  ): Observable<boolean>;

  /**
   * Xóa sản phẩm khỏi phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho
   * @param productId ID của sản phẩm
   */
  abstract removeProductFromInventory(
    inventoryCheck: InventoryCheck,
    productId: string
  ): boolean;

  /**
   * Cập nhật số lượng thực tế của sản phẩm
   * @param inventoryCheck Phiếu kiểm kho
   * @param productId ID của sản phẩm
   * @param quantity Số lượng thực tế
   */
  abstract updateActualQuantity(
    inventoryCheck: InventoryCheck,
    productId: string,
    quantity: number
  ): boolean;

  /**
   * Lấy danh sách kho
   */
  abstract getWarehouses(): Observable<Array<{
    _id: string;
    name: string;
  }>>;

  /**
   * Lấy danh sách vị trí kho theo warehouseId
   * @param warehouseId ID của kho
   */
  abstract getWarehouseLocations(warehouseId: string): Observable<Array<{
    _id: string;
    name: string;
    warehouse: { _id: string; name: string; };
  }>>;

  /**
   * Lấy danh sách danh mục sản phẩm
   */
  abstract getProductCategories(): Observable<Array<{
    _id: string;
    name: string;
  }>>;

  /**
   * Cập nhật tồn kho dựa trên kết quả kiểm kho
   * @param inventoryCheck Phiếu kiểm kho
   */
  abstract updateWarehouseStock(inventoryCheck: InventoryCheck): Observable<{
    success: boolean;
    message: string;
  }>;
}
