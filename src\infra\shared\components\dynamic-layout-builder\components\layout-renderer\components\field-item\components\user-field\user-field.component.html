@if (isVisible()) {
  <div class="user-field-container" [class.read-only]="isReadOnlyState()">
    
    <div class="field-label-container">
      <label class="field-label">
        {{ config.field.label }}
        @if (config.field.isRequired || config.field.required) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      @if (isReadOnlyState()) {
        <mat-icon
          class="read-only-icon ms-2"
          [matTooltip]="'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP' | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <div class="field-value-container">
      
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <mat-icon class="field-type-icon me-2">person</mat-icon>
          <div class="user-info">
            <span class="user-name">{{ mockValue() }}</span>
            <small class="user-email text-muted">user&#64;example.com</small>
          </div>
        </div>
      }
      
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <mat-icon matIconPrefix class="field-type-icon">person</mat-icon>
          
          <mat-select
            [placeholder]="(config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.USER') | translate"
            [formControl]="formControl()">

            @for (user of getUserOptions(); track user.value) {
              <mat-option [value]="user.value">
                <div class="user-option">
                  <span class="user-name">{{ user.label }}</span>
                </div>
              </mat-option>
            }
          </mat-select>
          
          @if (formControl().invalid && formControl().touched) {
            <mat-error>
              @if (formControl().hasError('required')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.REQUIRED' | translate }}
              }
            </mat-error>
          }
        </mat-form-field>
      }
    </div>

    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
