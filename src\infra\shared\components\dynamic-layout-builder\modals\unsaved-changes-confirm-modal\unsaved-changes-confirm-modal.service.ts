import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { 
  UnsavedChangesConfirmModalComponent, 
  UnsavedChangesConfirmResult 
} from './unsaved-changes-confirm-modal.component';
import { LayoutSwitchConfirmData } from '../../services/dynamic-layout-config-state.service';

/**
 * Service để mở modal xác nhận unsaved changes
 * Sử dụng ResponsiveModalService để hỗ trợ cả dialog và bottom sheet
 */
@Injectable({
  providedIn: 'root'
})
export class UnsavedChangesConfirmModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal xác nhận unsaved changes
   * @param data Dữ liệu layout switch confirmation
   * @returns Promise<UnsavedChangesConfirmResult | undefined> Kết quả từ modal
   */
  async confirm(data: LayoutSwitchConfirmData): Promise<UnsavedChangesConfirmResult | undefined> {
    try {
      const modalConfig = {
        data,
        width: '500px',
        maxWidth: '90vw',
        disableClose: true
      };

      const result = await this.responsiveModalService.open<
        LayoutSwitchConfirmData,
        UnsavedChangesConfirmResult,
        UnsavedChangesConfirmModalComponent
      >(UnsavedChangesConfirmModalComponent, modalConfig);

      return result;
    } catch (error) {
      console.error('Lỗi khi mở modal xác nhận unsaved changes:', error);
      return undefined;
    }
  }
}
