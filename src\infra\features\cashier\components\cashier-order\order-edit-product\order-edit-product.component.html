<div class="text-uppercase text-center fw-bold p-3 small">
  {{ name() }}
</div>

<div class="p-3">
  <mat-form-field
    appearance="outline"
    floatLabel="always"
    class="w-100"
    >
    <mat-label>Tên mặt hàng</mat-label>
    <input
      matInput
      type="text"
      [(ngModel)]="name"
      required
      >

      <button matSuffix aria-label="Clear" class="border-0 me-2" (click)="name.set('')">
      <span class="material-symbols-outlined">
        close
        </span>
    </button>
  </mat-form-field>

  <mat-form-field
    appearance="outline"
    floatLabel="always"
    class="w-100"
    >
    <mat-label><PERSON><PERSON><PERSON> bán</mat-label>
    <input
      matInput
      type="number"
      pattern="[0-9]*"
      inputmode="numeric"
      [(ngModel)]="price"
      required
      >

      <button matSuffix aria-label="Clear" class="border-0 me-2" (click)="price.set('')">
        <span class="material-symbols-outlined">
          close
          </span>
      </button>
  </mat-form-field>

  <mat-form-field
    appearance="outline"
    floatLabel="always"
    class="w-100"
    >
    <mat-label>Giá cost</mat-label>
    <input
      matInput
      type="number"
      pattern="[0-9]*"
      inputmode="numeric"
      [(ngModel)]="totalCost"
      required>
      

  </mat-form-field>

  <mat-form-field
    appearance="outline"
    floatLabel="always"
    class="w-100"
    >
    <mat-label>Ghi chú</mat-label>
    <input
      matInput
      type="text"
      [(ngModel)]="note"
      >

      <button matSuffix aria-label="Clear" class="border-0 me-2" (click)="note.set('')">
        <span class="material-symbols-outlined">
          close
          </span>
      </button>
  </mat-form-field>
</div>

<div class="border-top"></div>


@if(hasButton()) {
<div class="save px-3">
  <button class="text-uppercase btn btn-primary w-100" (click)="submit()">
    Lưu
  </button>
</div>
}
