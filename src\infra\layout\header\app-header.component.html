@if (viewport$ | async; as viewport) {
  <div class="app-header" #header>
    <div class="main-container d-flex align-items-center flex-nowrap px-2">
      <div class="app-header-brand">
        <div class="d-flex align-items-center relative">
          <div
            class="app-header-btn me-3"
            (click)="openNavigationDrawer()"
            matRipple
            >
            <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" focusable="false" aria-hidden="true" style="pointer-events: none;">
              <path d="M21 6H3V5h18v1zm0 5H3v1h18v-1zm0 6H3v1h18v-1z"></path>
            </svg>
          </div>

          <div class="cursor-pointer" (click)="openNavigationDrawer()">
            <img src="/assets/images/logo.svg">
          </div>

        </div>
      </div>

      @if (viewport.isDesktop) {
        <div class="app-header-sub-navigation">
          <app-sub-navigation></app-sub-navigation>
        </div>
      }

      <div class="app-header-menu ms-auto d-flex align-items-center">
        <div
          class="app-header-btn me-3"
          matRipple
          >
          <i class="icon ki-filled ki-magnifier"></i>
        </div>
        <div class="app-header-btn me-3" matRipple>
          <i class="icon ki-filled ki-messages"></i>
        </div>
        <div
          class="app-header-btn me-3"
          [class.active]="activeHeaderMenu()['notification']"
          matRipple
          [matRippleDisabled]="!!activeHeaderMenu()['notification']"
          [cdkMenuTriggerFor]="viewport.isDesktop ? customDropdown : null"
          [cdkMenuTriggerData]="{id: 'notification'}"
          (cdkMenuOpened)="viewport.isDesktop ? onCdkMenuOpened('notification') : null"
          (cdkMenuClosed)="viewport.isDesktop ? onCdkMenuClosed() : null"
          (click)="viewport.isDesktop ? onCdkMenuTriggerClick($event) : openBottomSheetMenu('notification')"
        >
          <i class="icon ki-filled ki-notification"></i>
        </div>
        <div
          [cdkMenuTriggerFor]="viewport.isDesktop ? customDropdown : null"
          [cdkMenuTriggerData]="{id: 'user-menu'}"
          (cdkMenuOpened)="viewport.isDesktop ? onCdkMenuOpened('user-menu') : null"
          (click)="viewport.isDesktop ? onCdkMenuTriggerClick($event) : openBottomSheetMenu('user-menu')"
          >
          <img alt="" class="rounded-full border-2 border-success user-avatar" src="/assets/images/mock/300-2.png">
        </div>
      </div>

    </div>

    @if (!viewport.isDesktop) {
      <div class="app-sub-navigation-mobile" #subNavMobile>
        <app-sub-navigation></app-sub-navigation>
      </div>
    }
  </div>

  <ng-template #customDropdown let-id='id'>
    <div class="dropdown-content light:border-gray-300" cdkMenu>
      @if (id === 'notification') {
        <app-notification></app-notification>
      } @else if(id === 'user-menu') {
        <app-user-menu></app-user-menu>
      }
    </div>
  </ng-template>

}
