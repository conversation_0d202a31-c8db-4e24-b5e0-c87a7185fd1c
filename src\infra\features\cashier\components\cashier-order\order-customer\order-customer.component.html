<div class="bg-white w-100">
  @if (order().diningOption === 'takeaway') {
  <div class="w-100 px-3">
    <mat-radio-group
      aria-label="Select an option"
      class="d-flex select-dining"
      [(ngModel)]="order().delivery.selfArrive"
      (ngModelChange)="updateDeliveryOption($event)"
      >

      <mat-radio-button [value]="false" class="col-6">
        Khách Ship
      </mat-radio-button>
      <mat-radio-button [value]="true" class="col-6">
        <PERSON>h<PERSON>ch tự đến lấy
      </mat-radio-button>
    </mat-radio-group>
  </div>
  }


  <app-cashier-order-customer-selection
    #customerSelection
    >
  </app-cashier-order-customer-selection>
</div>

@if (order().customer.phoneNumber || order().customer.address?.fullAddress) {
<div class="bg-white w-100 px-3 mt-4">
  <div class="text-uppercase pb-5 pt-4"><b>Thông tin khách hàng</b></div>


  <mat-form-field
    appearance="outline"
    floatLabel="always"
    class="w-100"
    [class.mat-form-field-invalid]=hasPhoneError()
    >
    <mat-label>Số điện thoại</mat-label>
    <input
      matInput
      type="text"
      pattern="[0-9]*"
      inputmode="numeric"
      [(ngModel)]="order().customer.phoneNumber"
      required>

      <button matSuffix aria-label="Clear" (click)="clearValue('phoneNumber')" class="border-0">
      <span class="material-symbols-outlined me-2">
        close
        </span>
    </button>
    @if (hasPhoneError()) {
    <mat-hint class="mat-mdc-form-field-error">Số điện thoại chưa chính xác.</mat-hint>
    }
  </mat-form-field>

  <app-input-address
    [defaultValue]="order().customer.address"
    [oldPlaces]="order().customer.allAddress"
    [defaultValue]="order().customer.latestUsedAddress"
    (selectedPlace)="onSelectedAddress($event)"
    (changingPlace)="onChangingAddress()"
    >
  </app-input-address>

  <mat-form-field
    appearance="outline"
    class="w-100"
    [class.mat-form-field-invalid]=hasGenderError()
    >
    <mat-label>Giới tính</mat-label>
    <mat-select [(ngModel)]="order().customer.gender" required>
      <mat-option value="none">Không rõ</mat-option>
      <mat-option value="female">Nữ</mat-option>
      <mat-option value="male">Nam</mat-option>
    </mat-select>

    @if (hasGenderError()) {
    <mat-hint class="mat-mdc-form-field-error">Vui lòng chọn giới tính khách hàng.</mat-hint>
    }
  </mat-form-field>

  <mat-form-field appearance="outline" floatLabel="always" class="w-100">
    <mat-label>Tên khách hàng</mat-label>
    <input matInput  [(ngModel)]="order().customer.name">
    <button matSuffix aria-label="Clear" (click)="clearValue('name')" class="border-0">
      <span class="material-symbols-outlined me-2">
        close
        </span>
    </button>
  </mat-form-field>


  <mat-form-field appearance="outline" class="w-100">
    <mat-label>Facebook</mat-label>
    <input matInput  [(ngModel)]="order().customer.facebook">
  </mat-form-field>

  <!-- <mat-form-field appearance="outline" class="w-100">
    <mat-label>Ghi chú khách hàng</mat-label>
    <input matInput  [(ngModel)]="order().customer.note">
    <mat-hint class="text-muted">VD: Khách VIP, chủ tịch, giám đốc...</mat-hint>
  </mat-form-field> -->

</div>

@if (!order().delivery.selfArrive) {
  <app-cashier-order-delivery
    #delivery
    >
  </app-cashier-order-delivery>
  }
}


<div class="mt-4 px-2 d-flex">
  <button
    class="reset text-uppercase btn btn-secondary me-3"
    matStepperPrevious
    >
    Quay lại
  </button>

  @if (hasNextButton()) {
  <button
    class="add-to-cart text-uppercase btn btn-primary flex-grow-1"
    matStepperNext
    (click)="submitCustomer()"
    >
    Tiếp theo
  </button>
  }
</div>
