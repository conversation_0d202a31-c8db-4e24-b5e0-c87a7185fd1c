import { Component, OnInit, signal, computed, inject, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FlashMessageService } from '@core/services/flash_message.service';

// Bootstrap và Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';

// Models và Services
import { Template } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.model';
import { ProductLayoutService } from './product-layout.service';

/**
 * ProductLayoutComponent - Component hiển thị danh sách layout sản phẩm
 *
 * Tính năng:
 * - <PERSON><PERSON>n thị danh sách templates dưới dạng cards
 * - Responsive design với CSS Grid và Bootstrap
 * - Navigation đến trang edit khi click card
 * - <PERSON><PERSON><PERSON> thị thống kê (số section, số field)
 * - Sử dụng Angular 19 Signals và OnPush change detection
 */
@Component({
  selector: 'app-product-layout',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule
  ],
  templateUrl: './product-layout.component.html',
  styleUrls: ['./product-layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductLayoutComponent implements OnInit {
  // Inject services
  private productLayoutService = inject(ProductLayoutService);
  private router = inject(Router);
  private flashMessageService = inject(FlashMessageService);
  private translateService = inject(TranslateService);

  // Signals cho state management
  templates = signal<Template[]>([]);
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);

  // Computed signals
  hasTemplates = computed(() => this.templates().length > 0);
  templateCount = computed(() => this.templates().length);

  ngOnInit(): void {
    this.loadTemplates();
  }

  /**
   * Load danh sách templates từ service
   */
  private loadTemplates(): void {
    try {
      this.isLoading.set(true);
      this.error.set(null);

      // Simulate loading delay để test loading state
      setTimeout(() => {
        const layouts = this.productLayoutService.getProductLayouts();
        this.templates.set(layouts);
        this.isLoading.set(false);
      }, 300);

    } catch (err: any) {
      this.error.set('Không thể tải danh sách layout. Vui lòng thử lại.');
      this.isLoading.set(false);
      // Thay thế console.error bằng FlashMessageService với i18n
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: err.message
        }
      );
    }
  }

  /**
   * TrackBy function để tối ưu hiệu suất *ngFor
   * @param index Index của item
   * @param template Template object
   * @returns Unique identifier
   */
  trackByTemplate(index: number, template: Template): string {
    return template.name;
  }

  /**
   * Lấy số lượng section của template
   * @param template Template cần đếm
   * @returns Số section
   */
  getSectionCount(template: Template): number {
    return this.productLayoutService.getSectionCount(template);
  }

  /**
   * Lấy tổng số field của template
   * @param template Template cần đếm
   * @returns Tổng số field
   */
  getFieldCount(template: Template): number {
    return this.productLayoutService.getTotalFieldCount(template);
  }

  /**
   * Lấy thống kê chi tiết của template
   * @param template Template cần thống kê
   * @returns Object thống kê
   */
  getTemplateStats(template: Template) {
    return this.productLayoutService.getTemplateStats(template);
  }

  /**
   * Xử lý khi click vào card template
   * Điều hướng đến trang edit với index của template
   * @param index Vị trí của template trong mảng
   */
  onTemplateClick(index: number): void {
    try {
      this.router.navigate(['/product/layouts/edit']);
    } catch (err: any) {
      // Thay thế console.error bằng FlashMessageService với i18n
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: err.message
        }
      );
      this.error.set('Không thể điều hướng đến trang chỉnh sửa.');
    }
  }

  /**
   * Xử lý retry khi có lỗi
   */
  onRetry(): void {
    this.loadTemplates();
  }

  /**
   * Lấy icon phù hợp cho template dựa trên tên
   * @param templateName Tên template
   * @returns Tên icon Material
   */
  getTemplateIcon(templateName: string): string {
    const name = templateName.toLowerCase();

    if (name.includes('thời trang') || name.includes('fashion')) {
      return 'checkroom';
    } else if (name.includes('mỹ phẩm') || name.includes('beauty')) {
      return 'face';
    } else if (name.includes('thực phẩm') || name.includes('food')) {
      return 'restaurant';
    } else if (name.includes('điện tử') || name.includes('electronics')) {
      return 'devices';
    }

    return 'view_module'; // Default icon
  }

  /**
   * Lấy màu chip cho template dựa trên loại
   * @param templateName Tên template
   * @returns CSS class cho màu
   */
  getTemplateChipColor(templateName: string): string {
    const name = templateName.toLowerCase();

    if (name.includes('thời trang')) {
      return 'primary';
    } else if (name.includes('mỹ phẩm')) {
      return 'accent';
    } else if (name.includes('thực phẩm')) {
      return 'warn';
    }

    return 'primary';
  }
}
