
@use '../../shared/styles/_variable' as *;

.app-header-btn {
  color: var(--tw-gray-500);
  border-radius: 100%;
  width: 36px;
  height: 36px;
  line-height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: 0;
  border: 1px solid transparent;
  font-weight: 500;
  font-size: 1rem;
  outline: none;
  font-size: 22px;
  cursor: pointer;
  transition: background-color .2s ease;

  .icon {
    transform: scale(1);
    transition: transform .1s ease, color .1s ease;
  }

  &:hover {
    background: #f5f5f5;
  }
  &.active {
    background: #fffae7;

    .icon {
      color: #ffc900;
      transform: scale(0.8);
    }
  }
}

.app-header {
  padding: .75em 0;


  .user-avatar {
    width: 30px;
    height: 30px;
    max-width: 30px;
    object-fit: cover;
  }
}

@media screen and (min-width: #{$breakpointMinHorizontalWidth}px) {
  app-header {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: $desktopHeaderHeight+px;
    background-color: #fff;
    box-shadow: 0px 10px 30px 0px rgba(82, 63, 105, 0.05);
    z-index: 9999;

    transition-duration: 400ms;
    transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
    transition-property: transform, left;
  }

  .app-header {
    padding: .6em 0;

    &-brand {
      // width: 200px;
      padding-right: 10px;
      margin-right: 10px;
    }
    &-menu {
      width: 200px;

    }
    &-sub-navigation {
      width: calc(100% - 400px);
      overflow: hidden;
    }
  }

  .cdk-overlay-pane {
    animation: scaleIn 0.25s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  }
}
