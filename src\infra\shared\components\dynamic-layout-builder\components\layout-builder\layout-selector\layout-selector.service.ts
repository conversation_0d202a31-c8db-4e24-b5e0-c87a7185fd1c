import { Injectable, inject } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

// ✅ REMOVED: DynamicLayoutBuilderService import - using MultiLayoutManagementService instead
import { MultiLayoutManagementService } from '../../../services/multi-layout-management.service';
// Import interface mới từ dynamic-layout-config.model.ts
import {
  DynamicLayoutConfig,
  DynamicLayoutBuilderConfig
} from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.model';
import { FlashMessageService } from '@core/services/flash_message.service';
import { CreateLayoutModalService } from '../../../modals/create-layout-modal/create-layout-modal.service';
import { CreateLayoutModalResult } from '../../../modals/create-layout-modal/create-layout-modal.component';

/**
 * LayoutSelectorService - High-level Layout Business Logic Service
 *
 * **Refactored Purpose (Post-Architecture Refactoring):**
 * - Handle high-level layout business logic và modal operations
 * - Coordinate between low-level DynamicLayoutBuilderService và UI components
 * - Manage layout creation, cloning, và state synchronization
 *
 * **Architecture Separation:**
 * - DynamicLayoutBuilderService: Low-level config management và data persistence
 * - LayoutSelectorService: High-level business logic, modal interactions, state coordination
 * - DynamicLayoutBuilderStateService: Centralized state management
 *
 * IMPORTANT: Service này KHÔNG được đăng ký ở root level để tránh state sharing.
 * Nó sẽ được inject thông qua DynamicLayoutBuilderComponent providers.
 */
export class LayoutSelectorService {

  // ==================== DEPENDENCIES ====================

  private multiLayoutManagementService = inject(MultiLayoutManagementService);
  private flashMessageService = inject(FlashMessageService);
  private translateService = inject(TranslateService);
  private createLayoutModalService = inject(CreateLayoutModalService);

  // Subscriptions container
  private subscriptions = new Subscription();

  constructor() {
    // console.log('✅ LayoutSelectorService: Simplified service for UI operations only');
  }


  /**
   * Create new layout với modal dialog
   * UI Logic: Handle modal operations và delegate creation to service
   */
  async createNewLayout(defaultLayoutConfig: DynamicLayoutConfig | null): Promise<string | null> {
    try {
      // Validation
      if (!defaultLayoutConfig) {
        console.error('❌ No defaultLayoutConfig provided');
        this.flashMessageService.error(
          this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.ERROR_MESSAGE')
        );
        return null;
      }

      // Get existing layout names for validation (from MultiLayoutManagementService)
      const availableLayouts = this.multiLayoutManagementService.availableLayouts();
      const existingLayoutNames = availableLayouts.map((layout: any) => layout.name);
      // console.log('🚀 LayoutSelectorService.createNewLayout() - Existing layouts:', existingLayoutNames);

      // Open modal with validation
      const result = await this.createLayoutModalService.openWithValidation(
        existingLayoutNames,
        '', // default name
        '' // default description
      );

      if (!result) {
        console.log('❌ CreateLayoutModal cancelled');
        return null;
      }

      // console.log('✅ CreateLayoutModal result:', result);
      return await this.handleCreateNewLayoutResult(result, defaultLayoutConfig);

    } catch (error) {
      // console.error('❌ Error in createNewLayout:', error);
      this.flashMessageService.error(
        this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.ERROR_MESSAGE')
      );
      return null;
    }
  }

  /**
   * Handle create new layout result từ modal
   * Business Logic: Process modal result và create layout
   */
  private async handleCreateNewLayoutResult(
    layoutResult: CreateLayoutModalResult,
    defaultConfig: DynamicLayoutConfig
  ): Promise<string | null> {
    return new Promise((resolve) => {
      // Validation
      if (!layoutResult.name || !layoutResult.description) {
        // console.error('❌ Invalid layout result:', layoutResult);
        this.flashMessageService.error(
          this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.ERROR_MESSAGE')
        );
        resolve(null);
        return;
      }

      // Double-check name uniqueness
      const availableLayouts = this.multiLayoutManagementService.availableLayouts();
      const existingLayoutNames = availableLayouts.map((layout: any) => layout.name);
      const nameExists = existingLayoutNames.some((name: any) =>
        name.toLowerCase().trim() === layoutResult.name.toLowerCase().trim()
      );

      if (nameExists) {
        // console.error('❌ Layout name already exists:', layoutResult.name);
        this.flashMessageService.error(
          this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.NAME_EXISTS_ERROR', {
            name: layoutResult.name
          })
        );
        resolve(null);
        return;
      }

      // Create new layout using high-level business logic
      this.subscriptions.add(
        this.createNewLayoutWithBusinessLogic(
          layoutResult.name,
          layoutResult.description,
          defaultConfig
        ).subscribe({
          next: (newLayoutId: string) => {
            // console.log('✅ New layout created successfully with ID:', newLayoutId);
            resolve(newLayoutId);
          },
          error: (error) => {
            // console.error('❌ Error creating new layout:', error);
            this.flashMessageService.error(
              this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.ERROR_MESSAGE')
            );
            resolve(null);
          }
        })
      );
    });
  }

  /**
   * Cleanup subscriptions
   */
  destroy(): void {
    // console.log('🧹 LayoutSelectorService: Cleaning up subscriptions');
    this.subscriptions.unsubscribe();
  }

  // ==================== LAYOUT BUSINESS LOGIC METHODS ====================
  // Moved from DynamicLayoutBuilderService for better separation of concerns

  /**
   * Create new layout - High-level business logic
   * ✅ REFACTORED: Delegates to MultiLayoutManagementService.createNewLayoutWithBusinessLogic()
   * Maintains backward compatibility while centralizing business logic
   */
  createNewLayoutWithBusinessLogic(name: string, description?: string, templateLayout?: DynamicLayoutConfig): Observable<string> {
    console.log('🔄 LayoutSelectorService: Delegating createNewLayoutWithBusinessLogic to MultiLayoutManagementService');

    // Delegate to centralized multi-layout management service
    return this.multiLayoutManagementService.createNewLayoutWithBusinessLogic(name, description, templateLayout);
  }

  // ✅ REMOVED: cloneLayoutWithNewIds() method moved to MultiLayoutManagementService
  // Use MultiLayoutManagementService.createNewLayoutWithBusinessLogic() for layout cloning operations

  // ✅ REMOVED: generateId() method moved to MultiLayoutManagementService
  // ✅ REMOVED: getDefaultDetailViewConfig() method moved to MultiLayoutManagementService
  // Use MultiLayoutManagementService for all layout utility operations

  // Note: saveMultiLayoutConfig and updateAvailableLayouts methods removed
  // These operations are now handled by DynamicLayoutBuilderService.updateMultiLayoutConfig()
}
