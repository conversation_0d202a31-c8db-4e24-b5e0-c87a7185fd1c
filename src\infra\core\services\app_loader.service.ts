import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
/**
 * https://www.angularjswiki.com/angular/progress-bar-in-angular-mat-progress-bar-examplematerial-design/
 * https://long2know.com/2017/01/angular2-http-interceptor-and-loading-indicator/
 */
@Injectable({
  providedIn: 'root'
})
export class AppLoaderService {
  private loadingSubject = new BehaviorSubject<boolean>(true);
  loading$ = this.loadingSubject.asObservable();

  loadingOn() {
    if(!this.loadingSubject.value) {
      this.loadingSubject.next(true);
    }
  }

  loadingOff() {
    if(this.loadingSubject.value) {
      this.loadingSubject.next(false);
    }
  }

  removeLoader() {
    document.querySelector('.app-loading')?.remove?.();
  }
}
