import { Injectable } from '@angular/core';
import { CreateOrderRequest } from 'salehub_shared_contracts/requests/sales/create_order';

/**
 * Service xử lý logic tính toán và kiểm tra dữ liệu cho OrderSummaryComponent
 */
@Injectable({
  providedIn: 'root'
})
export class OrderSummaryService {
  /**
   * Tính toán các giá trị tóm tắt dựa trên dữ liệu đơn hàng
   * @param order Đối tượng đơn hàng
   * @returns Tóm tắt các giá trị tính toán
   */
  calculateSummary(order: CreateOrderRequest): any {
    if (!order) {
      return {
        totalItemsAmount: 0,
        discountAmount: 0,
        deliveryFee: 0,
        surchargeFee: 0,
        finalAmount: 0,
        paidAmount: 0,
        change: 0,
        debt: 0,
        currentDebt: 0
      };
    }

    // Tính tổng tiền hàng
    const totalItemsAmount = this.calculateTotalItemsAmount(order);

    // Tính giảm giá
    const discountAmount = this.calculateDiscountAmount(order, totalItemsAmount);

    // Tính phí vận chuyển
    const deliveryFee = this.calculateDeliveryFee(order);

    // Tính phụ thu
    const surchargeFee = order.customerFare?.surchargeFee || 0;

    // Tính tổng số tiền khách cần trả
    const finalAmount = totalItemsAmount - discountAmount + deliveryFee + surchargeFee;

    // Tính số tiền khách đã thanh toán
    const paidAmount = order.payment?.paidAmount || 0;

    // Tính tiền thừa trả lại khách
    const change = paidAmount > finalAmount ? paidAmount - finalAmount : 0;

    // Tính công nợ khách hàng
    const debt = finalAmount > paidAmount ? finalAmount - paidAmount : 0;

    // TODO: Lấy dư nợ hiện tại từ API hoặc service khác
    const currentDebt = 0; // Placeholder, cần thay thế khi có API thực tế

    return {
      totalItemsAmount,
      discountAmount,
      deliveryFee,
      surchargeFee,
      finalAmount,
      paidAmount,
      change,
      debt,
      currentDebt
    };
  }

  /**
   * Tính tổng tiền hàng (không bao gồm giảm giá, phí vận chuyển, phụ thu)
   * @param order Đối tượng đơn hàng
   * @returns Tổng tiền hàng
   */
  private calculateTotalItemsAmount(order: CreateOrderRequest): number {
    if (!order.items || order.items.length === 0) {
      return 0;
    }

    return order.items.reduce((total: number, item: any) => {
      // Tính tiền cho từng sản phẩm (số lượng * đơn giá)
      const itemTotal = (item.quantity || 0) * (item.price || 0);
      return total + itemTotal;
    }, 0);
  }

  /**
   * Tính số tiền giảm giá dựa trên thông tin giảm giá
   * @param order Đối tượng đơn hàng
   * @param totalAmount Tổng tiền hàng
   * @returns Số tiền giảm giá
   */
  private calculateDiscountAmount(order: CreateOrderRequest, totalAmount: number): number {
    // Nếu không có thông tin giảm giá
    if (!order.discounts || order.discounts.length === 0) {
      return 0;
    }

    // Tính tổng giảm giá từ tất cả các khuyến mãi
    return order.discounts.reduce((total: number, discount: any) => {
      if (discount.type === 'percentage') {
        // Giảm giá theo phần trăm
        const percentValue = discount.value || 0;
        return total + (totalAmount * percentValue / 100);
      } if (discount.type === 'fixed') {
        // Giảm giá theo số tiền cố định
        return total + (discount.value || 0);
      }
      return total;
    }, 0);
  }

  /**
   * Tính phí vận chuyển từ thông tin giao hàng
   * @param order Đối tượng đơn hàng
   * @returns Phí vận chuyển
   */
  private calculateDeliveryFee(order: CreateOrderRequest): number {
    if (!order.delivery) {
      return 0;
    }

    // Nếu là tự vận chuyển, không tính phí với khách hàng
    if (order.delivery.selfTransport) {
      return 0;
    }

    // Nếu có thông tin vận chuyển thực tế
    if (order.delivery.physicalDelivery && order.delivery.physicalDelivery.deliveryInfo) {
      return order.delivery.physicalDelivery.deliveryInfo.deliveryFeeForCustomer || 0;
    }

    return 0;
  }

  /**
   * Tạo danh sách 8 giá trị thanh toán nhanh dựa trên tổng tiền
   * @param totalAmount Tổng số tiền cần thanh toán
   * @returns Danh sách giá trị thanh toán nhanh
   */
  calculateQuickValues(totalAmount: number): number[] {
    if (totalAmount <= 0) {
      return [0, 0, 0, 0, 0, 0, 0, 0];
    }

    // Làm tròn lên đến 1,000 đồng
    const roundedAmount = Math.ceil(totalAmount / 1000) * 1000;

    // Tạo 8 giá trị với độ chênh lệch phù hợp
    return [
      roundedAmount, // Giá trị chính xác
      roundedAmount + 5000, // +5,000
      roundedAmount + 10000, // +10,000
      roundedAmount + 20000, // +20,000
      roundedAmount + 50000, // +50,000
      roundedAmount + 100000, // +100,000
      roundedAmount + 200000, // +200,000
      roundedAmount + 500000 // +500,000
    ];
  }

  /**
   * Kiểm tra tính hợp lệ của đơn hàng
   * @param order Đối tượng đơn hàng
   * @returns true nếu đơn hàng hợp lệ, false nếu không
   */
  validateOrder(order: CreateOrderRequest): boolean {
    if (!order) {
      return false;
    }

    // Kiểm tra có ít nhất 1 sản phẩm
    if (!order.items || order.items.length === 0) {
      return false;
    }

    // Kiểm tra thông tin khách hàng
    if (!order.customer || !order.customer.phoneNumber) {
      return false;
    }

    // Kiểm tra thông tin vận chuyển nếu không phải tự vận chuyển
    if (order.delivery && !order.delivery.selfTransport) {
      // Nếu có thông tin vận chuyển nhưng không đầy đủ
      if (!order.delivery.physicalDelivery || !order.delivery.physicalDelivery.deliveryInfo) {
        return false;
      }
    }

    // Kiểm tra thông tin thanh toán
    if (!order.payment || !order.payment.payments || order.payment.payments.length === 0) {
      return false;
    }

    // Kiểm tra các trường bắt buộc khác
    if (!order.createdBy || !order.saleChannel || !order.status) {
      return false;
    }

    return true;
  }

  /**
   * Tổng hợp dữ liệu thành đối tượng CreateOrderRequest hoàn chỉnh
   * @param order Đối tượng đơn hàng cần hoàn thiện
   * @returns Đối tượng đơn hàng đã hoàn thiện
   */
  buildCreateOrderRequest(order: CreateOrderRequest): CreateOrderRequest {
    // Tạo bản sao để không ảnh hưởng đến dữ liệu gốc
    const finalOrder = { ...order };

    // Tính toán tóm tắt
    const summary = this.calculateSummary(finalOrder);

    // Cập nhật số tiền thực tế cho payment
    if (finalOrder.payment) {
      finalOrder.payment.totalAmount = summary.finalAmount;
      finalOrder.payment.remainingAmount = summary.debt;

      // Cập nhật trạng thái thanh toán
      if (summary.debt <= 0) {
        finalOrder.payment.paymentStatus = 'paid';
      } else if (finalOrder.payment.paidAmount > 0) {
        finalOrder.payment.paymentStatus = 'partially_paid';
      } else {
        finalOrder.payment.paymentStatus = 'unpaid';
      }
    }

    return finalOrder;
  }
}
