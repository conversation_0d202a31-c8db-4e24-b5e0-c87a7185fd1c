@use '../../../../shared/styles/_variable' as *;
 // Import biến nếu cần

.bottom-nav-container {
  position: fixed;
  bottom: -1px;
  left: 0;
  right: 0;
  height: $mobileBottomNavHeight+px; // <PERSON><PERSON><PERSON> nghĩa biến này trong variables.scss hoặc dùng giá trị cố định
  z-index: 9999; // Đ<PERSON>m bảo nằm trên cùng
  border-top: 1px solid var(--bs-border-color);
  background-color: #181818;
  padding-bottom: 20px; // fix bottom ios
}

.bottom-nav-item {
  flex: 1; // Chia đều không gian
  color: var(--bs-gray-600); // <PERSON><PERSON><PERSON> chữ mặc định
  transition: color 0.1s ease-in-out; // Hiệu ứng chuyển màu


  &.active {
    color: #ffcd37;

    .bottom-nav-icon {
      transform: scale(1.1);
    }
  }

  .bottom-nav-icon {
    transition: transform 0.2s ease;

    &.icon-active {
      animation: iconPulseMobile 0.2s ease;
    }
  }

  .bottom-nav-text {
    white-space: nowrap; // Ngăn text xuống dòng
    overflow: hidden;
    text-overflow: ellipsis; // Hiển thị dấu ... nếu text quá dài
    max-width: 100%; // Giới hạn chiều rộng text
    text-transform: uppercase;
    font-size: .65em;
  }
}

// Style cho button (mục "More")
button.bottom-nav-item {
  border: none;
  background: none;
  padding: 0; // Reset padding của button
  &:focus {
    outline: none; // Bỏ outline mặc định
    box-shadow: none;
  }
}
