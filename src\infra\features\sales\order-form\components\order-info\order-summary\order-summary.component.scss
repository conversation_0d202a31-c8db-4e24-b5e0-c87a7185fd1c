.order-summary-container {
  // height: 100%;
  width: 100%;
  // max-height: calc(100vh - 150px); // Đi<PERSON><PERSON> chỉnh theo layout tổng thể

  .order-summary-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 1rem;

    .payment-details-block {
      flex-grow: 1;
      margin-bottom: 1rem;
    }

    .payment-button-block {
      padding-top: 1rem;
      border-top: 1px solid #e0e0e0;
    }
  }
}
