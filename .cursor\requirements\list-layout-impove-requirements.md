

# Yêu Cầu <PERSON>ải Thiện ListLayoutComponent

## 1. Tự động điều chỉnh width của cột khi tổng width nhỏ hơn wrapper
- **Yêu cầu**: 
  - Hi<PERSON><PERSON> tại, mỗi cột đã có width được set trong `ListLayoutService` và lưu trong `localStorage`. <PERSON><PERSON>, khi số lượng cột quá ít, tổng width (tính bằng pixel) của các cột không đủ để bao phủ toàn bộ chiều rộng của wrapper (`scrollableContainer` hoặc `listContainer`).
  - Cần thêm logic để tự động tính toán và mở rộng width của các cột sao cho tổng width của tất cả các cột bằng 100% chiều rộng của wrapper khi tổng pixel của các cột nhỏ hơn chiều rộng wrapper.
  - Logic này nên được thực hiện trong `ListLayoutService` để giữ nguyên tắc separation of concerns, và áp dụng sau khi khởi tạo cột (`initializeColumnWidths`) hoặc khi số lượng cột thay đổi (`setColumns`).
  - Ưu tiên mở rộng đều các cột hoặc theo tỷ lệ width hiện tại của chúng, đảm bảo không vượt quá `maxWidth` (400px) được định nghĩa trong `config.columnWidthConstraints` (nếu chỉ có 1 cột hoặc 2 cột thì có thể để vượt maxWidth).
  - Cập nhật UI trong `ListLayoutComponent` để phản ánh width mới thông qua `applyColumnWidths`.

## 2. Khởi tạo width của cột dựa trên field type nếu không có trong localStorage
- **Yêu cầu**: 
  - Hiện tại, nếu width của cột không được lưu trong `localStorage`, nó sẽ sử dụng default CSS width. Thay vào đó, tôi muốn khởi tạo width ban đầu dựa trên loại field (`Field` từ `field.entity.ts`) khi cột được khởi tạo (`initializeColumnWidths` trong `ListLayoutService`).
  - Gợi ý giá trị width mặc định dựa trên loại field:
    - `number`, `percent`, `checkbox`: 120px (nhỏ gọn vì dữ liệu ngắn).
    - `text`, `email`, `phone`, `url`, `date`, `datetime`, `picklist`, `multi-picklist`, `user`, `search`: 200px (kích thước trung bình).
    - `textarea`, `upload-file`, `upload-image`: 300px (lớn hơn vì dữ liệu dài hoặc phức tạp).
    - `decimal`, `currency`: 150px (dữ liệu số nhưng cần không gian cho định dạng).
  - Logic này nên được tích hợp vào hàm `initializeColumnWidths` trong `ListLayoutService`. Nếu đã có width trong `localStorage`, ưu tiên sử dụng giá trị đó thay vì giá trị mặc định theo field type.
  - Đảm bảo các giá trị width tuân thủ `minWidth` (100px) và `maxWidth` (400px) trong `config.columnWidthConstraints`.

## 3. Dò tìm toàn bộ ListLayoutComponent/Service thành các component con các message khi có lỗi (console.warn hoặc console.error chẳng hạn), thông báo cho người dùng thông qua FlashMessageService, không dùng console nữa. Phải dùng i18n cho tất cả lỗi.

Tôi cho phép bạn có thể tùy ý suy nghĩ để cải thiện tốt hơn. Đây chỉ là suy nghỉ ban đầu của tôi.

