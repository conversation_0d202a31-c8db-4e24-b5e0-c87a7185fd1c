#!/usr/bin/env node
import { readFile, readdir, stat, writeFile } from 'fs/promises';
import { join, resolve } from 'path';
import glob from 'glob-promise';

// Hàm tìm translation key trong nội dung HTML
function findTranslationKeys(content, filePath) {
  const keys = [];
  // Regex tìm key trong {{ 'KEY' | translate }} hoặc [translate]="'KEY'"
  const regex = /\{\{\s*['"]([^'"]+)['"]\s*\|\s*translate\s*\}\}|\[translate\]\s*=\s*['"]([^'"]+)['"]/g;
  let match;

  // Đọc từng dòng để ghi số dòng
  const lines = content.split('\n');
  let lineNumber = 0;

  for (const line of lines) {
    lineNumber++;
    while ((match = regex.exec(line)) !== null) {
      const key = match[1] || match[2]; // Lấy key từ nhóm 1 hoặc 2
      if (key) {
        keys.push({ key, filePath, lineNumber, line: line.trim() });
      }
    }
  }

  return keys;
}

// Hàm quét file HTML trong folder
async function collectHtmlTranslationKeys(folderPath) {
  const keys = [];
  const pattern = `**/*.html`;
  const files = await glob(pattern, {
    cwd: folderPath,
    ignore: [
      'node_modules/**',
      'dist/**',
      'public/assets/i18n/**',
      'src/infra/i18n/**', // Bỏ qua file translation
    ],
    absolute: true,
  });

  console.log(`Tìm thấy ${files.length} file .html trong ${folderPath}`);

  for (const file of files) {
    try {
      const content = await readFile(file, 'utf-8');
      const fileKeys = findTranslationKeys(content, file);
      keys.push(...fileKeys);
    } catch (error) {
      console.warn(`Cảnh báo: Không thể đọc file ${file}: ${error.message}`);
    }
  }

  return keys;
}

// Hàm xuất danh sách key ra file
async function exportTranslationKeys(keys, outputFile) {
  const outputContent = `module.exports = ${JSON.stringify(keys, null, 2)};`;
  try {
    await writeFile(outputFile, outputContent, 'utf-8');
    console.log(`Đã xuất danh sách translation key ra: ${outputFile}`);
  } catch (error) {
    console.error(`Lỗi khi ghi file ${outputFile}: ${error.message}`);
  }
}


export { collectHtmlTranslationKeys, exportTranslationKeys };