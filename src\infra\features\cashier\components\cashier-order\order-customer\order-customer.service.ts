import { Injectable } from '@angular/core';
import { map, catchError } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { HttpService } from '@core/services/http.service';
import { isPhoneNumber, normalizeVietnameseStr } from '@shared/utils';
import { CustomerAutocompleteRecord, CustomerAutocompleteResult } from 'salehub_shared_contracts';

@Injectable({
  providedIn: 'root'
})

export class CashierOrderCustomerService {
  constructor(private http: HttpService) { }

  buildCreateNewData(term: string) {
    const isPhone = isPhoneNumber(term);
    const options = [
      {
        createNewUserWith: 'ADDRESS',
        text: `Tạo khách hàng mới với địa chỉ: ${term}`,
        value: term
      },
      {
        createNewUserWith: 'NAME',
        text: `Tạo khách hàng mới với tên khách hàng: ${term}`,
        value: term
      }
    ];
    return isPhone
      ? [...[
        {
          createNewUserWith: 'PHONE',
          text: `Tạo khách hàng mới với số ĐT: ${term}`,
          value: term
        }
      ], ...options]
      : options;
  }

  buildAutocompleteData(data: CustomerAutocompleteRecord[], term: string) {
    let hasExactMatch = false;
    const termVn = (normalizeVietnameseStr(term) || '').toLowerCase();
    const isPhoneTerm = isPhoneNumber(termVn);

    data.forEach(record => {
      record.exactMatch = false;

      if(isPhoneTerm) {
        if(record.phoneNumber === term) {
          record.exactMatch = true;
          record.semiExactMatch = true; // de sort
          hasExactMatch = true;
        }
      } else {
        const nameVn = (normalizeVietnameseStr(record.name) ?? '').toLowerCase();
        const addressVn = (normalizeVietnameseStr(record.address?.fullAddress) ?? '').toLowerCase();
        const phone = record.phoneNumber || '';

        if(
          nameVn.includes(termVn) ||
          addressVn.includes(termVn) ||
          phone.includes(termVn)
        ) {
          record.semiExactMatch = true;
        }
      }

      return record;
    });

    /**
     * sort theo exactMatch
     */
    data = data.sort((x, y) => Number(y.semiExactMatch) - Number(x.semiExactMatch));

    /**
     * search trên server như hạch
     * nếu ko có exact match vẫn phải thêm options
     * để tạo customer mới
     */
    if(!hasExactMatch) {
      return [
        ...data,
        ...this.buildCreateNewData(term)
      ];
    };

    return data;
  }

  search(term: string): Observable<CustomerAutocompleteResult | null> {
    // console.log(term);

    if(!term?.length || term?.length < 3) {
      return of(null);
    }

    return this.http.get('cashier', `cashier_customer&term=${term}`, { timeout: 10000 })
      .pipe(
        map((result: any) => {
          if(result?.cashier_customer?.length > 0) {
            return this.buildAutocompleteData(result.cashier_customer, term);
          }

          return this.buildCreateNewData(term);
        })
      )
      .pipe(
        catchError((error: any) => {
          console.warn(error);
          return of(this.buildCreateNewData(term));
        })
      );
  }
}
