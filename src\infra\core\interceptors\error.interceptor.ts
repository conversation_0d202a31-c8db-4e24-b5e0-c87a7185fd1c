import {  HttpErrorResponse } from '@angular/common/http';
import { catchError, throwError } from 'rxjs';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';
import { FlashMessageService } from '../services/flash_message.service';
import { HttpLoaderService } from '../services/http_loader.service';
import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const flashMessageService = inject(FlashMessageService); // Inject service trong function
  const loader = inject(HttpLoaderService); // Inject service trong function
  const { hideError, useLoader } = req.context.get(HTTP_REQUEST_OPTIONS);

  if(!hideError) {
    return next(req).pipe(
      catchError((err: HttpErrorResponse) => {
        let errorShow: string;
        // this.flashMessageService.error(JSON.stringify(err));

        if(err.status === 0) {
          errorShow = 'M<PERSON>y chủ không phản hồi';
        } else {
          let msg = (err.error && err.error.message) || err.error;
          if(typeof msg === 'object' && msg !== null) {
            msg = JSON.stringify(msg);
          }
          errorShow = `${msg}<br>${err.statusText}<br>Url: ${err.url}<br>Mã lỗi: ${err.status}`;
        }

        flashMessageService.error(errorShow);

        if(useLoader) {
          loader.setLoader(false);
        }

        return throwError(() => err);
      })
    );
  }

  return next(req);
};
