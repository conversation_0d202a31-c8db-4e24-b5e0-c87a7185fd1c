import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { join, resolve } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const sharedPath = resolve(join(__dirname + '/../../../src/infra/shared'));
const i18nPath = resolve(join(__dirname + '/../../../src/infra/i18n'));

export const configs = {
  componentPath: resolve(join(sharedPath, 'components', 'dynamic-layout-builder')),
  i18nComponentPath: resolve(join(i18nPath, 'shared', 'dynamic-layout-builder')),
}
