<div class="warehouse-modal">
  <h2 mat-dialog-title>{{ 'WAREHOUSE.SELECT_WAREHOUSE' | translate }}</h2>

  <mat-dialog-content>
    <form [formGroup]="warehouseForm">
      <div formGroupName="warehouseSection">
        <app-warehouse-and-location-picker
          [warehouses]="warehouses"
          [suppliers]="suppliers"
          [locations]="locations"
          (addSupplier)="onAddSupplier()">
        </app-warehouse-and-location-picker>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()">{{ 'COMMON.CANCEL' | translate }}</button>
    <button mat-raised-button color="primary" (click)="onSave()">{{ 'COMMON.SAVE' | translate }}</button>
  </mat-dialog-actions>
</div>
