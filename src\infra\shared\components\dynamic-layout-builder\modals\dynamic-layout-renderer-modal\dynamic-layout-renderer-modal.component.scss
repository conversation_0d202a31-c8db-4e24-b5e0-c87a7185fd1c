/**
 * DynamicLayoutRendererModal Styles
 * 
 * Chức năng:
 * - Responsive design cho modal layout
 * - Loading overlay styles
 * - Badge và status indicator styles
 * - Footer action styles
 */

.dynamic-layout-renderer-modal {
  // ===== MODAL LAYOUT =====
  
  .modal-header {
    padding: 1rem 1.5rem;
    background-color: var(--bs-light);
    
    .modal-title {
      font-weight: 600;
      color: var(--bs-dark);
    }
    
    // Badge styles
    .badge {
      font-size: 0.75rem;
      padding: 0.375rem 0.75rem;
      
      &.badge-primary {
        background-color: var(--bs-primary);
        color: white;
      }
      
      &.badge-secondary {
        background-color: var(--bs-secondary);
        color: white;
      }
      
      &.badge-warning {
        background-color: var(--bs-warning);
        color: var(--bs-dark);
      }
      
      &.badge-info {
        background-color: var(--bs-info);
        color: white;
      }
      
      i {
        font-size: 0.875rem;
      }
    }
  }

  .modal-body {
    position: relative;
    max-height: 70vh;
    overflow-y: auto;
    
    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: var(--bs-light);
    }
    
    &::-webkit-scrollbar-thumb {
      background: var(--bs-secondary);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: var(--bs-dark);
    }
  }

  .modal-footer {
    padding: 1rem 1.5rem;
    background-color: var(--bs-light);
    
    .footer-info {
      small {
        font-weight: 500;
        
        &.text-warning {
          color: var(--bs-warning) !important;
        }
        
        &.text-muted {
          color: var(--bs-secondary) !important;
        }
        
        i {
          font-size: 0.875rem;
        }
      }
    }
    
    .footer-actions {
      .btn {
        min-width: 100px;
        font-weight: 500;
        
        i {
          font-size: 0.875rem;
        }
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }

  // ===== LOADING OVERLAY =====
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    
    .loading-content {
      text-align: center;
      
      .spinner-border {
        width: 2rem;
        height: 2rem;
      }
      
      p {
        color: var(--bs-primary);
        font-weight: 500;
        font-size: 0.875rem;
      }
    }
  }

  // ===== EMPTY STATE =====
  
  .empty-state {
    padding: 3rem 2rem;
    
    i {
      opacity: 0.5;
    }
    
    h5 {
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    
    p {
      font-size: 0.875rem;
      margin-bottom: 0;
    }
  }

  // ===== RESPONSIVE DESIGN =====
  
  // Mobile styles
  @media (max-width: 767.98px) {
    .modal-header {
      padding: 0.75rem 1rem;
      
      .modal-title {
        font-size: 1.1rem;
      }
      
      .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
      }
    }
    
    .modal-body {
      max-height: 60vh;
    }
    
    .modal-footer {
      padding: 0.75rem 1rem;
      
      .footer-actions {
        .btn {
          min-width: 80px;
          font-size: 0.875rem;
        }
      }
    }
    
    .empty-state {
      padding: 2rem 1rem;
      
      i {
        font-size: 2rem !important;
      }
      
      h5 {
        font-size: 1rem;
      }
    }
  }

  // Tablet styles
  @media (min-width: 768px) and (max-width: 991.98px) {
    .modal-body {
      max-height: 65vh;
    }
  }

  // Desktop styles
  @media (min-width: 992px) {
    .modal-body {
      max-height: 70vh;
    }
  }

  // ===== ANIMATION ENHANCEMENTS =====
  
  .badge {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
  
  .btn {
    transition: all 0.2s ease-in-out;
    
    &:not(:disabled):hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
  
  .loading-overlay {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  // ===== ACCESSIBILITY =====
  
  // Focus styles
  .btn:focus,
  .modal-title:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
  }
  
  // High contrast mode support
  @media (prefers-contrast: high) {
    .badge {
      border: 1px solid currentColor;
    }
    
    .loading-overlay {
      background-color: rgba(255, 255, 255, 0.95);
    }
  }
  
  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    .badge,
    .btn,
    .loading-overlay {
      transition: none;
      animation: none;
    }
    
    .btn:not(:disabled):hover {
      transform: none;
    }
  }
}
