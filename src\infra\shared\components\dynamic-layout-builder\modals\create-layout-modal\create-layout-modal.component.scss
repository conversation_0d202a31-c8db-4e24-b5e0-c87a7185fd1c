/**
 * Styles cho CreateLayoutModalComponent
 * Modal để tạo layout mới trong Dynamic Layout Builder
 * Sử dụng với ResponsiveModalService và default actions
 */

.create-layout-modal-content {
  padding: 1rem;
  min-width: 400px;
  max-width: 500px;
}

.alert-info {
  background-color: #e7f3ff;
  border-color: #b8daff;
  color: #004085;
  border-radius: 0.375rem;
  padding: 0.75rem;
}

.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {
  margin-top: 0.5rem;
}

// Responsive styles
@media (max-width: 576px) {
  .create-layout-modal-content {
    min-width: 100%;
    max-width: 100%;
    padding: 0.75rem;
  }

  .alert-info {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .create-layout-modal-content {
    padding: 0.5rem;
  }
}
