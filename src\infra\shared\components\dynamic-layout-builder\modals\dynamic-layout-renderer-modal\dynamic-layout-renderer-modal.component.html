<!-- 
  DynamicLayoutRendererModal Template
  
  <PERSON><PERSON><PERSON> năng:
  - <PERSON><PERSON><PERSON> thị DynamicLayoutRendererComponent trong modal layout
  - Responsive design với Bootstrap classes
  - Hỗ trợ cả View mode và Form Edit mode
  - Loading state khi save form
-->

<div class="dynamic-layout-renderer-modal">
  <!-- Modal Header -->
  <div class="modal-header border-bottom">
    <div class="d-flex align-items-center">
      <!-- Modal Title -->
      <h4 class="modal-title mb-0 me-3">
        {{ modalTitle }}
      </h4>
      
      <!-- Mode Badge -->
      <span 
        class="badge"
        [class.badge-primary]="isEditMode"
        [class.badge-secondary]="!isEditMode">
        <i 
          class="fas fa-fw me-1"
          [class.fa-edit]="isEditMode"
          [class.fa-eye]="!isEditMode">
        </i>
        {{ isEditMode 
          ? ('DYNAMIC_LAYOUT_RENDERER_MODAL.BADGES.EDIT_MODE' | translate)
          : ('DYNAMIC_LAYOUT_RENDERER_MODAL.BADGES.VIEW_MODE' | translate) }}
      </span>
      
      <!-- Changes Indicator -->
      @if (hasChanges()) {
        <span class="badge badge-warning ms-2">
          <i class="fas fa-exclamation-triangle me-1"></i>
          {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.BADGES.HAS_CHANGES' | translate }}
        </span>
      }
      
      <!-- Saving Indicator -->
      @if (isSaving()) {
        <span class="badge badge-info ms-2">
          <i class="fas fa-spinner fa-spin me-1"></i>
          {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.BADGES.SAVING' | translate }}
        </span>
      }
    </div>
  </div>

  <!-- Modal Body -->
  <div class="modal-body p-0">
    <!-- Loading Overlay -->
    @if (isSaving()) {
      <div class="loading-overlay">
        <div class="loading-content">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
          </div>
          <p class="mt-2 mb-0">
            {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.LOADING.SAVING_FORM' | translate }}
          </p>
        </div>
      </div>
    }

    <!-- DynamicLayoutRenderer Component -->
    @if (rendererConfig()) {
      <app-dynamic-layout-renderer
        [config]="rendererConfig()!"
        (formDataChange)="onFormChange($event)"
        class="d-block">
      </app-dynamic-layout-renderer>
    } @else {
      <!-- Empty State -->
      <div class="empty-state text-center py-5">
        <i class="fas fa-exclamation-circle text-muted fa-3x mb-3"></i>
        <h5 class="text-muted">
          {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.EMPTY_STATE.NO_CONFIG' | translate }}
        </h5>
        <p class="text-muted">
          {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.EMPTY_STATE.NO_CONFIG_DESCRIPTION' | translate }}
        </p>
      </div>
    }
  </div>

  <!-- Modal Footer (chỉ hiển thị trong Edit Mode) -->
  @if (isEditMode) {
    <div class="modal-footer border-top">
      <div class="d-flex justify-content-between align-items-center w-100">
        <!-- Left Side - Status Info -->
        <div class="footer-info">
          @if (hasChanges()) {
            <small class="text-warning">
              <i class="fas fa-exclamation-triangle me-1"></i>
              {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.FOOTER.UNSAVED_CHANGES' | translate }}
            </small>
          } @else {
            <small class="text-muted">
              <i class="fas fa-check-circle me-1"></i>
              {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.FOOTER.NO_CHANGES' | translate }}
            </small>
          }
        </div>

        <!-- Right Side - Action Buttons -->
        <div class="footer-actions">
          <!-- Cancel Button -->
          <button 
            type="button" 
            class="btn btn-secondary me-2"
            [disabled]="isSaving()"
            data-bs-dismiss="modal">
            <i class="fas fa-times me-1"></i>
            {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.BUTTONS.CANCEL' | translate }}
          </button>

          <!-- Save Button -->
          <button 
            type="button" 
            class="btn btn-primary"
            [disabled]="!hasChanges() || isSaving() || !isValid()"
            (click)="onSave()">
            @if (isSaving()) {
              <i class="fas fa-spinner fa-spin me-1"></i>
              {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.BUTTONS.SAVING' | translate }}
            } @else {
              <i class="fas fa-save me-1"></i>
              {{ 'DYNAMIC_LAYOUT_RENDERER_MODAL.BUTTONS.SAVE' | translate }}
            }
          </button>
        </div>
      </div>
    </div>
  }
</div>
