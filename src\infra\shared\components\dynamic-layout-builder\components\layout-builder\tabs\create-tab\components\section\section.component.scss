// SectionComponent styles
.section-container {
  display: flex;
  flex-direction: column;
  background-color: var(--bs-white);
  border: 1px solid var(--bs-border-color);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: move;

  // Hover effect
  &:hover {
    border-color: var(--bs-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  // Section header
  .section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: .1rem 1rem;
    background-color: var(--bs-light);
    border-bottom: 1px solid var(--bs-border-color);

    // Drag handle
    .drag-handle {
      display: flex;
      align-items: center;
      color: var(--bs-text-muted);
      cursor: grab;

      &:active {
        cursor: grabbing;
      }

      mat-icon {
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }

    // Section title
    .section-title {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      cursor: pointer;

      .title-text {
        margin: 0;
        font-size: 1rem;
        font-weight: 600;
        color: var(--bs-dark);
        transition: color 0.2s ease;

        &:hover {
          color: var(--bs-primary);
        }
      }

      .edit-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
        color: var(--bs-text-muted);
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      &:hover .edit-icon {
        opacity: 1;
      }
    }

    // Title editor
    .title-editor {
      flex: 1;

      .title-input {
        width: 100%;

        ::ng-deep {
          .mat-mdc-form-field-wrapper {
            padding-bottom: 0;
          }

          .mat-mdc-text-field-wrapper {
            height: 2.5rem;
          }

          .mat-mdc-form-field-infix {
            min-height: 2.5rem;
            padding: 0.5rem 0;
          }
        }
      }
    }

    // Section actions
    .section-actions {
      display: flex;
      align-items: center;
      gap: 0.25rem;

      .add-field-btn {
        // width: 2.25rem;
        // height: 2.25rem;
        color: var(--bs-success);

        &:hover {
          background-color: rgba(var(--bs-success-rgb), 0.1);
        }

        // mat-icon {
        //   font-size: 1.125rem;
        //   width: 1.125rem;
        //   height: 1.125rem;
        // }
      }

      .delete-btn {
        // width: 2.25rem;
        // height: 2.25rem;
        color: var(--bs-danger);

        &:hover {
          background-color: rgba(var(--bs-danger-rgb), 0.1);
        }

        // mat-icon {
        //   font-size: 1.125rem;
        //   width: 1.125rem;
        //   height: 1.125rem;
        // }
      }
    }
  }

  // Section content
  .section-content {
    flex: 1;

    // Field drop zone
    .field-drop-zone {
      min-height: 4rem;
      padding: 1rem;

      &.has-fields {
        padding: 0.75rem;
      }

      // Empty drop zone
      .empty-drop-zone {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 6rem;
        padding: 1.5rem;
        border: 2px dashed var(--bs-border-color);
        border-radius: 6px;
        background-color: var(--bs-light);
        transition: all 0.2s ease;

        .drop-icon {
          font-size: 2.5rem;
          width: 2.5rem;
          height: 2.5rem;
          color: var(--bs-text-muted);
          margin-bottom: 0.5rem;
          opacity: 0.6;
        }

        .drop-message {
          font-size: 0.875rem;
          color: var(--bs-text-muted);
          text-align: center;
          margin: 0;
          line-height: 1.4;
        }

        // Hover effect when dragging
        &:hover,
        &.cdk-drop-list-receiving {
          border-color: var(--bs-primary);
          background-color: rgba(var(--bs-primary-rgb), 0.05);

          .drop-icon {
            color: var(--bs-primary);
            opacity: 0.8;
          }

          .drop-message {
            color: var(--bs-primary);
          }
        }
      }
    }
  }

  // Drag preview
  .section-drag-preview {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background-color: var(--bs-primary);
    color: white;
    border-radius: 6px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);

    .preview-content {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;

      .field-count {
        font-size: 0.75rem;
        opacity: 0.8;
      }
    }
  }
}

// CDK drag states
.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .section-container:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drag-placeholder {
  opacity: 0.4;
  border: 2px dashed var(--bs-primary);
  background-color: var(--bs-light);
}

// Responsive design
@media (max-width: 768px) {
  .section-container {
    .section-header {
      padding: 0.75rem;
      gap: 0.5rem;

      .title-text {
        font-size: 0.9375rem;
      }

      .delete-btn {
        width: 2rem;
        height: 2rem;

        mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }

    .section-content {
      .field-drop-zone {
        padding: 0.75rem;

        &.has-fields {
          padding: 0.5rem;
        }

        .empty-drop-zone {
          min-height: 5rem;
          padding: 1rem;

          .drop-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
          }

          .drop-message {
            font-size: 0.8125rem;
          }
        }
      }
    }
  }
}

// Quick Add Field Menu styles
::ng-deep .quick-add-field-menu {
  min-width: 400px;

  .mat-mdc-menu-content {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
  }
  
  .mat-mdc-menu-panel {
    max-width: 280px;
    min-width: 240px;
  }

  .menu-section-header {
    padding: 0.5rem 1rem;
    background-color: var(--bs-light);
    border-bottom: 1px solid var(--bs-border-color);

    .menu-section-title {
      font-size: 0.75rem;
      font-weight: 600;
      color: var(--bs-text-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
  .mat-mdc-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.4;
    width: 50%;

    .mat-mdc-menu-item-text {
      overflow: hidden;
      white-space: nowrap;
    }

    mat-icon {
      font-size: 1.125rem;
      width: 1.125rem;
      height: 1.125rem;
      color: var(--bs-primary);
    }

    &:hover {
      background-color: rgba(var(--bs-primary-rgb), 0.08);

      mat-icon {
        color: var(--bs-primary);
      }
    }
  }
  

  .mat-mdc-menu-divider {
    margin: 0.25rem 0;
    border-top-color: var(--bs-border-color);
  }
}

