import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';

import { LocationService } from './location.service';
import { LocationType, LocationStatus, LocationFormData } from '../models/view/location-view.model';

describe('LocationService', () => {
  let service: LocationService;
  let httpMock: HttpTestingController;

  const mockLocation = {
    id: '1',
    name: 'Test Location',
    code: 'WH-1',
    type: LocationType.Warehouse,
    level: 1,
    parentId: null,
    capacity: 1000,
    dimensions: { length: 10, width: 10, height: 5 },
    status: LocationStatus.Active,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockFormData: LocationFormData = {
    name: 'Test Location',
    code: 'WH-1',
    type: LocationType.Warehouse,
    parentId: null,
    capacity: 1000,
    dimensions: { length: 10, width: 10, height: 5 },
    status: LocationStatus.Active,
    autoGenerateCode: false,
    quantity: 1
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [LocationService]
    });

    service = TestBed.inject(LocationService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get all locations', (done) => {
    service.getLocations().subscribe(locations => {
      expect(locations).toBeTruthy();
      expect(locations.length).toBeGreaterThanOrEqual(1);
      done();
    });
  });

  it('should get location by id', (done) => {
    service.getLocationById('1').subscribe(location => {
      if (location) {
        expect(location.id).toBe('1');
      }
      done();
    });
  });

  it('should create a new location', (done) => {
    service.createLocation(mockFormData).subscribe(location => {
      expect(location).toBeTruthy();
      expect(location.name).toBe(mockFormData.name);
      expect(location.code).toBe(mockFormData.code);
      done();
    });
  });

  it('should update a location', (done) => {
    const updatedData = { ...mockFormData, name: 'Updated Location' };

    service.updateLocation('1', updatedData).subscribe(location => {
      expect(location).toBeTruthy();
      expect(location.name).toBe(updatedData.name);
      done();
    });
  });

  it('should delete a location', (done) => {
    service.deleteLocation('1').subscribe(result => {
      expect(result).toBeTrue();
      done();
    });
  });

  it('should generate a location code', () => {
    const code = service.generateLocationCode('WH-1', LocationType.Zone);
    expect(code).toBeTruthy();
    expect(code.includes('WH-1-Z')).toBeTrue();
  });

  it('should convert locations to tree nodes', () => {
    const treeNodes = service.convertToTreeNodes([mockLocation]);
    expect(treeNodes).toBeTruthy();
    expect(treeNodes.length).toBe(1);
    expect(treeNodes[0].label).toContain(mockLocation.name);
  });
});
