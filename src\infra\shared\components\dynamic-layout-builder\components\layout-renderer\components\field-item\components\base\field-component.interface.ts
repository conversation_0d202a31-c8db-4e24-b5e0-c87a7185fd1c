import { EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';

import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

/**
 * Interface định nghĩa contract cho tất cả field components
 * 
 * Đảm bảo tính nhất quán giữa các field components và cung cấp
 * type safety cho việc implement các methods bắt buộc.
 * 
 * Extends từ BaseFieldComponent và FormFieldComponent để maintain
 * backward compatibility với existing interfaces.
 */
export interface FieldComponentInterface {
  
  // ===== REQUIRED PROPERTIES =====
  
  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   * Tuân thủ yêu cầu: component chỉ nhận 1 input object
   */
  config: FieldItemConfig;

  /**
   * Form control cho form mode
   * Được quản lý bởi AbstractFieldComponent
   */
  formControl: () => FormControl;

  /**
   * Event emitter cho form value changes
   * Emit khi user thay đổi giá trị trong form mode
   */
  valueChange: EventEmitter<{ fieldId: string; value: FieldValue }>;

  // ===== REQUIRED METHODS =====

  /**
   * Khởi tạo form control cho form mode
   * Bao gồm validators và disabled state
   * 
   * Implementation note: Sử dụng field.value làm nguồn dữ liệu duy nhất
   */
  initializeFormControl(): void;

  /**
   * Handle khi field.value thay đổi từ bên ngoài (ngOnChanges)
   * Cập nhật form control value nếu cần
   * 
   * Implementation note: Được gọi trong ngOnChanges khi config.field.value thay đổi
   */
  handleFieldValueChange(): void;

  /**
   * Lấy giá trị hiện tại của field
   * Trả về form control value (form mode) hoặc current value (view mode)
   */
  getFieldValue(): FieldValue;

  /**
   * Lấy icon phù hợp cho field type
   * Sử dụng trong template để hiển thị icon
   */
  getFieldIcon(): string;

  // ===== OPTIONAL METHODS =====

  /**
   * Bind giá trị từ form data vào field
   * Sử dụng khi initialize form với existing data
   * 
   * @param value - Giá trị cần bind vào field
   */
  bindFormValue?(value: FieldValue): void;

  /**
   * Handle khi user thay đổi giá trị field
   * Emit change event để Form Management Service track
   * 
   * @param value - Giá trị mới của field
   */
  onValueChange?(value: FieldValue): void;

  // Note: validateFieldType, generateMockValue, getValidators are implemented as protected methods in AbstractFieldComponent

  /**
   * Lấy HTML input type phù hợp (cho text-based fields)
   * Ví dụ: 'email', 'tel', 'url', 'number', etc.
   */
  getInputType?(): string;

  /**
   * Lấy step value cho number inputs
   * Ví dụ: 1 cho integer, 0.01 cho decimal, etc.
   */
  getStepValue?(): number;

  /**
   * Lấy suffix text cho number fields
   * Ví dụ: '%', 'VND', etc.
   */
  getSuffixText?(): string;

  /**
   * Kiểm tra xem có phải multi-select không (cho select fields)
   */
  isMultiSelect?(): boolean;

  /**
   * Lấy options cho select fields
   */
  getSelectOptions?(): Array<{ value: any; label: string }>;

  /**
   * Lấy mock values cho multi-select fields
   */
  getMockValues?(): string[];
}

