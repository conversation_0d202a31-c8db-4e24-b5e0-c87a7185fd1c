import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { NoteModalComponent } from './note-modal.component';
import { NoteModalData, NoteModalResult } from './models/note-modal.model';

/**
 * Service để mở modal ghi chú
 */
@Injectable({
  providedIn: 'root'
})
export class NoteModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal ghi chú
   * @param data Dữ liệu cho modal
   * @returns Promise<NoteModalResult | null> Kết quả ghi chú hoặc null nếu hủy
   */
  async open(data: NoteModalData): Promise<NoteModalResult | null> {
    try {
      const modalConfig = {
        data,
        width: '500px',
        maxWidth: '95vw'
      };

      const result = await this.responsiveModalService.open<
        NoteModalData,
        NoteModalResult,
        NoteModalComponent
      >(NoteModalComponent, modalConfig);

      return result || null;
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú:', error);
      return null;
    }
  }
}
