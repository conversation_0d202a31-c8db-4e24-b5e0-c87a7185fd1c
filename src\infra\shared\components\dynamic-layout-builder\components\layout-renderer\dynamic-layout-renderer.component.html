<!-- Dynamic Layout Renderer Container -->
<div class="dynamic-layout-renderer-container">
  
  <!-- Header Section -->
  <div class="renderer-header">
    
    <!-- Component Title (Optional) -->
    <div class="header-title mb-3">
      <h4 class="mb-1">{{ 'DYNAMIC_LAYOUT_RENDERER.TITLE' | translate }}</h4>
      <p class="text-muted mb-0">{{ 'DYNAMIC_LAYOUT_RENDERER.DESCRIPTION' | translate }}</p>
    </div>
    
    <!-- Permission Profile Selector -->
    @if (shouldShowPermissionSelector()) {
      <div class="permission-selector-section mb-3">
        <app-permission-selector
          [config]="permissionSelectorConfig()"
          (permissionProfileChange)="onPermissionProfileChange($event)">
        </app-permission-selector>
      </div>
    }
    
    <!-- Tab Switcher (View/Form) -->
    @if (shouldShowTabSwitcher()) {
      <div class="tab-switcher-section">
        <mat-tab-group 
          [selectedIndex]="getCurrentTabIndex()"
          (selectedTabChange)="onTabChange($event.index)"
          class="renderer-tabs"
          animationDuration="300ms">
          
          <!-- View Tab -->
          <mat-tab>
            <ng-template mat-tab-label>
              <mat-icon class="tab-icon me-2">visibility</mat-icon>
              {{ 'DYNAMIC_LAYOUT_RENDERER.TABS.VIEW' | translate }}
            </ng-template>
          </mat-tab>
          
          <!-- Form Tab -->
          <mat-tab>
            <ng-template mat-tab-label>
              <mat-icon class="tab-icon me-2">edit</mat-icon>
              {{ 'DYNAMIC_LAYOUT_RENDERER.TABS.FORM' | translate }}
            </ng-template>
          </mat-tab>
        </mat-tab-group>
      </div>
    }
  </div>
  
  <!-- Content Section -->
  <div class="renderer-content">

    <!-- Sections Container -->
    @if (hasSectionsToRender()) {
      <div class="sections-container">
        @for (sectionConfig of sectionConfigs(); track sectionConfig.section._id) {
          <app-section
            [config]="sectionConfig"
            (valueChange)="onFieldValueChange($event)"
            class="section-wrapper">
          </app-section>
        }
      </div>
    }

    <!-- Empty State -->
    @else {
      <div class="empty-state-container">
        <mat-card class="empty-state-card text-center">
          <mat-card-content class="py-5">
            <mat-icon class="empty-state-icon mb-3">folder_open</mat-icon>
            <h5 class="mb-2">{{ 'DYNAMIC_LAYOUT_RENDERER.EMPTY_STATE.TITLE' | translate }}</h5>
            <p class="text-muted mb-0">{{ 'DYNAMIC_LAYOUT_RENDERER.EMPTY_STATE.DESCRIPTION' | translate }}</p>
          </mat-card-content>
        </mat-card>
      </div>
    }
  </div>

  <!-- Form Controls Section (Only show in Form Edit Mode) -->
  @if (isFormEditMode()) {
    <div class="form-controls-section mt-4">
      <mat-card class="form-controls-card">
        <mat-card-content class="py-3">
          <div class="d-flex justify-content-between align-items-center">

            <!-- Form Status Info -->
            <div class="form-status-info">
              @if (formState().isDirty) {
                <span class="badge bg-warning text-dark me-2">
                  <mat-icon class="me-1" style="font-size: 14px;">edit</mat-icon>
                  {{ 'FORM_MESSAGES.UNSAVED_CHANGES' | translate }}
                </span>
              }
              @if (!formState().isValid) {
                <span class="badge bg-danger me-2">
                  <mat-icon class="me-1" style="font-size: 14px;">error</mat-icon>
                  {{ 'FORM_MESSAGES.VALIDATION_ERRORS' | translate }}
                </span>
              }
            </div>

            <!-- Action Buttons -->
            <div class="form-actions">
              <!-- Cancel Button -->
              <button
                type="button"
                mat-stroked-button
                color="warn"
                class="me-2"
                [disabled]="formState().isLoading"
                (click)="onFormCancel()">
                <mat-icon class="me-1">cancel</mat-icon>
                {{ 'FORM_ACTIONS.CANCEL' | translate }}
              </button>

              <!-- Save Button -->
              <button
                type="button"
                mat-raised-button
                color="primary"
                [disabled]="!canSave()"
                (click)="onFormSave()">
                @if (formState().isLoading) {
                  <mat-icon class="me-1 spinner">refresh</mat-icon>
                } @else {
                  <mat-icon class="me-1">save</mat-icon>
                }
                {{ 'FORM_ACTIONS.SAVE' | translate }}
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  }
</div>
