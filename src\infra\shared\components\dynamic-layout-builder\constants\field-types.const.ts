import { FieldType, Field } from '@domain/entities/field.entity';


/**
 * Constant chứa tất cả field types mặc định có thể sử dụng
 * Di chuyển từ method getDefaultFieldTypes() trong CoreLayoutBuilderService
 * Mỗi field có type để xác định loại - _id sẽ được generate khi tạo field mới
 * Descriptions sử dụng i18n keys với format FIELD_TYPES.{FIELD_TYPE}.DESCRIPTION
 */
export const DEFAULT_FIELD_TYPES: Field[] = [
  // Trường cơ bản
  {
    type: 'text',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT',
    order: 1,
    description: 'FIELD_TYPES.TEXT.DESCRIPTION',
    permissionProfiles: [] // Sẽ được khởi tạo với giá trị mặc định khi tạo field
  },
  {
    type: 'number',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.NUMBER',
    order: 2,
    description: 'FIELD_TYPES.NUMBER.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'email',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.EMAIL',
    order: 3,
    description: 'FIELD_TYPES.EMAIL.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'phone',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.PHONE',
    order: 4,
    description: 'FIELD_TYPES.PHONE.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'textarea',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT_AREA',
    order: 5,
    description: 'FIELD_TYPES.TEXTAREA.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'date',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DATE',
    order: 6,
    description: 'FIELD_TYPES.DATE.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'datetime',
    label: 'DateTime',
    order: 7,
    description: 'FIELD_TYPES.DATETIME.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'file',
    label: 'File',
    order: 8,
    description: 'FIELD_TYPES.FILE.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'image',
    label: 'Image',
    order: 9,
    description: 'FIELD_TYPES.IMAGE.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'checkbox',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.CHECKBOX',
    order: 10,
    description: 'FIELD_TYPES.CHECKBOX.DESCRIPTION',
    permissionProfiles: []
  },
  // Trường nâng cao
  {
    type: 'picklist',
    label: 'Picklist',
    order: 17,
    description: 'FIELD_TYPES.PICKLIST.DESCRIPTION',
    permissionProfiles: [],
    constraints: {
      picklistOptions: [],
    }
  },
  {
    type: 'multi-picklist',
    label: 'Multi Picklist',
    order: 18,
    description: 'FIELD_TYPES.MULTI_PICKLIST.DESCRIPTION',
    permissionProfiles: [],
    constraints: {
      picklistOptions: [],
    }
  },
  {
    type: 'url',
    label: 'URL',
    order: 19,
    description: 'FIELD_TYPES.URL.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'decimal',
    label: 'Decimal',
    order: 20,
    description: 'FIELD_TYPES.DECIMAL.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'currency',
    label: 'Currency',
    order: 21,
    description: 'FIELD_TYPES.CURRENCY.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'percent',
    label: 'Percent',
    order: 22,
    description: 'FIELD_TYPES.PERCENT.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'search',
    label: 'Search',
    order: 23,
    description: 'FIELD_TYPES.SEARCH.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'user',
    label: 'User',
    order: 24,
    description: 'FIELD_TYPES.USER.DESCRIPTION',
    permissionProfiles: []
  }
];

export const FIELD_ICON_MAPS: { [key in FieldType]: string } = {
  'text': 'text_fields',
  'number': 'numbers',
  'email': 'email',
  'phone': 'phone',
  'textarea': 'notes',
  'date': 'calendar_today',
  'datetime': 'schedule',
  'file': 'attach_file',
  'image': 'image',
  'checkbox': 'check_box',
  'radio': 'radio_button_checked',
  'select': 'arrow_drop_down',
  'picklist': 'arrow_drop_down',
  'multi-picklist': 'checklist',
  'url': 'link',
  'decimal': 'numbers',
  'currency': 'attach_money',
  'percent': 'percent',
  'search': 'search',
  'user': 'person'
}

export const FIELD_TYPE_LABEL_KEYS: { [key in FieldType]: string } = {
  'text': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT',
  'number': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.NUMBER',
  'email': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.EMAIL',
  'phone': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.PHONE',
  'textarea': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT_AREA',
  'date': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DATE',
  'datetime': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DATETIME',
  'file': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.FILE',
  'image': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.IMAGE',
  'checkbox': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.CHECKBOX',
  'radio': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.RADIO',
  'select': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.SELECT',
  'picklist': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.PICKLIST',
  'multi-picklist': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.MULTI_PICKLIST',
  'url': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.URL',
  'decimal': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DECIMAL',
  'currency': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.CURRENCY',
  'percent': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.PERCENT',
  'search': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.SEARCH',
  'user': 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.USER'
}

/**
 * Constants chứa màu sắc icon cho từng field type
 * Được extract từ SCSS files để centralize color management
 */
export const FIELD_ICON_COLORS: { [key in FieldType]: string } = {
  'text': '#4caf50',
  'number': '#ff9800',
  'email': '#2196f3',
  'phone': '#9c27b0',
  'textarea': '#607d8b',
  'date': '#f44336',
  'datetime': '#e91e63',
  'file': '#795548',
  'image': '#ff5722',
  'checkbox': '#8bc34a',
  'radio': '#cddc39',
  'select': '#00bcd4',
  'picklist': '#00bcd4',
  'multi-picklist': '#00bcd4',
  'url': '#2196f3',
  'decimal': '#ff9800',
  'currency': '#4caf50',
  'percent': '#ff9800',
  'search': '#2196f3', // Default color for search field
  'user': '#9c27b0' // Default color for user field
}

/**
 * Constants chứa màu sắc background cho từng field type
 * Được extract từ SCSS files để centralize color management
 * Sử dụng rgba với opacity 0.1 để tạo background subtle
 */
export const FIELD_BACKGROUND_COLORS: { [key in FieldType]: string } = {
  'text': 'rgba(76, 175, 80, 0.1)',
  'number': 'rgba(255, 152, 0, 0.1)',
  'email': 'rgba(33, 150, 243, 0.1)',
  'phone': 'rgba(156, 39, 176, 0.1)',
  'textarea': 'rgba(76, 175, 80, 0.1)',
  'date': 'rgba(244, 67, 54, 0.1)',
  'datetime': 'rgba(233, 30, 99, 0.1)',
  'file': 'rgba(121, 85, 72, 0.1)',
  'image': 'rgba(255, 87, 34, 0.1)',
  'checkbox': 'rgba(139, 195, 74, 0.1)',
  'radio': 'rgba(205, 220, 57, 0.1)',
  'select': 'rgba(0, 188, 212, 0.1)',
  'picklist': 'rgba(0, 188, 212, 0.1)',
  'multi-picklist': 'rgba(0, 188, 212, 0.1)',
  'url': 'rgba(33, 150, 243, 0.1)',
  'decimal': 'rgba(255, 152, 0, 0.1)',
  'currency': 'rgba(76, 175, 80, 0.1)',
  'percent': 'rgba(255, 152, 0, 0.1)',
  'search': 'rgba(33, 150, 243, 0.1)', // Default background for search field
  'user': 'rgba(156, 39, 176, 0.1)' // Default background for user field
}
