<!-- Permission Profile Selector -->
@if (config.showSelector && config.permissionProfiles.length > 0) {
  <div class="permission-selector-container">
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'DYNAMIC_LAYOUT_RENDERER.PERMISSION_SELECTOR.LABEL' | translate }}</mat-label>
      
      <mat-select 
        [value]="selectedProfileId()"
        (selectionChange)="onSelectionChange($event.value)"
        class="permission-select">
        
        @for (profile of config.permissionProfiles; track profile._id) {
          <mat-option [value]="profile._id" class="permission-option">
            <div class="d-flex align-items-center">
              <!-- Permission Icon -->
              <mat-icon 
                [class]="getPermissionColor(profile.permission)"
                class="me-2 permission-icon">
                {{ getPermissionIcon(profile.permission) }}
              </mat-icon>
              
              <!-- Profile Name -->
              <span class="profile-name">{{ profile.name }}</span>
              
              <!-- Permission Badge -->
              <span 
                class="ms-auto badge permission-badge"
                [class]="'badge-' + profile.permission">
                {{ 'DYNAMIC_LAYOUT_RENDERER.PERMISSION_LEVELS.' + profile.permission.toUpperCase() | translate }}
              </span>
            </div>
          </mat-option>
        }
      </mat-select>
      
      <!-- Helper text -->
      <mat-hint>
        {{ 'DYNAMIC_LAYOUT_RENDERER.PERMISSION_SELECTOR.HINT' | translate }}
      </mat-hint>
    </mat-form-field>
  </div>
}
