import { Settings } from '@shared/models/view/settings.model';

/**
 * Mock data cho danh sách các cài đặt hệ thống
 * Bao gồm đầy đủ các loại cài đặt: toggle, select, radio
 */
export const mockAllSettings: Settings = [
  // ===== TOGGLE SETTINGS =====
  {
    id: 'notifications_enabled',
    name: 'Bật thông báo',
    desc: 'Nhận thông báo về các hoạt động quan trọng trong hệ thống',
    type: 'toggle'
  },
  {
    id: 'auto_save',
    name: 'Tự động lưu',
    desc: 'Tự động lưu các thay đổi khi chỉnh sửa dữ liệu',
    type: 'toggle'
  },
  {
    id: 'dark_mode',
    name: 'Chế độ tối',
    desc: 'Sử dụng giao diện tối để giảm mỏi mắt khi làm việc ban đêm',
    type: 'toggle'
  },
  {
    id: 'email_notifications',
    name: 'Thông báo email',
    desc: '<PERSON><PERSON><PERSON> thông báo qua email cho các sự kiện quan trọng',
    type: 'toggle'
  },
  {
    id: 'sound_alerts',
    name: 'Âm thanh cảnh báo',
    desc: 'Phát âm thanh khi có thông báo mới hoặc cảnh báo',
    type: 'toggle'
  },

  // ===== SELECT SETTINGS =====
  {
    id: 'language',
    name: 'Ngôn ngữ',
    desc: 'Chọn ngôn ngữ hiển thị cho giao diện hệ thống',
    type: 'select',
    options: [
      { value: 'vi', label: 'Tiếng Việt' },
      { value: 'en', label: 'English' },
      { value: 'zh', label: '中文' },
      { value: 'ja', label: '日本語' }
    ]
  },
  {
    id: 'timezone',
    name: 'Múi giờ',
    desc: 'Chọn múi giờ để hiển thị thời gian chính xác',
    type: 'select',
    options: [
      { value: 'Asia/Ho_Chi_Minh', label: 'Việt Nam (GMT+7)' },
      { value: 'Asia/Tokyo', label: 'Tokyo (GMT+9)' },
      { value: 'America/New_York', label: 'New York (GMT-5)' },
      { value: 'Europe/London', label: 'London (GMT+0)' },
      { value: 'Asia/Singapore', label: 'Singapore (GMT+8)' }
    ]
  },
  {
    id: 'currency',
    name: 'Đơn vị tiền tệ',
    desc: 'Chọn đơn vị tiền tệ mặc định cho hệ thống',
    type: 'select',
    options: [
      { value: 'VND', label: 'Việt Nam Đồng (₫)' },
      { value: 'USD', label: 'US Dollar ($)' },
      { value: 'EUR', label: 'Euro (€)' },
      { value: 'JPY', label: 'Japanese Yen (¥)' },
      { value: 'CNY', label: 'Chinese Yuan (¥)' }
    ]
  },
  {
    id: 'date_format',
    name: 'Định dạng ngày',
    desc: 'Chọn cách hiển thị ngày tháng trong hệ thống',
    type: 'select',
    options: [
      { value: 'dd/MM/yyyy', label: 'DD/MM/YYYY (31/12/2024)' },
      { value: 'MM/dd/yyyy', label: 'MM/DD/YYYY (12/31/2024)' },
      { value: 'yyyy-MM-dd', label: 'YYYY-MM-DD (2024-12-31)' },
      { value: 'dd-MM-yyyy', label: 'DD-MM-YYYY (31-12-2024)' }
    ]
  },
  {
    id: 'items_per_page',
    name: 'Số mục trên trang',
    desc: 'Số lượng mục hiển thị trên mỗi trang danh sách',
    type: 'select',
    options: [
      { value: '10', label: '10 mục' },
      { value: '25', label: '25 mục' },
      { value: '50', label: '50 mục' },
      { value: '100', label: '100 mục' }
    ]
  },

  // ===== RADIO SETTINGS =====
  {
    id: 'theme_preference',
    name: 'Giao diện',
    desc: 'Chọn chế độ giao diện phù hợp với thời gian làm việc',
    type: 'radio',
    options: [
      { value: 'light', label: 'Sáng' },
      { value: 'dark', label: 'Tối' },
      { value: 'auto', label: 'Tự động theo hệ thống' }
    ]
  },
  {
    id: 'notification_frequency',
    name: 'Tần suất thông báo',
    desc: 'Chọn mức độ thông báo phù hợp với nhu cầu làm việc',
    type: 'radio',
    options: [
      { value: 'all', label: 'Tất cả thông báo' },
      { value: 'important', label: 'Chỉ thông báo quan trọng' },
      { value: 'minimal', label: 'Tối thiểu' },
      { value: 'none', label: 'Không thông báo' }
    ]
  },
  {
    id: 'backup_frequency',
    name: 'Tần suất sao lưu',
    desc: 'Chọn tần suất sao lưu dữ liệu tự động',
    type: 'radio',
    options: [
      { value: 'daily', label: 'Hàng ngày' },
      { value: 'weekly', label: 'Hàng tuần' },
      { value: 'monthly', label: 'Hàng tháng' },
      { value: 'manual', label: 'Thủ công' }
    ]
  },
  {
    id: 'security_level',
    name: 'Mức độ bảo mật',
    desc: 'Chọn mức độ bảo mật phù hợp với môi trường làm việc',
    type: 'radio',
    options: [
      { value: 'basic', label: 'Cơ bản' },
      { value: 'standard', label: 'Tiêu chuẩn' },
      { value: 'high', label: 'Cao' },
      { value: 'maximum', label: 'Tối đa' }
    ]
  },
  {
    id: 'data_sync_mode',
    name: 'Chế độ đồng bộ dữ liệu',
    desc: 'Chọn cách thức đồng bộ dữ liệu giữa các thiết bị',
    type: 'radio',
    options: [
      { value: 'realtime', label: 'Thời gian thực' },
      { value: 'scheduled', label: 'Theo lịch trình' },
      { value: 'manual', label: 'Thủ công' },
      { value: 'offline', label: 'Chỉ offline' }
    ]
  }
];

/**
 * Mock data cho các cài đặt theo danh mục
 */
export const mockSettingsByCategory = {
  general: mockAllSettings.filter(setting => 
    ['language', 'timezone', 'currency', 'date_format', 'theme_preference'].includes(setting.id)
  ),
  notifications: mockAllSettings.filter(setting => 
    ['notifications_enabled', 'email_notifications', 'sound_alerts', 'notification_frequency'].includes(setting.id)
  ),
  system: mockAllSettings.filter(setting => 
    ['auto_save', 'items_per_page', 'backup_frequency', 'data_sync_mode'].includes(setting.id)
  ),
  security: mockAllSettings.filter(setting => 
    ['security_level'].includes(setting.id)
  ),
  appearance: mockAllSettings.filter(setting => 
    ['dark_mode', 'theme_preference'].includes(setting.id)
  )
};

/**
 * Mock data cho giá trị mặc định của các cài đặt
 */
export const mockDefaultSettingValues: Record<string, any> = {
  // Toggle settings
  notifications_enabled: true,
  auto_save: true,
  dark_mode: false,
  email_notifications: true,
  sound_alerts: false,

  // Select settings
  language: 'vi',
  timezone: 'Asia/Ho_Chi_Minh',
  currency: 'VND',
  date_format: 'dd/MM/yyyy',
  items_per_page: '25',

  // Radio settings
  theme_preference: 'auto',
  notification_frequency: 'important',
  backup_frequency: 'weekly',
  security_level: 'standard',
  data_sync_mode: 'scheduled'
};
