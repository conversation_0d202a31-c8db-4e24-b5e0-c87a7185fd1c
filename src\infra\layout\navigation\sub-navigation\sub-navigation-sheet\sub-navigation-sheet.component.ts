import { Component, Inject, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { Navigation, NavigationItem } from '@config/navigation.config';
import { NavigationService } from '../../navigation.service';


@Component({
  selector: 'app-sub-navigation-sheet',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    MatBottomSheetModule
  ],
  templateUrl: './sub-navigation-sheet.component.html',
  styleUrls: ['./sub-navigation-sheet.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SubNavigationSheetComponent {
  items: NavigationItem[];
  activeNavigation!: Navigation | null;


  constructor(
    @Inject(MAT_BOTTOM_SHEET_DATA) public data: {
      items: NavigationItem[];
      parent?: NavigationItem
    },
    private bottomSheetRef: MatBottomSheetRef<SubNavigationSheetComponent>,
    private navigationService: NavigationService
  ) {
    this.activeNavigation = this.navigationService.findActiveParentNavigation(
      data.items?.[0]?.routerLink
    );

    if(!data.parent) {
      this.items = data.items;
    } else {
      this.items = [
        data.parent
      ];
    }

    console.log(this.items);
  }

  isNavigationItemActive(item: NavigationItem): boolean {
    return this.navigationService.isNavigationItemActive(item);
  }

  /**
   * Đóng bottom sheet khi một mục được chọn.
   * @param event Sự kiện click.
   */
  closeSheet(event: MouseEvent): void {
    event.preventDefault(); // Ngăn chặn hành vi mặc định nếu cần
    this.bottomSheetRef.dismiss();
  }

  /**
   * Hàm trackBy cho *ngFor.
   */
  trackByFn(index: number, item: NavigationItem): string {
    return this.navigationService.trackByFn(index, item);
  }
}
