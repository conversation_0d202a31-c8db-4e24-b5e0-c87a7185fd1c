import { Component, OnInit, OnDestroy, inject, signal, computed, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { LayoutSelectorService } from './layout-selector.service';
import { DynamicLayoutBuilderStateService } from '../../../services/dynamic-layout-builder-state.service';
import { DynamicLayoutConfigStateService } from '../../../services/dynamic-layout-config-state.service';
import { DynamicLayoutConfig } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.model';

/**
 * LayoutSelectorComponent - Component cho vi<PERSON><PERSON> chọn và quản lý layouts
 * 
 * Chức năng:
 * - Hiển thị mat-menu danh sách layouts từ DynamicLayoutBuilderConfig
 * - Cho phép switch giữa các layouts thông qua LayoutSelectorService
 * - Hiển thị thông tin metadata của layout (tên, mô tả, số sections/fields)
 * - Emit events khi layout thay đổi (backward compatibility với parent components)
 * - Hỗ trợ tạo layout mới với defaultLayoutConfig làm template
 *
 * Architecture:
 * - UI Logic: Component chỉ xử lý UI interactions và event binding
 * - Business Logic: Delegate tất cả business logic cho LayoutSelectorService
 * - Type Safety: Sử dụng DynamicLayoutBuilderConfig interface thay vì separate inputs
 */
@Component({
  selector: 'app-layout-selector',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatMenuModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule
  ],
  templateUrl: './layout-selector.component.html',
  styleUrls: ['./layout-selector.component.scss']
})
export class LayoutSelectorComponent implements OnInit, OnDestroy {
  private layoutSelectorService = new LayoutSelectorService();
  private stateService = inject(DynamicLayoutBuilderStateService); // UI state
  private configStateService = inject(DynamicLayoutConfigStateService); // Config & layout state


  availableLayouts = computed(() => (this.configStateService.state()?.config.layouts || []));
  selectedLayoutId = computed(() => (this.configStateService.currentLayout()?._id || ''));
  selectedLayout = this.configStateService.currentLayout;
  
  isLoading = this.stateService.isLoading;


  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    this.layoutSelectorService.destroy();
  }


  /**
   * Handle layout change from menu selection
   * UI Logic: Delegate to DynamicLayoutConfigStateService
   */
  onLayoutSelected(layoutId: string): void {
    if (layoutId && layoutId !== this.selectedLayoutId()) {
      this.configStateService.switchLayout(layoutId)
      .then(success => {
        if (success) {
          // console.log('✅ Layout switched successfully from LayoutSelector component');
        } else {
          // console.error('❌ Layout switch failed from LayoutSelector component');
        }
      });
    } else {
      // console.log('ℹ️ LayoutSelector: No switch needed - already at layout:', layoutId);
    }
  }

  async onCreateNewLayout(): Promise<void> {
    const defaultConfig = this.configStateService.state()!?.config?.defaultLayoutConfig;

    if (!defaultConfig) {
      // console.error('❌ No default layout config available for creating new layout');
      return;
    }

    // console.log('🚀 LayoutSelectorComponent: Opening CreateLayoutModal via service');

    try {
      // Delegate to service để mở modal và handle tạo layout
      const newLayoutId = await this.layoutSelectorService.createNewLayout(defaultConfig);

      if (newLayoutId) {
        // console.log('✅ New layout created successfully from LayoutSelector component:', {
        //   newLayoutId: newLayoutId,
        //   defaultConfigTitle: defaultConfig.title
        // });

        // ✅ FIXED: Sync new layout to centralized state service
        // Get updated config from MultiLayoutManagementService và update state service
        const multiLayoutManagementService = this.layoutSelectorService['multiLayoutManagementService'];
        const updatedMultiConfig = multiLayoutManagementService.multiLayoutConfig();

        if (updatedMultiConfig && updatedMultiConfig.layouts[newLayoutId]) {
          const newLayout = updatedMultiConfig.layouts[newLayoutId];

          // Add new layout to current config via DynamicLayoutConfigStateService
          const success = this.configStateService.addLayout(newLayout);
          if (success) {
            // console.log('✅ Added new layout to config state service');

            // Now switch to new layout
            this.configStateService.switchLayout(newLayoutId).then(switchSuccess => {
              if (switchSuccess) {
                console.log('✅ Switched to new layout successfully');
              } else {
                console.error('❌ Failed to switch to new layout after sync');
              }
            });
          }
        } else {
          console.error('❌ Could not find new layout in updated multi config');
        }
      } else {
        console.log('❌ CreateLayoutModal was cancelled or failed');
      }
    } catch (error) {
      console.error('❌ Error creating new layout:', error);
    }
  }





  /**
   * Format date for display
   * UI Helper: Simple date formatting
   */
  formatDate(date: Date): string {
    return date.toLocaleDateString('vi-VN');
  }
}
