<div class="p-3 py-5 invoice-bill" #bill>
  <div class="d-flex mb-4">
    <div>
      <div>
        <!-- fai resize 40px ko loi tren ios -->
        <img src="./assets/images/svg/logo-bill.svg" height="40" width="182" />
      </div>
      <div class="invoice-id">
        Hóa đơn #{{ order.invoiceId }}
      </div>
      <div class="text-muted mt-2">
        @if (order.times?.completedAt) {
        {{ order.times?.completedAt | formatDate: 'vietnamDateStr'}}
        } @else if(order.scheduledOrderInfo?.expectedDeliveryTime) {
          Hẹn giao vào {{ order.scheduledOrderInfo?.expectedDeliveryTime | formatDate: 'vietnamDateStr'}}
        } @else  {
          {{ order.times?.createdAt | formatDate: 'vietnamDateStr'}}
        }
      </div>
    </div>

    <div class="ms-auto mt-2 text-end res-info">
      @if (order.store?.address) {
      <div class="mb-1">
        {{ order.store?.address }}
      </div>
      }

      @if (order.store?.phone) {
      <div class="mb-1">
        {{ order.store?.phone }}
      </div>
      }

      @if (order.store?.website) {
      <div>
        {{ order.store?.website }}
      </div>
      }
    </div>
  </div>

  @if (order.customer &&
    (
      order.customer.phoneNumber ||
      order.customer.address?.fullAddress
    )
  ) {
  <div class="bill-customer mb-4">
    <div class="bill-customer-header">
      Thông tin khách hàng
    </div>
    <div class="bill-customer-body">
      @if (order.delivery && order.delivery.displayAddress) {
      <div class="mb-3 d-flex">
        <div class="label">Địa chỉ:</div>
        <div class="value">
          {{ order.delivery.displayAddress }}
        </div>
      </div>
      }

      @if (order.customer.phoneNumber) {
      <div class="d-flex">
        <div class="label">Điện thoại:</div>
        <div class="value">{{ order.customer.phoneNumber | formatString: 'phone'}}</div>
      </div>
      }
    </div>
  </div>
  }

  @if (order.delivery &&
    !order.delivery.selfArrive &&
    (
      (order.delivery.driver && order.delivery.driver.name) ||
      (order.delivery.company && order.delivery.company.name)
    )
    ) {
    <div class="bill-customer mb-4">
      <div class="bill-customer-header">
        Thông tin vận chuyển
      </div>
      <div class="bill-customer-body">
        @if (order.delivery.company && order.delivery.company.name) {
        <div class="mb-3 d-flex">
          <div class="label">Công ty:</div>
          <div class="value">
            {{ order.delivery.company.name }}
          </div>
        </div>
        }

        @if (order.delivery.company && order.delivery.company.phoneNumber) {
        <div class="mb-3 d-flex">
          <div class="label">Tổng đài:</div>
          <div class="value">
            {{ order.delivery.company.phoneNumber | formatString: 'phone' }}
          </div>
        </div>
        }

        @if (order.delivery.driver && order.delivery.driver.name) {
        <div class="mb-3 d-flex">
          <div class="label">Tài xế:</div>
          <div class="value">
            {{ order.delivery.driver.name }}
          </div>
        </div>
        }

        @if (order.delivery.driver && order.delivery.driver.phoneNumber) {
        <div class="mb-3 d-flex">
          <div class="label">Điện thoại:</div>
          <div class="value">
            {{ order.delivery.driver.phoneNumber | formatString: 'phone' }}
          </div>
        </div>
        }

        @if(!order.isThirdPartyInvoice) {
          @if (order.delivery.distance !== undefined) {
          <div class="mb-3 d-flex">
            <div class="label">Khoảng cách:</div>
            <div class="value">
              {{ order.delivery.distance }} km
            </div>
          </div>
          }

          @if (order.delivery.fee) {
          <div class="mb-3 d-flex">
            <div class="label">Phí vận chuyển:</div>
            <div class="value">
              <b>{{ order.delivery.fee | formatString: 'money' }}</b>
            </div>
          </div>
          }
        }

      </div>
    </div>
  }

  <div class="bill-main-container">
    <div class="d-flex align-items-center text-uppercase fw-bold bill-item-header bill-item">
      <div class="product">
        Sản phẩm
      </div>
      <div class="qty text-center">
        SL
      </div>
      <div class="unit text-center">
        Đ.giá
      </div>
      <div class="itotal text-end">
        Thành tiền
      </div>
    </div>

    @for (item of order.items; track item) {
    <div class="bill-item">
      <div class="d-flex align-items-center">
        <div class="product">
          <b>{{ item.thirdPartyItem?.name ?? item.name }}</b>
        </div>

        <div class="qty text-center">
          {{ item.quantity | formatString: 'money' }}
        </div>

        <div class="unit text-center">
          {{ item.price | formatString: 'money' }}
        </div>

        <div class="itotal text-end">
          @if (item.discount) {
          <div>
            <s class="text-muted">
              {{ item.subTotal | formatString: 'money'}}
            </s>
          </div>

          <div class="d-flex align-items-center justify-content-end">
            <span class="material-symbols-outlined icon-discount">loyalty</span>
            <span>
              {{ (item.subTotal - item.discount) | formatString: 'money'}}
            </span>
          </div>
          } @else {
            {{ item.subTotal | formatString: 'money'}}
          }
        </div>
      </div>

      @if (item.options && item.options.length > 0) {
      <div class="text-uppercase text-muted small mt-3 mb-2 ps-10px">
        Gọi thêm
      </div>

      @for (opt of item.options; track opt) {
      <div class="d-flex align-items-center mb-2">
        <div class="product d-flex ps-10px">
          <b>{{ opt.thirdPartyItem?.name ?? opt.name }}</b>
        </div>

        <div class="qty text-center">{{ opt.quantity * item.quantity| formatString: 'money' }}</div>
        <div class="unit text-center">{{ opt.price  | formatString: 'money' }}</div>

        <div class="itotal text-end">
          <div>{{ opt.subTotal  | formatString: 'money' }}</div>
        </div>
      </div>
      }


      }

      @if (item.note) {
      <div class="text-uppercase text-muted small mt-4 mb-1 ps-10px">
        Ghi chú món ăn
      </div>

      <div class="note fw-bold mb-2 ps-10px">
        {{ item.note }}
      </div>
      }
    </div>
    }

    @if (order.note) {
     <div class="border-bottom py-3 ps-10px">
      <div class="text-uppercase text-muted small mb-2">
        Ghi chú toàn đơn
      </div>

      <div class="note fw-bold">
        {{ order.note }}
      </div>
    </div>
    }


    @if (order.discounts && order.discounts.length > 0) {
    <div class="p-10px pt-3 discounts">
      <div class="mb-3">
        <b>Giảm giá</b>
      </div>

      @for (item of order.discounts; track item) {
      <div class="d-flex mb-3">
        <div class="name">
          {{ item.name }}
        </div>
        <div class="ms-auto value d-flex justify-content-end align-items-center">
          <span class="material-symbols-outlined icon-discount">loyalty</span>
          <span>
            {{ item.amount | formatString: 'money' }}
          </span>
        </div>
      </div>
      }
    </div>

    <div class="border-bottom"></div>
    }


    <div class="p-10px py-3 summarize">
      <div class="d-flex mb-2">
        <div>
          Thành tiền
        </div>
        <div class="ms-auto">
          {{ order.billDisplay?.summarize?.subTotal }}
        </div>
      </div>

      @if (order.billDisplay?.summarize?.extraFee?.name) {
      <div class="d-flex mb-2">
        <div>
          {{ order.billDisplay?.summarize?.extraFee?.name }}
        </div>
        <div class="ms-auto">
          {{ order.billDisplay?.summarize?.extraFee?.total }}
        </div>
      </div>
      }

      @if (order.delivery && order.delivery.fee && !order.thirdPartyInvoice?.partyName) {
      <div class="d-flex mb-2">
        <div>Phí vận chuyển</div>
        <div class="ms-auto">
          {{ order.delivery.fee | formatString: 'money' }}
        </div>
      </div>
      }

      @if (order.billDisplay?.summarize?.discount) {
      <div class="d-flex">
        <div>
          Tổng giảm giá
        </div>
        <div class="ms-auto">
          {{ order.billDisplay?.summarize?.discount }}
        </div>
      </div>
      }
    </div>

    <div class="p-10px py-3 border-top">
      <div class="final-total d-flex fw-bold">
        <div>
          Thanh toán
        </div>
        <div class="ms-auto">
          {{ order.billDisplay?.posTotalAmount | formatString: 'money' }}
        </div>
      </div>

      @if (order.thirdPartyInvoice?.partyName) {
        <div class="py-3 text-muted small">
          Thanh toán chưa bao gồm phí ship và giảm giá <b>{{ order.thirdPartyInvoice?.partyName?.toUpperCase?.() }}</b> dành riêng cho từng khách.
        </div>
        }
    </div>

  </div>

  @if (order.status !== 'completed' && order.payment?.qrPaymentUrl && !order.thirdPartyInvoice?.partyName) {
  <div class="bill-customer mb-4 mt-4">
    <div class="bill-customer-header">
      Thông tin thanh toán
    </div>
    <div class="bill-customer-body d-flex align-items-center">
      <div>
        <img src="{{ order.payment?.qrPaymentUrl }}" width="100" />
      </div>

      <div class="ms-3">
        <div class="mb-2 d-flex">
          <div class="label">Ngân hàng:</div>
          <div class="value">{{ order.store?.bank?.bankName }}</div>
        </div>
        <div class="mb-2 d-flex">
          <div class="label">Số tài khoản:</div>
          <div class="value">{{ order.store?.bank?.accountId }}</div>
        </div>
        <div class="mb-2 d-flex">
          <div class="label">Chủ tài khoản: </div>
          <div class="value">{{ order.store?.bank?.accountName }}</div>
        </div>
        <div class="mb-2 d-flex">
          <div class="label">Số tiền: </div>
          <div class="value"><b>{{ order.payment?.amount | formatString: 'money' }}</b></div>
        </div>
      </div>
    </div>
  </div>
  }


  <div class="mb-5"></div>
  <div class="mb-5"></div>
</div>
