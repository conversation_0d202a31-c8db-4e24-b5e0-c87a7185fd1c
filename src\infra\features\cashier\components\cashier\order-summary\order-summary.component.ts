import {
  MAT_BOTTOM_SHEET_DATA,
  MatBottomSheetRef
} from '@angular/material/bottom-sheet';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';

import { ChangeDetectionStrategy, Component, Inject, OnDestroy } from '@angular/core';
import {
  MatDialog
} from '@angular/material/dialog';
import { Router } from '@angular/router';
import html2canvas from 'html2canvas';
import { FormatStringPipe } from '@shared/pipes/format_str.pipe';
import { formatDateTimeLocalValue, scrollTop, getGoogleMapDirectionUrl, getGoogleMapDisplayUrl, getGoogleMapStreetViewUrl } from '@shared/utils';
import { HttpService } from '@core/services/http.service';
import { ReplaceLineBreaksPipe } from '@shared/pipes/line_breaks.pipe';
import { FlashMessageService } from '@/core/services/flash_message.service';
import { AppChannelService } from '@core/services/app_channel.service';
import { TranslateService } from '@ngx-translate/core';

import { FormatDatePipe } from '@shared/pipes/format_date.pipe';
import { HttpLoaderService } from '@core/services/http_loader.service';
import { AppCommonService } from '@core/services/common.service';
import { ROUTER_LINKS, Coordinates, InvoiceBill, PosInvoice, PosInvoiceItem, StoreConfigs } from 'salehub_shared_contracts';
import { InitDataStore } from '@core/store/init_data.store';
import { ReviewGalleryDialog } from './review.dialog';
import { HttpContext } from '@angular/common/http';
import { HTTP_REQUEST_OPTIONS } from '@core/tokens/http-context-token';
import { buildFullPosOrder } from '@features/cashier/utils/order.util';
import { DeliveryInvoiceDialog } from './delivery-dialog/delivery-dialog.component';
import { NoteInvoiceDialog } from './note-dialog/note-dialog.component';
import { DeleteInvoiceDialog } from './delete-dialog/delete-dialog.component';
import { DiscountDialog } from './discount-dialog/discount-dialog.component';
import { CashierOrderBillComponent } from '../order-bill/order-bill.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-cashier-order-summary',
  templateUrl: 'order-summary.component.html',
  standalone: true,
  imports: [
    MatMenuModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    ReplaceLineBreaksPipe,
    FormatDatePipe,
    FormatStringPipe,
    MatDatepickerModule,
    CashierOrderBillComponent
  ],
  styleUrl: './order-summary.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class CashierOrderSummaryComponent implements OnDestroy {
  isSubmitting = false;
  showItemDiscountAndNote = false;
  isPreOrder!: boolean;
  preOrderTime!: string | undefined;
  preOrderError!: string | undefined;
  originalInvoiceStatus!: PosInvoice['status'];
  isOwner = false;
  store!: StoreConfigs;
  isRenderBill = false;
  ratingArr = new Array(5).fill('');

  // Memory management: Container để quản lý tất cả subscriptions
  private readonly subscriptions = new Subscription();

  constructor(
    private _bottomSheetRef: MatBottomSheetRef<CashierOrderSummaryComponent>,
    private http: HttpService,
    @Inject(MAT_BOTTOM_SHEET_DATA) public order: PosInvoice,
    public dialog: MatDialog,
    private router : Router,
    private flashMessageService: FlashMessageService,
    private appChannel: AppChannelService,
    private loader: HttpLoaderService,
    initStore: InitDataStore,
    private commonService: AppCommonService,
    private translateService: TranslateService
  ) {
    this.originalInvoiceStatus = order.status;
    this.isPreOrder = order.status === 'preorder';
    this.isOwner = !!initStore.getData().user?.isOwner;
    this.store = initStore.getData().store;

    if(this.order.scheduledOrderInfo?.expectedDeliveryTime) {
      this.order.scheduledOrderInfo.expectedDeliveryTime = new Date(this.order.scheduledOrderInfo.expectedDeliveryTime);
      this.preOrderTime = formatDateTimeLocalValue(this.order.scheduledOrderInfo.expectedDeliveryTime);
    }

    // Xóa các comment console.log và debug code không cần thiết
  }

  convertPreOrderDateTime(event: Event) {
    if(this.isPreOrder) {
      const { value } = (event.target as HTMLInputElement);
      if(value) {
        const date = new Date(value);

        if(date.getTime() <= Date.now()) {
          this.preOrderError = 'Ngày giờ giao hàng sai, ngày giờ phải lớn hơn thời điểm hiện tại.';
        } else {
          this.preOrderError = undefined;
          this.order.scheduledOrderInfo = {
            expectedDeliveryTime: date
          };
        }
      }
    }
  }

  openDiscount(item?: PosInvoiceItem): void {
    const dialogRef = this.dialog.open(DiscountDialog, {
      data: item
        ? {
          discount: {
            amount: item.discount ?? 0
          },
          max: item.total,
          title: `Giảm giá cho ${item.quantity} suất ${item.name}`
        }
        : {
          discount: this.order.discountWholeOrder,
          max: this.order.summarize.totalRevenue,
          title: 'Giảm giá trên tổng giá trị đơn hàng'
        }
    });

    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      dialogRef.afterClosed()
        .subscribe({
          next: (discount: PosInvoice['discountWholeOrder']) => {
            if(discount?.amount !== undefined) {
              if(!item) {
                this.order.discountWholeOrder = discount;
                this.order.discounts = [
                  {
                    amount: discount.amount,
                    name: discount.name ?? 'Giảm giá toàn hóa đơn'
                  }
                ];
              } else {
                item.discount = discount.amount;
              }

              buildFullPosOrder(this.order);
            }
          },
          error: () => {
          }
        })
    );
  }

  openDelete(): void {
    const dialogRef = this.dialog.open(DeleteInvoiceDialog, {
      data: this.order
    });

    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      dialogRef.afterClosed()
        .subscribe({
          next: (value: boolean) => {
            if(value) {
              // Memory management: Thêm nested subscription vào container
              this.subscriptions.add(
                this.http.post<PosInvoice>(
                  'cashier',
                  'cancel_pos_invoice',
                  {
                    _id: this.order._id
                  },
                  {
                    context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
                      useLoader: true
                    })
                  }
                )
                  .subscribe(
                  {
                    next: (data) => {
                      if(data.status) {
                        this.order.status = data.status;
                        this.order.statusText = data.statusText;
                        this.order.isFinalized = true;
                      }
                      this.close(true);
                    },
                    error: () => {
                    }
                  })
              );
            }
          },
          error: () => {
          }
        })
    );
  }

  openNote(item?: PosInvoiceItem): void {
    const dialogRef = this.dialog.open(NoteInvoiceDialog, {
      data: {
        note: item?.note,
        title: item ? `Ghi chú cho ${item?.quantity} suất ${item?.name}` : 'Ghi chú',
        invoice: this.order,
        isItemNote: !!item
      }
    });

    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      dialogRef.afterClosed()
        .subscribe({
          next: (value: string) => {
            if(item) {
              item.note = value;
            }
          },
          error: () => {
          }
        })
    );
  }

  openImage(url: string) {
    this.dialog.open(ReviewGalleryDialog, {
      data: url,
      disableClose: true,
      panelClass: ['full-screen-modal']
    });
  }

  call(phone: string, event: Event) {
    this.appChannel.launch(`tel:${phone}`, event);
  }

  print() {
    if(!this.commonService.hasFlutterAppCommunicate()) {
      // Sử dụng i18n key thay vì hardcode text
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.DEVICE.NOT_SUPPORTED')
      );
      return;
    }

    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      this.http.get<{pos_print_invoice: InvoiceBill}>(
        'cashier',
        `pos_print_invoice&id=${this.order._id}`,
        {
          context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
            useLoader: true
          })
        }
      ).subscribe({
        next: (data) => {
          this.appChannel.send({
            type: 'printer',
            data: data.pos_print_invoice
          });
        }
      })
    );
  }

  preOrder() {
    if(this.order.invoiceId) {
      // Sử dụng i18n key cho business logic error
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.VALIDATION.INVALID'),
        {
          description: 'Không thể sửa hóa đơn hiện tại thành Đơn đặt trước, vui lòng tạo đơn mới.'
        }
      );
      return;
    }

    this.isPreOrder = !this.isPreOrder;

    if(this.isPreOrder) {
      this.order.status = 'preorder';
      this.order.scheduledOrderInfo = {};
    } else {
      this.order.status = this.originalInvoiceStatus;
      delete this.order.scheduledOrderInfo;
      this.preOrderTime = undefined;
    }
  }

  edit() {
    this.router.navigate(
      ['cashier/order'],
      {
        queryParams: {
          _id: this.order._id
        }
      }
    ).then(() => {
      this.close(false);
      scrollTop();
    });
  }

  copyInvoice() {
    this.router.navigate(
      ['cashier/order'],
      {
        queryParams: {
          copy_id: this.order._id
        }
      }
    ).then(() => {
      this.close(false);
      scrollTop();
    });
  }

  submit() {
    buildFullPosOrder(this.order);

    if(!this.order.items?.length) {
      // Sử dụng i18n key cho validation error
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.VALIDATION.INVALID'),
        {
          description: 'Không thể lưu hóa đơn trống.'
        }
      );
      return;
    }

    this.isSubmitting = true;

    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      this.http.post('cashier', 'save_pos_invoice', this.order)
        .subscribe({
          next: () => {
            this.isSubmitting = false;
            // Thêm success message cho người dùng
            this.flashMessageService.success(
              this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.SAVED')
            );
            this.close(true);
          },
          error: (error: any) => {
            this.isSubmitting = false;
            // Cải thiện error handling với thông báo rõ ràng
            this.flashMessageService.error(
              this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
              {
                description: error.message || this.translateService.instant('FLASH_MESSAGES.ERRORS.API.SERVER_ERROR')
              }
            );
          }
        })
    );
  }

  setPreparing() {
    this.isSubmitting = true;

    buildFullPosOrder(this.order);

    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      this.http.post('cashier', 'set_pos_invoice_preparing', this.order)
        .subscribe({
          next: () => {
            this.isSubmitting = false;
            this.close(true);
          },
          error: () => {
            this.isSubmitting = false;
          }
        })
    );
  }

  setDelivery() {
    const dialogRef = this.dialog.open(DeliveryInvoiceDialog, {
      disableClose: true
    });

    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      dialogRef.afterClosed()
        .subscribe({
          next: (value: { deliveryTime: Date, sendDeliveryStatusToCustomer: boolean }) => {
            if(value) {
              this.order.status = 'delivering';
              this.order.sendDeliveryStatusToCustomer = value.sendDeliveryStatusToCustomer;
              this.order.times ||= {};
              this.order.times.deliveredAt = value.deliveryTime;

              return this.submit();
            }
          },
          error: () => {
          }
        })
    );
  }

  setCompleted() {
    this.order.status = 'completed';
    return this.submit();
  }

  setSave() {
    if(this.order.status !== 'preorder') {
      this.order.status = 'preparing';
    }

    return this.submit();
  }

  showCustomerDetails() {
    this.router.navigate(
      ['frame'],
      {
        queryParams: {
          url: `/manage/customer/view?id=${this.order.customer._id}`
        }
      }
    ).then(() => {
      this.close(false);
      scrollTop();
    });
  }

  showCustomerInvoices() {
    this.router.navigate(
      [ROUTER_LINKS.cashier.invoices],
      {
        queryParams: {
          customer_id: this.order.customer._id
        }
      }
    ).then(() => {
      this.close(false);
      scrollTop();
    });
  }

  saveAs(uri: string, filename: string) {
    const link = document.createElement('a');

    if (typeof link.download === 'string') {
      link.href = uri;
      link.download = filename;

      // Firefox requires the link to be in the body
      document.body.appendChild(link);

      // simulate click
      link.click();

      // remove the link when done
      document.body.removeChild(link);
    } else {
      window.open(uri);
    }
  }

  renderBill() {
    if(!this.isRenderBill) {
      this.loader.setLoader(true);
      this.isRenderBill = true;
    }
  }

  async shareBill() {
    try {
      const canvas = await html2canvas(
        document.querySelector('app-invoice-bill') as HTMLElement,
        {
          useCORS: true
        }
      );
      const dataUrl = canvas.toDataURL();

      const blob = await (await fetch(dataUrl)).blob();

      this.loader.setLoader(false);

      let isSuccessfullCopy = false;
      let isSuccessfullShare = false;

      if(navigator.clipboard?.write) {
        try {
          await navigator.clipboard.write([
            new ClipboardItem({
              'image/png': blob
            })
          ]);
          // Sử dụng i18n key cho success message
          this.flashMessageService.success(
            this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.COPIED')
          );
          isSuccessfullCopy = true;
        } catch(err: any) {
          // Cải thiện error message với i18n
          const copyError = this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED');
          this.flashMessageService.error(`${copyError}: ${err.message}`);
        }
      } else {
        // Sử dụng i18n key cho device limitation
        this.flashMessageService.error(
          this.translateService.instant('FLASH_MESSAGES.ERRORS.DEVICE.FEATURE_UNAVAILABLE')
        );
      }

      isSuccessfullShare = await this.appChannel.share({
        text: `Hóa đơn: ${this.order.invoiceId}`,
        files: [
          new File(
            [blob],
            'bill_image.png',
            {
              type: blob.type,
              lastModified: new Date().getTime()
            }
          )
        ],
        base64Image: dataUrl
      });

      if(!isSuccessfullCopy && !isSuccessfullShare) {
        // Sử dụng i18n key cho info message
        this.flashMessageService.info(
          this.translateService.instant('FLASH_MESSAGES.INFO.FILE.SAVING_TO_DEVICE')
        );
        this.saveAs(dataUrl, 'bill_image.png');
      }
    } catch(e: any) {
      // Cải thiện error handling với i18n
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: e.message
        }
      );
    }

    this.isRenderBill = false;
  }

  close(submited: boolean) {
    this._bottomSheetRef.dismiss(submited);
  }

  get hasPreOrderButton() {
    return !this.order.invoiceId && !this.order.isFinalized && !this.order.isThirdPartyInvoice;
  }

  get hasActionButtons() {
    return this.order.paymentMethod &&
    (
      this.order.status !== 'preorder' ||
      (this.order.status === 'preorder' &&
        !this.preOrderError &&
        this.order.scheduledOrderInfo?.expectedDeliveryTime
      )
    );
  }

  get hasCompleteButton() {
    return this.order.paymentMethod &&
    this.order.paymentMethod !== 'unspecified' &&
    !this.order.isThirdPartyInvoice &&
    !this.hasDeliveryButton &&
    this.order.status !== 'preorder'
    ;
  }

  get hasDeliveryButton() {
    return this.order.paymentMethod &&
    this.order.paymentMethod !== 'unspecified' &&
    this.order.diningOption === 'takeaway' &&
    this.order.customer?.phoneNumber &&
    this.order.delivery?.company?.name &&
    !this.order.isThirdPartyInvoice &&
    this.order.status !== 'preorder'
    ;
  }

  get hasPrepareButton() {
    return this.order.status === 'preorder' && !!this.order.invoiceId;
  }

  get hasButton() {
    return !this.order.isThirdPartyInvoice &&
      !this.order.isFinalized &&
      !['delivering', 'completed'].includes(this.order.status as any)
    ;
  }

  get saveButtonText() {
    if(this.order.status !== 'preorder') {
      if(!this.order.invoiceId) {
        return 'Chuẩn bị';
      }
    }

    return 'Lưu';
  }

  get mapDirUrl() {
    if(
      this.store?.addressInfo?.lat &&
      this.store?.addressInfo?.lng &&
      this.order.customer?.address?.lat &&
      this.order.customer?.address?.lng
    ) {
      return getGoogleMapDirectionUrl(
        this.store.addressInfo as Coordinates,
        this.order.customer.address as Coordinates
      );
    }

    return '';
  }

  get mapPlaceDisplayUrl() {
    if(
      this.order.customer?.address?.lat &&
      this.order.customer?.address?.lng
    ) {
      return getGoogleMapDisplayUrl(
        this.order.customer.address as Coordinates
      );
    }

    return '';
  }

  get mapPlaceStreetViewUrl() {
    if(
      this.order.customer?.address?.lat &&
      this.order.customer?.address?.lng
    ) {
      return getGoogleMapStreetViewUrl(
        this.order.customer.address as Coordinates
      );
    }

    return '';
  }

  ngOnDestroy(): void {
    // Memory management: Unsubscribe tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }
}
