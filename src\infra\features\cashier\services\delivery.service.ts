import { Injectable } from '@angular/core';
import { PosBackendCashierData } from 'salehub_shared_contracts';

@Injectable({
  providedIn: 'root'
})

export class DeliveryService {
  constructor() { }

  getAllDeliveryFees(companies: PosBackendCashierData['deliveryCompanies'], distance: number) {
    const fee: {[companyId: string]: number} = {};
    companies.forEach(company => {
      fee[company.id] = this.getDeliveryFee(company, distance);
    });
    return fee;
  }

  /**
   * sửa j phải sửa cả trên server core -> delivery -> getDeliveryFee
   */
  getDeliveryFee(company: PosBackendCashierData['deliveryCompanies'][number], distance: number) {
    let currentMaxDistance = 0;
    let currentFee = 0;
    let result!: number;

    for(let i = 0; i < company.fixedFeeByDistance.length; i++) {
      const range = company.fixedFeeByDistance[i];
      currentMaxDistance = range.lt;
      currentFee = range.fee;

      if(distance < currentMaxDistance) {
        result = range.fee;
        break;
      }
    }

    if(!result) {
      if(company.fixedFeeAfterFixedDistance) {
        let j = 0;
        while(j < 100) {
          currentMaxDistance += 1;
          currentFee += company.fixedFeeAfterFixedDistance;

          if(distance < currentMaxDistance) {
            result = currentFee;
            break;
          }

          j++;
        }
      } else if(company.fixedMultipleFeeAfterFixedDistance) {
        result = Math.ceil(distance) * company.fixedMultipleFeeAfterFixedDistance;
      }
    }

    if(!result) {
      throw new Error(`Không thể tính phí vận chuyển do quãng đường quá xa: ${distance}`);
    }

    return result;
  }
}
