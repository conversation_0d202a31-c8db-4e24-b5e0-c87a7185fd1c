# Hướng dẫn sử dụng ResponsiveModalService với StrictModalComponent

## Tổng quan

`ResponsiveModalService` là dịch vụ modal thống nhất cho hệ thống ERP, tự động chuyển đổi giữa dialog (desktop) và bottom sheet (mobile) dựa trên kích thước màn hình. Dịch vụ này sử dụng `StrictModalComponent` interface để đảm bảo type safety và cung cấp các tính năng nâng cao như validation form, title động với TemplateRef.

### Tính năng chính:
- **Type Safety**: Sử dụng generic types `<TData, TResult>` để đảm bảo an toàn kiểu dữ liệu
- **Auto-responsive**: Tự động chọn dialog hoặc bottom sheet dựa trên breakpoint
- **Form Validation**: Tích hợp validation form với điều khiển nút Save
- **Dynamic Title**: Hỗ trợ title dạng string hoặc TemplateRef
- **Lifecycle Hooks**: Các method hook cho modal open/close

### Phương thức chính:
```typescript
async open<
  TData = unknown,
  TResult = unknown,
  TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
>(
  component: Type<TComponent>,
  modalConfig: ResponsiveModalConfig<TData> = {},
  forceMode?: 'dialog' | 'bottom-sheet'
): Promise<TResult | undefined>
```

**Tham số:**
- `TData`: Kiểu dữ liệu đầu vào cho modal
- `TResult`: Kiểu dữ liệu kết quả trả về từ modal
- `TComponent`: Component class phải implement `StrictModalComponent<TData, TResult>`
- `modalConfig`: Cấu hình modal với các thuộc tính như title, width, data...
- `forceMode`: Ép sử dụng dialog hoặc bottom-sheet (tùy chọn)

## 1. Hướng dẫn tạo component modal kế thừa StrictModalComponent

### 1.1. Định nghĩa interfaces cho TData và TResult

Trước tiên, cần định nghĩa rõ ràng các interface cho dữ liệu đầu vào và kết quả trả về:

```typescript
/**
 * Interface cho dữ liệu đầu vào của modal
 */
export interface UserFormData {
  userId?: number;
  name?: string;
  email?: string;
  department?: string;
}

/**
 * Interface cho kết quả trả về từ modal
 */
export interface UserFormResult {
  userId: number;
  name: string;
  email: string;
  department: string;
  isUpdated: boolean;
  timestamp: Date;
}
```

### 1.2. Tạo component kế thừa StrictModalComponent

```typescript
import { Component, signal, computed, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';

import { StrictModalComponent } from '@shared/components/standard-dialog/interfaces/modal-component.interface';

@Component({
  selector: 'app-user-form-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    TranslateModule
  ],
  templateUrl: './user-form-modal.component.html',
  styleUrls: ['./user-form-modal.component.scss']
})
export class UserFormModalComponent implements StrictModalComponent<UserFormData, UserFormResult>, OnInit {
  // Signal để lưu trữ dữ liệu đầu vào
  private readonly inputData = signal<UserFormData | undefined>(undefined);

  // Form group cho reactive form
  userForm!: FormGroup;

  // Danh sách phòng ban
  readonly departments = [
    { value: 'IT', label: 'Công nghệ thông tin' },
    { value: 'HR', label: 'Nhân sự' },
    { value: 'FINANCE', label: 'Tài chính' },
    { value: 'SALES', label: 'Kinh doanh' }
  ];

  constructor(private fb: FormBuilder) {
    this.initializeForm();
  }

  ngOnInit(): void {
    // Khởi tạo form với dữ liệu ban đầu nếu có
    const data = this.inputData();
    if (data) {
      this.userForm.patchValue({
        name: data.name || '',
        email: data.email || '',
        department: data.department || ''
      });
    }
  }

  /**
   * Khởi tạo reactive form với validation
   */
  private initializeForm(): void {
    this.userForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      department: ['', [Validators.required]]
    });
  }

  /**
   * Method bắt buộc: Trả về kết quả từ modal
   */
  getModalResult(): UserFormResult {
    const formValue = this.userForm.value;
    const inputData = this.inputData();

    return {
      userId: inputData?.userId || 0,
      name: formValue.name,
      email: formValue.email,
      department: formValue.department,
      isUpdated: true,
      timestamp: new Date()
    };
  }

  /**
   * Method bắt buộc: Kiểm tra form có hợp lệ không
   */
  isValid(): boolean {
    return this.userForm.valid;
  }

  /**
   * Method tùy chọn: Cập nhật dữ liệu cho component
   */
  updateData(data: UserFormData): void {
    this.inputData.set(data);

    // Cập nhật form với dữ liệu mới
    this.userForm.patchValue({
      name: data.name || '',
      email: data.email || '',
      department: data.department || ''
    });
  }

  /**
   * Method tùy chọn: Được gọi khi modal mở
   */
  onModalOpen(): void {
    console.log('Modal đã được mở');
    // Focus vào field đầu tiên
    setTimeout(() => {
      const firstInput = document.querySelector('input[formControlName="name"]') as HTMLInputElement;
      firstInput?.focus();
    }, 100);
  }

  /**
   * Method tùy chọn: Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Kiểm tra nếu form đã thay đổi nhưng chưa lưu
    if (this.userForm.dirty && !this.userForm.valid) {
      return confirm('Bạn có chắc chắn muốn đóng modal mà không lưu thay đổi?');
    }
    return true;
  }

  /**
   * Getter để kiểm tra lỗi của từng field
   */
  get nameError(): string | null {
    const control = this.userForm.get('name');
    if (control?.errors && control.touched) {
      if (control.errors['required']) return 'Tên là bắt buộc';
      if (control.errors['minlength']) return 'Tên phải có ít nhất 2 ký tự';
    }
    return null;
  }

  get emailError(): string | null {
    const control = this.userForm.get('email');
    if (control?.errors && control.touched) {
      if (control.errors['required']) return 'Email là bắt buộc';
      if (control.errors['email']) return 'Email không hợp lệ';
    }
    return null;
  }

  get departmentError(): string | null {
    const control = this.userForm.get('department');
    if (control?.errors && control.touched) {
      if (control.errors['required']) return 'Phòng ban là bắt buộc';
    }
    return null;
  }
}
```

### 1.3. Template HTML cho component

```html
<!-- user-form-modal.component.html -->
<div class="user-form-container">
  <form [formGroup]="userForm" class="user-form">
    <!-- Trường tên -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'USER_FORM.NAME' | translate }}</mat-label>
      <input
        matInput
        formControlName="name"
        [placeholder]="'USER_FORM.NAME_PLACEHOLDER' | translate">
      <mat-error *ngIf="nameError">{{ nameError }}</mat-error>
    </mat-form-field>

    <!-- Trường email -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'USER_FORM.EMAIL' | translate }}</mat-label>
      <input
        matInput
        type="email"
        formControlName="email"
        [placeholder]="'USER_FORM.EMAIL_PLACEHOLDER' | translate">
      <mat-error *ngIf="emailError">{{ emailError }}</mat-error>
    </mat-form-field>

    <!-- Trường phòng ban -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'USER_FORM.DEPARTMENT' | translate }}</mat-label>
      <mat-select formControlName="department">
        <mat-option
          *ngFor="let dept of departments"
          [value]="dept.value">
          {{ dept.label }}
        </mat-option>
      </mat-select>
      <mat-error *ngIf="departmentError">{{ departmentError }}</mat-error>
    </mat-form-field>
  </form>
</div>
```

### 1.4. Styles SCSS cho component

```scss
/* user-form-modal.component.scss */
.user-form-container {
  padding: 1rem;
  min-width: 400px;

  @media (max-width: 768px) {
    min-width: unset;
    padding: 0.5rem;
  }
}

.user-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .w-100 {
    width: 100%;
  }

  mat-form-field {
    margin-bottom: 0.5rem;
  }

  mat-error {
    font-size: 0.875rem;
    color: #f44336;
  }
}

// Responsive styles cho mobile
@media (max-width: 768px) {
  .user-form-container {
    .user-form {
      gap: 0.75rem;

      mat-form-field {
        margin-bottom: 0.25rem;
      }
    }
  }
}
```

## 2. Validation form trước khi hiển thị nút Save

### 2.1. Cách hoạt động của validation trong StrictModalComponent

`StrictModalComponent` sử dụng method `isValid()` để kiểm tra trạng thái validation của form. StandardDialog và StandardBottomSheet sẽ tự động:
- Gọi `isValid()` để kiểm tra form có hợp lệ không
- Hiển thị/ẩn nút Save dựa trên kết quả validation
- Disable nút Save khi form không hợp lệ

### 2.2. Ví dụ validation với Angular Reactive Forms

```typescript
import { Component, signal, computed } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { StrictModalComponent } from '@shared/components/standard-dialog/interfaces/modal-component.interface';

// Custom validator cho số điện thoại
function phoneValidator(control: AbstractControl): { [key: string]: any } | null {
  const phoneRegex = /^[0-9]{10,11}$/;
  const valid = phoneRegex.test(control.value);
  return valid ? null : { invalidPhone: true };
}

// Custom async validator cho email duy nhất
function uniqueEmailValidator(control: AbstractControl): Promise<{ [key: string]: any } | null> {
  return new Promise((resolve) => {
    // Giả lập API call kiểm tra email
    setTimeout(() => {
      const existingEmails = ['<EMAIL>', '<EMAIL>'];
      const isUnique = !existingEmails.includes(control.value);
      resolve(isUnique ? null : { emailExists: true });
    }, 500);
  });
}

@Component({
  selector: 'app-advanced-user-form-modal',
  template: `
    <div class="advanced-form-container">
      <form [formGroup]="advancedForm" class="advanced-form">
        <!-- Tên người dùng -->
        <mat-form-field appearance="outline">
          <mat-label>Tên người dùng</mat-label>
          <input matInput formControlName="username">
          <mat-error *ngIf="getFieldError('username')">
            {{ getFieldError('username') }}
          </mat-error>
        </mat-form-field>

        <!-- Email với async validation -->
        <mat-form-field appearance="outline">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email">
          <mat-spinner
            *ngIf="advancedForm.get('email')?.pending"
            diameter="20">
          </mat-spinner>
          <mat-error *ngIf="getFieldError('email')">
            {{ getFieldError('email') }}
          </mat-error>
        </mat-form-field>

        <!-- Số điện thoại với custom validator -->
        <mat-form-field appearance="outline">
          <mat-label>Số điện thoại</mat-label>
          <input matInput formControlName="phone">
          <mat-error *ngIf="getFieldError('phone')">
            {{ getFieldError('phone') }}
          </mat-error>
        </mat-form-field>

        <!-- Mật khẩu -->
        <mat-form-field appearance="outline">
          <mat-label>Mật khẩu</mat-label>
          <input matInput type="password" formControlName="password">
          <mat-error *ngIf="getFieldError('password')">
            {{ getFieldError('password') }}
          </mat-error>
        </mat-form-field>

        <!-- Xác nhận mật khẩu -->
        <mat-form-field appearance="outline">
          <mat-label>Xác nhận mật khẩu</mat-label>
          <input matInput type="password" formControlName="confirmPassword">
          <mat-error *ngIf="getFieldError('confirmPassword')">
            {{ getFieldError('confirmPassword') }}
          </mat-error>
        </mat-form-field>

        <!-- Hiển thị trạng thái validation tổng thể -->
        <div class="validation-status" [class.valid]="isValid()" [class.invalid]="!isValid()">
          <mat-icon>{{ isValid() ? 'check_circle' : 'error' }}</mat-icon>
          <span>{{ isValid() ? 'Form hợp lệ' : 'Vui lòng kiểm tra lại thông tin' }}</span>
        </div>
      </form>
    </div>
  `,
  styles: [`
    .advanced-form-container {
      padding: 1rem;
      min-width: 500px;
    }

    .advanced-form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .validation-status {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem;
      border-radius: 4px;

      &.valid {
        background-color: #e8f5e8;
        color: #2e7d32;
      }

      &.invalid {
        background-color: #ffebee;
        color: #c62828;
      }
    }
  `]
})
export class AdvancedUserFormModalComponent implements StrictModalComponent<UserFormData, UserFormResult> {
  advancedForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.initializeAdvancedForm();
  }

  /**
   * Khởi tạo form với các validator phức tạp
   */
  private initializeAdvancedForm(): void {
    this.advancedForm = this.fb.group({
      username: ['', [
        Validators.required,
        Validators.minLength(3),
        Validators.maxLength(20),
        Validators.pattern(/^[a-zA-Z0-9_]+$/) // Chỉ cho phép chữ, số và underscore
      ]],
      email: ['',
        [Validators.required, Validators.email],
        [uniqueEmailValidator] // Async validator
      ],
      phone: ['', [Validators.required, phoneValidator]],
      password: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/) // Mật khẩu mạnh
      ]],
      confirmPassword: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator // Cross-field validator
    });
  }

  /**
   * Custom validator để kiểm tra mật khẩu khớp nhau
   */
  private passwordMatchValidator(form: AbstractControl): { [key: string]: any } | null {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  /**
   * Method bắt buộc: Kiểm tra form có hợp lệ không
   * Được gọi bởi StandardDialog/StandardBottomSheet để điều khiển nút Save
   */
  isValid(): boolean {
    return this.advancedForm.valid && !this.advancedForm.pending;
  }

  /**
   * Method bắt buộc: Trả về kết quả từ modal
   */
  getModalResult(): UserFormResult {
    const formValue = this.advancedForm.value;
    return {
      userId: 0,
      name: formValue.username,
      email: formValue.email,
      department: 'IT',
      isUpdated: true,
      timestamp: new Date()
    };
  }

  /**
   * Helper method để lấy lỗi của field
   */
  getFieldError(fieldName: string): string | null {
    const control = this.advancedForm.get(fieldName);
    if (!control || !control.errors || !control.touched) {
      return null;
    }

    const errors = control.errors;

    // Xử lý các loại lỗi khác nhau
    if (errors['required']) return `${fieldName} là bắt buộc`;
    if (errors['minlength']) return `${fieldName} phải có ít nhất ${errors['minlength'].requiredLength} ký tự`;
    if (errors['maxlength']) return `${fieldName} không được vượt quá ${errors['maxlength'].requiredLength} ký tự`;
    if (errors['email']) return 'Email không hợp lệ';
    if (errors['pattern']) {
      if (fieldName === 'username') return 'Tên người dùng chỉ được chứa chữ, số và dấu gạch dưới';
      if (fieldName === 'password') return 'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường, 1 số và 1 ký tự đặc biệt';
    }
    if (errors['invalidPhone']) return 'Số điện thoại không hợp lệ (10-11 chữ số)';
    if (errors['emailExists']) return 'Email này đã được sử dụng';
    if (errors['passwordMismatch']) return 'Mật khẩu xác nhận không khớp';

    return 'Dữ liệu không hợp lệ';
  }

  /**
   * Method tùy chọn: Cập nhật dữ liệu
   */
  updateData(data: UserFormData): void {
    this.advancedForm.patchValue({
      username: data.name || '',
      email: data.email || ''
    });
  }
}
```

## 3. Sử dụng TemplateRef cho title động

### 3.1. Tạo title dưới dạng TemplateRef trong component cha

```typescript
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';

@Component({
  selector: 'app-user-management',
  template: `
    <div class="user-management-container">
      <!-- Nút mở modal với title động -->
      <button
        mat-raised-button
        color="primary"
        (click)="openUserModal('create')">
        Tạo người dùng mới
      </button>

      <button
        mat-raised-button
        color="accent"
        (click)="openUserModal('edit')">
        Chỉnh sửa người dùng
      </button>

      <!-- Template cho title tạo mới -->
      <ng-template #createUserTitle>
        <div class="modal-title-container">
          <mat-icon class="title-icon create-icon">person_add</mat-icon>
          <span class="title-text">{{ 'USER_MANAGEMENT.CREATE_USER' | translate }}</span>
          <mat-chip class="status-chip new">{{ 'COMMON.NEW' | translate }}</mat-chip>
        </div>
      </ng-template>

      <!-- Template cho title chỉnh sửa -->
      <ng-template #editUserTitle>
        <div class="modal-title-container">
          <mat-icon class="title-icon edit-icon">edit</mat-icon>
          <span class="title-text">{{ 'USER_MANAGEMENT.EDIT_USER' | translate }}</span>
          <mat-chip class="status-chip edit">{{ 'COMMON.EDIT' | translate }}</mat-chip>
        </div>
      </ng-template>

      <!-- Template cho title với thông tin user -->
      <ng-template #userInfoTitle>
        <div class="modal-title-container">
          <div class="user-avatar">
            <mat-icon>account_circle</mat-icon>
          </div>
          <div class="user-info">
            <div class="user-name">{{ selectedUser?.name }}</div>
            <div class="user-department">{{ selectedUser?.department }}</div>
          </div>
          <mat-chip
            class="status-chip"
            [class.active]="selectedUser?.isActive"
            [class.inactive]="!selectedUser?.isActive">
            {{ selectedUser?.isActive ? 'COMMON.ACTIVE' : 'COMMON.INACTIVE' | translate }}
          </mat-chip>
        </div>
      </ng-template>
    </div>
  `,
  styles: [`
    .user-management-container {
      padding: 1rem;

      button {
        margin-right: 1rem;
        margin-bottom: 1rem;
      }
    }

    .modal-title-container {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.5rem 0;

      .title-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;

        &.create-icon {
          color: #4caf50;
        }

        &.edit-icon {
          color: #ff9800;
        }
      }

      .title-text {
        font-size: 1.25rem;
        font-weight: 500;
        flex: 1;
      }

      .status-chip {
        font-size: 0.75rem;
        height: 24px;

        &.new {
          background-color: #e8f5e8;
          color: #2e7d32;
        }

        &.edit {
          background-color: #fff3e0;
          color: #f57c00;
        }

        &.active {
          background-color: #e8f5e8;
          color: #2e7d32;
        }

        &.inactive {
          background-color: #ffebee;
          color: #c62828;
        }
      }

      .user-avatar {
        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
          color: #666;
        }
      }

      .user-info {
        flex: 1;

        .user-name {
          font-weight: 500;
          font-size: 1.1rem;
        }

        .user-department {
          font-size: 0.875rem;
          color: #666;
        }
      }
    }
  `]
})
export class UserManagementComponent {
  // ViewChild để lấy reference đến các template
  @ViewChild('createUserTitle', { static: true }) createUserTitle!: TemplateRef<any>;
  @ViewChild('editUserTitle', { static: true }) editUserTitle!: TemplateRef<any>;
  @ViewChild('userInfoTitle', { static: true }) userInfoTitle!: TemplateRef<any>;

  // Dữ liệu user được chọn (cho title động)
  selectedUser: UserFormData | null = null;

  constructor(private responsiveModalService: ResponsiveModalService) {}

  /**
   * Mở modal với title động dựa trên mode
   */
  async openUserModal(mode: 'create' | 'edit'): Promise<void> {
    let titleTemplate: TemplateRef<any>;
    let modalData: UserFormData;

    if (mode === 'create') {
      titleTemplate = this.createUserTitle;
      modalData = {}; // Dữ liệu trống cho tạo mới
    } else {
      // Giả lập dữ liệu user để edit
      this.selectedUser = {
        userId: 123,
        name: 'Nguyễn Văn A',
        email: '<EMAIL>',
        department: 'IT'
      };

      titleTemplate = this.userInfoTitle; // Sử dụng template với thông tin user
      modalData = this.selectedUser;
    }

    const modalConfig = {
      title: titleTemplate, // Truyền TemplateRef thay vì string
      data: modalData,
      width: '600px',
      maxWidth: '95vw',
      panelClass: 'user-form-modal'
    };

    try {
      const result = await this.responsiveModalService.open<
        UserFormData,
        UserFormResult,
        UserFormModalComponent
      >(UserFormModalComponent, modalConfig);

      if (result) {
        console.log('Kết quả từ modal:', result);
        // Xử lý kết quả...
      }
    } catch (error) {
      console.error('Lỗi khi mở modal:', error);
    }
  }

  /**
   * Mở modal với title động dựa trên dữ liệu runtime
   */
  async openUserModalWithDynamicTitle(user: UserFormData): Promise<void> {
    // Cập nhật selectedUser để template có thể sử dụng
    this.selectedUser = user;

    const modalConfig = {
      title: this.userInfoTitle, // Template sẽ hiển thị thông tin user
      data: user,
      width: '600px',
      maxWidth: '95vw'
    };

    const result = await this.responsiveModalService.open<
      UserFormData,
      UserFormResult,
      UserFormModalComponent
    >(UserFormModalComponent, modalConfig);

    if (result) {
      console.log('User đã được cập nhật:', result);
    }
  }
}
```

### 3.2. Nhận và render TemplateRef trong modal component

Modal component không cần làm gì đặc biệt để nhận TemplateRef. StandardDialog và StandardBottomSheet sẽ tự động:
- Kiểm tra `config.title` có phải là `TemplateRef` không
- Nếu là `TemplateRef`, sẽ render template thay vì hiển thị text
- Nếu là `string`, sẽ hiển thị text bình thường

```typescript
// Trong StandardDialogWrapperComponent hoặc StandardBottomSheetWrapperComponent
export class StandardDialogWrapperComponent<T extends StrictModalComponent = StrictModalComponent> {
  // Computed property để xử lý title
  readonly title = computed(() => this._config().title);
  readonly isTemplateTitle = computed(() => this.title() instanceof TemplateRef);
  readonly titleText = computed(() =>
    typeof this.title() === 'string' ? this.title() as string : ''
  );
  readonly titleTemplate = computed(() =>
    this.title() instanceof TemplateRef ? this.title() as TemplateRef<any> : null
  );

  // Template sẽ sử dụng:
  // <ng-container *ngIf="isTemplateTitle(); else textTitle">
  //   <ng-container *ngTemplateOutlet="titleTemplate()"></ng-container>
  // </ng-container>
  // <ng-template #textTitle>
  //   <span>{{ titleText() }}</span>
  // </ng-template>
}
```
## 4. Ví dụ hoàn chỉnh

### 4.1. Component modal hoàn chỉnh với tất cả tính năng

```typescript
// complete-user-modal.component.ts
import { Component, signal, computed, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { StrictModalComponent } from '@shared/components/standard-dialog/interfaces/modal-component.interface';
import { FlashMessageService } from '@core/services/flash_message.service';

/**
 * Interface cho dữ liệu đầu vào modal hoàn chỉnh
 */
export interface CompleteUserModalData {
  userId?: number;
  name?: string;
  email?: string;
  department?: string;
  isActive?: boolean;
  permissions?: string[];
  mode: 'create' | 'edit' | 'view';
}

/**
 * Interface cho kết quả trả về từ modal hoàn chỉnh
 */
export interface CompleteUserModalResult {
  userId: number;
  name: string;
  email: string;
  department: string;
  isActive: boolean;
  permissions: string[];
  action: 'save' | 'delete' | 'cancel';
  timestamp: Date;
}

@Component({
  selector: 'app-complete-user-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  templateUrl: './complete-user-modal.component.html',
  styleUrls: ['./complete-user-modal.component.scss']
})
export class CompleteUserModalComponent
  implements StrictModalComponent<CompleteUserModalData, CompleteUserModalResult>, OnInit {

  // Signals cho reactive state management
  private readonly inputData = signal<CompleteUserModalData | undefined>(undefined);
  private readonly isLoading = signal(false);
  private readonly lastAction = signal<'save' | 'delete' | 'cancel'>('cancel');

  // Form group
  userForm!: FormGroup;

  // Computed properties
  readonly mode = computed(() => this.inputData()?.mode || 'create');
  readonly isCreateMode = computed(() => this.mode() === 'create');
  readonly isEditMode = computed(() => this.mode() === 'edit');
  readonly isViewMode = computed(() => this.mode() === 'view');
  readonly modalTitle = computed(() => {
    switch (this.mode()) {
      case 'create': return 'USER_MODAL.CREATE_TITLE';
      case 'edit': return 'USER_MODAL.EDIT_TITLE';
      case 'view': return 'USER_MODAL.VIEW_TITLE';
      default: return 'USER_MODAL.DEFAULT_TITLE';
    }
  });

  // Danh sách options
  readonly departments = [
    { value: 'IT', label: 'DEPARTMENTS.IT' },
    { value: 'HR', label: 'DEPARTMENTS.HR' },
    { value: 'FINANCE', label: 'DEPARTMENTS.FINANCE' },
    { value: 'SALES', label: 'DEPARTMENTS.SALES' },
    { value: 'MARKETING', label: 'DEPARTMENTS.MARKETING' }
  ];

  readonly availablePermissions = [
    { value: 'read', label: 'PERMISSIONS.READ' },
    { value: 'write', label: 'PERMISSIONS.WRITE' },
    { value: 'delete', label: 'PERMISSIONS.DELETE' },
    { value: 'admin', label: 'PERMISSIONS.ADMIN' }
  ];

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private flashMessageService: FlashMessageService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    const data = this.inputData();
    if (data) {
      this.populateForm(data);
    }
  }

  /**
   * Khởi tạo reactive form với validation đầy đủ
   */
  private initializeForm(): void {
    this.userForm = this.fb.group({
      name: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50)
      ]],
      email: ['', [
        Validators.required,
        Validators.email,
        Validators.maxLength(100)
      ]],
      department: ['', [Validators.required]],
      isActive: [true],
      permissions: [[], [Validators.required]]
    });

    // Disable form nếu ở chế độ view
    if (this.isViewMode()) {
      this.userForm.disable();
    }
  }

  /**
   * Điền dữ liệu vào form
   */
  private populateForm(data: CompleteUserModalData): void {
    this.userForm.patchValue({
      name: data.name || '',
      email: data.email || '',
      department: data.department || '',
      isActive: data.isActive ?? true,
      permissions: data.permissions || []
    });

    // Disable form nếu ở chế độ view
    if (this.isViewMode()) {
      this.userForm.disable();
    }
  }

  /**
   * Method bắt buộc: Kiểm tra form có hợp lệ không
   */
  isValid(): boolean {
    // Nếu ở chế độ view, luôn valid
    if (this.isViewMode()) {
      return true;
    }

    // Kiểm tra form validation và không đang loading
    return this.userForm.valid && !this.isLoading();
  }
```
  /**
   * Method bắt buộc: Trả về kết quả từ modal
   */
  getModalResult(): CompleteUserModalResult {
    const formValue = this.userForm.value;
    const inputData = this.inputData();

    return {
      userId: inputData?.userId || 0,
      name: formValue.name,
      email: formValue.email,
      department: formValue.department,
      isActive: formValue.isActive,
      permissions: formValue.permissions,
      action: this.lastAction(),
      timestamp: new Date()
    };
  }

  /**
   * Method tùy chọn: Cập nhật dữ liệu
   */
  updateData(data: CompleteUserModalData): void {
    this.inputData.set(data);
    this.populateForm(data);
  }

  /**
   * Method tùy chọn: Được gọi khi modal mở
   */
  onModalOpen(): void {
    console.log('Complete User Modal đã được mở với mode:', this.mode());

    // Focus vào field đầu tiên nếu không phải view mode
    if (!this.isViewMode()) {
      setTimeout(() => {
        const firstInput = document.querySelector('input[formControlName="name"]') as HTMLInputElement;
        firstInput?.focus();
      }, 100);
    }

    // Hiển thị thông báo chào mừng
    const welcomeMessage = this.translateService.instant('USER_MODAL.WELCOME_MESSAGE', {
      mode: this.translateService.instant(this.modalTitle())
    });
    this.flashMessageService.info(welcomeMessage);
  }

  /**
   * Method tùy chọn: Được gọi trước khi modal đóng
   */
  async onModalClose(): Promise<boolean> {
    // Nếu form đã thay đổi nhưng chưa lưu
    if (this.userForm.dirty && this.lastAction() === 'cancel' && !this.isViewMode()) {
      const confirmMessage = this.translateService.instant('USER_MODAL.CONFIRM_CLOSE_WITHOUT_SAVE');
      const shouldClose = confirm(confirmMessage);

      if (!shouldClose) {
        return false; // Không đóng modal
      }
    }

    // Hiển thị thông báo tạm biệt
    const goodbyeMessage = this.translateService.instant('USER_MODAL.GOODBYE_MESSAGE');
    this.flashMessageService.info(goodbyeMessage);

    return true; // Cho phép đóng modal
  }

  /**
   * Xử lý khi nhấn nút Save
   */
  async onSave(): Promise<void> {
    if (!this.isValid()) {
      this.flashMessageService.error('USER_MODAL.FORM_INVALID');
      return;
    }

    this.isLoading.set(true);

    try {
      // Giả lập API call
      await this.simulateApiCall();

      this.lastAction.set('save');
      this.flashMessageService.success('USER_MODAL.SAVE_SUCCESS');

      // Modal sẽ tự động đóng với kết quả từ getModalResult()
    } catch (error) {
      console.error('Lỗi khi lưu user:', error);
      this.flashMessageService.error('USER_MODAL.SAVE_ERROR');
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Xử lý khi nhấn nút Delete (chỉ hiển thị ở edit mode)
   */
  async onDelete(): Promise<void> {
    const confirmMessage = this.translateService.instant('USER_MODAL.CONFIRM_DELETE');
    if (!confirm(confirmMessage)) {
      return;
    }

    this.isLoading.set(true);

    try {
      // Giả lập API call delete
      await this.simulateApiCall();

      this.lastAction.set('delete');
      this.flashMessageService.success('USER_MODAL.DELETE_SUCCESS');

      // Modal sẽ tự động đóng
    } catch (error) {
      console.error('Lỗi khi xóa user:', error);
      this.flashMessageService.error('USER_MODAL.DELETE_ERROR');
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Xử lý khi nhấn nút Cancel
   */
  onCancel(): void {
    this.lastAction.set('cancel');
    // Modal sẽ tự động đóng với kết quả từ getModalResult()
  }

  /**
   * Giả lập API call
   */
  private simulateApiCall(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1500); // Giả lập delay 1.5 giây
    });
  }

  /**
   * Helper method để lấy lỗi của field
   */
  getFieldError(fieldName: string): string | null {
    const control = this.userForm.get(fieldName);
    if (!control || !control.errors || !control.touched) {
      return null;
    }

    const errors = control.errors;
    const fieldLabel = this.translateService.instant(`USER_FORM.${fieldName.toUpperCase()}`);

    if (errors['required']) {
      return this.translateService.instant('VALIDATION.REQUIRED', { field: fieldLabel });
    }
    if (errors['minlength']) {
      return this.translateService.instant('VALIDATION.MIN_LENGTH', {
        field: fieldLabel,
        length: errors['minlength'].requiredLength
      });
    }
    if (errors['maxlength']) {
      return this.translateService.instant('VALIDATION.MAX_LENGTH', {
        field: fieldLabel,
        length: errors['maxlength'].requiredLength
      });
    }
    if (errors['email']) {
      return this.translateService.instant('VALIDATION.EMAIL_INVALID');
    }

    return this.translateService.instant('VALIDATION.INVALID', { field: fieldLabel });
  }

  /**
   * Kiểm tra xem permission có được chọn không
   */
  isPermissionSelected(permission: string): boolean {
    const permissions = this.userForm.get('permissions')?.value || [];
    return permissions.includes(permission);
  }

  /**
   * Toggle permission
   */
  togglePermission(permission: string): void {
    if (this.isViewMode()) return;

    const permissions = this.userForm.get('permissions')?.value || [];
    const index = permissions.indexOf(permission);

    if (index > -1) {
      permissions.splice(index, 1);
    } else {
      permissions.push(permission);
    }

    this.userForm.patchValue({ permissions });
  }

  /**
   * Lấy icon cho permission
   */
  getPermissionIcon(permission: string): string {
    switch (permission) {
      case 'read': return 'visibility';
      case 'write': return 'edit';
      case 'delete': return 'delete';
      case 'admin': return 'admin_panel_settings';
      default: return 'help';
    }
  }
}
```
### 4.2. Template HTML hoàn chỉnh

```html
<!-- complete-user-modal.component.html -->
<div class="complete-user-modal-container">
  <!-- Loading overlay -->
  <div *ngIf="isLoading()" class="loading-overlay">
    <mat-spinner diameter="40"></mat-spinner>
    <span class="loading-text">{{ 'COMMON.PROCESSING' | translate }}</span>
  </div>

  <!-- Form container -->
  <form [formGroup]="userForm" class="user-form" [class.disabled]="isViewMode()">
    <!-- Thông tin cơ bản -->
    <div class="form-section">
      <h3 class="section-title">{{ 'USER_FORM.BASIC_INFO' | translate }}</h3>

      <!-- Tên -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'USER_FORM.NAME' | translate }}</mat-label>
        <input
          matInput
          formControlName="name"
          [placeholder]="'USER_FORM.NAME_PLACEHOLDER' | translate">
        <mat-error *ngIf="getFieldError('name')">{{ getFieldError('name') }}</mat-error>
      </mat-form-field>

      <!-- Email -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'USER_FORM.EMAIL' | translate }}</mat-label>
        <input
          matInput
          type="email"
          formControlName="email"
          [placeholder]="'USER_FORM.EMAIL_PLACEHOLDER' | translate">
        <mat-error *ngIf="getFieldError('email')">{{ getFieldError('email') }}</mat-error>
      </mat-form-field>

      <!-- Phòng ban -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'USER_FORM.DEPARTMENT' | translate }}</mat-label>
        <mat-select formControlName="department">
          <mat-option
            *ngFor="let dept of departments"
            [value]="dept.value">
            {{ dept.label | translate }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="getFieldError('department')">{{ getFieldError('department') }}</mat-error>
      </mat-form-field>

      <!-- Trạng thái hoạt động -->
      <div class="checkbox-field">
        <mat-checkbox
          formControlName="isActive"
          [disabled]="isViewMode()">
          {{ 'USER_FORM.IS_ACTIVE' | translate }}
        </mat-checkbox>
      </div>
    </div>

    <!-- Phân quyền -->
    <div class="form-section">
      <h3 class="section-title">{{ 'USER_FORM.PERMISSIONS' | translate }}</h3>

      <div class="permissions-grid">
        <mat-chip-listbox
          class="permissions-chips"
          [disabled]="isViewMode()">
          <mat-chip-option
            *ngFor="let permission of availablePermissions"
            [selected]="isPermissionSelected(permission.value)"
            (click)="togglePermission(permission.value)"
            [disabled]="isViewMode()">
            <mat-icon>{{ getPermissionIcon(permission.value) }}</mat-icon>
            {{ permission.label | translate }}
          </mat-chip-option>
        </mat-chip-listbox>
      </div>

      <mat-error *ngIf="getFieldError('permissions')" class="permissions-error">
        {{ getFieldError('permissions') }}
      </mat-error>
    </div>

    <!-- Thông tin trạng thái form -->
    <div class="form-status" [class.valid]="isValid()" [class.invalid]="!isValid()">
      <mat-icon>{{ isValid() ? 'check_circle' : 'error' }}</mat-icon>
      <span>
        {{ isValid() ? 'USER_FORM.FORM_VALID' : 'USER_FORM.FORM_INVALID' | translate }}
      </span>
    </div>
  </form>

  <!-- Custom actions cho edit mode -->
  <div *ngIf="isEditMode()" class="custom-actions">
    <button
      mat-button
      color="warn"
      (click)="onDelete()"
      [disabled]="isLoading()">
      <mat-icon>delete</mat-icon>
      {{ 'COMMON.DELETE' | translate }}
    </button>
  </div>
</div>
```

### 4.3. Styles SCSS hoàn chỉnh

```scss
/* complete-user-modal.component.scss */
.complete-user-modal-container {
  position: relative;
  padding: 1.5rem;
  min-width: 600px;
  max-height: 80vh;
  overflow-y: auto;

  @media (max-width: 768px) {
    min-width: unset;
    padding: 1rem;
    max-height: 90vh;
  }

  // Loading overlay
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    gap: 1rem;

    .loading-text {
      font-size: 0.875rem;
      color: #666;
    }
  }

  // Form styles
  .user-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;

    &.disabled {
      pointer-events: none;
      opacity: 0.7;
    }

    .form-section {
      .section-title {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 1rem;
        color: #333;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 0.5rem;
      }

      .w-100 {
        width: 100%;
        margin-bottom: 1rem;
      }

      .checkbox-field {
        margin: 1rem 0;
      }
    }

    // Permissions section
    .permissions-grid {
      .permissions-chips {
        width: 100%;

        mat-chip-option {
          margin: 0.25rem;

          mat-icon {
            margin-right: 0.5rem;
            font-size: 1rem;
          }
        }
      }

      .permissions-error {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #f44336;
      }
    }

    // Form status
    .form-status {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem;
      border-radius: 4px;
      font-size: 0.875rem;
      margin-top: 1rem;

      &.valid {
        background-color: #e8f5e8;
        color: #2e7d32;
        border: 1px solid #4caf50;
      }

      &.invalid {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #f44336;
      }

      mat-icon {
        font-size: 1.25rem;
      }
    }
  }

  // Custom actions
  .custom-actions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;

    button {
      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .complete-user-modal-container {
    .user-form {
      gap: 1rem;

      .form-section {
        .section-title {
          font-size: 1rem;
        }

        .w-100 {
          margin-bottom: 0.75rem;
        }
      }

      .permissions-grid {
        .permissions-chips {
          mat-chip-option {
            font-size: 0.875rem;

            mat-icon {
              font-size: 0.875rem;
            }
          }
        }
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .complete-user-modal-container {
    .user-form {
      .form-section {
        .section-title {
          color: #fff;
          border-bottom-color: #424242;
        }
      }

      .form-status {
        &.valid {
          background-color: #1b5e20;
          color: #a5d6a7;
          border-color: #4caf50;
        }

        &.invalid {
          background-color: #b71c1c;
          color: #ef9a9a;
          border-color: #f44336;
        }
      }
    }

    .custom-actions {
      border-top-color: #424242;
    }

    .loading-overlay {
      background-color: rgba(0, 0, 0, 0.8);

      .loading-text {
        color: #ccc;
      }
    }
  }
}
```
### 4.4. Component gọi modal với TemplateRef title

```typescript
// user-management.component.ts
import { Component, TemplateRef, ViewChild, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { FlashMessageService } from '@core/services/flash_message.service';
import {
  CompleteUserModalComponent,
  CompleteUserModalData,
  CompleteUserModalResult
} from './complete-user-modal.component';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    TranslateModule
  ],
  template: `
    <div class="user-management-container">
      <div class="action-buttons">
        <!-- Nút tạo user mới -->
        <button
          mat-raised-button
          color="primary"
          (click)="openCreateUserModal()">
          <mat-icon>person_add</mat-icon>
          {{ 'USER_MANAGEMENT.CREATE_USER' | translate }}
        </button>

        <!-- Nút chỉnh sửa user -->
        <button
          mat-raised-button
          color="accent"
          (click)="openEditUserModal()">
          <mat-icon>edit</mat-icon>
          {{ 'USER_MANAGEMENT.EDIT_USER' | translate }}
        </button>

        <!-- Nút xem user -->
        <button
          mat-stroked-button
          (click)="openViewUserModal()">
          <mat-icon>visibility</mat-icon>
          {{ 'USER_MANAGEMENT.VIEW_USER' | translate }}
        </button>
      </div>

      <!-- Template cho title tạo mới -->
      <ng-template #createUserTitle>
        <div class="modal-title-container">
          <mat-icon class="title-icon create-icon">person_add</mat-icon>
          <span class="title-text">{{ 'USER_MODAL.CREATE_TITLE' | translate }}</span>
          <mat-chip class="status-chip new">{{ 'COMMON.NEW' | translate }}</mat-chip>
        </div>
      </ng-template>

      <!-- Template cho title chỉnh sửa -->
      <ng-template #editUserTitle>
        <div class="modal-title-container">
          <mat-icon class="title-icon edit-icon">edit</mat-icon>
          <span class="title-text">{{ 'USER_MODAL.EDIT_TITLE' | translate }}</span>
          <mat-chip class="status-chip edit">{{ 'COMMON.EDIT' | translate }}</mat-chip>
        </div>
      </ng-template>

      <!-- Template cho title xem -->
      <ng-template #viewUserTitle>
        <div class="modal-title-container">
          <mat-icon class="title-icon view-icon">visibility</mat-icon>
          <span class="title-text">{{ 'USER_MODAL.VIEW_TITLE' | translate }}</span>
          <mat-chip class="status-chip view">{{ 'COMMON.VIEW' | translate }}</mat-chip>
        </div>
      </ng-template>

      <!-- Template cho title với thông tin user cụ thể -->
      <ng-template #userInfoTitle>
        <div class="modal-title-container">
          <div class="user-avatar">
            <mat-icon>account_circle</mat-icon>
          </div>
          <div class="user-info">
            <div class="user-name">{{ selectedUser?.name }}</div>
            <div class="user-department">{{ selectedUser?.department }}</div>
          </div>
          <mat-chip
            class="status-chip"
            [class.active]="selectedUser?.isActive"
            [class.inactive]="!selectedUser?.isActive">
            {{ (selectedUser?.isActive ? 'COMMON.ACTIVE' : 'COMMON.INACTIVE') | translate }}
          </mat-chip>
        </div>
      </ng-template>
    </div>
  `,
  styles: [`
    .user-management-container {
      padding: 2rem;

      .action-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;

        button {
          mat-icon {
            margin-right: 0.5rem;
          }
        }
      }
    }

    .modal-title-container {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.5rem 0;

      .title-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;

        &.create-icon {
          color: #4caf50;
        }

        &.edit-icon {
          color: #ff9800;
        }

        &.view-icon {
          color: #2196f3;
        }
      }

      .title-text {
        font-size: 1.25rem;
        font-weight: 500;
        flex: 1;
      }

      .status-chip {
        font-size: 0.75rem;
        height: 24px;

        &.new {
          background-color: #e8f5e8;
          color: #2e7d32;
        }

        &.edit {
          background-color: #fff3e0;
          color: #f57c00;
        }

        &.view {
          background-color: #e3f2fd;
          color: #1976d2;
        }

        &.active {
          background-color: #e8f5e8;
          color: #2e7d32;
        }

        &.inactive {
          background-color: #ffebee;
          color: #c62828;
        }
      }

      .user-avatar {
        mat-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
          color: #666;
        }
      }

      .user-info {
        flex: 1;

        .user-name {
          font-weight: 500;
          font-size: 1.1rem;
        }

        .user-department {
          font-size: 0.875rem;
          color: #666;
        }
      }
    }

    @media (max-width: 768px) {
      .user-management-container {
        padding: 1rem;

        .action-buttons {
          flex-direction: column;

          button {
            width: 100%;
          }
        }
      }

      .modal-title-container {
        .title-text {
          font-size: 1.1rem;
        }

        .user-avatar mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }
  `]
})
export class UserManagementComponent {
  // Inject services
  private readonly responsiveModalService = inject(ResponsiveModalService);
  private readonly translateService = inject(TranslateService);
  private readonly flashMessageService = inject(FlashMessageService);

  // ViewChild để lấy reference đến các template
  @ViewChild('createUserTitle', { static: true }) createUserTitle!: TemplateRef<any>;
  @ViewChild('editUserTitle', { static: true }) editUserTitle!: TemplateRef<any>;
  @ViewChild('viewUserTitle', { static: true }) viewUserTitle!: TemplateRef<any>;
  @ViewChild('userInfoTitle', { static: true }) userInfoTitle!: TemplateRef<any>;

  // Dữ liệu user được chọn (cho title động)
  selectedUser: CompleteUserModalData | null = null;

  /**
   * Mở modal tạo user mới
   */
  async openCreateUserModal(): Promise<void> {
    const modalData: CompleteUserModalData = {
      mode: 'create'
    };

    const modalConfig = {
      title: this.createUserTitle, // Sử dụng TemplateRef
      data: modalData,
      width: '700px',
      maxWidth: '95vw',
      panelClass: 'create-user-modal'
    };

    try {
      const result = await this.responsiveModalService.open<
        CompleteUserModalData,
        CompleteUserModalResult,
        CompleteUserModalComponent
      >(CompleteUserModalComponent, modalConfig);

      if (result && result.action === 'save') {
        this.flashMessageService.success('USER_MANAGEMENT.CREATE_SUCCESS');
        console.log('User mới được tạo:', result);
        // Refresh danh sách user...
      }
    } catch (error) {
      console.error('Lỗi khi tạo user:', error);
      this.flashMessageService.error('USER_MANAGEMENT.CREATE_ERROR');
    }
  }

  /**
   * Mở modal chỉnh sửa user
   */
  async openEditUserModal(): Promise<void> {
    // Giả lập dữ liệu user để edit
    this.selectedUser = {
      userId: 123,
      name: 'Nguyễn Văn A',
      email: '<EMAIL>',
      department: 'IT',
      isActive: true,
      permissions: ['read', 'write'],
      mode: 'edit'
    };

    const modalConfig = {
      title: this.userInfoTitle, // Sử dụng template với thông tin user
      data: this.selectedUser,
      width: '700px',
      maxWidth: '95vw',
      panelClass: 'edit-user-modal'
    };

    try {
      const result = await this.responsiveModalService.open<
        CompleteUserModalData,
        CompleteUserModalResult,
        CompleteUserModalComponent
      >(CompleteUserModalComponent, modalConfig);

      if (result) {
        if (result.action === 'save') {
          this.flashMessageService.success('USER_MANAGEMENT.UPDATE_SUCCESS');
          console.log('User đã được cập nhật:', result);
        } else if (result.action === 'delete') {
          this.flashMessageService.success('USER_MANAGEMENT.DELETE_SUCCESS');
          console.log('User đã được xóa:', result);
        }
        // Refresh danh sách user...
      }
    } catch (error) {
      console.error('Lỗi khi chỉnh sửa user:', error);
      this.flashMessageService.error('USER_MANAGEMENT.UPDATE_ERROR');
    }
  }

  /**
   * Mở modal xem user (read-only)
   */
  async openViewUserModal(): Promise<void> {
    // Giả lập dữ liệu user để xem
    this.selectedUser = {
      userId: 456,
      name: 'Trần Thị B',
      email: '<EMAIL>',
      department: 'HR',
      isActive: false,
      permissions: ['read'],
      mode: 'view'
    };

    const modalConfig = {
      title: this.userInfoTitle, // Sử dụng template với thông tin user
      data: this.selectedUser,
      width: '700px',
      maxWidth: '95vw',
      panelClass: 'view-user-modal'
    };

    try {
      const result = await this.responsiveModalService.open<
        CompleteUserModalData,
        CompleteUserModalResult,
        CompleteUserModalComponent
      >(CompleteUserModalComponent, modalConfig);

      console.log('Modal xem user đã đóng:', result);
    } catch (error) {
      console.error('Lỗi khi xem user:', error);
      this.flashMessageService.error('USER_MANAGEMENT.VIEW_ERROR');
    }
  }

  /**
   * Mở modal với title string đơn giản (fallback)
   */
  async openModalWithStringTitle(): Promise<void> {
    const modalData: CompleteUserModalData = {
      mode: 'create'
    };

    const modalConfig = {
      title: 'Tạo người dùng mới', // Sử dụng string thay vì TemplateRef
      data: modalData,
      width: '700px',
      maxWidth: '95vw'
    };

    const result = await this.responsiveModalService.open<
      CompleteUserModalData,
      CompleteUserModalResult,
      CompleteUserModalComponent
    >(CompleteUserModalComponent, modalConfig);

    console.log('Kết quả từ modal với string title:', result);
  }
}
```
## 5. Lưu ý quan trọng và Best Practices

### 5.1. Type Safety và Generic Types

```typescript
// ✅ ĐÚNG: Luôn chỉ định rõ generic types
const result = await this.responsiveModalService.open<
  UserFormData,        // TData - Kiểu dữ liệu đầu vào
  UserFormResult,      // TResult - Kiểu kết quả trả về
  UserFormModalComponent // TComponent - Component class
>(UserFormModalComponent, modalConfig);

// ❌ SAI: Không chỉ định generic types
const result = await this.responsiveModalService.open(UserFormModalComponent, modalConfig);
```

### 5.2. Implement StrictModalComponent Interface

```typescript
// ✅ ĐÚNG: Component phải implement đầy đủ StrictModalComponent
export class MyModalComponent implements StrictModalComponent<MyData, MyResult> {
  // Method bắt buộc
  getModalResult(): MyResult { /* ... */ }
  isValid(): boolean { /* ... */ }

  // Method tùy chọn nhưng khuyến khích
  updateData?(data: MyData): void { /* ... */ }
  onModalOpen?(): void { /* ... */ }
  onModalClose?(): boolean | Promise<boolean> { /* ... */ }
}

// ❌ SAI: Không implement interface hoặc thiếu method
export class MyModalComponent {
  // Thiếu getModalResult() và isValid()
}
```

### 5.3. Form Validation Best Practices

```typescript
// ✅ ĐÚNG: Validation đầy đủ với reactive forms
isValid(): boolean {
  // Kiểm tra form valid và không đang loading
  return this.userForm.valid && !this.isLoading();
}

// ✅ ĐÚNG: Custom validation với error messages
getFieldError(fieldName: string): string | null {
  const control = this.userForm.get(fieldName);
  if (!control || !control.errors || !control.touched) {
    return null;
  }

  // Xử lý từng loại lỗi cụ thể
  const errors = control.errors;
  if (errors['required']) return `${fieldName} là bắt buộc`;
  if (errors['email']) return 'Email không hợp lệ';
  // ...

  return 'Dữ liệu không hợp lệ';
}

// ❌ SAI: Validation không đầy đủ
isValid(): boolean {
  return true; // Luôn trả về true
}
```

### 5.4. TemplateRef Usage

```typescript
// ✅ ĐÚNG: Sử dụng ViewChild để lấy TemplateRef
@ViewChild('myTitle', { static: true }) myTitle!: TemplateRef<any>;

const modalConfig = {
  title: this.myTitle, // TemplateRef
  data: modalData
};

// ✅ ĐÚNG: Fallback với string title
const modalConfig = {
  title: 'Simple Title', // String
  data: modalData
};

// ❌ SAI: Truyền undefined hoặc null
const modalConfig = {
  title: undefined, // Sẽ không hiển thị title
  data: modalData
};
```

### 5.5. Error Handling

```typescript
// ✅ ĐÚNG: Xử lý lỗi đầy đủ
try {
  const result = await this.responsiveModalService.open<TData, TResult, TComponent>(
    component,
    modalConfig
  );

  if (result) {
    // Xử lý kết quả thành công
    this.flashMessageService.success('OPERATION_SUCCESS');
  }
} catch (error) {
  console.error('Lỗi khi mở modal:', error);
  this.flashMessageService.error('OPERATION_ERROR');
}

// ❌ SAI: Không xử lý lỗi
const result = await this.responsiveModalService.open(component, modalConfig);
// Nếu có lỗi sẽ crash ứng dụng
```

### 5.6. Responsive Design

```typescript
// ✅ ĐÚNG: Cấu hình responsive
const modalConfig = {
  width: '600px',      // Desktop width
  maxWidth: '95vw',    // Mobile max width
  panelClass: 'my-modal-class'
};

// ✅ ĐÚNG: Force mode khi cần thiết
const result = await this.responsiveModalService.open(
  component,
  modalConfig,
  'bottom-sheet' // Force bottom sheet trên desktop
);
```

### 5.7. I18n Integration

```typescript
// ✅ ĐÚNG: Sử dụng i18n cho tất cả text
const welcomeMessage = this.translateService.instant('USER_MODAL.WELCOME_MESSAGE', {
  mode: this.translateService.instant(this.modalTitle())
});
this.flashMessageService.info(welcomeMessage);

// ✅ ĐÚNG: I18n trong template
<mat-label>{{ 'USER_FORM.NAME' | translate }}</mat-label>
<mat-error *ngIf="getFieldError('name')">{{ getFieldError('name') }}</mat-error>

// ❌ SAI: Hardcode text
<mat-label>Tên người dùng</mat-label>
<mat-error>Tên là bắt buộc</mat-error>
```

### 5.8. Performance Optimization

```typescript
// ✅ ĐÚNG: Sử dụng signals cho reactive state
private readonly isLoading = signal(false);
private readonly inputData = signal<MyData | undefined>(undefined);

// Computed properties cho derived state
readonly isCreateMode = computed(() => this.inputData()?.mode === 'create');
readonly isFormValid = computed(() => this.userForm.valid && !this.isLoading());

// ✅ ĐÚNG: OnPush change detection cho performance
@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  // ...
})
```

### 5.9. Testing Considerations

```typescript
// ✅ ĐÚNG: Component có thể test được
export class MyModalComponent implements StrictModalComponent<MyData, MyResult> {
  // Public methods cho testing
  public getModalResult(): MyResult { /* ... */ }
  public isValid(): boolean { /* ... */ }

  // Helper methods cũng public để test
  public getFieldError(fieldName: string): string | null { /* ... */ }
}

// Test example
it('should return valid result when form is valid', () => {
  component.userForm.patchValue({ name: 'Test', email: '<EMAIL>' });
  expect(component.isValid()).toBe(true);

  const result = component.getModalResult();
  expect(result.name).toBe('Test');
  expect(result.email).toBe('<EMAIL>');
});
```

### 5.10. Module Imports

Đảm bảo import các module cần thiết:

```typescript
// app.module.ts hoặc standalone component imports
imports: [
  // Angular Material modules
  MatDialogModule,
  MatBottomSheetModule,
  MatFormFieldModule,
  MatInputModule,
  MatSelectModule,
  MatCheckboxModule,
  MatButtonModule,
  MatIconModule,
  MatChipsModule,
  MatProgressSpinnerModule,

  // Angular modules
  CommonModule,
  FormsModule,
  ReactiveFormsModule,

  // Translation
  TranslateModule,

  // Custom modules
  // ...
]
```

## 6. Kết luận

ResponsiveModalService với StrictModalComponent interface cung cấp một giải pháp modal mạnh mẽ và type-safe cho hệ thống ERP. Các tính năng chính bao gồm:

1. **Type Safety**: Generic types đảm bảo an toàn kiểu dữ liệu
2. **Auto-responsive**: Tự động chuyển đổi giữa dialog và bottom sheet
3. **Form Validation**: Tích hợp validation với điều khiển nút Save
4. **Dynamic Title**: Hỗ trợ TemplateRef cho title phức tạp
5. **Lifecycle Hooks**: Các method hook cho modal lifecycle
6. **I18n Support**: Tích hợp đầy đủ với ngx-translate
7. **Performance**: Sử dụng signals và OnPush change detection

Bằng cách tuân thủ các best practices và ví dụ trong tài liệu này, bạn có thể tạo ra các modal component mạnh mẽ, dễ bảo trì và có trải nghiệm người dùng tốt trên cả desktop và mobile.
