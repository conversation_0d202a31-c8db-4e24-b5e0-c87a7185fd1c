---
type: "always_apply"
---

## Nguy<PERSON>n tắc chung

- **C<PERSON>u trúc i18n**:
  - File dịch gốc: `src/infra/i18n/<global|feature|sub-feature>/<lang>.json`.
  - File gộp: `public/assets/i18n/<lang>.json` (tạo bởi `scripts/merge-translations.js`, không chỉnh sửa thủ công).
  - Các folder:
    - `global/`: Key chung (`COMMON.SAVE`).
    - `<feature>/`: Key giao diện chung của module (`CASHIER.PANEL.TITLE`).
    - `<feature>/<sub-feature>/`: Key trang/chức năng chính (`CASHIER.CREATE_ORDER.TITLE`).
- **Ngôn ngữ**: Chỉ hỗ trợ `en` (Tiếng Anh) và `vi` (Tiếng Việt).
- **Key format**: **SCREAMING_SNAKE_CASE** (chữ in hoa, d<PERSON><PERSON> gạch dướ<PERSON>, ví dụ: `CASHIER_CREATE_ORDER_TITLE`).
- **Namespace**: Key bắt đầu bằng `COMMON` (global), tên module (`CASHIER`, `ECOMMERCE`, `WAREHOUSE`, ...), hoặc `PRODUCTS` (ngành hàng như `FASHION`, `FOOD`).
- **Mục tiêu**:
  - Tránh trùng key trong toàn bộ `src/infra/i18n/`.
  - Đảm bảo `en.json` và `vi.json` trong cùng folder có cùng số lượng và danh sách key.
  - Đảm bảo bản dịch đúng ngữ nghĩa, nhất quán với từ điển chuẩn.
- **Công cụ kiểm tra**: Sử dụng `compare-translations.js` để kiểm tra key trùng lặp, đối chiếu key, và chuẩn hóa bản dịch.

## Kiểm tra key trùng lặp

AI Agent **phải** sử dụng `compare-translations.js` để kiểm tra key trùng lặp trước khi thêm key mới, đảm bảo không có key nào trùng trong toàn bộ `src/infra/i18n/`.

**Quy tắc:**
1. **Kiểm tra trong file**:
   - Khi thêm key vào file JSON (ví dụ: `cashier/create-order/en.json`), chạy `compare-translations` để kiểm tra key trùng trong file.
   - Ví dụ: Nếu `COMMON.SAVE` xuất hiện 2 lần trong `global/en.json`, script báo lỗi:
     ```
     Lỗi: Phát hiện key trùng lặp trong src/infra/i18n/global/en.json:
       - Key "COMMON.SAVE" xuất hiện 2 lần.
         Đề xuất: Xóa hoặc hợp nhất các key trùng lặp trong src/infra/i18n/global/en.json.
     ```
2. **Kiểm tra toàn cục**:
   - Chạy `compare-translations` để kiểm tra key mới có trùng với key trong bất kỳ file `en.json` nào trong `src/infra/i18n/`.
   - Ví dụ: Nếu thêm `CASHIER.CREATE_ORDER.TITLE` vào `cashier/create-order/en.json` nhưng key đã tồn tại trong `cashier/en.json`, script báo lỗi:
     ```
     Lỗi: Key "CASHIER.CREATE_ORDER.TITLE" trùng lặp:
       - Xuất hiện trong src/infra/i18n/cashier/en.json
       - Xuất hiện trong src/infra/i18n/cashier/create-order/en.json
         Đề xuất: Sử dụng key khác (ví dụ: CASHIER.CREATE_ORDER.PAGE_TITLE) hoặc kiểm tra ngữ nghĩa.
     ```
3. **Namespace hợp lệ**:
   - Key phải bắt đầu bằng `COMMON`, tên module (`CASHIER`, `ECOMMERCE`, `PRODUCTS`), hoặc sub-feature hợp lệ dựa trên URL/chức năng.
   - Ví dụ: `CASHIER.INVALID_KEY` không được phép nếu không thuộc cấu trúc `CASHIER.PANEL`, `CASHIER.CREATE_ORDER`, ...
   - Nếu key không đúng namespace, báo lỗi:
     ```
     Lỗi: Key "CASHIER.INVALID_KEY" không hợp lệ về namespace.
       Đề xuất: Sử dụng key như CASHIER.PANEL.<PROPERTY> hoặc CASHIER.CREATE_ORDER.<PROPERTY>.
     ```

**Chú thích cho AI Agent:**
- Chạy `compare-translations` trước khi thêm key mới:
  ```bash
  compare-translations src/infra/i18n/cashier/create-order
  ```
- Nếu phát hiện key trùng, đề xuất key mới với hậu tố cụ thể hơn (ví dụ: `TITLE` → `PAGE_TITLE`, `SAVE` → `SAVE_BUTTON`).
- Đảm bảo key đúng namespace dựa trên danh sách module (`cashier`, `ecommerce`, ...) trong `FOLDER_STRUCTURE.md`.

## Đối chiếu key giữa các bản dịch

AI Agent **phải** sử dụng `compare-translations.js` để đối chiếu key giữa `en.json` và `vi.json` trong cùng folder sau khi thêm/sửa key.

**Quy tắc:**
1. **So sánh số lượng key**:
   - Chạy `compare-translations` để đếm key trong `en.json` và `vi.json`.
   - Nếu số lượng không khớp, báo lỗi:
     ```
     Lỗi: Số lượng key không khớp trong src/infra/i18n/cashier/create-order:
       - en.json: 5 key
       - vi.json: 4 key
     ```
2. **So sánh danh sách key**:
   - Kiểm tra từng key trong `en.json` có tồn tại trong `vi.json` và ngược lại.
   - Nếu thiếu key, báo lỗi và đề xuất bổ sung:
     ```
     Lỗi: Key "CASHIER.CREATE_ORDER.SUBMIT" có trong en.json nhưng thiếu trong vi.json.
       Đề xuất: Thêm key vào src/infra/i18n/cashier/create-order/vi.json:
       {
         "CASHIER": {
           "CREATE_ORDER": {
             "SUBMIT": "Xác Nhận Đơn Hàng"
           }
         }
       }
     ```
3. **Kiểm tra vị trí key**:
   - Đảm bảo key được thêm đúng folder:
     - `global/` cho key chung (`COMMON.SAVE`).
     - `<feature>/` cho key giao diện chung (`CASHIER.PANEL.TITLE`).
     - `<feature>/<sub-feature>/` cho key trang/chức năng (`CASHIER.CREATE_ORDER.TITLE`).
   - Nếu key sai vị trí, báo lỗi:
     ```
     Lỗi: Key "CASHIER.CREATE_ORDER.TITLE" được thêm vào src/infra/i18n/cashier/en.json.
       Đề xuất: Di chuyển key đến src/infra/i18n/cashier/create-order/en.json.
     ```

**Chú thích cho AI Agent:**
- Chạy `compare-translations` sau khi thêm/sửa key:
  ```bash
  cd src/infra/i18n/cashier/create-order
  compare-translations
  ```
- Tự động bổ sung key thiếu vào file tương ứng, sử dụng từ điển chuẩn để gợi ý bản dịch (ví dụ: `"Submit": "Xác Nhận"`).
- Kiểm tra vị trí key dựa trên cấu trúc URL (ví dụ: `/cashier/create-order` → `cashier/create-order/`).

## Chuẩn hóa bản dịch

AI Agent **phải** sử dụng `compare-translations.js` để kiểm tra ngữ nghĩa và tính nhất quán của bản dịch dựa trên từ điển chuẩn.

**Quy tắc:**
1. **Ngữ nghĩa đúng**:
   - Bản dịch trong `vi.json` phải khớp với giá trị trong `en.json` theo từ điển chuẩn.
   - Ví dụ:
     - `en.json`: `"COMMON.SAVE": "Save"`
     - `vi.json`: `"COMMON.SAVE": "Lưu"`
   - Nếu bản dịch không đúng, báo lỗi:
     ```
     Lỗi: Bản dịch không nhất quán trong src/infra/i18n/global:
       - Key: COMMON.SAVE
       - en.json: "Save"
       - vi.json: "Ghi Lại"
         Đề xuất: Sửa vi.json thành "Lưu".
     ```
2. **Tính nhất quán**:
   - Các từ phổ biến phải dùng bản dịch thống nhất theo từ điển chuẩn:
     - "Save" → "Lưu"
     - "Cancel" → "Hủy"
     - "Submit" → "Xác Nhận"
     - "Create" → "Tạo"
     - "Order" → "Đơn Hàng"
   - Nếu phát hiện không nhất quán, báo lỗi:
     ```
     Lỗi: Bản dịch không nhất quán trong src/infra/i18n/cashier:
       - Key: CASHIER.SAVE
       - en.json: "Save"
       - vi.json: "Ghi Lại"
         Đề xuất: Sửa vi.json thành "Lưu".
     ```
3. **Ngữ cảnh phù hợp**:
   - Key phải phù hợp với vị trí sử dụng:
     - `COMMON.*` trong `global/<lang>.json`.
     - `<FEATURE>.PANEL.*`, `<FEATURE>.DIALOG.*` trong `<feature>/<lang>.json`.
     - `<FEATURE>.<SUB_FEATURE>.*` trong `<feature>/<sub-feature>/<lang>.json`.
   - Nếu key không đúng ngữ cảnh, báo lỗi:
     ```
     Lỗi: Key "CASHIER.PANEL.TITLE" trong src/infra/i18n/cashier/create-order/en.json không đúng ngữ cảnh.
       Đề xuất: Di chuyển key đến src/infra/i18n/cashier/en.json.
     ```

**Chú thích cho AI Agent:**
- Sử dụng từ điển chuẩn trong `compare-translations.js` để kiểm tra bản dịch.
- Tự động đề xuất sửa bản dịch không nhất quán (dựa trên từ điển).
- Kiểm tra ngữ cảnh dựa trên cấu trúc key và folder.

## Quy trình thực hiện

AI Agent **phải** tuân theo quy trình sau, sử dụng `compare-translations.js` để kiểm tra key và bản dịch:

1. **Kiểm tra key trùng lặp**:
   - Chạy `compare-translations` để kiểm tra key mới có trùng trong file hoặc toàn bộ `src/infra/i18n/`.
   - Nếu trùng, đề xuất key mới (ví dụ: `CASHIER.CREATE_ORDER.PAGE_TITLE`) hoặc yêu cầu xác nhận.
2. **Thêm key vào file JSON**:
   - Xác định vị trí đúng:
     - Key chung → `global/<lang>.json`.
     - Key giao diện chung → `<feature>/<lang>.json`.
     - Key trang/chức năng → `<feature>/<sub-feature>/<lang>.json`.
   - Thêm key vào cả `en.json` và `vi.json` với bản dịch từ từ điển chuẩn.
3. **Đối chiếu key**:
   - Chạy `compare-translations` trong folder tương ứng để so sánh `en.json` và `vi.json`.
   - Nếu thiếu key, bổ sung với bản dịch đề xuất từ từ điển.
4. **Chuẩn hóa bản dịch**:
   - Chạy `compare-translations` để kiểm tra ngữ nghĩa và tính nhất quán.
   - Sửa bản dịch nếu phát hiện lỗi (dựa trên từ điển chuẩn).
5. **Chạy script merge-translations**:
   - Chạy `npm run merge-translations` để gộp file vào `public/assets/i18n/<lang>.json`.
   - Kiểm tra `public/assets/i18n/<lang>.json` chứa key mới.
6. **Kiểm tra trong UI**:
   - Truy cập trang liên quan (ví dụ: `/cashier/create-order`).
   - Xác nhận key hiển thị đúng trong cả `en` và `vi`.

**Chú thích cho AI Agent:**
- Tự động chạy `compare-translations` sau mỗi bước thêm/sửa key:
  ```bash
  compare-translations src/infra/i18n/<folder>
  ```
- Lưu log lỗi (trùng key, thiếu key, bản dịch không đúng) trong IDE để người dùng xem xét.
- Đề xuất sửa lỗi cụ thể (thêm key, sửa bản dịch, di chuyển file).

## Ví dụ áp dụng

### Tình huống: Thêm key cho `/cashier/create-order`

1. **Phát hiện key mới**:
   - Template: `<h1>{{ 'CASHIER.CREATE_ORDER.TITLE' | translate }}</h1>`.
   - Key: `CASHIER.CREATE_ORDER.TITLE`.

2. **Kiểm tra key trùng lặp**:
   - Chạy:
     ```bash
     compare-translations src/infra/i18n/cashier/create-order
     ```
   - Kết quả: Không tìm thấy `CASHIER.CREATE_ORDER.TITLE` trong `src/infra/i18n/`.

3. **Thêm key**:
   - File `cashier/create-order/en.json`:
     ```json
     {
       "CASHIER": {
         "CREATE_ORDER": {
           "TITLE": "Create Order"
         }
       }
     }
     ```
   - File `cashier/create-order/vi.json`:
     ```json
     {
       "CASHIER": {
         "CREATE_ORDER": {
           "TITLE": "Tạo Đơn Hàng"
         }
       }
     }
     ```

4. **Đối chiếu key**:
   - Chạy:
     ```bash
     compare-translations src/infra/i18n/cashier/create-order
     ```
   - Output:
     ```
     Thành công: en.json và vi.json trong src/infra/i18n/cashier/create-order có cùng key, không có key trùng lặp, và bản dịch nhất quán.
     - Tổng số key: 1
     ```

5. **Chuẩn hóa bản dịch**:
   - Kiểm tra: `"Create Order" → "Tạo Đơn Hàng"` khớp với ngữ cảnh và từ điển chuẩn.

### Tình huống: Phát hiện lỗi

1. **Key trùng lặp**:
   - Thêm `COMMON.SAVE` vào `global/en.json` khi key đã tồn tại:
     ```json
     {
       "COMMON": {
         "SAVE": "Save",
         "SAVE": "Save Again",
         "CANCEL": "Cancel"
       }
     }
     ```
   - Chạy:
     ```bash
     compare-translations src/infra/i18n/global
     ```
   - Output:
     ```
     Lỗi: Phát hiện key trùng lặp trong src/infra/i18n/global/en.json:
       - Key "COMMON.SAVE" xuất hiện 2 lần.
         Đề xuất: Xóa hoặc hợp nhất các key trùng lặp trong src/infra/i18n/global/en.json.
     ```

2. **Key trùng toàn cục**:
   - Thêm `CASHIER.CREATE_ORDER.TITLE` vào `cashier/create-order/en.json` khi key đã tồn tại trong `cashier/en.json`.
   - Chạy:
     ```bash
     compare-translations src/infra/i18n/cashier/create-order
     ```
   - Output:
     ```
     Lỗi: Key "CASHIER.CREATE_ORDER.TITLE" trùng lặp:
       - Xuất hiện trong src/infra/i18n/cashier/en.json
       - Xuất hiện trong src/infra/i18n/cashier/create-order/en.json
         Đề xuất: Sử dụng key khác (ví dụ: CASHIER.CREATE_ORDER.PAGE_TITLE) hoặc kiểm tra ngữ nghĩa.
     ```

3. **Thiếu key**:
   - `cashier/create-order/en.json`:
     ```json
     {
       "CASHIER": {
         "CREATE_ORDER": {
           "TITLE": "Create Order",
           "SUBMIT": "Confirm Order"
         }
       }
     }
     ```
   - `cashier/create-order/vi.json`:
     ```json
     {
       "CASHIER": {
         "CREATE_ORDER": {
           "TITLE": "Tạo Đơn Hàng"
         }
       }
     }
     ```
   - Chạy:
     ```bash
     compare-translations src/infra/i18n/cashier/create-order
     ```
   - Output:
     ```
     Lỗi: Số lượng key không khớp trong src/infra/i18n/cashier/create-order:
       - en.json: 2 key
       - vi.json: 1 key
     Lỗi: Key "CASHIER.CREATE_ORDER.SUBMIT" có trong en.json nhưng thiếu trong vi.json.
       Đề xuất: Thêm key vào src/infra/i18n/cashier/create-order/vi.json:
       {
         "CASHIER": {
           "CREATE_ORDER": {
             "SUBMIT": "Xác Nhận Đơn Hàng"
           }
         }
       }
     ```

4. **Bản dịch không nhất quán**:
   - `global/en.json`: `"COMMON.SAVE": "Save"`
   - `global/vi.json`: `"COMMON.SAVE": "Ghi Lại"`
   - Chạy:
     ```bash
     compare-translations src/infra/i18n/global
     ```
   - Output:
     ```
     Lỗi: Bản dịch không nhất quán trong src/infra/i18n/global:
       - Key: COMMON.SAVE
       - en.json: "Save"
       - vi.json: "Ghi Lại"
         Đề xuất: Sửa vi.json thành "Lưu".
     ```

## Lưu ý cho AI Agent

- **Tự động hóa**:
  - Tích hợp `compare-translations.js` vào pipeline:
    - Chạy trước khi thêm key để kiểm tra trùng lặp.
    - Chạy sau khi thêm/sửa key để đối chiếu và chuẩn hóa.
  - Ví dụ:
    ```bash
    compare-translations src/infra/i18n/cashier/create-order
    ```
- **Log lỗi**:
  - Hiển thị lỗi trong IDE (trùng key, thiếu key, bản dịch không đúng).
  - Lưu log chi tiết để người dùng xem xét.
- **Đề xuất thông minh**:
  - Trong `<mat-panel>`: Gợi ý key như `<FEATURE>.PANEL.<PROPERTY>` (ví dụ: `CASHIER.PANEL.TITLE`).
  - Trong `<h1>`: Gợi ý key như `<FEATURE>.<SUB_FEATURE>.TITLE` (ví dụ: `CASHIER.CREATE_ORDER.TITLE`).
  - Khi thiếu key trong `vi.json`, gợi ý bản dịch từ từ điển chuẩn.
- **Tích hợp merge-translations**:
  - Sau khi kiểm tra bằng `compare-translations`, chạy `npm run merge-translations` để gộp file.
  - Kiểm tra `public/assets/i18n/<lang>.json` chứa key mới.
- **Từ điển chuẩn**:
  - Sử dụng từ điển trong script để đề xuất bản dịch:
    - "Save" → "Lưu"
    - "Cancel" → "Hủy"
    - "Submit" → "Xác Nhận"
    - "Create" → "Tạo"
    - "Order" → "Đơn Hàng"
  - Nếu từ không có trong từ điển, yêu cầu người dùng cung cấp bản dịch.



