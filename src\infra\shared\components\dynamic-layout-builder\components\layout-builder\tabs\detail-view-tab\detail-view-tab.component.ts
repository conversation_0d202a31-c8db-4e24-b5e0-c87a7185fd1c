import { Component, computed, inject, ChangeDetectionStrategy, OnInit} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { DragDropModule, CdkDragDrop } from '@angular/cdk/drag-drop';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { DynamicLayoutBuilderStateService } from '../../../../services/dynamic-layout-builder-state.service';
import { DetailViewTabService } from './detail-view-tab.service';
import { WidgetConfig, DetailViewSection } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.dto';
import { TabComponentReference } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.model';
import { DynamicLayoutConfigStateService } from '../../../../services/dynamic-layout-config-state.service';
import { ConfirmModalService } from '@/shared/modals/common/confirm-modal/confirm-modal.service';
import { ConfirmModalData } from '@/shared/modals/common/confirm-modal/confirm-modal.component';
import { FlashMessageService } from '@core/services/flash_message.service';
import { SectionWithWidgets } from '../../../../models/dynamic-layout-builder.model';

/**
 * DetailViewTabComponent - Tab hiển thị và quản lý widgets trong sections
 *
 * Tính năng:
 * - Hiển thị sections và widgets từ config
 * - Kéo thả để sắp xếp widgets trong section
 * - Xóa widgets khỏi section
 * - Standalone component cho ResponsiveModalService
 */
@Component({
  selector: 'app-detail-view-tab',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    DragDropModule,
    TranslateModule
  ],
  // REMOVED providers - DetailViewTabService được provide bởi DynamicLayoutBuilderComponent
  templateUrl: './detail-view-tab.component.html',
  styleUrls: ['./detail-view-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DetailViewTabComponent implements OnInit, TabComponentReference {
  // Services - Inject theo phân chia trách nhiệm mới
  private translateService = inject(TranslateService);
  private confirmModalService = inject(ConfirmModalService);
  private flashMessageService = inject(FlashMessageService);
  private configStateService = inject(DynamicLayoutConfigStateService); // Config & layout state
  private stateService = inject(DynamicLayoutBuilderStateService); // UI state
  
  private detailViewTabService!: DetailViewTabService;


  totalWidgets = computed(() =>
    this.sections().reduce((total: number, section: SectionWithWidgets) => total + section.widgets.length, 0)
  );


  detailViewConfig = this.configStateService.detailViewConfig;
  sections = this.configStateService.detailViewTabSections;


  ngOnInit() {
    this.detailViewTabService = new DetailViewTabService(
      this.configStateService,
      this.stateService,
      this.flashMessageService,
      this.translateService
    );
  }


  /**
   * Xử lý khi widget được xóa
   * UI Logic: Hiển thị confirm modal, track event, delegate business logic cho service
   */
  async onWidgetDelete(sectionId: string, widgetId: string) {
    try {
      const confirmData: ConfirmModalData = {
        title: 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.CONFIRM_DELETE_WIDGET_TITLE',
        message: 'DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.CONFIRM_DELETE_WIDGET_MESSAGE',
        confirmText: 'COMMON.DELETE',
        cancelText: 'COMMON.CANCEL',
        confirmColor: 'warn'
      };

      // Mở modal xác nhận và nhận kết quả
      const confirmed = await this.confirmModalService.confirm(confirmData);

      if (confirmed) {
        return this.detailViewTabService.deleteWidget(sectionId, widgetId)
      }
    } catch (error) {
      console.error('❌ Error opening confirm modal:', error);
      // Hiển thị thông báo lỗi khi không thể mở modal
      const errorMessage = this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.WIDGET_DELETE_ERROR');
      this.flashMessageService.error(errorMessage);
    }
  }

  /**
   * Handle widget reordering within a section using Angular CDK Drag & Drop
   * UI Logic: Track event, process CdkDragDrop event và delegate business logic cho service
   */
  onWidgetDrop(event: CdkDragDrop<WidgetConfig[]>, section: DetailViewSection): void {
    if (event.previousIndex === event.currentIndex) {
      console.log('⏭️ Same position, skipping reorder');
      return;
    }

    return this.detailViewTabService.reorderWidgets({
      sectionId: section._id,
      widgets: [...section.widgets],
      oldIndex: event.previousIndex,
      newIndex: event.currentIndex
    });
  }



  /**
   * TrackBy function cho sections
   */
  trackBySection(_index: number, section: DetailViewSection): string {
    return section._id;
  }

  /**
   * TrackBy function cho widgets
   */
  trackByWidget(_index: number, widget: WidgetConfig): string {
    return widget._id;
  }

  /**
   * Kiểm tra section có widgets không
   */
  hasWidgets(section: DetailViewSection): boolean {
    return section.widgets && section.widgets.length > 0;
  }
  

  /**
   * Get widget count text
   */
  getWidgetCountText(count: number): string {
    return count === 1
      ? this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.WIDGET_SINGULAR')
      : this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DETAIL_VIEW.WIDGET_PLURAL');
  }

  /**
   * ✅ NEW: Public method để implement TabComponentReference
   */
  refreshData(): void {
    console.log('🔄 DetailViewTabComponent: Refresh data');
  }
}
