import { CustomerAutocompleteList } from 'salehub_shared_contracts/requests/shared/customer_list';
// <PERSON>h sách khách hàng tự động hoàn thành
export const mockCustomerAutocompleteList: CustomerAutocompleteList = [
  {
    _id: 'cust1',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    phoneNumber: '0901234567',
    address: { province: 'Hồ Chí Minh', district: 'Quận 1', ward: 'Phường Bến Nghé', address: '123 Lê Lợi' },
    latestUsedAddress: { province: 'Hồ Chí Minh', district: 'Quận 1', ward: 'Ph<PERSON>ờng Bến Nghé', address: '123 Lê Lợi' },
    totalSold: 5000000,
    countOrders: 10,
    gender: 'female',
    avatar: 'assets/images/avatar1.jpg',
    note: 'Khách hàng VIP',
    tags: [
      { type: 'success', text: 'VIP' },
      { type: 'primary', text: '<PERSON><PERSON><PERSON> thiết' }
    ],
    score: {
      value: 8.5,
      orderStatusPercent: {
        success: 90,
        return: 5,
        others: 5
      }
    }
  },
  {
    _id: 'cust2',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    phoneNumber: '0912345678',
    address: { province: 'Hà Nội', district: 'Cầu Giấy', ward: 'Dịch Vọng', address: '45 Xuân Thủy' },
    latestUsedAddress: { province: 'Hà Nội', district: 'Cầu Giấy', ward: 'Dịch Vọng', address: '45 Xuân Thủy' },
    totalSold: 3000000,
    countOrders: 5,
    gender: 'male',
    score: {
      value: 7.5,
      orderStatusPercent: {
        success: 80,
        return: 10,
        others: 10
      }
    }
  }
];