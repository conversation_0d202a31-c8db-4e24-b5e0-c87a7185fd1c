/* Connection Create Modal Styles */

// Modal header styling
.modal-header {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 1rem;
  
  .platform-logo {
    img {
      border: 1px solid #e0e0e0;
      object-fit: contain;
    }
  }
  
  .platform-info {
    h5 {
      color: #333;
      font-weight: 600;
      margin-bottom: 0.25rem;
    }
    
    p {
      color: #6c757d;
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }
}

// Modal body styling
.modal-body {
  position: relative;
  min-height: 200px;
  
  // Loading overlay
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 10;
    border-radius: 8px;
  }
}

// Form styling
.account-login-form,
.api-auth-form,
.cookie-auth-form {
  .mat-mdc-form-field {
    width: 100%;
    
    &.w-100 {
      width: 100% !important;
    }
    
    ::ng-deep {
      .mat-mdc-text-field-wrapper {
        .mat-mdc-form-field-flex {
          .mat-mdc-floating-label {
            color: #6c757d;
          }
          
          .mat-mdc-input-element {
            color: #333;
            
            &::placeholder {
              color: #adb5bd;
            }
          }
        }
      }
      
      // Error styling
      .mat-mdc-form-field-error {
        color: #dc3545;
        font-size: 0.875rem;
      }
      
      // Focus styling
      &.mat-focused {
        .mat-mdc-text-field-wrapper {
          .mat-mdc-form-field-flex {
            .mat-mdc-floating-label {
              color: #1976d2;
            }
          }
        }
      }
    }
  }
  
  // Button styling
  .mat-mdc-raised-button {
    &.w-100 {
      width: 100% !important;
      padding: 0.75rem 1rem;
      
      .mat-icon {
        margin-right: 0.5rem;
      }
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

// Browser auth specific styling
.cookie-auth-form {
  .browser-auth-info {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 0.375rem;
    
    p {
      color: #495057;
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: 0;
    }
  }
  
  .text-success {
    color: #28a745 !important;
    
    .mat-icon {
      vertical-align: middle;
      margin-right: 0.5rem;
      font-size: 1.2rem;
    }
  }
}

// Modal footer styling
.modal-footer {
  border-top: 1px solid #e0e0e0;
  padding-top: 1rem;
  
  .mat-mdc-button,
  .mat-mdc-raised-button {
    min-width: 80px;
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  .gap-2 {
    gap: 0.5rem !important;
  }
}

// Gap utility classes
.gap-3 {
  gap: 1rem !important;
}

.d-flex {
  display: flex !important;
}

.flex-column {
  flex-direction: column !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.text-center {
  text-align: center !important;
}

.w-100 {
  width: 100% !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.text-muted {
  color: #6c757d !important;
}

.text-success {
  color: #28a745 !important;
}

.rounded {
  border-radius: 0.375rem !important;
}

.border {
  border: 1px solid #dee2e6 !important;
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

// Responsive adjustments
@media (max-width: 576px) {
  .connection-create-modal {
    .modal-header {
      .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        
        .platform-logo {
          margin-bottom: 0.75rem;
          margin-right: 0 !important;
        }
      }
    }
    
    .modal-footer {
      .d-flex {
        flex-direction: column;
        
        .mat-mdc-button,
        .mat-mdc-raised-button {
          width: 100%;
          margin-bottom: 0.5rem;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
