import { ChangeDetectionStrategy, Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialogModule } from '@angular/material/dialog';
import { FlashMessageService } from '@core/services/flash_message.service';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatTableModule } from '@angular/material/table';
import { Field, FieldPermission, FieldPermissionProfile } from '@domain/entities/field.entity';
import { StrictModalComponent } from '@shared/components/standard-dialog/interfaces/modal-component.interface';

/**
 * Interface cho dữ liệu đầu vào của modal
 */
export interface FieldPermissionModalData {
  field: Field;
}

/**
 * <PERSON><PERSON><PERSON> dữ liệu trả về từ modal
 */
export type FieldPermissionModalResult = FieldPermissionProfile[] | undefined;

/**
 * Component modal thiết lập quyền truy cập field
 */
@Component({
  selector: 'app-field-permission-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatRadioModule,
    MatTableModule,
    TranslateModule
  ],
  templateUrl: './field-permission-modal.component.html',
  styleUrls: ['./field-permission-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FieldPermissionModalComponent implements StrictModalComponent<FieldPermissionModalData, FieldPermissionModalResult> {
  /**
   * Dữ liệu đầu vào cho modal
   */
  data: FieldPermissionModalData = {
    field: {} as Field
  };

  /**
   * Danh sách profiles với quyền truy cập (reactive signal)
   */
  profiles = signal<FieldPermissionProfile[]>([]);

  /**
   * Tên field để hiển thị trong header
   */
  fieldName = signal<string>('');

  /**
   * Các cột hiển thị trong table
   */
  displayedColumns: string[] = ['profileName', 'readWrite', 'readOnly', 'dontShow'];

  constructor(
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {
    // Khởi tạo signals với dữ liệu từ data.field
    this.initializeFromField();
  }

  /**
   * Khởi tạo dữ liệu từ data.field
   */
  private initializeFromField(): void {
    const fieldData = this.data.field;
    if (fieldData && fieldData.label) {
      this.fieldName.set(fieldData.label);
      this.profiles.set([...fieldData.permissionProfiles || []]);
    }
  }

  /**
   * Xử lý khi thay đổi quyền cho một profile
   * @param profileId ID của profile
   * @param permission Quyền mới
   */
  onPermissionChange(profileId: string, permission: FieldPermission): void {
    const currentProfiles = this.profiles();
    const updatedProfiles = currentProfiles.map(profile =>
      profile._id === profileId
        ? { ...profile, permission }
        : profile
    );
    this.profiles.set(updatedProfiles);
  }

  /**
   * Đặt quyền "read_write" cho tất cả profiles
   */
  setAllReadWrite(): void {
    const currentProfiles = this.profiles();
    const updatedProfiles = currentProfiles.map(profile => ({
      ...profile,
      permission: 'read_write' as const
    }));
    this.profiles.set(updatedProfiles);
  }

  /**
   * Đặt quyền "read" cho tất cả profiles
   */
  setAllReadOnly(): void {
    const currentProfiles = this.profiles();
    const updatedProfiles = currentProfiles.map(profile => ({
      ...profile,
      permission: 'read' as const
    }));
    this.profiles.set(updatedProfiles);
  }

  /**
   * Đặt quyền "none" cho tất cả profiles
   */
  setAllDontShow(): void {
    const currentProfiles = this.profiles();
    const updatedProfiles = currentProfiles.map(profile => ({
      ...profile,
      permission: 'none' as const
    }));
    this.profiles.set(updatedProfiles);
  }

  /**
   * Xử lý khi nhấn nút Save
   */
  onSave(): void {
    this.close();
  }

  /**
   * Xử lý khi nhấn nút Cancel
   */
  onCancel(): void {
    this.close();
  }

  /**
   * TrackBy function cho ngFor để tối ưu hiệu suất
   * @param _index Index của item (không sử dụng)
   * @param profile Profile object
   * @returns ID của profile
   */
  trackByProfileId(_index: number, profile: FieldPermissionProfile): string {
    return profile._id;
  }

  /**
   * Đóng modal - Không cần thiết với ResponsiveModalService pattern
   * Modal sẽ tự động đóng khi getModalResult() được gọi
   */
  private close(): void {
    // ResponsiveModalService sẽ tự động xử lý việc đóng modal
    // Không cần gọi dialogRef.close() hay bottomSheetRef.dismiss()
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): FieldPermissionModalResult {
    return this.profiles();
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    // FieldPermissionModal luôn valid vì không có validation phức tạp
    return true;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: FieldPermissionModalData): void {
    // Cập nhật data
    this.data = data;
    this.fieldName.set(data.field.label);
    // Tạo bản sao của profiles để tránh mutate dữ liệu gốc
    this.profiles.set([...data.field.permissionProfiles || []]);
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Khởi tạo lại dữ liệu từ data.field
    this.initializeFromField();

    // Kiểm tra dữ liệu bắt buộc
    const fieldData = this.data.field;
    if (!fieldData.permissionProfiles || fieldData.permissionProfiles.length === 0) {
      // Thay thế console.warn bằng FlashMessageService
      if (this.flashMessageService && this.translateService) {
        this.flashMessageService.warning(
          this.translateService.instant('FLASH_MESSAGES.WARNING.GENERAL.INCOMPLETE_DATA'),
          {
            description: 'No permission profiles provided for field'
          }
        );
      }
    }
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
