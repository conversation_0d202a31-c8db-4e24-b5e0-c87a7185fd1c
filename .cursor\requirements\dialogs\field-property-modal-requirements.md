

### Modal Sửa thuộc tính

#### 1. Tổng quan

**M<PERSON><PERSON> tiêu**:  
<PERSON><PERSON> "Sửa thuộc tính" cho phép người dùng (admin, quản lý, hoặc người dùng được phân quyền qua **IAM**) chỉnh sửa các thuộc tính của một **Custom Field** trong `DynamicLayoutBuilderComponent`. Modal hỗ trợ chỉnh sửa nhãn, quyề<PERSON> công khai, t<PERSON>h b<PERSON><PERSON> buộ<PERSON>, tooltip, và các tùy chọn đặc thù theo loại field (ví dụ: số ký tự, danh sách tùy chọn, định dạng tiền tệ). Modal đảm bảo tính linh hoạt, hỗ trợ các ngành bán lẻ online (thờ<PERSON> trang, mỹ ph<PERSON>m, mẹ và bé, đồ gia dụ<PERSON>, c<PERSON><PERSON>h<PERSON>, th<PERSON>ao, <PERSON><PERSON><PERSON>, thự<PERSON>, d<PERSON><PERSON> v<PERSON>ố) và tích hợp với các sàn TMĐT (Shopee, Lazada, TikTok Shop, Tiki, Sendo).

**Vị trí sử dụng**:  
- Modal được mở từ `FieldComponent` trong `DynamicLayoutBuilderComponent` thông qua `MatDialog`.  
- Input được truyền qua `MAT_DIALOG_DATA` với cấu trúc `FieldPropertiesData`.  
- Khi đóng (save/cancel), modal trả về `CustomField` đã chỉnh sửa (hoặc không thay đổi nếu hủy).  

**Đường dẫn triển khai**:  
- Đặt trong `src/infra/shared/modals/field-properties/`:  
  - `field-properties.component.ts`  
  - `field-properties.component.html`  
  - `field-properties.component.scss`  
- View Model: `src/infra/shared/models/view/field-properties.model.ts`.  
- i18n: `src/infra/i18n/shared/modals/field-properties/` (các file `en.json`, `vi.json`).  

**Yêu cầu cơ bản**:  
- Tuân thủ [rules/angular.mdc](../../rules/angular.mdc) và [rules/requirements.mdc](../../rules/requirements.mdc).  
- Sử dụng **MCP server** để debug lỗi và xem giao diện trên browser (`http://localhost:4200/#/test`).  
- Hỏi lại nếu có điểm cần làm rõ để tránh sửa đổi nhiều lần.  
- Sử dụng **Angular 19**, **standalone components**, **Signals** cho trạng thái, **OnPush Change Detection**, và **lazy loading**.  
- Tích hợp **i18n** với `ngx-translate`, thêm key vào `src/infra/i18n/` với format `SCREAMING_SNAKE_CASE`.  
- Sử dụng **Angular Material** (`@angular/material`), **CDK DragDrop** (`@angular/cdk/drag-drop`), **PrimeNG**, **Bootstrap**, **Tailwind CSS** (đã import trong `styles.scss`).  
- Giao diện responsive, dựa trên theme **Metronic** (https://keenthemes.com/metronic/tailwind/demo1/).  
- Tối ưu hiệu suất với `trackBy`, **OnPush**, và lazy-load tùy chọn đặc thù.  
- Hỗ trợ **accessibility** (WCAG 2.1): `aria-label`, điều hướng bàn phím (Tab, Enter, Escape).  

---

#### 2. Giao diện và Cấu trúc Modal

**Header**:  
- **Tiêu đề**: "Sửa thuộc tính: [Field Type]" (Field Type là tên hiển thị của loại field, ví dụ: "Single Line", "Pick List").  
  - Ví dụ: Nếu `field.type` là `text`, tiêu đề là "Sửa thuộc tính: Single Line".  
  - Hỗ trợ **i18n** với key `EDIT_FIELD_PROPERTIES.TITLE` và tham số `{ fieldType }`.  
  - Sử dụng `mat-dialog-title` để hiển thị tiêu đề.  

**Body**:  
- Sử dụng `mat-dialog-content` để chứa các trường nhập liệu.  
- Bố cục:  
  - **Các trường chung**: Hiển thị cho tất cả loại field, đặt ở trên cùng.  
  - **Các tùy chọn đặc thù**: Hiển thị dựa trên `field.type`, nhóm trong `mat-expansion-panel` (nếu nhiều tùy chọn) hoặc trực tiếp bên dưới.  
  - Sử dụng **CSS Grid/Flexbox** để bố trí responsive, ưu tiên UX (nhãn ở trên, các trường đặc thù ở dưới).  

**Footer**:  
- Sử dụng `mat-dialog-actions` để chứa các nút:  
  - **Save**: Lưu thay đổi và đóng modal, trả về `CustomField` đã cập nhật.  
    - Sử dụng `mat-raised-button`, màu primary (theo theme Metronic).  
    - Phím tắt: Enter.  
  - **Cancel**: Đóng modal mà không lưu, trả về `null` hoặc không trả về gì.  
    - Sử dụng `mat-button`, màu neutral.  
    - Phím tắt: Escape.  
- Căn chỉnh nút sang phải, khoảng cách đều (theo Tailwind CSS).

---

#### 3. Input và Output

**Input**:  
- Modal nhận input qua `MAT_DIALOG_DATA` với cấu trúc `FieldPropertiesData`:  
  ```typescript
  // src/infra/shared/models/view/field-properties.model.ts
  import { CustomField } from 'src/domain/entities/custom-field';

  export interface FieldPropertiesData {
    field: CustomField; // Custom Field hiện tại cần chỉnh sửa
    availableModules?: Array<{
      _id: string;
      name: string;
    }>; // Danh sách module khả dụng cho field Search (tùy chọn)
  }
  ```  
- **field**: Chứa thông tin `CustomField` (bao gồm `type`, `value`, `constraints`) để hiển thị giá trị hiện tại.  
- **availableModules**: Dùng cho field `Search` để hiển thị danh sách module trong dropdown (mặc định: `sales_quotes`, `contacts`, `transactions`).  

**Output**:  
- Khi **Save**: Modal trả về `CustomField` đã được cập nhật (nhãn, thuộc tính chung, constraints).  
  ```typescript
  dialogRef.close(updatedField: CustomField);
  ```  
- Khi **Cancel**: Modal đóng mà không trả về gì (`dialogRef.close()`).  
- Output được xử lý bởi `FieldComponent` để cập nhật layout và gửi API lưu vào backend.

---

#### 4. Các trường chung (Áp dụng cho tất cả loại field)

Các trường chung được hiển thị trong một section ở đầu body, sử dụng **Reactive Forms** để quản lý input và validation.

1. **Label (Nhãn)**:  
   - **UI**: `mat-form-field` với `matInput` (type `text`).  
   - **Mặc định**: `field.label` từ input `FieldPropertiesData`.  
   - **Validation**:  
     - Bắt buộc (không để trống).  
     - Tối đa 255 ký tự.  
     - Hiển thị lỗi inline với `mat-error` (ví dụ: "Nhãn không được để trống").  
   - **i18n**: Label của trường là `EDIT_FIELD_PROPERTIES.LABEL`.  
   - **Tích hợp**: Cập nhật `CustomField.label`.  

2. **Đánh dấu là Trường Công Khai (Public Field)**:  
   - **UI**: `mat-checkbox`.  
   - **Mặc định**: `field.isPublic` (mặc định `false` nếu không định nghĩa).  
   - **Ý nghĩa**: Nếu chọn, field có thể chia sẻ công khai (ví dụ: đồng bộ với Shopee, báo cáo **BI**).  
   - **i18n**: Label là `EDIT_FIELD_PROPERTIES.IS_PUBLIC`.  
   - **Tích hợp**: Cập nhật `CustomField.isPublic`.  

3. **Đánh dấu là Trường Bắt buộc (Required Field)**:  
   - **UI**: `mat-checkbox`.  
   - **Mặc định**: `field.isRequired` (mặc định `false` nếu không định nghĩa).  
   - **Ý nghĩa**: Nếu chọn, người dùng phải nhập giá trị khi tạo/sửa dữ liệu.  
   - **i18n**: Label là `EDIT_FIELD_PROPERTIES.IS_REQUIRED`.  
   - **Tích hợp**: Cập nhật `CustomField.isRequired`.  

4. **Không cho phép các giá trị trùng lặp (Unique Values)**:  
   - **UI**: `mat-checkbox`.  
   - **Mặc định**: `field.constraints.unique` (mặc định `false` nếu không định nghĩa).  
   - **Ý nghĩa**: Đảm bảo giá trị duy nhất trong hệ thống (chỉ áp dụng cho `text`, `email`, `phone`, `url`).  
   - **Hiển thị điều kiện**: Chỉ hiển thị cho các field `text`, `email`, `phone`, `url`.  
   - **i18n**: Label là `EDIT_FIELD_PROPERTIES.UNIQUE`.  
   - **Tích hợp**: Cập nhật `CustomField.constraints.unique`.  

5. **Hiển thị Tooltip**:  
   - **UI**:  
     - Một `mat-checkbox` để bật/tắt tooltip.  
     - Khi chọn: Hiển thị `mat-form-field` với `matInput` (type `text`) để nhập nội dung tooltip.  
     - Khi bỏ chọn: Ẩn `mat-form-field` và xóa nội dung tooltip.  
   - **Mặc định**:  
     - Checkbox: `!!field.tooltip` (chọn nếu `tooltip` có giá trị).  
     - Text field: `field.tooltip` (rỗng nếu không định nghĩa).  
   - **Validation**:  
     - Tối đa 500 ký tự (nếu bật).  
     - Không bắt buộc.  
     - Hiển thị lỗi inline với `mat-error` (ví dụ: "Tooltip quá dài").  
   - **i18n**:  
     - Checkbox label: `EDIT_FIELD_PROPERTIES.SHOW_TOOLTIP`.  
     - Text field label: `EDIT_FIELD_PROPERTIES.TOOLTIP`.  
   - **Tích hợp**: Cập nhật `CustomField.tooltip`.  
   - **UX**: Tooltip hiển thị trên giao diện chính bằng `matTooltip` khi hover vào field.

---

#### 5. Các tùy chọn đặc thù theo loại Field

Các tùy chọn đặc thù được hiển thị trong section riêng (dưới các trường chung), sử dụng `mat-expansion-panel` để nhóm nếu nhiều tùy chọn. Mỗi loại field có giao diện và logic riêng, dựa trên `field.type` và `field.constraints`.

##### 5.1. Field Type: Email / Date / DateTime / Percent
- **Tùy chọn đặc thù**: Không có (chỉ hiển thị các trường chung).  
- **Giải thích**:  
  - **Email**: Validation định dạng email (`<EMAIL>`).  
  - **Date**: Định dạng `YYYY/MM/DD`, tự động điều chỉnh múi giờ.  
  - **DateTime**: Định dạng `YYYY/MM/DD HH:MM`, tự động điều chỉnh múi giờ.  
  - **Percent**: Giá trị từ 0-100, không cần tùy chỉnh thêm.  
- **Validation**: Xử lý ở frontend (Reactive Forms) và backend.  
- **Tích hợp**: Không cập nhật `constraints` (rỗng).

##### 5.2. Field Type: Text (Single Line) / Phone / URL
- **Tùy chọn đặc thù**:  
  - **Số ký tự được phép (Maximum Length)**:  
    - **UI**: `mat-form-field` với `matInput` (type `number`).  
    - **Mặc định**: `constraints.maxLength` (255 nếu không định nghĩa).  
    - **Validation**:  
      - Giá trị từ 1 đến 255.  
      - Chỉ chấp nhận số nguyên.  
      - Hiển thị lỗi inline với `mat-error` (ví dụ: "Số ký tự phải từ 1 đến 255").  
    - **i18n**: Label là `EDIT_FIELD_PROPERTIES.MAX_LENGTH`.  
    - **Tích hợp**: Cập nhật `CustomField.constraints.maxLength`.  

- **Giải thích**:  
  - **Text**: Văn bản ngắn (tên, địa chỉ).  
  - **Phone**: Số điện thoại (validation regex, ví dụ: `+84...`).  
  - **URL**: Liên kết web (validation định dạng URL).  
  - Giới hạn ký tự đảm bảo hiệu suất và phù hợp với TMĐT (mô tả sản phẩm ngắn).

##### 5.3. Field Type: Number / Phone
- **Tùy chọn đặc thù**:  
  - **Số chữ số tối đa (Maximum Digits)**:  
    - **UI**: `mat-form-field` với `matInput` (type `number`).  
    - **Mặc định**: `constraints.maxDigits` (9 nếu không định nghĩa).  
    - **Validation**:  
      - Giá trị từ 1 đến 9.  
      - Chỉ chấp nhận số nguyên.  
      - Hiển thị lỗi inline với `mat-error`.  
    - **i18n**: Label là `EDIT_FIELD_PROPERTIES.MAX_DIGITS`.  
    - **Tích hợp**: Cập nhật `CustomField.constraints.maxDigits`.  

- **Giải thích**:  
  - **Number**: Số nguyên (số lượng sản phẩm, mã sản phẩm).  
  - **Phone**: Số điện thoại (trùng tùy chọn `maxLength` ở trên, nhưng nhấn mạnh chữ số).  
  - Giới hạn chữ số phù hợp với bán lẻ (số lượng tồn kho).

##### 5.4. Field Type: Textarea (Multi Line)
- **Tùy chọn đặc thù**:  
  - **Kiểu (Type)**:  
    - **UI**: `mat-select` với 3 tùy chọn:  
      - Plain Text, Nhỏ (`small`, 2000 ký tự).  
      - Plain Text, Lớn (`large`, 32000 ký tự).  
      - Văn bản Phong phú (`rich`, 50000 ký tự, hỗ trợ bold, italic, list).  
    - **Mặc định**: `constraints.textType` (`small` nếu không định nghĩa).  
    - **Hiển thị phụ**: Khi chọn, hiển thị text hướng dẫn:  
      - **Plain Text, Nhỏ**:  
        - "Giới Hạn Ký Tự: 2000" (`EDIT_FIELD_PROPERTIES.MAX_LENGTH_2000`).  
        - "Số trường còn lại: 9/10" (`EDIT_FIELD_PROPERTIES.REMAINING_FIELDS`, lấy từ config).  
      - **Plain Text, Lớn**:  
        - "Giới Hạn Ký Tự: 32000" (`EDIT_FIELD_PROPERTIES.MAX_LENGTH_32000`).  
        - "Số trường còn lại: 10/10".  
      - **Văn bản Phong phú**:  
        - "Giới Hạn Ký Tự: 50000" (`EDIT_FIELD_PROPERTIES.MAX_LENGTH_50000`).  
        - "Số trường rich text còn lại: 4/5" (`EDIT_FIELD_PROPERTIES.REMAINING_RICH_FIELDS`).  
    - **Validation**:  
      - Bắt buộc chọn một giá trị.  
      - Giới hạn số trường lấy từ backend/config (giả định 10 field nhỏ, 5 rich text).  
    - **i18n**: Label là `EDIT_FIELD_PROPERTIES.TEXT_TYPE`.  
    - **Tích hợp**: Cập nhật `CustomField.constraints.textType` và `CustomField.constraints.maxLength` (2000, 32000, hoặc 50000).  

- **Giải thích**:  
  - **Plain Text, Nhỏ**: Mô tả ngắn (ghi chú đơn hàng).  
  - **Plain Text, Lớn**: Mô tả dài (thông tin sản phẩm).  
  - **Văn bản Phong phú**: Mô tả HTML cho TMĐT.  
  - Giới hạn số trường đảm bảo hiệu suất.

##### 5.5. Field Type: Picklist / MultiPicklist
- **Tùy chọn đặc thù**:  
  1. **Block Tùy Chọn Danh Sách**:  
     - **Header**:  
       - **Tiêu đề**: "Tùy Chọn Danh Sách" (`EDIT_FIELD_PROPERTIES.PICKLIST_OPTIONS`).  
       - **Nút "+"**: Thêm row mới vào danh sách (cuối danh sách).  
       - **Icon gear** (`mat-icon: settings`): Mở `mat-menu` với:  
         - **Thêm hàng loạt** (`EDIT_FIELD_PROPERTIES.ADD_BULK`).  

     - **Body (Chế độ thông thường)**:  
       - Danh sách row, mỗi row gồm:  
         - **cdkDragHandle** (`☰`): Kéo-thả sắp xếp (sử dụng `@angular/cdk/drag-drop`).  
         - **mat-form-field** với `matInput`: Nhập/sửa giá trị tùy chọn (ví dụ: "Đỏ").  
         - **Nút "+"**: Thêm row mới ngay dưới row hiện tại.  
         - **Nút "-"**: Xóa row hiện tại.  
       - **Validation**:  
         - Tùy chọn không được để trống.  
         - Tối đa 255 ký tự mỗi tùy chọn.  
         - Không trùng lặp (so sánh không phân biệt hoa/thường).  
         - Tối đa 100 tùy chọn (config hệ thống).  
       - **UX**:  
         - Hiển thị lỗi inline với `mat-error` (ví dụ: "Tùy chọn trùng lặp").  
         - Hiệu ứng kéo-thả mượt mà với `cdkDragDrop`.  
       - **Tích hợp**: Cập nhật `CustomField.constraints.picklistValues`.  

     - **Body (Chế độ Thêm hàng loạt)**:  
       - **UI**: `mat-form-field` với `matInput` (type `textarea`).  
       - **Placeholder**: "Nhập mỗi tùy chọn trên một dòng" (`EDIT_FIELD_PROPERTIES.BULK_PLACEHOLDER`).  
       - **Nút**:  
         - **Save** (`mat-raised-button`): Parse textarea, validate, và cập nhật danh sách.  
         - **Cancel** (`mat-button`): Hủy, trở về chế độ thông thường.  
       - **Validation**:  
         - Parse theo ký tự xuống dòng (`\n`).  
         - Loại bỏ dòng rỗng/khoảng trắng.  
         - Kiểm tra: Không rỗng, tối đa 255 ký tự, không trùng lặp, tối đa 100 tùy chọn.  
       - **UX**:  
         - Hiển thị số dòng/ký tự đã nhập.  
         - Phím tắt: Ctrl+Enter (Save), Escape (Cancel).  
       - **Tích hợp**: Cập nhật `CustomField.constraints.picklistValues`.  

  2. **Chọn giá trị mặc định**:  
     - **UI**: `mat-select` (single cho `picklist`, multiple cho `multi-picklist`).  
     - **Mặc định**: `constraints.defaultValue` (rỗng nếu không định nghĩa).  
     - **Dữ liệu**: Lấy từ `constraints.picklistValues`.  
     - **UX**: Cập nhật động khi `picklistValues` thay đổi.  
     - **i18n**: Label là `EDIT_FIELD_PROPERTIES.DEFAULT_VALUE`.  
     - **Tích hợp**: Cập nhật `CustomField.constraints.defaultValue`.  

  3. **Hiển thị theo thứ tự bảng chữ cái**:  
     - **UI**: `mat-select` với:  
       - "Theo thứ tự nhập" (`input`).  
       - "Theo thứ tự bảng chữ cái" (`alphabetical`).  
     - **Mặc định**: `constraints.sortOrder` (`input` nếu không định nghĩa).  
     - **UX**: Preview sắp xếp trong `mat-select` giá trị mặc định.  
     - **i18n**: Label là `EDIT_FIELD_PROPERTIES.SORT_ORDER`.  
     - **Tích hợp**: Cập nhật `CustomField.constraints.sortOrder`.  

##### 5.6. Field Type: Search
- **Tùy chọn đặc thù**:  
  - **Mô-đun Search**:  
    - **UI**: `mat-select` với các tùy chọn từ `availableModules` (trong `FieldPropertiesData`).  
    - **Mặc định**: `constraints.searchModule` (hoặc `sales_quotes` nếu không định nghĩa).  
    - **Dữ liệu**:  
      - Nếu `availableModules` được cung cấp, hiển thị `{ name }` và lưu `_id`.  
      - Nếu không, hiển thị danh sách mặc định: `sales_quotes`, `contacts`, `transactions`.  
    - **Validation**: Bắt buộc chọn một giá trị.  
    - **i18n**: Label là `EDIT_FIELD_PROPERTIES.SEARCH_MODULE`.  
    - **Tích hợp**: Cập nhật `CustomField.constraints.searchModule`.  

- **Giải thích**: Tìm kiếm dữ liệu từ module ERP (**CRM**, **OMS**).

##### 5.7. Field Type: User
- **Tùy chọn đặc thù**:  
  - **Loại (Type)**:  
    - **UI**: `mat-select` với:  
      - Một người dùng (`single`).  
      - Nhiều người dùng (`multiple`).  
    - **Mặc định**: `constraints.userType` (`single` nếu không định nghĩa).  
    - **Validation**: Bắt buộc chọn một giá trị.  
    - **i18n**: Label là `EDIT_FIELD_PROPERTIES.USER_TYPE`.  
    - **Tích hợp**: Cập nhật `CustomField.constraints.userType`.  

- **Giải thích**: Gán người dùng từ **HRM** (nhân viên, nhóm).

##### 5.8. Field Type: Upload File
- **Tùy chọn đặc thù**:  
  - **Tải lên nhiều tập tin**:  
    - **UI**: `mat-checkbox`.  
    - **Mặc định**: `constraints.allowMultipleFiles` (`false` nếu không định nghĩa).  
    - **Hiển thị phụ**: Nếu chọn, hiển thị `mat-form-field` để nhập số file tối đa (1-5).  
    - **Validation**:  
      - Nếu chọn, `maxFiles` từ 1 đến 5.  
      - Nếu không chọn, `maxFiles` là 1.  
    - **i18n**:  
      - Checkbox label: `EDIT_FIELD_PROPERTIES.ALLOW_MULTIPLE_FILES`.  
      - Number field label: `EDIT_FIELD_PROPERTIES.MAX_FILES`.  
    - **Tích hợp**: Cập nhật `CustomField.constraints.allowMultipleFiles` và `CustomField.constraints.maxFiles`.  

- **Giải thích**: Tải lên tài liệu (hợp đồng, hóa đơn).

##### 5.9. Field Type: Upload Image
- **Tùy chọn đặc thù**:  
  - **Hình ảnh tối đa**:  
    - **UI**: `mat-select` với các giá trị: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10.  
    - **Mặc định**: `constraints.maxImages` (1 nếu không định nghĩa).  
    - **Validation**: Bắt buộc chọn một giá trị.  
    - **i18n**: Label là `EDIT_FIELD_PROPERTIES.MAX_IMAGES`.  
    - **Tích hợp**: Cập nhật `CustomField.constraints.maxImages`.  

- **Giải thích**: Tải lên hình ảnh sản phẩm (thời trang, mỹ phẩm).

##### 5.10. Field Type: Currency
- **Tùy chọn đặc thù**:  
  1. **Số chữ số tối đa**:  
     - **UI**: `mat-form-field` với `matInput` (type `number`).  
     - **Mặc định**: `constraints.maxDigits` (9 nếu không định nghĩa).  
     - **Validation**: Giá trị từ 1 đến 16.  
     - **i18n**: Label là `EDIT_FIELD_PROPERTIES.MAX_DIGITS`.  
     - **Tích hợp**: Cập nhật `CustomField.constraints.maxDigits`.  

  2. **Số chữ số thập phân**:  
     - **UI**: `mat-form-field` với `matInput` (type `number`).  
     - **Mặc định**: `constraints.decimalPlaces` (2 nếu không định nghĩa).  
     - **Validation**: Giá trị từ 0 đến 4.  
     - **i18n**: Label là `EDIT_FIELD_PROPERTIES.DECIMAL_PLACES`.  
     - **Tích hợp**: Cập nhật `CustomField.constraints.decimalPlaces`.  

  3. **Tùy chọn làm tròn**:  
     - **UI**: `mat-select` với:  
       - Normal (`normal`).  
       - Rounding Off (`off`).  
       - Rounding Up (`up`).  
       - Rounding Down (`down`).  
     - **Mặc định**: `constraints.rounding` (`normal` nếu không định nghĩa).  
     - **i18n**: Label là `EDIT_FIELD_PROPERTIES.ROUNDING`.  
     - **Tích hợp**: Cập nhật `CustomField.constraints.rounding`.  

- **Giải thích**: Giá trị tiền tệ (giá sản phẩm, chiết khấu).

##### 5.11. Field Type: Decimal
- **Tùy chọn đặc thù**:  
  1. **Số chữ số tối đa**:  
     - **UI**: `mat-form-field` với `matInput` (type `number`).  
     - **Mặc định**: `constraints.maxDigits` (16 nếu không định nghĩa).  
     - **Validation**: Giá trị từ 1 đến 16.  
     - **i18n**: Label là `EDIT_FIELD_PROPERTIES.MAX_DIGITS`.  
     - **Tích hợp**: Cập nhật `CustomField.constraints.maxDigits`.  

  2. **Số chữ số thập phân**:  
     - **UI**: `mat-form-field` với `matInput` (type `number`).  
     - **Mặc định**: `constraints.decimalPlaces` (2 nếu không định nghĩa).  
     - **Validation**: Giá trị từ 0 đến 8.  
     - **i18n**: Label là `EDIT_FIELD_PROPERTIES.DECIMAL_PLACES`.  
     - **Tích hợp**: Cập nhật `CustomField.constraints.decimalPlaces`.  

  3. **Dấu phân cách số**:  
     - **UI**: `mat-checkbox`.  
     - **Mặc định**: `constraints.useNumberSeparator` (`false` nếu không định nghĩa).  
     - **i18n**: Label là `EDIT_FIELD_PROPERTIES.USE_NUMBER_SEPARATOR`.  
     - **Tích hợp**: Cập nhật `CustomField.constraints.useNumberSeparator`.  

- **Giải thích**: Giá trị thập phân (trọng lượng, tỷ lệ).

##### 5.12. Field Type: Checkbox
- **Tùy chọn đặc thù**:  
  - **Kích hoạt mặc định**:  
    - **UI**: `mat-checkbox`.  
    - **Mặc định**: `constraints.enableByDefault` (`false` nếu không định nghĩa).  
    - **i18n**: Label là `EDIT_FIELD_PROPERTIES.ENABLE_BY_DEFAULT`.  
    - **Tích hợp**: Cập nhật `CustomField.constraints.enableByDefault` và `CustomField.value`.  

- **Giải thích**: Giá trị boolean (đồng ý nhận tin, trạng thái).

---

#### 6. Yêu cầu bổ sung

**Hiệu suất**:  
- Sử dụng **OnPush Change Detection** cho component.  
- Thêm `trackBy` cho danh sách tùy chọn trong `Picklist`/`MultiPicklist`:  
  ```typescript
  trackByOption(index: number, option: string): string {
    return option;
  }
  ```  
- Lazy-load tùy chọn đặc thù dựa trên `field.type` (dùng `*ngIf` hoặc `ngSwitch`).  

**Responsive UX**:  
- Sử dụng **CSS Grid/Flexbox** (theo Tailwind CSS) để bố trí.  
- Responsive trên mobile:  
  - Thu gọn `mat-select` và `mat-form-field` (dùng `appearance="fill"`).  
  - Hỗ trợ scroll trong `mat-dialog-content`.  
- **Accessibility** (WCAG 2.1):  
  - Thêm `aria-label` cho input, checkbox, select.  
  - Đảm bảo focus và điều hướng bàn phím (Tab, Enter, Escape).  

**i18n**:  
- Thêm key vào `src/infra/i18n/shared/modals/field-properties/` (`en.json`, `vi.json`):  
  ```json
  {
    "EDIT_FIELD_PROPERTIES.TITLE": "Edit Properties: {fieldType}",
    "EDIT_FIELD_PROPERTIES.LABEL": "Label",
    "EDIT_FIELD_PROPERTIES.IS_PUBLIC": "Mark as Public Field",
    "EDIT_FIELD_PROPERTIES.IS_REQUIRED": "Mark as Required Field",
    "EDIT_FIELD_PROPERTIES.UNIQUE": "Do not allow duplicate values",
    "EDIT_FIELD_PROPERTIES.SHOW_TOOLTIP": "Show Tooltip",
    "EDIT_FIELD_PROPERTIES.TOOLTIP": "Tooltip Content",
    "EDIT_FIELD_PROPERTIES.MAX_LENGTH": "Maximum Length",
    "EDIT_FIELD_PROPERTIES.MAX_DIGITS": "Maximum Digits",
    "EDIT_FIELD_PROPERTIES.TEXT_TYPE": "Type",
    "EDIT_FIELD_PROPERTIES.MAX_LENGTH_2000": "Character Limit: 2000",
    "EDIT_FIELD_PROPERTIES.MAX_LENGTH_32000": "Character Limit: 32000",
    "EDIT_FIELD_PROPERTIES.MAX_LENGTH_50000": "Character Limit: 50000",
    "EDIT_FIELD_PROPERTIES.REMAINING_FIELDS": "Remaining Fields: {count}/{total}",
    "EDIT_FIELD_PROPERTIES.REMAINING_RICH_FIELDS": "Remaining Rich Text Fields: {count}/{total}",
    "EDIT_FIELD_PROPERTIES.PICKLIST_OPTIONS": "List Options",
    "EDIT_FIELD_PROPERTIES.ADD_BULK": "Add Bulk Options",
    "EDIT_FIELD_PROPERTIES.BULK_PLACEHOLDER": "Enter each option on a new line",
    "EDIT_FIELD_PROPERTIES.DEFAULT_VALUE": "Default Value",
    "EDIT_FIELD_PROPERTIES.SORT_ORDER": "Sort Order",
    "EDIT_FIELD_PROPERTIES.SEARCH_MODULE": "Search Module",
    "EDIT_FIELD_PROPERTIES.USER_TYPE": "Type",
    "EDIT_FIELD_PROPERTIES.ALLOW_MULTIPLE_FILES": "Allow Multiple Files",
    "EDIT_FIELD_PROPERTIES.MAX_FILES": "Maximum Files",
    "EDIT_FIELD_PROPERTIES.MAX_IMAGES": "Maximum Images",
    "EDIT_FIELD_PROPERTIES.DECIMAL_PLACES": "Number of Decimal Places",
    "EDIT_FIELD_PROPERTIES.ROUNDING": "Rounding Option",
    "EDIT_FIELD_PROPERTIES.USE_NUMBER_SEPARATOR": "Display with Number Separator",
    "EDIT_FIELD_PROPERTIES.ENABLE_BY_DEFAULT": "Enable by Default"
  }
  ```  
- Chạy `compare-translations.js` để kiểm tra key trùng lặp.  
- Gộp vào `public/assets/i18n/` với `npm run merge-translations`.  

**Tích hợp hệ thống**:  
- **IAM**: Kiểm tra `field.profilePermissions` để đảm bảo quyền chỉnh sửa.  
- **Audit Logging**: Ghi log thay đổi qua `AuditLogUseCase`:  
  ```typescript
  { userId, action: 'update_field_properties', details: { fieldId: field._id, changes } }
  ```  
- **TMĐT**: Đảm bảo `constraints` (như `picklistValues`, `maxImages`) tương thích với Shopee, Lazada.  
- **Chuẩn hóa**:  
  - **SAP**: Tương tự Field Status Groups.  
  - **Odoo**: Tương tự Custom Fields.  
  - **Zoho**: Tương tự Field Properties.  

**Test**:  
- Test giao diện tại `http://localhost:4200/#/test` trong `@test-theme.component.ts`.  
- Kiểm tra responsive trên Chrome DevTools (desktop, mobile).  
- Chạy `ng build` để kiểm tra lỗi.

---

#### 7. Quy trình thực hiện

1. **Thiết kế giao diện**:  
   - Tạo component trong `src/infra/shared/modals/field-properties/`.  
   - Sử dụng **Angular Material** (`mat-form-field`, `mat-checkbox`, `mat-select`, `cdkDragDrop`).  
   - Bố cục: Trường chung ở trên, tùy chọn đặc thù trong `mat-expansion-panel`.  

2. **Xử lý logic**:  
   - Sử dụng **Reactive Forms** để quản lý input và validation.  
   - Dùng **Signals** cho trạng thái (ví dụ: bật/ẩn tooltip, chế độ bulk).  
   - Xử lý kéo-thả danh sách tùy chọn với `cdkDragDrop`.  
   - Validate input ở frontend và backend.  

3. **Tích hợp**:  
   - Nhận input `FieldPropertiesData` qua `@Inject(MAT_DIALOG_DATA)`.  
   - Trả về `CustomField` khi lưu qua `MatDialogRef.close()`.  
   - Gọi `AuditLogUseCase` khi lưu.  

4. **i18n**:  
   - Thêm key vào `en.json`, `vi.json`.  
   - Chạy `compare-translations.js` và `npm run merge-translations`.  

5. **Test**:  
   - Kiểm tra giao diện, responsive, và validation.  
   - Chạy `ng build` để đảm bảo không có lỗi.

---

#### 8. Kết luận

Modal "Sửa thuộc tính" cung cấp giao diện trực quan, linh hoạt để chỉnh sửa `CustomField`, nhận input `FieldPropertiesData`, và trả về `CustomField` đã cập nhật khi lưu. Modal hỗ trợ đầy đủ các trường chung (nhãn, công khai, bắt buộc, unique, tooltip) và tùy chọn đặc thù (maxLength, picklistValues, maxImages, v.v.), đảm bảo tích hợp với ERP, TMĐT, và chuẩn hóa với **SAP**, **Odoo**, **Zoho**. Giao diện responsive, tối ưu hiệu suất, và hỗ trợ **i18n**, **accessibility**, phù hợp với các ngành bán lẻ online.

**Lưu ý**: Nếu có bất kỳ điểm nào cần làm rõ (ví dụ: giới hạn cụ thể cho số trường rich text, thêm field mới như `LongInteger`, hoặc tích hợp API cụ thể), hãy cho tôi biết để tôi điều chỉnh prompt hoặc cung cấp mã triển khai chi tiết!

--- 

### Artifact


# Prompt for Modal Sửa thuộc tính

## 1. Tổng quan

**Mục tiêu**:  
Modal "Sửa thuộc tính" cho phép chỉnh sửa thuộc tính của một **Custom Field** trong `DynamicLayoutBuilderComponent`, bao gồm nhãn, quyền công khai, tính bắt buộc, tooltip, và các tùy chọn đặc thù theo loại field. Modal hỗ trợ các ngành bán lẻ online (thời trang, mỹ phẩm, mẹ và bé, v.v.) và tích hợp TMĐT (Shopee, Lazada, TikTok Shop).

**Vị trí sử dụng**:  
- Mở từ `FieldComponent` qua `MatDialog`.  
- Input: `FieldPropertiesData`.  
- Output: `CustomField` đã cập nhật (khi lưu) hoặc `null` (khi hủy).  

**Đường dẫn triển khai**:  
- `src/infra/shared/modals/field-properties/`:
  - `field-properties.component.ts`
  - `field-properties.component.html`
  - `field-properties.component.scss`
- View Model: `src/infra/shared/models/view/field-properties.model.ts`.  
- i18n: `src/infra/i18n/shared/modals/field-properties/`.  

**Yêu cầu cơ bản**:  
- Tuân thủ [rules/angular.mdc](../../rules/angular.mdc) và [rules/requirements.mdc](../../rules/requirements.mdc).  
- Dùng **MCP server** để debug (`http://localhost:4200/#/test`).  
- Hỏi lại nếu cần làm rõ.  
- Sử dụng **Angular 19**, **standalone components**, **Signals**, **OnPush**, **lazy loading**.  
- Tích hợp **i18n** (`ngx-translate`, key `SCREAMING_SNAKE_CASE`).  
- Sử dụng **Angular Material**, **CDK DragDrop**, **PrimeNG**, **Bootstrap**, **Tailwind CSS**.  
- Giao diện responsive, theme **Metronic** (https://keenthemes.com/metronic/tailwind/demo1/).  
- Tối ưu với `trackBy`, **OnPush**, lazy-load.  
- Hỗ trợ **accessibility** (WCAG 2.1).

## 2. Giao diện và Cấu trúc

**Header**:  
- Tiêu đề: "Sửa thuộc tính: [Field Type]" (`EDIT_FIELD_PROPERTIES.TITLE`, tham số `{ fieldType }`).  
- Sử dụng `mat-dialog-title`.  

**Body**:  
- `mat-dialog-content` chứa:  
  - **Trường chung**: Nhãn, công khai, bắt buộc, unique, tooltip.  
  - **Tùy chọn đặc thù**: Dựa trên `field.type`, nhóm trong `mat-expansion-panel` (nếu nhiều).  
- Bố cục: **CSS Grid/Flexbox**, responsive (Tailwind CSS).  

**Footer**:  
- `mat-dialog-actions`:  
  - **Save**: `mat-raised-button`, primary, Enter.  
  - **Cancel**: `mat-button`, neutral, Escape.  
- Căn phải, khoảng cách đều.

## 3. Input và Output

**Input**:  
- `FieldPropertiesData` qua `MAT_DIALOG_DATA`:  
  ```typescript
  import { CustomField } from 'src/domain/entities/custom-field';

  export interface FieldPropertiesData {
    field: CustomField;
    availableModules?: Array<{ _id: string; name: string }>;
  }
  ```

**Output**:  
- **Save**: `CustomField` đã cập nhật (`dialogRef.close(updatedField)`).  
- **Cancel**: Không trả về (`dialogRef.close()`).

## 4. Trường chung

1. **Label**:  
   - UI: `mat-form-field`, `matInput` (text).  
   - Mặc định: `field.label`.  
   - Validation: Bắt buộc, tối đa 255 ký tự.  
   - i18n: `EDIT_FIELD_PROPERTIES.LABEL`.  
   - Tích hợp: `CustomField.label`.  

2. **Public Field**:  
   - UI: `mat-checkbox`.  
   - Mặc định: `field.isPublic` (`false`).  
   - i18n: `EDIT_FIELD_PROPERTIES.IS_PUBLIC`.  
   - Tích hợp: `CustomField.isPublic`.  

3. **Required Field**:  
   - UI: `mat-checkbox`.  
   - Mặc định: `field.isRequired` (`false`).  
   - i18n: `EDIT_FIELD_PROPERTIES.IS_REQUIRED`.  
   - Tích hợp: `CustomField.isRequired`.  

4. **Unique Values**:  
   - UI: `mat-checkbox`.  
   - Mặc định: `constraints.unique` (`false`).  
   - Hiển thị: Chỉ cho `text`, `email`, `phone`, `url`.  
   - i18n: `EDIT_FIELD_PROPERTIES.UNIQUE`.  
   - Tích hợp: `CustomField.constraints.unique`.  

5. **Tooltip**:  
   - UI: `mat-checkbox` + `mat-form-field` (text, hiển thị khi chọn).  
   - Mặc định: Checkbox (`!!field.tooltip`), text (`field.tooltip`).  
   - Validation: Tối đa 500 ký tự.  
   - i18n: `EDIT_FIELD_PROPERTIES.SHOW_TOOLTIP`, `EDIT_FIELD_PROPERTIES.TOOLTIP`.  
   - Tích hợp: `CustomField.tooltip`.  

## 5. Tùy chọn đặc thù

### 5.1. Email / Date / DateTime / Percent
- Không có tùy chọn đặc thù.  
- Validation: Email (định dạng), Date (`YYYY/MM/DD`), DateTime (`YYYY/MM/DD HH:MM`), Percent (0-100).  

### 5.2. Text / Phone / URL
- **Maximum Length**:  
  - UI: `mat-form-field`, `matInput` (number).  
  - Mặc định: `constraints.maxLength` (255).  
  - Validation: 1-255.  
  - i18n: `EDIT_FIELD_PROPERTIES.MAX_LENGTH`.  
  - Tích hợp: `CustomField.constraints.maxLength`.  

### 5.3. Number / Phone
- **Maximum Digits**:  
  - UI: `mat-form-field`, `matInput` (number).  
  - Mặc định: `constraints.maxDigits` (9).  
  - Validation: 1-9.  
  - i18n: `EDIT_FIELD_PROPERTIES.MAX_DIGITS`.  
  - Tích hợp: `CustomField.constraints.maxDigits`.  

### 5.4. Textarea
- **Type**:  
  - UI: `mat-select` (`small`, `large`, `rich`).  
  - Mặc định: `constraints.textType` (`small`).  
  - Hiển thị phụ:  
    - Small: "Giới Hạn Ký Tự: 2000" (`EDIT_FIELD_PROPERTIES.MAX_LENGTH_2000`).  
    - Large: "Giới Hạn Ký Tự: 32000" (`EDIT_FIELD_PROPERTIES.MAX_LENGTH_32000`).  
    - Rich: "Giới Hạn Ký Tự: 50000" (`EDIT_FIELD_PROPERTIES.MAX_LENGTH_50000`).  
    - Số trường còn lại: `EDIT_FIELD_PROPERTIES.REMAINING_FIELDS`, `EDIT_FIELD_PROPERTIES.REMAINING_RICH_FIELDS`.  
  - Validation: Bắt buộc.  
  - i18n: `EDIT_FIELD_PROPERTIES.TEXT_TYPE`.  
  - Tích hợp: `CustomField.constraints.textType`, `CustomField.constraints.maxLength`.  

### 5.5. Picklist / MultiPicklist
1. **List Options**:  
   - Header:  
     - Tiêu đề: `EDIT_FIELD_PROPERTIES.PICKLIST_OPTIONS`.  
     - Nút "+": Thêm row.  
     - Gear: `mat-menu` với "Add Bulk" (`EDIT_FIELD_PROPERTIES.ADD_BULK`).  
   - Body (Thông thường):  
     - Row: `cdkDragHandle`, `mat-form-field`, nút "+", "-".  
     - Validation: Không rỗng, tối đa 255 ký tự, không trùng, tối đa 100 tùy chọn.  
     - Tích hợp: `CustomField.constraints.picklistValues`.  
   - Body (Bulk):  
     - UI: `mat-form-field`, `matInput` (textarea).  
     - Placeholder: `EDIT_FIELD_PROPERTIES.BULK_PLACEHOLDER`.  
     - Nút: Save, Cancel.  
     - Validation: Parse `\n`, không rỗng, tối đa 255 ký tự, không trùng, tối đa 100.  
     - UX: Ctrl+Enter (Save), Escape (Cancel).  
     - Tích hợp: `CustomField.constraints.picklistValues`.  

2. **Default Value**:  
   - UI: `mat-select` (single/multiple).  
   - Mặc định: `constraints.defaultValue`.  
   - i18n: `EDIT_FIELD_PROPERTIES.DEFAULT_VALUE`.  
   - Tích hợp: `CustomField.constraints.defaultValue`.  

3. **Sort Order**:  
   - UI: `mat-select` (`input`, `alphabetical`).  
   - Mặc định: `constraints.sortOrder` (`input`).  
   - i18n: `EDIT_FIELD_PROPERTIES.SORT_ORDER`.  
   - Tích hợp: `CustomField.constraints.sortOrder`.  

### 5.6. Search
- **Search Module**:  
  - UI: `mat-select` (từ `availableModules` hoặc mặc định).  
  - Mặc định: `constraints.searchModule` (`sales_quotes`).  
  - Validation: Bắt buộc.  
  - i18n: `EDIT_FIELD_PROPERTIES.SEARCH_MODULE`.  
  - Tích hợp: `CustomField.constraints.searchModule`.  

### 5.7. User
- **Type**:  
  - UI: `mat-select` (`single`, `multiple`).  
  - Mặc định: `constraints.userType` (`single`).  
  - Validation: Bắt buộc.  
  - i18n: `EDIT_FIELD_PROPERTIES.USER_TYPE`.  
  - Tích hợp: `CustomField.constraints.userType`.  

### 5.8. Upload File
- **Allow Multiple Files**:  
  - UI: `mat-checkbox` + `mat-form-field` (number, nếu chọn).  
  - Mặc định: `constraints.allowMultipleFiles` (`false`), `constraints.maxFiles` (1).  
  - Validation: `maxFiles` 1-5 (nếu chọn).  
  - i18n: `EDIT_FIELD_PROPERTIES.ALLOW_MULTIPLE_FILES`, `EDIT_FIELD_PROPERTIES.MAX_FILES`.  
  - Tích hợp: `CustomField.constraints.allowMultipleFiles`, `CustomField.constraints.maxFiles`.  

### 5.9. Upload Image
- **Maximum Images**:  
  - UI: `mat-select` (1-10).  
  - Mặc định: `constraints.maxImages` (1).  
  - Validation: Bắt buộc.  
  - i18n: `EDIT_FIELD_PROPERTIES.MAX_IMAGES`.  
  - Tích hợp: `CustomField.constraints.maxImages`.  

### 5.10. Currency
1. **Maximum Digits**:  
   - UI: `mat-form-field`, `matInput` (number).  
   - Mặc định: `constraints.maxDigits` (9).  
   - Validation: 1-16.  
   - i18n: `EDIT_FIELD_PROPERTIES.MAX_DIGITS`.  
   - Tích hợp: `CustomField.constraints.maxDigits`.  

2. **Decimal Places**:  
   - UI: `mat-form-field`, `matInput` (number).  
   - Mặc định: `constraints.decimalPlaces` (2).  
   - Validation: 0-4.  
   - i18n: `EDIT_FIELD_PROPERTIES.DECIMAL_PLACES`.  
   - Tích hợp: `CustomField.constraints.decimalPlaces`.  

3. **Rounding**:  
   - UI: `mat-select` (`normal`, `off`, `up`, `down`).  
   - Mặc định: `constraints.rounding` (`normal`).  
   - i18n: `EDIT_FIELD_PROPERTIES.ROUNDING`.  
   - Tích hợp: `CustomField.constraints.rounding`.  

### 5.11. Decimal
1. **Maximum Digits**:  
   - UI: `mat-form-field`, `matInput` (number).  
   - Mặc định: `constraints.maxDigits` (16).  
   - Validation: 1-16.  
   - i18n: `EDIT_FIELD_PROPERTIES.MAX_DIGITS`.  
   - Tích hợp: `CustomField.constraints.maxDigits`.  

2. **Decimal Places**:  
   - UI: `mat-form-field`, `matInput` (number).  
   - Mặc định: `constraints.decimalPlaces` (2).  
   - Validation: 0-8.  
   - i18n: `EDIT_FIELD_PROPERTIES.DECIMAL_PLACES`.  
   - Tích hợp: `CustomField.constraints.decimalPlaces`.  

3. **Number Separator**:  
   - UI: `mat-checkbox`.  
   - Mặc định: `constraints.useNumberSeparator` (`false`).  
   - i18n: `EDIT_FIELD_PROPERTIES.USE_NUMBER_SEPARATOR`.  
   - Tích hợp: `CustomField.constraints.useNumberSeparator`.  

### 5.12. Checkbox
- **Enable by Default**:  
  - UI: `mat-checkbox`.  
  - Mặc định: `constraints.enableByDefault` (`false`).  
  - i18n: `EDIT_FIELD_PROPERTIES.ENABLE_BY_DEFAULT`.  
  - Tích hợp: `CustomField.constraints.enableByDefault`, `CustomField.value`.  

## 6. Yêu cầu bổ sung

**Hiệu suất**:  
- **OnPush Change Detection**.  
- `trackBy` cho danh sách tùy chọn:  
  ```typescript
  trackByOption(index: number, option: string): string {
    return option;
  }
  ```  
- Lazy-load tùy chọn đặc thù (`*ngIf`, `ngSwitch`).  

**Responsive UX**:  
- **CSS Grid/Flexbox** (Tailwind CSS).  
- Mobile: Thu gọn `mat-select`, hỗ trợ scroll.  
- **Accessibility**: `aria-label`, điều hướng bàn phím.  

**i18n**:  
- Key trong `src/infra/i18n/shared/modals/field-properties/` (`en.json`, `vi.json`).  
- Chạy `compare-translations.js`, `npm run merge-translations`.  

**Tích hợp**:  
- **IAM**: Kiểm tra `field.profilePermissions`.  
- **Audit Logging**: Ghi log `{ userId, action: 'update_field_properties', details: { fieldId, changes } }`.  
- **TMĐT**: Tương thích Shopee, Lazada (`picklistValues`, `maxImages`).  
- **Chuẩn hóa**: SAP (Field Status Groups), Odoo (Custom Fields), Zoho (Field Properties).  

**Test**:  
- Test tại `http://localhost:4200/#/test`.  
- Kiểm tra responsive (Chrome DevTools).  
- Chạy `ng build`.

## 7. Quy trình thực hiện

1. **Thiết kế giao diện**:  
   - Tạo component trong `src/infra/shared/modals/field-properties/`.  
   - Dùng **Angular Material**, **CDK DragDrop**.  
   - Bố cục: Trường chung, tùy chọn đặc thù (`mat-expansion-panel`).  

2. **Xử lý logic**:  
   - **Reactive Forms** cho input, validation.  
   - **Signals** cho trạng thái.  
   - Kéo-thả với `cdkDragDrop`.  
   - Validate frontend/backend.  

3. **Tích hợp**:  
   - Input: `FieldPropertiesData` qua `@Inject(MAT_DIALOG_DATA)`.  
   - Output: `CustomField` qua `MatDialogRef.close()`.  
   - Gọi `AuditLogUseCase`.  

4. **i18n**:  
   - Thêm key `en.json`, `vi.json`.  
   - Chạy `compare-translations.js`, `npm run merge-translations`.  

5. **Test**:  
   - Kiểm tra giao diện, responsive, validation.  
   - Chạy `ng build`.

## 8. Kết luận

Modal "Sửa thuộc tính" cung cấp giao diện trực quan, linh hoạt để chỉnh sửa `CustomField`, nhận `FieldPropertiesData`, trả về `CustomField` khi lưu. Hỗ trợ trường chung, tùy chọn đặc thù, tích hợp ERP, TMĐT, và chuẩn hóa với SAP, Odoo, Zoho. Giao diện responsive, tối ưu, hỗ trợ **i18n**, **accessibility**.
