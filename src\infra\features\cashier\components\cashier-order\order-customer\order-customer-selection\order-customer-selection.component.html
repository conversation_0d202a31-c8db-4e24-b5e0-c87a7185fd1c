<mat-form-field
  class="w-100 px-3 my-3 customer-autocomplete"
  appearance="outline"
  >
  <mat-label>T<PERSON><PERSON>h<PERSON> hàng</mat-label>
  @if (isAutocompleteNumericKeyboard()) {
  <input
    type="text"
    pattern="[0-9]*"
    inputmode="numeric"
    matInput
    [matAutocomplete]="auto"
    [(ngModel)]="order().customer"
    (ngModelChange)="onModelChange($event)"
    #searchCustomerInput
    >
  } @else {
    <input
    type="text"
    matInput
    [matAutocomplete]="auto"
    [(ngModel)]="order().customer"
    (ngModelChange)="onModelChange($event)"
    #searchCustomerInput
    >
  }

  <button
    matSuffix
    aria-label="Đổi bàn phím"
    class="border-0 me-3"
    (click)="toggleAutocompleteNumericKeyboard()"
    >
    <span class="material-symbols-outlined">
      keyboard
    </span>
  </button>

  <button
    matSuffix
    aria-label="Clear"
    class="border-0 me-2"
    (click)="clearSelectedValue()"
    >
    <span class="material-symbols-outlined">
      close
      </span>
  </button>



  <mat-autocomplete
    #auto="matAutocomplete"
    (optionSelected)="selectCustomer($event)"
    [displayWith]="displayAutoCompleteInput"
    >
    @for (option of searchResultsSubscribedArray(); track option) {
      <mat-option
        [value]="option"
        class="py-2"
        >

        @if (!option.text) {
        <div>
          <b>
            @if (option.name) {
            {{ option.name }} -
            }
            @if (option.phoneNumber) {
              <span>{{ option.phoneNumber | formatString: 'phone' }}</span>
            }
          </b>
        </div>

          @if (option.address && option.address.fullAddress) {
            <small>{{ option.address.fullAddress }}</small>
          }

        } @else {
        <div>
          {{ option.text }}
        </div>
        }

      </mat-option>
    }
  </mat-autocomplete>
</mat-form-field>
