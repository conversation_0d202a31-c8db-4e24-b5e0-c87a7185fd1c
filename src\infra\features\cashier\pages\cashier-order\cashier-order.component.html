<div class="invoice">
  <app-shift-check></app-shift-check>

  @if(!isHiddenCashier()) {
    <mat-stepper
      headerPosition="top"
      #stepper
      [linear]="true"
      [selectedIndex]="0"
      animationDuration="0ms"
      >

      <mat-step>
        <ng-template matStepLabel>Chọn món</ng-template>

        @if(stepper.selectedIndex === 0) {
          <app-cashier-order-dining></app-cashier-order-dining>

          @if (order().diningOption) {
            <app-order-product-list
              (invoiceItems)="updateOrderItems($event)"
              [iSelectedItems]="order().items"
              [iProducts]="products()"
              [iToppingProducts]="toppingProducts()"
            ></app-order-product-list>
          }
        }
      </mat-step>

      <mat-step>
        <ng-template matStepLabel>Thông tin khách hàng</ng-template>

        @if(stepper.selectedIndex === 1) {
          <app-cashier-order-customer
            #customer
            (customerEvent)="summarize()"
          >
          </app-cashier-order-customer>
        }
      </mat-step>
    </mat-stepper>
  } @else {
    <div class="p-3 py-4">
      Không thể tạo hóa đơn do bạn đang không trong ca làm việc. <br /><br />
      Để tạo đơn đặt trước, <a href="#" (click)="showCashier($event)">bấm vào đây</a>.
    </div>
  }

</div>
