import { Injectable, signal, WritableSignal } from '@angular/core';
import { Observable, of, Subject, takeUntil } from 'rxjs';
import { NAVIGATIONS, Navigation, NavigationItem } from '@config/navigation.config';
import { InitDataStore } from '@core/store/init_data.store';
import { AppNavigationDrawerComponent } from './navigation-drawer/app-navigation-drawer.component';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { ViewportService } from '@core/services/viewport.service';
import { AppNavigationDrawerService } from './navigation-drawer/app-navigation-drawer.service';
import { RouterEventService } from '@core/services/router_events.service';
import { NavigationEnd, Router, RoutesRecognized } from '@angular/router';
import { SubNavigationSheetComponent } from './sub-navigation/sub-navigation-sheet/sub-navigation-sheet.component';

/**
 * Service xử lý các tác vụ liên quan đến khách hàng
 */
@Injectable({
  providedIn: 'root'
})
export class NavigationService {
  // Signal lưu trữ URL hiện tại
  currentUrl: WritableSignal<string> = signal('');
  activeNavigation: WritableSignal<Navigation | null> = signal(null);
  /**
   * các item trên cùng trong cây url để build navigation
   */
  topLevelItems: WritableSignal<NavigationItem[]> = signal([]);
  /**
   * toàn bộ cây url
   */
  treeUrl!: Navigation[];

  private onChange$ = new Subject<{
    currentUrl: string,
    activeNavigation: Navigation,
    topLevelItems: NavigationItem[]
  }>();



  constructor(
    private initStore: InitDataStore,
    private routerEvent: RouterEventService,
    private bottomSheet: MatBottomSheet,
    router: Router
  ) {
    this.currentUrl.set(router.url);
    this.treeUrl = this.getNavigation();
  }

  /**
   * Thiết lập theo dõi sự kiện router để cập nhật navigation khi route thay đổi
   */
  initialize(destroy$: Subject<void>) {
    this.routerEvent.observeNavigationEvent(destroy$).subscribe(event => {
      // if (event instanceof RoutesRecognized) {
      //   // eslint-disable-next-line
      //   const newBlockName = event.state.root.firstChild?.data?.['resolverNavigationBlock'] as Navigation['module'];

      //   if(newBlockName !== this.currentNavigationBlock) {
      //     this.currentNavigationBlock = newBlockName;
      //   }
      // }

      if(event instanceof NavigationEnd) {
        this.currentUrl.set(event.urlAfterRedirects);
        this.updateNavigationBasedOnCurrentUrl();
      }
    });
  }

  /**
   * Kiểm tra xem một item có đang active hay không
   * @param item Item cần kiểm tra
   * @returns true nếu item đang active
   */
  isNavigationItemActive(item: NavigationItem): boolean {
    return this.isUrlInNavigationTree(this.currentUrl(), item);
  }

  /**
   * Cập nhật navigation dựa trên URL hiện tại
   */
  private updateNavigationBasedOnCurrentUrl(): void {
    const activeNavigation = this.findActiveParentNavigation(this.currentUrl());
    this.activeNavigation.set(activeNavigation);
    this.topLevelItems.set(activeNavigation ? activeNavigation.items : []);

    this.onChange$.next({
      currentUrl: this.currentUrl(),
      activeNavigation: activeNavigation as Navigation,
      topLevelItems: this.topLevelItems()
    })
  }

  findActiveParentNavigation(url: string): Navigation | null {
    for (const nav of NAVIGATIONS) {
      if (this.isUrlInNavigationTree(url, nav)) {
        return nav;
      }
    }
    return null;
  }

  isEqualNavigationUrls(routerLink: string, url: string) {
    /**
     * dùng trong nhiều trường hợp nên phải chuẩn hóa trước
     */
    if(routerLink && !routerLink.startsWith('/')) {
      routerLink = `/${routerLink}`;
    }
    if(url && !url.startsWith('/')) {
      url = `/${url}`;
    }

    if (
      routerLink &&
      url.startsWith(routerLink)
    ) {
      if (routerLink === '' && url === '/') return true;
      if (
        url !== '/' && (
          url.length === routerLink.length ||
          url[routerLink.length] === '/' ||
          url[routerLink.length] === '?'
        )
      ) {
        return true;
      }
    }

    return false;
  }

  isUrlInNavigationTree(url: string, navItem: Navigation | NavigationItem): boolean {
    let { routerLink } = navItem;

    if(this.isEqualNavigationUrls(routerLink, url)) {
      return true;
    }

    if (navItem.items && navItem.items.length > 0) {
      return navItem.items.some((subItem: NavigationItem) => this.isUrlInNavigationTree(url, subItem));
    }
    return false;
  }

  /**
   * Xây dựng mục navigation
   */
  getNavigation()  {
    const initData = this.initStore.getData();
  const sortFunc = (a: { isUnderDevelopment?: boolean }, b: { isUnderDevelopment?: boolean }) => {
      if (a.isUnderDevelopment === b.isUnderDevelopment) {
        return 0; // Giữ nguyên thứ tự gốc
      }
      if (a.isUnderDevelopment) {
        return 1; // Đẩy a xuống dưới nếu a unavailable
      }
      return -1; // Đẩy b xuống dưới nếu b unavailable
    };
    return NAVIGATIONS
      .filter(nav => {
        if(nav.activateWhen) {
          return nav.activateWhen(initData);
        }
        return true;
      })
      .sort(sortFunc)
      .map(nav => {
        if(nav.items) {
          nav.items = nav.items
            .filter(item => {
              if(nav.isUnderDevelopment) {
                item.isUnderDevelopment = true;
              }

              if(item.activateWhen) {
                return item.activateWhen(initData);
              }

              if(item.items?.length) {
                item.items = item.items
                .filter(subItem => {
                  if(nav.isUnderDevelopment || item.isUnderDevelopment) {
                    subItem.isUnderDevelopment = true;
                  }

                  if(subItem.activateWhen) {
                    return subItem.activateWhen(initData);
                  }

                  return true;
                })
                .sort(sortFunc)
                ;
              }

              return true;
            })
            .sort(sortFunc)
            ;
        }

        return nav;
      });
  }

  /**
   * Xây dựng danh sách các mục navigation dựa trên module name
   */
  // buildSubNavigationItems(blockName: Navigation['module']): Navigation['items'] {
  //   if(!blockName) {
  //     return [];
  //   }
  //   const module = NAVIGATIONS.find(nav => nav.module === blockName);
  //   if(!module) {
  //     return [];
  //   }

  //   const initData = this.initStore.getData();

  //   return module.items
  //     .filter(item => {
  //       if(item.activateWhen) {
  //         return item.activateWhen(initData);
  //       }

  //       if(item.items?.length) {
  //         item.items = item.items.filter(subItem => {
  //           if(subItem.activateWhen) {
  //             return subItem.activateWhen(initData);
  //           }

  //           return true;
  //         });
  //       }

  //       return true;
  //     });
  // }

  navigationOnChange(destroy$: Observable<void>) {
    return this.onChange$.asObservable().pipe(
      takeUntil(destroy$)
    );
  }

  /**
   * Mở bottom sheet hiển thị các sub-navigation
   * @param items Danh sách các item con
   * @param event Sự kiện click
   */
  openSubNavigationSheet(items: NavigationItem[] | undefined, parent?: NavigationItem): void {
    if (!items || items.length === 0) return;

    this.bottomSheet.open(SubNavigationSheetComponent, {
      data: { items, parent },
      panelClass: ['bottom-sheet-no-padding', 'bottom-sheet-overflow-visible']
    });
  }

  /**
   * Hàm trackBy cho *ngFor để tối ưu hóa hiệu suất.
   * @param index Chỉ số của mục.
   * @param item Mục navigation.
   * @returns Khóa định danh duy nhất cho mục (dựa trên module hoặc text).
   */
  trackByFn(index: number, item: Navigation | NavigationItem): string {
    // Sử dụng routerLink hoặc text làm key vì module không có ở mọi cấp
    return (item as Navigation).module  || item.routerLink || (item as NavigationItem).text;
  }
}
