<!-- Modal Header -->
<div class="modal-header border-bottom pb-3 mb-4">
  <div class="d-flex align-items-center">
    <mat-icon class="me-2 text-primary">history</mat-icon>
    <div>
      <h4 class="mb-0 fw-bold">{{ 'INTEGRATION_ACCOUNT_LOGS_TITLE' | translate }}</h4>
      <p class="mb-0 text-muted small">{{ modalTitle() }}</p>
    </div>
  </div>
</div>

<!-- Modal Content - Logs Only -->
<div class="modal-content-logs">
  @if (logs().length > 0) {
    <!-- Logs List với Virtual Scrolling -->
    <div class="logs-container">
      <cdk-virtual-scroll-viewport 
        itemSize="60" 
        class="logs-viewport">
        
        @for (log of logs(); track log.time.getTime()) {
          <div class="log-entry border-bottom py-2">
            <div class="d-flex align-items-start">
              <!-- Log Type Icon -->
              <div class="log-icon me-3 mt-1">
                @switch (log.type) {
                  @case ('success') {
                    <mat-icon class="text-success small-icon">check_circle</mat-icon>
                  }
                  @case ('error') {
                    <mat-icon class="text-danger small-icon">error</mat-icon>
                  }
                  @case ('warning') {
                    <mat-icon class="text-warning small-icon">warning</mat-icon>
                  }
                  @case ('info') {
                    <mat-icon class="text-info small-icon">info</mat-icon>
                  }
                }
              </div>
              
              <!-- Log Content -->
              <div class="log-content flex-grow-1">
                <div class="log-time text-muted small mb-1">
                  {{ formatLogTime(log.time) }}
                </div>
                <div class="log-text" [class]="getLogTypeClass(log)">
                  {{ log.text }}
                </div>
              </div>
            </div>
          </div>
        }
        
      </cdk-virtual-scroll-viewport>
    </div>
  } @else {
    <!-- No Logs Available -->
    <div class="text-center py-5">
      <mat-icon class="display-4 text-muted mb-3">history_off</mat-icon>
      <h5 class="text-muted">{{ 'NO_LOGS_AVAILABLE' | translate }}</h5>
      <p class="text-muted">{{ 'NO_LOGS_AVAILABLE_DESC' | translate }}</p>
    </div>
  }
</div>

<!-- Modal Footer -->
<div class="modal-footer border-top pt-3 mt-4">
  <div class="d-flex justify-content-between align-items-center w-100">
    <!-- Logs Count -->
    <div class="logs-count">
      <small class="text-muted">
        <mat-icon class="small-icon me-1">history</mat-icon>
        {{ 'TOTAL_LOGS' | translate }}: {{ logs().length }}
      </small>
    </div>
    
    <!-- Activity Status -->
    <div class="activity-status">
      <small class="text-muted">
        {{ 'ACTIVITY_LOGS' | translate }}
      </small>
    </div>
  </div>
</div>
