<!-- Modal Header -->
<div class="modal-header border-bottom pb-3 mb-4">
  <div class="d-flex align-items-center">
    <mat-icon class="me-2 text-primary">settings</mat-icon>
    <div>
      <h4 class="mb-0 fw-bold">{{ 'INTEGRATION_ACCOUNT_SETTINGS_TITLE' | translate }}</h4>
      <p class="mb-0 text-muted small">{{ modalTitle() }}</p>
    </div>
  </div>
</div>

<!-- Modal Content - Settings Only -->
<div class="modal-content-settings">
  @if (config(); as settingsConfig) {
    <!-- Settings List Component -->
    <app-settings-list
      [config]="settingsConfig"
      (settingChange)="onSettingChange($event)"
      (save)="onSettingsSave($event)"
      (cancel)="onSettingsCancel()"
      class="settings-container">
    </app-settings-list>
  } @else {
    <!-- No Settings Available -->
    <div class="text-center py-5">
      <mat-icon class="display-4 text-muted mb-3">settings_off</mat-icon>
      <h5 class="text-muted">{{ 'NO_SETTINGS_AVAILABLE' | translate }}</h5>
      <p class="text-muted">{{ 'NO_SETTINGS_AVAILABLE_DESC' | translate }}</p>
    </div>
  }
</div>

<!-- Modal Footer -->
<div class="modal-footer border-top pt-3 mt-4">
  <div class="d-flex justify-content-between align-items-center w-100">
    <!-- Status Info -->
    <div class="status-info">
      @if (settingsChanged()) {
        <small class="text-warning">
          <mat-icon class="small-icon me-1">edit</mat-icon>
          {{ 'SETTINGS_MODIFIED' | translate }}
        </small>
      } @else {
        <small class="text-muted">
          <mat-icon class="small-icon me-1">check</mat-icon>
          {{ 'NO_CHANGES' | translate }}
        </small>
      }
    </div>
    
    <!-- Settings Info -->
    <div class="settings-info">
      <small class="text-muted">
        {{ 'SETTINGS_CONFIGURATION' | translate }}
      </small>
    </div>
  </div>
</div>
