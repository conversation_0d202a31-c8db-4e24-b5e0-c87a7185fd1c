$minLeftPanelWidth: 400;
.dynamic-layout-builder-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    // Compact Toolbar styles - Single line layout
    .layout-toolbar {
        background-color: #fff;
        border-bottom: 1px solid #e0e0e0;
        z-index: 10;
        min-height: 56px;
        padding: 0 16px;
        &.compact-toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 16px;
        }
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;
            min-width: 0;
        }
        .inline-layout-selector {
            flex-shrink: 0;
            min-width: 180px;
             ::ng-deep app-layout-selector {
                .layout-selector-container {
                    margin-bottom: 0;
                    .layout-selector-wrapper {
                        .form-label {
                            font-size: 0.7rem;
                            margin-bottom: 2px;
                            font-weight: 500;
                            color: #666;
                            line-height: 1;
                        }
                        .layout-menu-container {
                            .layout-trigger-button {
                                height: 28px;
                                font-size: 0.8rem;
                                padding: 0 6px;
                                min-height: 28px;
                                border-radius: 4px;
                                border: 1px solid #ddd;
                                background: #fff;
                                .layout-name {
                                    font-size: 0.8rem;
                                    line-height: 1.2;
                                    white-space: nowrap;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    max-width: 140px;
                                }
                                mat-icon {
                                    font-size: 16px;
                                    width: 16px;
                                    height: 16px;
                                    margin-left: 4px;
                                }
                            }
                        }
                    }
                }
            }
        }
        .inline-tabs {
            flex: 1;
            min-width: 0;
            .compact-tabs-nav {
                display: flex;
                align-items: center;
                gap: 4px;
                height: 36px;
                .compact-tab-btn {
                    background: none;
                    border: none;
                    padding: 6px 8px;
                    border-radius: 6px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    position: relative;
                    height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 4px;
                    .compact-tab-label {
                        font-size: 0.8rem;
                        font-weight: 500;
                        text-transform: none;
                        color: #666;
                        white-space: nowrap;
                    }
                    .tab-info-icon {
                        font-size: 14px;
                        width: 14px;
                        height: 14px;
                        color: #999;
                        opacity: 0.7;
                        transition: all 0.2s ease;
                        cursor: help;
                        &:hover {
                            opacity: 1;
                            color: #666;
                        }
                    }
                    &:hover {
                        background-color: #f5f5f5;
                        .compact-tab-label {
                            color: #333;
                        }
                        .tab-info-icon {
                            opacity: 1;
                            color: #666;
                        }
                    }
                    &.active {
                        background-color: #e3f2fd;
                        .compact-tab-label {
                            color: #1976d2;
                            font-weight: 600;
                        }
                        .tab-info-icon {
                            color: #1976d2;
                            opacity: 0.8;
                        }
                        &::after {
                            content: '';
                            position: absolute;
                            bottom: 0;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 80%;
                            height: 2px;
                            background-color: #1976d2;
                            border-radius: 1px;
                        }
                    }
                    &:focus {
                        outline: none;
                        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
                    }
                }
            }
        }
        .spacer {
            flex: 1 1 auto;
        }
        .toolbar-actions {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-shrink: 0;
            .more-options-btn {
                width: 36px;
                height: 36px;
                mat-icon {
                    font-size: 18px;
                    width: 18px;
                    height: 18px;
                }
            }
            .btn {
                &.btn-sm {
                    padding: 6px 12px;
                    font-size: 0.875rem;
                    i {
                        font-size: 0.75rem;
                    }
                }
                &.active {
                    background-color: #e3f2fd;
                    color: #1976d2;
                }
            }
        }
    }
    // Main content area
    .layout-content {
        flex: 1;
        position: relative;
        // overflow: hidden;
        // height: calc(100vh - 56px);
        &.preview-mode {
            // Preview mode styles
        }
        .tab-content-container {
            height: 100%;
            width: 100%;
            .tab-content {
                height: 100%;
                width: 100%;
                display: none;
                &.active {
                    display: block;
                }
                >* {
                    height: 100%;
                }
            }
        }
    }
}

// More Options Menu styles
::ng-deep .more-options-menu {
    .mat-mdc-menu-content {
        padding: 8px 0;
        .mat-mdc-menu-item {
            height: 40px;
            line-height: 40px;
            padding: 0 16px;
            font-size: 0.875rem;
            mat-icon {
                margin-right: 12px;
                font-size: 18px;
                width: 18px;
                height: 18px;
                color: #666;
            }
            &:hover {
                background-color: #f5f5f5;
            }
        }
    }
}

// CDK Drag Drop styles
.cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
    opacity: 0;
}

.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .cdk-drag {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

// Responsive design
@media (max-width: 768px) {
    .dynamic-layout-builder-container {
        .layout-toolbar {
            &.compact-toolbar {
                flex-direction: column;
                gap: 8px;
                padding: 8px 12px;
                min-height: auto;
                .toolbar-left {
                    width: 100%;
                    flex-direction: column;
                    gap: 8px;
                    .inline-layout-selector {
                        width: 100%;
                        min-width: auto;
                        max-width: none;
                         ::ng-deep app-layout-selector {
                            .layout-selector-container .layout-selector-wrapper {
                                .form-label {
                                    font-size: 0.65rem;
                                }
                                .layout-menu-container .layout-trigger-button {
                                    height: 26px;
                                    font-size: 0.75rem;
                                    padding: 0 4px;
                                    .layout-name {
                                        font-size: 0.75rem;
                                        max-width: 120px;
                                    }
                                    mat-icon {
                                        font-size: 14px;
                                        width: 14px;
                                        height: 14px;
                                    }
                                }
                            }
                        }
                    }
                    .inline-tabs {
                        width: 100%;
                        .compact-tabs-nav {
                            gap: 2px;
                            .compact-tab-btn {
                                padding: 3px 6px;
                                height: 26px;
                                gap: 2px;
                                .compact-tab-label {
                                    font-size: 0.7rem;
                                }
                                .tab-info-icon {
                                    font-size: 12px;
                                    width: 12px;
                                    height: 12px;
                                }
                            }
                        }
                    }
                }
                .toolbar-actions {
                    width: 100%;
                    justify-content: flex-end;
                    gap: 6px;
                    .btn.btn-sm {
                        padding: 4px 8px;
                        font-size: 0.8rem;
                    }
                }
            }
        }
        .layout-content {
            height: calc(100vh - 120px);
        }
    }
}

// Extra small screens
@media (max-width: 480px) {
    .dynamic-layout-builder-container {
        .layout-toolbar.compact-toolbar {
            .toolbar-left {
                .inline-layout-selector {
                     ::ng-deep app-layout-selector {
                        .layout-selector-container .layout-selector-wrapper {
                            .form-label {
                                font-size: 0.6rem;
                            }
                            .layout-menu-container .layout-trigger-button {
                                height: 24px;
                                font-size: 0.7rem;
                                padding: 0 3px;
                                .layout-name {
                                    font-size: 0.7rem;
                                    max-width: 100px;
                                }
                                mat-icon {
                                    font-size: 12px;
                                    width: 12px;
                                    height: 12px;
                                }
                            }
                        }
                    }
                }
                .inline-tabs {
                    .compact-tabs-nav {
                        gap: 1px;
                        .compact-tab-btn {
                            padding: 2px 4px;
                            height: 24px;
                            gap: 2px;
                            .compact-tab-label {
                                font-size: 0.65rem;
                            }
                            .tab-info-icon {
                                font-size: 10px;
                                width: 10px;
                                height: 10px;
                            }
                        }
                    }
                }
            }
            .toolbar-actions {
                .btn.btn-sm {
                    padding: 3px 6px;
                    font-size: 0.75rem;
                    i {
                        font-size: 0.7rem;
                    }
                }
                .more-options-btn {
                    width: 32px;
                    height: 32px;
                    mat-icon {
                        font-size: 16px;
                        width: 16px;
                        height: 16px;
                    }
                }
            }
        }
    }
}