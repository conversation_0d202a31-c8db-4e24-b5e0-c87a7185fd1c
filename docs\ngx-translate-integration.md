# Hướng dẫn sử dụng `ngx-translate` trong dự án ERP Angular

## <PERSON><PERSON><PERSON> lụ<PERSON>
1. [<PERSON><PERSON><PERSON><PERSON> thiệu](#giới-thiệu)
2. [<PERSON><PERSON>u trúc thư mục i18n](#cấu-trúc-thư-mục-i18n)
3. [Quy tắc đặt tên key dịch](#quy-tắc-đặt-tên-key-dịch)
4. [Cách tạo folder và file i18n](#cách-tạo-folder-và-file-i18n)
5. [<PERSON><PERSON>ch thêm key dịch vào file JSON](#cách-thêm-key-dịch-vào-file-json)
6. [Sử dụng key dịch trong ứng dụng](#sử-dụng-key-dịch-trong-ứng-dụng)
7. [Tích hợp script merge-translations](#tích-hợp-script-merge-translations)
8. [Best Practices](#best-practices)
9. [T<PERSON><PERSON> liệu tham khảo](#tài-liệu-tham-khảo)

## Giớ<PERSON> thiệu

Dự án ERP sử dụng **Angular 19** với **standalone components**, tích hợp **ngx-translate** để quản lý đa ngôn ngữ (i18n). Hệ thống i18n được tối ưu cho 16 module ERP (`IAM`, `POS`, `WMS`, `ECOMMERCE`, ...) và 9 ngành hàng bán lẻ online (Thời trang, Mỹ phẩm, Đồ ăn, ...), hỗ trợ tích hợp đa kênh (Shopee, Lazada).

**Đặc điểm chính:**
- File dịch gốc được đặt trong `src/infra/i18n/`, tổ chức theo **global** (bản dịch chung), **feature** (module), và **sub-feature** (chức năng cụ thể).
- Script `merge-translations.js` gộp tất cả file dịch thành `public/assets/i18n/<lang>.json` (ví dụ: `en.json`, `vi.json`) để tải với **1 request HTTP**, tối ưu hiệu suất.
- Key dịch sử dụng **SCREAMING_SNAKE_CASE** (chữ in hoa, dấu gạch dưới) với namespace để tránh trùng lặp.

**Mục tiêu tài liệu:**
- Hướng dẫn **AI Coding Agent** (Cursor AI) tạo folder/file i18n, thêm key dịch đúng chỗ trong `src/infra/i18n/`.
- Hướng dẫn sử dụng key dịch trong template/TypeScript.
- Đảm bảo tích hợp với `merge-translations.js` trong dev/production.
- Hỗ trợ các module ERP và ngành hàng với key cụ thể (ví dụ: `PRODUCTS.FASHION.SIZE`, `ECOMMERCE.SHOPEE.SYNC`).

**Lưu ý:**
- File dịch gốc: `src/infra/i18n/<feature>/<sub-feature>/<lang>.json`.
- File gộp: `public/assets/i18n/<lang>.json`.
- Ngôn ngữ hỗ trợ: `en` (Tiếng Anh), `vi` (Tiếng Việt).
- **Không chỉnh sửa thủ công** file trong `public/assets/i18n/` (do script tự động tạo).

## Cấu trúc thư mục i18n

Thư mục i18n được chia thành **file gốc** (`src/infra/i18n/`) và **file gộp** (`public/assets/i18n/`).

### File gốc (`src/infra/i18n/`)

File dịch gốc được tổ chức theo **global** (bản dịch chung), **feature** (module như `cashier`, `warehouse`), và **sub-feature** (chức năng như `create-order`, `sync-orders`).

```plaintext
src/
├── infra/
│   ├── i18n/
│   │   ├── global/                          # Bản dịch chung (SAVE, CANCEL)
│   │   │   ├── en.json
│   │   │   ├── vi.json
│   │   ├── cashier/                         # Module POS
│   │   │   ├── en.json                     # Key chung cho cashier
│   │   │   ├── vi.json
│   │   │   ├── create-order/               # Sub-feature: Tạo đơn
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   │   ├── order-history/              # Sub-feature: Lịch sử đơn
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   ├── warehouse/                       # Module WMS
│   │   │   ├── en.json
│   │   │   ├── vi.json
│   │   │   ├── inventory-check/            # Sub-feature: Kiểm kho
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   │   ├── stock-adjustment/           # Sub-feature: Điều chỉnh tồn kho
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   ├── ecommerce/                       # Module ECOMMERCE
│   │   │   ├── en.json
│   │   │   ├── vi.json
│   │   │   ├── sync-orders/                # Sub-feature: Đồng bộ đơn
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
│   │   │   ├── order-management/           # Sub-feature: Quản lý đơn
│   │   │   │   ├── en.json
│   │   │   │   ├── vi.json
```

**Chú thích:**
- **Global** (`global/<lang>.json`): Chứa key chung cho toàn ứng dụng (ví dụ: `COMMON.SAVE`).
- **Feature** (`cashier/<lang>.json`): Chứa key chung cho module, thường dùng cho giao diện tái sử dụng (`panel`, `dialog`).
- **Sub-feature** (`cashier/create-order/<lang>.json`): Chứa key cho chức năng cụ thể, thường là nội dung chính của trang theo router.

### File gộp (`public/assets/i18n/`)

Script `merge-translations.js` gộp tất cả file dịch từ `src/infra/i18n/` thành file JSON phẳng trong `public/assets/i18n/`.

```plaintext
public/
├── assets/
│   ├── i18n/
│   │   ├── en.json                      # Gộp tất cả en.json
│   │   ├── vi.json                      # Gộp tất cả vi.json
```

**Ví dụ nội dung `public/assets/i18n/en.json`:**
```json
{
  "COMMON.SAVE": "Save",
  "COMMON.CANCEL": "Cancel",
  "CASHIER.PANEL.TITLE": "Cashier Panel",
  "CASHIER.CREATE_ORDER.TITLE": "Create Order",
  "PRODUCTS.FASHION.SIZE": "Size",
  "ECOMMERCE.SHOPEE.SYNC": "Sync with Shopee"
}
```

**Chú thích cho AI Coding Agent:**
- **File gốc**: Chỉ thêm/sửa trong `src/infra/i18n/`.
- **File gộp**: Tự động tạo bởi `merge-translations.js`, không chỉnh sửa thủ công.
- File gộp là JSON phẳng, key dùng dấu chấm (`.`) để phân cấp (ví dụ: `CASHIER.CREATE_ORDER.TITLE`).

## Quy tắc đặt tên key dịch

Key dịch **phải** dùng **SCREAMING_SNAKE_CASE** (chữ in hoa, dấu gạch dưới) và tổ chức theo namespace để tránh trùng lặp.

**Quy tắc:**
1. **Namespace theo module/sub-feature:**
   - Bắt đầu bằng tên module (`CASHIER`, `WAREHOUSE`, `ECOMMERCE`) hoặc `COMMON` (cho global).
   - Sub-feature thêm tên chức năng (ví dụ: `CASHIER.CREATE_ORDER.TITLE`).
   - Ví dụ: `CASHIER.PANEL.TITLE`, `ECOMMERCE.SHOPEE.SYNC`, `COMMON.SAVE`.
2. **Phân cấp:** Dùng dấu chấm (`.`) để tạo cấu trúc phân cấp trong file JSON.
   - Ví dụ: `CASHIER.CREATE_ORDER.TITLE` tương ứng với `{ "CASHIER": { "CREATE_ORDER": { "TITLE": "..." } } }` trong `src/infra/i18n/`.
3. **Key theo vị trí:**
   - **Global** (`global/<lang>.json`): Key chung, bắt đầu bằng `COMMON` (ví dụ: `COMMON.SAVE`).
   - **Feature** (`<feature>/<lang>.json`): Key cho giao diện chung (`panel`, `dialog`), bắt đầu bằng tên module (ví dụ: `CASHIER.PANEL.TITLE`).
   - **Sub-feature** (`<feature>/<sub-feature>/<lang>.json`): Key cho trang/chức năng chính (ví dụ: `CASHIER.CREATE_ORDER.TITLE`).
4. **Ngành hàng và đa kênh:**
   - Ngành hàng (Thời trang, Đồ ăn) dùng namespace `PRODUCTS` với phụ (`FASHION`, `FOOD`).
     - Ví dụ: `PRODUCTS.FASHION.SIZE` trong `products/fashion/<lang>.json`.
   - Đa kênh (Shopee, Lazada) dùng namespace `ECOMMERCE` với phụ (`SHOPEE`, `LAZADA`).
     - Ví dụ: `ECOMMERCE.SHOPEE.SYNC` trong `ecommerce/sync-orders/<lang>.json`.

**Ví dụ key:**
- **Global**: `COMMON.SAVE`, `COMMON.CANCEL`.
- **Feature**: `CASHIER.PANEL.TITLE`, `WAREHOUSE.DIALOG.CONFIRM`.
- **Sub-feature**: `CASHIER.CREATE_ORDER.TITLE`, `WAREHOUSE.INVENTORY_CHECK.SUBMIT`.
- **Ngành hàng**: `PRODUCTS.FASHION.SIZE`, `PRODUCTS.FOOD.EXPIRY_DATE`.
- **Đa kênh**: `ECOMMERCE.SHOPEE.SYNC`, `ECOMMERCE.LAZADA.ERROR`.

## Cách tạo folder và file i18n

Để thêm bản dịch, AI Coding Agent cần tạo folder và file trong `src/infra/i18n/` theo **global**, **feature**, hoặc **sub-feature**.

**Bước thực hiện:**
1. **Xác định vị trí:**
   - **Global**: Key chung (ví dụ: `COMMON.SAVE`) → tạo trong `src/infra/i18n/global/`.
   - **Feature**: Key cho giao diện chung (`panel`, `dialog`) của module → tạo trong `src/infra/i18n/<feature>/`.
     - Ví dụ: `CASHIER.PANEL.TITLE` → `cashier/<lang>.json`.
   - **Sub-feature**: Key cho trang/chức năng chính → tạo trong `src/infra/i18n/<feature>/<sub-feature>/`.
     - Ví dụ: `CASHIER.CREATE_ORDER.TITLE` → `cashier/create-order/<lang>.json`.
2. **Tạo folder:**
   - **Global**: Tạo `src/infra/i18n/global/` nếu chưa tồn tại.
   - **Feature**: Tạo `src/infra/i18n/<feature>/` (feature thuộc danh sách module: `cashier`, `warehouse`, `ecommerce`, ...).
   - **Sub-feature**: Tạo `src/infra/i18n/<feature>/<sub-feature>/` dựa trên URL hoặc chức năng.
     - Ví dụ: URL `/cashier/create-order` → tạo `cashier/create-order/`.
3. **Tạo file JSON:**
   - Tạo `en.json` và `vi.json` trong folder tương ứng.
   - Ví dụ: `src/infra/i18n/cashier/create-order/en.json`, `src/infra/i18n/cashier/create-order/vi.json`.

**Ví dụ tạo folder cho `/cashier/create-order`:**
```plaintext
src/infra/i18n/
├── global/
│   ├── en.json             # Key chung: COMMON.SAVE
│   ├── vi.json
├── cashier/
│   ├── en.json             # Key chung: CASHIER.PANEL.TITLE
│   ├── vi.json
│   ├── create-order/
│   │   ├── en.json         # Key trang: CASHIER.CREATE_ORDER.TITLE
│   │   ├── vi.json
```

**Nội dung mẫu `global/en.json`:**
```json
{
  "COMMON": {
    "SAVE": "Save",
    "CANCEL": "Cancel"
  }
}
```

**Nội dung mẫu `cashier/en.json` (feature):**
```json
{
  "CASHIER": {
    "PANEL": {
      "TITLE": "Cashier Panel"
    },
    "DIALOG": {
      "CONFIRM": "Confirm Action"
    }
  }
}
```

**Nội dung mẫu `cashier/create-order/en.json` (sub-feature):**
```json
{
  "CASHIER": {
    "CREATE_ORDER": {
      "TITLE": "Create Order",
      "SUBMIT": "Confirm Order"
    }
  }
}
```

**Chú thích cho AI Coding Agent:**
- Tạo folder theo **feature** (`cashier/`) cho key giao diện chung (`panel`, `dialog`).
- Tạo folder theo **sub-feature** (`cashier/create-order/`) cho key trang/chức năng chính.
- Đảm bảo file `en.json` và `vi.json` có định dạng JSON hợp lệ.
- Không tạo file trong `public/assets/i18n/` (do script gộp tự động).

## Cách thêm key dịch vào file JSON

AI Coding Agent cần thêm key dịch vào đúng file JSON trong `src/infra/i18n/` dựa trên loại bản dịch.

**Bước thực hiện:**
1. **Xác định loại key và vị trí:**
   - **Global**: Key chung → thêm vào `global/<lang>.json`.
     - Ví dụ: `COMMON.SAVE` → `global/en.json`.
   - **Feature**: Key giao diện chung (`panel`, `dialog`) → thêm vào `<feature>/<lang>.json`.
     - Ví dụ: `CASHIER.PANEL.TITLE` → `cashier/en.json`.
   - **Sub-feature**: Key trang/chức năng chính → thêm vào `<feature>/<sub-feature>/<lang>.json`.
     - Ví dụ: `CASHIER.CREATE_ORDER.TITLE` → `cashier/create-order/en.json`.
2. **Thêm key với namespace:**
   - Dùng **SCREAMING_SNAKE_CASE** và cấu trúc phân cấp.
   - Ví dụ: `CASHIER.CREATE_ORDER.TITLE` trong `cashier/create-order/en.json`:
     ```json
     {
       "CASHIER": {
         "CREATE_ORDER": {
           "TITLE": "Create Order"
         }
       }
     }
     ```
3. **Đồng bộ ngôn ngữ:**
   - Thêm bản dịch cho cả `en.json` và `vi.json`.
   - Ví dụ: `cashier/create-order/vi.json`:
     ```json
     {
       "CASHIER": {
         "CREATE_ORDER": {
           "TITLE": "Tạo Đơn Hàng"
         }
       }
     }
     ```

**Ví dụ thêm key cho `/cashier/create-order`:**
- **Key trang chính** (`CASHIER.CREATE_ORDER.TITLE`):
  - File: `cashier/create-order/en.json`
    ```json
    {
      "CASHIER": {
        "CREATE_ORDER": {
          "TITLE": "Create Order",
          "SUBMIT": "Confirm Order"
        }
      }
    }
    ```
  - File: `cashier/create-order/vi.json`
    ```json
    {
      "CASHIER": {
        "CREATE_ORDER": {
          "TITLE": "Tạo Đơn Hàng",
          "SUBMIT": "Xác Nhận Đơn Hàng"
        }
      }
    }
    ```
- **Key giao diện chung** (`CASHIER.PANEL.TITLE`):
  - File: `cashier/en.json`
    ```json
    {
      "CASHIER": {
        "PANEL": {
          "TITLE": "Cashier Panel"
        }
      }
    }
    ```
  - File: `cashier/vi.json`
    ```json
    {
      "CASHIER": {
        "PANEL": {
          "TITLE": "Bảng Điều Khiển Thu Ngân"
        }
      }
    }
    ```
- **Key chung** (`COMMON.SAVE`):
  - File: `global/en.json`
    ```json
    {
      "COMMON": {
        "SAVE": "Save"
      }
    }
    ```
  - File: `global/vi.json`
    ```json
    {
      "COMMON": {
        "SAVE": "Lưu"
      }
    }
    ```

**Ví dụ cho ngành hàng (Thời trang):**
- File: `products/en.json` (feature, giao diện chung)
  ```json
  {
    "PRODUCTS": {
      "PANEL": {
        "TITLE": "Product Panel"
      }
    }
  }
  ```
- File: `products/fashion/en.json` (sub-feature, trang chính)
  ```json
  {
    "PRODUCTS": {
      "FASHION": {
        "SIZE": "Size",
        "COLOR": "Color"
      }
    }
  }
  ```

**Ví dụ cho đa kênh (Shopee):**
- File: `ecommerce/sync-orders/en.json` (sub-feature)
  ```json
  {
    "ECOMMERCE": {
      "SHOPEE": {
        "SYNC": "Sync with Shopee"
      }
    }
  }
  ```

**Chú thích cho AI Coding Agent:**
- Thêm key giao diện chung (`panel`, `dialog`) vào `<feature>/<lang>.json`.
- Thêm key trang/chức năng chính vào `<feature>/<sub-feature>/<lang>.json`.
- Thêm key chung vào `global/<lang>.json`.
- Đảm bảo **SCREAMING_SNAKE_CASE** và đồng bộ `en.json`, `vi.json`.

## Sử dụng key dịch trong ứng dụng

Key dịch được sử dụng trong template (HTML) hoặc TypeScript thông qua `ngx-translate`. File gộp (`public/assets/i18n/<lang>.json`) được tải bởi `CustomTranslateHttpLoader`.

### 1. Trong template (HTML)

Sử dụng **pipe `translate`** hoặc **directive `translate`** để hiển thị bản dịch.

```html
<!-- Key trang chính (sub-feature) -->
<h1>{{ 'CASHIER.CREATE_ORDER.TITLE' | translate }}</h1>

<!-- Key giao diện chung (feature) -->
<mat-panel-title>{{ 'CASHIER.PANEL.TITLE' | translate }}</mat-panel-title>

<!-- Key chung (global) -->
<button mat-button>{{ 'COMMON.CANCEL' | translate }}</button>

<!-- Với tham số động -->
<p>{{ 'CASHIER.CREATE_ORDER.GREETING' | translate:{ NAME: userName } }}</p>
```

**Ví dụ cho `/cashier/create-order`:**
```html
<h1>{{ 'CASHIER.CREATE_ORDER.TITLE' | translate }}</h1>
<mat-panel>
  <mat-panel-title>{{ 'CASHIER.PANEL.TITLE' | translate }}</mat-panel-title>
</mat-panel>
<button mat-raised-button>{{ 'CASHIER.CREATE_ORDER.SUBMIT' | translate }}</button>
<button mat-button>{{ 'COMMON.CANCEL' | translate }}</button>
```

**Ví dụ cho ngành Thời trang (`/products/fashion`):**
```html
<mat-form-field>
  <mat-label>{{ 'PRODUCTS.FASHION.SIZE' | translate }}</mat-label>
  <mat-select>
    <mat-option value="S">S</mat-option>
    <mat-option value="M">M</mat-option>
  </mat-select>
</mat-form-field>
```

**Ví dụ cho tích hợp Shopee (`/ecommerce/sync-orders`):**
```html
<h1>{{ 'ECOMMERCE.SYNC_ORDERS.TITLE' | translate }}</h1>
<button mat-raised-button>{{ 'ECOMMERCE.SHOPEE.SYNC' | translate }}</button>
```

### 2. Trong TypeScript

Sử dụng `TranslateService` để lấy bản dịch theo lập trình.

```typescript
import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-cashier-create-order',
  template: '...'
})
export class CashierCreateOrderComponent {
  constructor(private translate: TranslateService) {
    // Lấy key trang chính
    this.translate.get('CASHIER.CREATE_ORDER.TITLE').subscribe((res: string) => {
      console.log(res); // "Create Order" (en) hoặc "Tạo Đơn Hàng" (vi)
    });

    // Lấy key giao diện chung
    this.translate.get('CASHIER.PANEL.TITLE').subscribe((res: string) => {
      console.log(res); // "Cashier Panel" (en)
    });

    // Lấy nhiều key
    this.translate.get([
      'CASHIER.CREATE_ORDER.TITLE',
      'COMMON.SAVE'
    ]).subscribe((res: any) => {
      console.log(res['CASHIER.CREATE_ORDER.TITLE']); // "Create Order"
      console.log(res['COMMON.SAVE']); // "Save"
    });
  }
}
```

**Chú thích cho AI Coding Agent:**
- Sử dụng key với dấu chấm (`.`) trong template/TypeScript.
- Đảm bảo key đã được định nghĩa trong file JSON đúng:
  - `global/<lang>.json` cho key chung.
  - `<feature>/<lang>.json` cho key giao diện chung.
  - `<feature>/<sub-feature>/<lang>.json` cho key trang/chức năng chính.

## Tích hợp script merge-translations

Script `merge-translations.js` (trong `scripts/`) gộp file dịch từ `src/infra/i18n/` thành `public/assets/i18n/<lang>.json`.



### Cách hoạt động
- **Dev**:
  - Chạy `npm start`:
    - Script `merge-translations.js --watch` theo dõi thay đổi trong `src/infra/i18n/`.
    - Tự động gộp file khi thêm/sửa `en.json`, `vi.json`.
  - File gộp (`public/assets/i18n/en.json`, `vi.json`) được tải bởi `ngx-translate`.
- **Production**:
  - Chạy `npm run build`:
    - Script gộp file một lần, tạo `public/assets/i18n/<lang>.json`.

### Kiểm tra
- Thêm key mới vào `src/infra/i18n/cashier/create-order/en.json`:
  ```json
  {
    "CASHIER": {
      "CREATE_ORDER": {
        "NEW_KEY": "New Key"
      }
    }
  }
  ```
- Chạy `npm start`, kiểm tra `public/assets/i18n/en.json`:
  ```json
  {
    "CASHIER.CREATE_ORDER.NEW_KEY": "New Key"
  }
  ```
- Mở DevTools (Network tab), xác nhận chỉ có **1 request** cho `assets/i18n/en.json`.

**Chú thích cho AI Coding Agent:**
- Không chỉnh sửa `public/assets/i18n/<lang>.json`.
- Chỉ thêm/sửa file trong `src/infra/i18n/`.
- Chạy `npm start` để kiểm tra gộp file tự động.

## Best Practices

1. **Dùng SCREAMING_SNAKE_CASE:** Key phải chữ in hoa, dấu gạch dưới (ví dụ: `CASHIER_CREATE_ORDER_TITLE`).
2. **Phân loại key đúng:**
   - Key chung → `global/<lang>.json` (ví dụ: `COMMON.SAVE`).
   - Key giao diện chung (`panel`, `dialog`, `modal`) → `<feature>/<lang>.json` (ví dụ: `CASHIER.PANEL.TITLE`).
   - Key trang/chức năng chính → `<feature>/<sub-feature>/<lang>.json` (ví dụ: `CASHIER.CREATE_ORDER.TITLE`).
3. **Đồng bộ ngôn ngữ:** Cập nhật cả `en.json` và `vi.json` khi thêm key.
4. **Ngăn trùng key:** Dùng namespace (`CASHIER`, `ECOMMERCE`) để tránh trùng lặp.
5. **Hỗ trợ ngành hàng:**
   - Thêm namespace `PRODUCTS.FASHION`, `PRODUCTS.FOOD` trong `products/<sub-feature>/<lang>.json`.
   - Ví dụ: `PRODUCTS.FASHION.SIZE` trong `products/fashion/<lang>.json`.
6. **Hỗ trợ đa kênh:**
   - Thêm key `ECOMMERCE.SHOPEE`, `ECOMMERCE.LAZADA` trong `ecommerce/sync-orders/<lang>.json`.
   - Ví dụ: `ECOMMERCE.SHOPEE.SYNC`.
7. **Tận dụng script merge-translations:**
   - Chạy `npm start` trong dev để watch file dịch.
   - Chạy `npm run build` để gộp file cho production.
8. **Debug console:** Nếu key không hiển thị, kiểm tra:
   - Key có trong `public/assets/i18n/<lang>.json` không.
   - File JSON trong `src/infra/i18n/` có định dạng hợp lệ không.

## Tài liệu tham khảo

- [Trang chủ `ngx-translate`](https://github.com/ngx-translate/core)
- [API `TranslateService`](https://github.com/ngx-translate/core/blob/master/projects/ngx-translate/core/src/lib/translate.service.ts)
- [Chokidar (dùng trong merge-translations.js)](https://github.com/paulmillr/chokidar)
- [Danh sách module ERP](#): Xem cấu trúc thư mục dự án (`FOLDER_STRUCTURE.md`) để biết danh sách module (`cashier`, `warehouse`, `ecommerce`, ...).

---

### Giải thích tài liệu

1. **Cấu trúc i18n mới:**
   - **Bỏ first path/full path**: Thay bằng **global**, **feature**, **sub-feature** trong `src/infra/i18n/`.
   - **File gộp**: Tất cả file dịch được gộp thành `public/assets/i18n/<lang>.json`, tải với 1 request HTTP.
   - **Script merge-translations.js**: Tự động gộp file, hỗ trợ watch trong dev.

2. **Tối ưu cho AI Coding Agent:**
   - Hướng dẫn rõ cách tạo folder:
     - `global/` cho key chung.
     - `<feature>/` cho key giao diện chung.
     - `<feature>/<sub-feature>/` cho key trang/chức năng chính.
   - Đưa ra ví dụ cụ thể cho từng loại key.
   - Nhấn mạnh **SCREAMING_SNAKE_CASE** và đồng bộ `en.json`, `vi.json`.

3. **Hỗ trợ ERP và TMĐT:**
   - Ví dụ bao gồm module (`cashier`, `warehouse`, `ecommerce`) và sub-feature (`create-order`, `sync-orders`).
   - Key ngành hàng: `PRODUCTS.FASHION.SIZE`, `PRODUCTS.FOOD.EXPIRY_DATE` trong `products/<sub-feature>/`.
   - Key đa kênh: `ECOMMERCE.SHOPEE.SYNC` trong `ecommerce/sync-orders/`.

4. **Tích hợp script:**
   - Hướng dẫn chạy `npm start` (watch) và `npm run build` (gộp một lần).
   - Đảm bảo AI không chỉnh sửa `public/assets/i18n/`.

5. **Ngăn lỗi:**
   - Hướng dẫn kiểm tra file gộp và console log để phát hiện key thiếu.
   - Đề xuất namespace để tránh trùng key.

---

### Quy trình áp dụng cho AI Coding Agent

1. **Tạo folder i18n:**
   - Khi thêm key cho URL `/cashier/create-order`:
     - Tạo `src/infra/i18n/cashier/` cho key giao diện chung (`CASHIER.PANEL.TITLE`).
     - Tạo `src/infra/i18n/cashier/create-order/` cho key trang chính (`CASHIER.CREATE_ORDER.TITLE`).
   - Kiểm tra danh sách module (`cashier`, `warehouse`, `ecommerce`, ...) trong `FOLDER_STRUCTURE.md`.

2. **Thêm key dịch:**
   - Phát hiện key trong template (như `{{ 'CASHIER.CREATE_ORDER.TITLE' | translate }}`):
     - Thêm vào `cashier/create-order/<lang>.json`.
   - Phát hiện key giao diện chung (như `CASHIER.PANEL.TITLE`):
     - Thêm vào `cashier/<lang>.json`.
   - Key chung (`COMMON.SAVE`):
     - Thêm vào `global/<lang>.json`.

3. **Kiểm tra gộp file:**
   - Chạy `npm start` để watch file dịch.
   - Kiểm tra `public/assets/i18n/<lang>.json` chứa key mới.

4. **Đề xuất tự động:**
   - Trong `<mat-panel>` → gợi ý key như `CASHIER.PANEL.TITLE` cho `cashier/<lang>.json`.
   - Trong `<h1>` của trang → gợi ý key như `CASHIER.CREATE_ORDER.TITLE` cho `cashier/create-order/<lang>.json`.
   - Đảm bảo key ở **SCREAMING_SNAKE_CASE**.

---

### Kiểm tra và debug

1. **Kiểm tra folder i18n:**
   - Mở `src/infra/i18n/`:
     - `global/` có `en.json`, `vi.json` cho key chung.
     - `cashier/`, `warehouse/` có `en.json`, `vi.json` cho key giao diện chung.
     - `cashier/create-order/`, `ecommerce/sync-orders/` có `en.json`, `vi.json` cho key trang/chức năng.
   - Mở `public/assets/i18n/`:
     - Xác nhận `en.json`, `vi.json` chứa tất cả key gộp.

2. **Kiểm tra key dịch:**
   - Mở file JSON:
     - `cashier/en.json`: Kiểm tra key như `CASHIER.PANEL.TITLE`.
     - `cashier/create-order/en.json`: Kiểm tra key như `CASHIER.CREATE_ORDER.TITLE`.
   - Đảm bảo key ở **SCREAMING_SNAKE_CASE** và đồng bộ `en.json`, `vi.json`.

3. **Kiểm tra trong UI:**
   - Truy cập `/cashier/create-order`:
     - Xác nhận key từ `cashier/create-order/<lang>.json` (`CASHIER.CREATE_ORDER.TITLE`) và `cashier/<lang>.json` (`CASHIER.PANEL.TITLE`).
   - Truy cập `/ecommerce/sync-orders`:
     - Xác nhận key từ `ecommerce/sync-orders/<lang>.json` (`ECOMMERCE.SHOPEE.SYNC`).
   - Chuyển ngôn ngữ để kiểm tra `en` và `vi`.

4. **Kiểm tra request HTTP:**
   - Mở DevTools (Network tab).
   - Tải trang, xác nhận chỉ có **1 request** cho `assets/i18n/<lang>.json`.

