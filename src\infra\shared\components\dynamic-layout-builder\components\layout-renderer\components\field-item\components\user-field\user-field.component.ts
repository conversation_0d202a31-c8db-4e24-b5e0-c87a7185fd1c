import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

@Component({
  selector: 'app-user-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    MatTooltipModule,
    MatChipsModule,
    TranslateModule
  ],
  templateUrl: './user-field.component.html',
  styleUrls: ['./user-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  @Input({ required: true }) config!: FieldItemConfig;
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    if (this.config.field.type !== 'user') {
      console.warn(`UserFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate mock value dựa trên field type
   */
  protected override generateMockValue(): void {
    const mockData = this.mockDataService.generateMockData('user');
    this.mockValue.set(String(mockData));
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected override getValidators(): any[] {
    return this.getCommonValidators();
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    return 'person';
  }

  // ===== IMPLEMENT INTERFACE METHODS =====

  /**
   * Handle khi field.value thay đổi từ bên ngoài
   */
  override handleFieldValueChange(): void {
    super.handleFieldValueChange();
  }

  /**
   * Lấy giá trị hiện tại của field
   */
  getFieldValue(): FieldValue {
    return this.getCurrentValue();
  }

  /**
   * Override onValueChange để emit event
   */
  override onValueChange(value: FieldValue): void {
    super.onValueChange!(value);

    // Emit change event để Form Management Service track
    const fieldId = this.config.field._id || '';
    this.valueChange.emit({ fieldId, value });
  }

  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Lấy danh sách users mock
   */
  getUserOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'user1', label: 'John Doe' },
      { value: 'user2', label: 'Jane Smith' },
      { value: 'user3', label: 'Bob Johnson' }
    ];
  }

  /**
   * Kiểm tra xem có phải multi-select không
   */
  isMultiSelect(): boolean {
    return (this.config.field as any).multiple === true;
  }
}
