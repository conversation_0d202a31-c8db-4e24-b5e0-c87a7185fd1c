import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Company } from 'salehub_shared_contracts';

@Component({
  selector: 'app-organization-info-form',
  templateUrl: './organization-info-form.component.html',
  styleUrls: ['./organization-info-form.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule
  ]
})
export class OrganizationInfoFormComponent implements OnInit {
  companyForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService
  ) {
    // Đảm bảo sử dụng tiếng Việt cho form
    this.translateService.use('vi');
  }

  ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    try {
      this.companyForm = this.fb.group({
        businessName: ['', [Validators.required]],
        description: [''],
        address: this.fb.group({
          fullAddress: ['', [Validators.required]],
          street: [''],
          city: [''],
          province: [''],
          country: [''],
          postalCode: [''],
          lat: [null],
          lng: [null],
          instruction: ['']
        }),
        taxCode: ['', [Validators.required]],
        taxAuthority: [''],
        establishmentDate: [null],
        workPhones: this.fb.array([
          this.createPhoneControl()
        ]),
        workEmail: ['', [Validators.email]],
        industries: [[]],
        owner: this.fb.group({
          _id: [''],
          name: ['', [Validators.required]]
        })
      });
      console.log('Form initialized successfully');
    } catch (error) {
      console.error('Error initializing form:', error);
    }
  }

  private createPhoneControl(): FormGroup {
    return this.fb.group({
      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10,11}$/)]]
    });
  }

  get workPhonesArray(): FormArray {
    return this.companyForm.get('workPhones') as FormArray;
  }

  addPhone(): void {
    try {
      this.workPhonesArray.push(this.createPhoneControl());
    } catch (error) {
      console.error('Error adding phone:', error);
    }
  }

  removePhone(index: number): void {
    try {
      if (this.workPhonesArray.length > 1) {
        this.workPhonesArray.removeAt(index);
      }
    } catch (error) {
      console.error('Error removing phone:', error);
    }
  }

  onSubmit(): void {
    try {
      if (this.companyForm.valid) {
        console.log('Form data:', this.companyForm.value);
      } else {
        this.markFormGroupTouched(this.companyForm);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    try {
      Object.values(formGroup.controls).forEach(control => {
        control.markAsTouched();

        if (control instanceof FormGroup) {
          this.markFormGroupTouched(control);
        }
      });
    } catch (error) {
      console.error('Error marking form as touched:', error);
    }
  }
}
