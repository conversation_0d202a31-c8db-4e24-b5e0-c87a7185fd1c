:host {
  display: block;
  margin-bottom: 1.5rem;
}

::ng-deep .mat-expansion-panel-body {
  padding: 0 1rem 1rem 1rem;
}

.mat-button-toggle-group {
  width: 100%;
  margin-bottom: 1rem;
  display: flex;
}

.mat-button-toggle {
  flex: 1;
  text-align: center;
}

.w-100 {
  width: 100%;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.me-4 {
  margin-right: 1rem;
}

h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 500;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
}

.alert {
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

@media (max-width: 767.98px) {
  .mat-button-toggle-group {
    flex-direction: column;
  }

  .mat-button-toggle {
    width: 100%;
  }

  .d-flex {
    flex-direction: column;
  }

  .me-4 {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
