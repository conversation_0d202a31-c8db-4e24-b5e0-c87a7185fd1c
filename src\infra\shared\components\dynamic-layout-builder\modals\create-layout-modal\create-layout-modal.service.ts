import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { TranslateService } from '@ngx-translate/core';
import { CreateLayoutModalComponent, CreateLayoutModalData, CreateLayoutModalResult } from './create-layout-modal.component';

/**
 * Service để mở CreateLayoutModal sử dụng ResponsiveModalService
 * 
 * Chức năng:
 * - Wrap việc mở modal với ResponsiveModalService.open
 * - Type safety với CreateLayoutModalData và CreateLayoutModalResult
 * - Cấu hình modal phù hợp cho việc tạo layout
 */
@Injectable()
export class CreateLayoutModalService {
  private responsiveModalService = inject(ResponsiveModalService);
  private translateService = inject(TranslateService);

  /**
   * Mở modal tạo layout mới
   * 
   * @param data Dữ liệu đầu vào cho modal (optional)
   * @returns Promise<CreateLayoutModalResult | undefined> Kết quả từ modal
   */
  async open(data?: CreateLayoutModalData): Promise<CreateLayoutModalResult | undefined> {
    try {
      const modalConfig = {
        data: data || {},
        width: '600px',
        maxWidth: '95vw',
        minWidth: '500px',
        disableClose: false,
        hasBackdrop: true,
        enableClose: true,
        actions: {
          useDefault: true // Sử dụng default actions (Hủy/Xác nhận)
        }
      };

      console.log(this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.MODAL_DEBUG.OPENING_MODAL_WITH_DATA'), data);

      const result = await this.responsiveModalService.open<
        CreateLayoutModalData,
        CreateLayoutModalResult,
        CreateLayoutModalComponent
      >(CreateLayoutModalComponent, modalConfig);

      console.log(this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.MODAL_DEBUG.MODAL_CLOSED_WITH_RESULT'), result);
      return result;

    } catch (error) {
      console.error(this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.MODAL_DEBUG.ERROR_OPENING_MODAL'), error);
      return undefined;
    }
  }

  /**
   * Mở modal với danh sách layout đã tồn tại để validate
   * 
   * @param existingLayouts Danh sách tên layout đã tồn tại
   * @param defaultName Tên mặc định (optional)
   * @param defaultDescription Mô tả mặc định (optional)
   * @returns Promise<CreateLayoutModalResult | undefined> Kết quả từ modal
   */
  async openWithValidation(
    existingLayouts: string[],
    defaultName?: string,
    defaultDescription?: string
  ): Promise<CreateLayoutModalResult | undefined> {
    const data: CreateLayoutModalData = {
      existingLayouts,
      defaultName,
      defaultDescription
    };

    return this.open(data);
  }
}
