
# StandardDialogComponent

## Mục tiêu
Tạo một `StandardDialogComponent` mở rộng từ Angular Material's `MatDialog` để chuẩn hóa dialog cho toàn bộ dự án. Component này cung cấp giao diện và hành vi đồng nhất cho các dialog.

## Chi tiết Component
- **Vị trí**: `src/infra/shared/components/standard-dialog`
- **Mục đích**: Chuẩn hóa giao diện dialog với hai khối chính: `mat-dialog-title` và `mat-dialog-actions`.
- **Chức năng**:
  - **Tiêu đề Dialog**:
    - Nhận input `dialogTitle`, có thể là một chuỗi văn bản hoặc một template tùy chỉnh.
  - **Nút Đóng**:
    - Đư<PERSON>c điều khiển bởi input `enableClose` (boolean, mặc định: `true`).
    - Nếu `true`, hiển thị nút đóng (X) ở phía bên phải tiêu đề, đóng dialog khi được nhấp.
  - **<PERSON><PERSON><PERSON> động Dialog**:
    - Được điều khiển bởi input `useDefault` (boolean).
    - Nếu `true`, hiển thị hai nút mặc định:
      - **Hủy**: Đóng dialog mà không trả về dữ liệu.
      - **Xác nhận**: Đóng dialog và trả về dữ liệu.
    - Nhận input `customActionsTemplate` để truyền template tùy chỉnh cho `mat-dialog-actions`, cho phép thay thế hoặc bổ sung các nút hành động.
