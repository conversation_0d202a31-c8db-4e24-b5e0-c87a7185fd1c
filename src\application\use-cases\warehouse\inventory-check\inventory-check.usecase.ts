import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { InventoryCheck } from '../../../../domain/entities/inventory-check.entity';
import { InventoryCheckRepository } from '../../../../domain/repositories/warehouse/inventory-check.repository';

/**
 * Use case cho Inventory Check
 * Điều phối logic nghiệp vụ giữa domain và infrastructure
 */
@Injectable()
export class InventoryCheckUseCase {
  constructor(private inventoryCheckRepository: InventoryCheckRepository) {}

  /**
   * Khởi tạo một phiếu kiểm kho mới
   */
  initInventoryCheck(): InventoryCheck {
    return this.inventoryCheckRepository.initInventoryCheck();
  }

  /**
   * Tải phiếu kiểm kho theo ID
   * @param id ID của phiếu kiểm kho
   */
  loadInventoryCheck(id: string): Observable<InventoryCheck> {
    return this.inventoryCheckRepository.loadInventoryCheck(id);
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách phiếu kiểm kho
   * @param page Số trang
   * @param pageSize Số lượng item trên mỗi trang
   */
  getInventoryCheckList(page: number, pageSize: number): Observable<{
    items: InventoryCheck[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    return this.inventoryCheckRepository.getInventoryCheckList(page, pageSize);
  }

  /**
   * Lưu phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho cần lưu
   * @param status Trạng thái của phiếu kiểm kho
   */
  saveInventoryCheck(
    inventoryCheck: InventoryCheck,
    status: 'draft' | 'completed'
  ): Observable<{
    _id: string;
    createdAt?: Date;
    updatedAt?: Date;
  }> {
    // Kiểm tra dữ liệu trước khi lưu
    this.validateInventoryCheck(inventoryCheck);

    return this.inventoryCheckRepository.saveInventoryCheck(inventoryCheck, status);
  }

  /**
   * Thêm sản phẩm vào phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho
   * @param productId ID của sản phẩm
   */
  addProductToInventory(
    inventoryCheck: InventoryCheck,
    productId: string
  ): Observable<boolean> {
    return this.inventoryCheckRepository.addProductToInventory(inventoryCheck, productId);
  }

  /**
   * Xóa sản phẩm khỏi phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho
   * @param productId ID của sản phẩm
   */
  removeProductFromInventory(
    inventoryCheck: InventoryCheck,
    productId: string
  ): boolean {
    return this.inventoryCheckRepository.removeProductFromInventory(inventoryCheck, productId);
  }

  /**
   * Cập nhật số lượng thực tế của sản phẩm
   * @param inventoryCheck Phiếu kiểm kho
   * @param productId ID của sản phẩm
   * @param quantity Số lượng thực tế
   */
  updateActualQuantity(
    inventoryCheck: InventoryCheck,
    productId: string,
    quantity: number
  ): boolean {
    return this.inventoryCheckRepository.updateActualQuantity(inventoryCheck, productId, quantity);
  }

  /**
   * Lấy danh sách kho
   */
  getWarehouses(): Observable<Array<{
    _id: string;
    name: string;
  }>> {
    return this.inventoryCheckRepository.getWarehouses();
  }

  /**
   * Lấy danh sách vị trí kho theo warehouseId
   * @param warehouseId ID của kho
   */
  getWarehouseLocations(warehouseId: string): Observable<Array<{
    _id: string;
    name: string;
    warehouse: { _id: string; name: string; };
  }>> {
    return this.inventoryCheckRepository.getWarehouseLocations(warehouseId);
  }

  /**
   * Lấy danh sách danh mục sản phẩm
   */
  getProductCategories(): Observable<Array<{
    _id: string;
    name: string;
  }>> {
    return this.inventoryCheckRepository.getProductCategories();
  }

  /**
   * Kiểm tra dữ liệu phiếu kiểm kho
   * @param inventoryCheck Phiếu kiểm kho cần kiểm tra
   * @throws Error nếu dữ liệu không hợp lệ
   */
  private validateInventoryCheck(inventoryCheck: InventoryCheck): void {
    if (!inventoryCheck.warehouse || !inventoryCheck.warehouse._id) {
      throw new Error('Vui lòng chọn kho kiểm');
    }

    if (!inventoryCheck.items || inventoryCheck.items.length === 0) {
      throw new Error('Vui lòng thêm ít nhất một sản phẩm vào phiếu kiểm kho');
    }
  }
}
