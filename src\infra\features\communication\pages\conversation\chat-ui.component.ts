import { Component, OnInit, AfterViewInit, ViewChild, Renderer2, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ScrollingModule, CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import {TooltipPosition, MatTooltipModule} from '@angular/material/tooltip';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-chat-ui',
  standalone: true,
  imports: [CommonModule, FormsModule, ScrollingModule, MatTooltipModule],
  templateUrl: './chat-ui.component.html',
  styleUrls: ['./chat-ui.component.scss']
})
export class ChatUiComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild(CdkVirtualScrollViewport) viewport!: CdkVirtualScrollViewport;

  messages: any[] = [];
  newMessage: string = '';
  currentUser = {
    id: 'current-user',
    name: '<PERSON>',
    avatar: 'assets/images/avatars/erik-tenhag.svg',
    isAdmin: true
  };

  // Memory management: Container để quản lý tất cả subscriptions
  private readonly subscriptions = new Subscription();

  groups: any[] = [
    {
      id: '1',
      name: 'United Family 👹',
      avatar: 'assets/images/avatars/united-family.svg',
      description: 'Hey lads, tough game yesterday. Let\'s talk about what went wrong and how we can improve it. #GGMU🔴',
      isActive: true,
      unreadCount: 3,
      lastMessage: 'Rashford is trying...',
      lastMessageTime: '05:26 AM'
    },
    {
      id: '2',
      name: 'Harry Maguire',
      avatar: 'assets/images/avatars/harry-maguire.svg',
      isActive: false,
      unreadCount: 0,
      lastMessage: 'Hey coach, tough game yesterday...',
      lastMessageTime: '02:12 AM'
    },
    {
      id: '3',
      name: 'Rasmus Højlund',
      avatar: 'assets/images/avatars/rasmus-hojlund.svg',
      isActive: false,
      unreadCount: 1,
      lastMessage: 'Hey, I need to talk today Hey, I need to talk today Hey, I need to talk today',
      lastMessageTime: '01:13 AM'
    },
    {
      id: '4',
      name: 'Andre Onana',
      avatar: 'assets/images/avatars/andre-onana.svg',
      isActive: false,
      unreadCount: 0,
      lastMessage: 'I need more time bro 😔',
      lastMessageTime: '11:54 AM'
    },
    {
      id: '5',
      name: 'Reguilón',
      avatar: 'assets/images/avatars/reguilon.svg',
      isActive: false,
      unreadCount: 0,
      lastMessage: 'Great performance at all',
      lastMessageTime: '09:12 AM'
    },
    {
      id: '6',
      name: 'Bruno Fernandes',
      avatar: 'assets/images/avatars/bruno-fernandes.svg',
      isActive: false,
      unreadCount: 0,
      lastMessage: 'Play the game Bruno!',
      lastMessageTime: '10:21 PM'
    },
    {
      id: '7',
      name: 'Mason Mount',
      avatar: 'assets/images/avatars/mason-mount.svg',
      isActive: false,
      unreadCount: 0,
      lastMessage: 'How about your injury?',
      lastMessageTime: '10:17 PM'
    },
    {
      id: '8',
      name: 'Lisandro Martínez',
      avatar: 'assets/images/avatars/lisandro-martinez.svg',
      isActive: false,
      unreadCount: 1,
      lastMessage: 'I need some partners',
      lastMessageTime: '09:28 PM'
    }
  ];

  selectedGroup = this.groups[0];

  chatMessages: any[] = [];
  allMessages: any[] = []; // Store all messages
  pageSize: number = 20;
  loading: boolean = false;

  mediaFiles = [
    { type: 'image', url: 'assets/images/media/football-tactic.svg', name: 'Tactic 1' },
    { type: 'image', url: 'assets/images/media/football-tactic.svg', name: 'Tactic 2' },
    { type: 'image', url: 'assets/images/media/football-tactic.svg', name: 'Team 1' },
    { type: 'image', url: 'assets/images/media/football-tactic.svg', name: 'Team 2' },
    { type: 'image', url: 'assets/images/media/football-tactic.svg', name: 'Training 1' },
    { type: 'image', url: 'assets/images/media/football-tactic.svg', name: 'Training 2' },
    { type: 'document', url: '#', name: 'tactics.pdf' },
    { type: 'document', url: '#', name: 'analysis.docx' }
  ];

  constructor(private renderer: Renderer2) {
    this.renderer.addClass(document.body, 'overflow-hidden')
  }

  ngOnDestroy(): void {
    // Memory management: Unsubscribe tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();

    // DOM cleanup: Remove CSS class
    this.renderer.removeClass(document.body, 'overflow-hidden')
  }

  ngOnInit(): void {
    // Generate a large array of test messages
    this.generateTestMessages();

    // Initially load only the last 20 messages
    this.loadInitialMessages();
  }

  ngAfterViewInit(): void {
    // Set up scroll event listener for infinite scrolling
    this.setupInfiniteScroll();
  }

  generateTestMessages(): void {
    const senders = [
      {
        id: 'harry-maguire',
        name: 'Harry Maguire',
        avatar: 'assets/images/avatars/harry-maguire.svg'
      },
      {
        id: 'bruno-fernandes',
        name: 'Bruno Fernandes',
        avatar: 'assets/images/avatars/bruno-fernandes.svg'
      },
      {
        id: 'current-user',
        name: 'Erik TenHag',
        avatar: 'assets/images/avatars/erik-tenhag.svg'
      },
      {
        id: 'rasmus-hojlund',
        name: 'Rasmus Højlund',
        avatar: 'assets/images/avatars/rasmus-hojlund.svg'
      }
    ];

    const messageContents = [
      'Hey team, let\'s focus on our next match.',
      'We need to improve our defensive strategy.',
      'Great work in training today!',
      'I think we should change our formation for the next game.',
      'Let\'s analyze the opponent\'s playing style.',
      'We need more intensity in our pressing.',
      'The midfield needs to be more compact.',
      'Our counter-attacks need to be faster.',
      'Good job on set pieces today!',
      'We need to be more clinical in front of goal.'
    ];

    // Generate 200 test messages
    for (let i = 0; i < 200; i++) {
      const sender = senders[Math.floor(Math.random() * senders.length)];
      const content = messageContents[Math.floor(Math.random() * messageContents.length)];
      const hours = Math.floor(Math.random() * 12) + 1;
      const minutes = Math.floor(Math.random() * 60);
      const ampm = Math.random() > 0.5 ? 'AM' : 'PM';
      const timestamp = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${ampm}`;

      const message = {
        id: (i + 1).toString(),
        senderId: sender.id,
        senderName: sender.name,
        senderAvatar: sender.avatar,
        content: content,
        timestamp: timestamp,
        isRead: Math.random() > 0.3
      };

      // Add audio message or attachments randomly
      if (Math.random() > 0.9) {
        Object.assign(message, {
          audioMessage: true,
          audioDuration: '00:' + Math.floor(Math.random() * 59).toString().padStart(2, '0')
        });
      } else if (Math.random() > 0.8) {
        Object.assign(message, {
          attachments: [
            { type: 'image', url: 'assets/images/media/football-tactic.svg', name: 'Tactic ' + (i % 5 + 1) }
          ]
        });
      }

      this.allMessages.push(message);
    }
  }

  loadInitialMessages(): void {
    // Load the last 20 messages
    if (this.allMessages.length <= this.pageSize) {
      this.chatMessages = [...this.allMessages];
    } else {
      this.chatMessages = this.allMessages.slice(this.allMessages.length - this.pageSize);
    }
  }

  loadMoreMessages(): void {
    if (this.loading || this.chatMessages.length >= this.allMessages.length) {
      return;
    }

    this.loading = true;

    // Simulate network delay
    setTimeout(() => {
      const currentLength = this.chatMessages.length;
      const itemsToLoad = Math.min(this.pageSize, this.allMessages.length - currentLength);

      if (itemsToLoad <= 0) {
        this.loading = false;
        return;
      }

      // Get more messages from the start of the current messages
      const moreMessages = this.allMessages.slice(
        this.allMessages.length - currentLength - itemsToLoad,
        this.allMessages.length - currentLength
      );

      // Add to the beginning of the array
      this.chatMessages = [...moreMessages, ...this.chatMessages];

      this.loading = false;
    }, 500);
  }

  setupInfiniteScroll(): void {
    if (this.viewport) {
      // Memory management: Thêm subscription vào container để tự động cleanup
      this.subscriptions.add(
        this.viewport.elementScrolled().subscribe(() => {
          // When user scrolls to top (less than 10% from top), load more messages
          const scrollPosition = this.viewport.measureScrollOffset('top');
          if (scrollPosition < 100 && !this.loading) {
            this.loadMoreMessages();
          }
        })
      );
    }
  }

  scrollToBottom(): void {
    if (this.viewport) {
      this.viewport.scrollTo({ bottom: 0, behavior: 'smooth' });
    }
  }

  sendMessage(): void {
    if (this.newMessage.trim() === '') {
      return;
    }

    const newMsg = {
      id: Date.now().toString(),
      senderId: this.currentUser.id,
      senderName: this.currentUser.name,
      senderAvatar: this.currentUser.avatar,
      content: this.newMessage,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      isRead: false
    };

    // Add to both arrays
    this.allMessages.push(newMsg);
    this.chatMessages.push(newMsg);
    this.newMessage = '';

    // Scroll to bottom of chat
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  }

  selectGroup(group: any): void {
    this.selectedGroup = group;
    this.groups.forEach(g => g.isActive = g.id === group.id);

    // Reset and reload messages when changing groups
    this.loadInitialMessages();
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  }
}
