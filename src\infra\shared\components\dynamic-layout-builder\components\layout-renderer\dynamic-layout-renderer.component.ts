import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, signal, computed, OnD<PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { DynamicLayoutRendererConfig, DynamicLayoutRendererFormData } from '../../models/dynamic-layout-renderer.model';
import { PermissionSelectorComponent } from './components/permission-selector/permission-selector.component';
import { SectionComponent } from './components/section/section.component';
import { FlashMessageService } from '@core/services/flash_message.service';
import {
  PermissionSelectorConfig,
  SectionConfig,
  PermissionProfileChangeEvent,
  ViewModeChangeEvent,
  FieldValue
} from '../../models/dynamic-layout-renderer.model';
import { FieldPermissionProfile, Field } from '@domain/entities/field.entity';
import { Section } from '../../models/dynamic-layout-config.dto';
import { FormDataManagementService } from '../../services/form-data-management.service';

/**
 * Main component để render giao diện dựa trên cấu hình từ DynamicLayoutBuilder
 * Hỗ trợ 2 chế độ hiển thị: view (chỉ xem) và form (có thể chỉnh sửa)
 * 
 * Features:
 * - Header với Permission Selector và Tab Switcher (View/Form)
 * - Content area render sections theo layout
 * - State management cho current view mode và permission profile
 * - Responsive design với Bootstrap
 * - Hỗ trợ i18n với ngx-translate
 */
@Component({
  selector: 'app-dynamic-layout-renderer',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    TranslateModule,
    PermissionSelectorComponent,
    SectionComponent
  ],
  templateUrl: './dynamic-layout-renderer.component.html',
  styleUrls: ['./dynamic-layout-renderer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: []
})
export class DynamicLayoutRendererComponent implements OnInit, OnDestroy {

  /**
   * Configuration input chứa tất cả dữ liệu cần thiết
   */
  @Input({ required: true }) config!: DynamicLayoutRendererConfig;

  /**
   * Event emit khi có thay đổi trong form data
   * Emit DynamicLayoutRendererFormData khi form có thay đổi
   */
  @Output() formDataChange = new EventEmitter<DynamicLayoutRendererFormData>();

  // Inject services
  private flashMessageService = inject(FlashMessageService);

  // Create dedicated FormDataManagementService instance for this component
  private formDataService = new FormDataManagementService();

  // Subscriptions để cleanup
  private subscriptions = new Subscription();

  // Signals để quản lý state
  currentViewMode = signal<'view' | 'form'>('view');
  currentPermissionProfileId = signal<string>('');
  currentPermissionProfile = signal<FieldPermissionProfile | null>(null);
  visibleSections = signal<Section[]>([]);

  // Form mode state
  isFormEditMode = signal<boolean>(false);
  isFormSaving = signal<boolean>(false);

  // Computed signals
  permissionSelectorConfig = computed<PermissionSelectorConfig>(() => ({
    permissionProfiles: this.config.fieldDefaultSettings.permissionProfiles,
    currentPermissionProfileId: this.currentPermissionProfileId(),
    showSelector: this.config.showPermissionProfiles
  }));

  sectionConfigs = computed<SectionConfig[]>(() => {
    const profile = this.currentPermissionProfile();
    if (!profile) return [];

    return this.visibleSections().map(section => ({
      config: this.config,
      section: section,
      currentViewMode: this.currentViewMode(),
      currentPermissionProfile: profile,
      // Pass form data service để field components có thể access
      formDataService: this.isFormEditMode() ? this.formDataService : undefined
    }));
  });

  // Computed signal cho form state
  formState = computed(() => this.formDataService.formState());

  // Computed signal để check có thể save không
  canSave = computed(() => {
    const formState = this.formState();
    return this.isFormEditMode() && formState.isDirty && formState.isValid && !formState.isLoading;
  });

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    // Cleanup subscriptions
    this.subscriptions.unsubscribe();

    // Cleanup FormDataManagementService instance
    this.formDataService.destroy();
  }

  /**
   * Khởi tạo component dựa trên config
   */
  private initializeComponent(): void {
    // Khởi tạo view mode
    this.currentViewMode.set(this.config.defaultView);

    // Khởi tạo permission profile
    this.currentPermissionProfileId.set(this.config.currentPermissionProfileId);
    this.updateCurrentPermissionProfile();

    // Khởi tạo visible sections
    this.visibleSections.set(this.config.sections);

    // Khởi tạo form edit mode nếu được enable
    this.initializeFormEditMode();
  }

  /**
   * Khởi tạo Form Edit Mode nếu được enable trong config
   */
  private initializeFormEditMode(): void {
    if (this.config.enableEditMode) {
      this.isFormEditMode.set(true);

      // Collect tất cả fields từ visible sections để validate
      const allFields = this.visibleSections().flatMap(section => section.fields);

      // Initialize form data service với initial values và fields
      const initialValues = this.config.formValues || {};
      this.formDataService.initializeForm(initialValues, allFields);

      // Switch to form mode nếu đang ở view mode
      if (this.currentViewMode() === 'view') {
        this.currentViewMode.set('form');
      }
    }
  }

  /**
   * Cập nhật current permission profile object từ ID
   */
  private updateCurrentPermissionProfile(): void {
    const profile = this.config.fieldDefaultSettings.permissionProfiles.find(
      (p: FieldPermissionProfile) => p._id === this.currentPermissionProfileId()
    );
    this.currentPermissionProfile.set(profile || null);
  }

  /**
   * Xử lý khi thay đổi permission profile
   * @param event Event từ PermissionSelectorComponent
   */
  onPermissionProfileChange(event: PermissionProfileChangeEvent): void {
    console.log('🔄 Permission profile changed:', {
      oldProfileId: this.currentPermissionProfileId(),
      newProfileId: event.permissionProfileId,
      newProfile: event.permissionProfile
    });

    this.currentPermissionProfileId.set(event.permissionProfileId);
    this.currentPermissionProfile.set(event.permissionProfile);

    console.log('✅ Permission profile updated:', {
      currentProfileId: this.currentPermissionProfileId(),
      currentProfile: this.currentPermissionProfile()
    });
  }

  /**
   * Xử lý khi thay đổi view mode từ tab switcher
   * @param viewMode View mode mới được chọn
   */
  onViewModeChange(viewMode: 'view' | 'form'): void {
    this.currentViewMode.set(viewMode);
  }

  /**
   * Kiểm tra xem có nên hiển thị permission selector hay không
   */
  shouldShowPermissionSelector(): boolean {
    return this.config.showPermissionProfiles && 
           this.config.fieldDefaultSettings.permissionProfiles.length > 0;
  }

  /**
   * Kiểm tra xem có nên hiển thị tab switcher hay không
   */
  shouldShowTabSwitcher(): boolean {
    return this.config.showAllTab;
  }

  /**
   * Kiểm tra xem có sections để hiển thị hay không
   */
  hasSectionsToRender(): boolean {
    return this.visibleSections().length > 0;
  }

  /**
   * Lấy index của tab hiện tại cho MatTabs
   */
  getCurrentTabIndex(): number {
    return this.currentViewMode() === 'view' ? 0 : 1;
  }

  /**
   * Xử lý khi thay đổi tab trong MatTabs
   * @param index Index của tab được chọn
   */
  onTabChange(index: number): void {
    const viewMode = index === 0 ? 'view' : 'form';
    this.onViewModeChange(viewMode);
  }

  // ===== FORM EDIT MODE METHODS =====

  /**
   * Handle form save action
   */
  async onFormSave(): Promise<void> {
    if (!this.canSave() || !this.config.onFormSave) {
      return;
    }

    try {
      // Set loading state
      this.isFormSaving.set(true);
      this.formDataService.setLoading(true);

      // Build form data
      const formData = this.formDataService.buildFormData();

      // Call the save callback
      await this.config.onFormSave(formData);

      // Reset form dirty state (keep edit mode active)
      this.formDataService.resetForm();

      // Show success message
      this.flashMessageService.success('FORM_MESSAGES.SAVE_SUCCESS');

    } catch (error) {
      console.error('Form save error:', error);

      // Show error message
      this.flashMessageService.error('FORM_MESSAGES.SAVE_ERROR');

    } finally {
      // Clear loading state
      this.isFormSaving.set(false);
      this.formDataService.setLoading(false);
    }
  }

  /**
   * Handle form cancel action
   */
  onFormCancel(): void {
    if (!this.isFormEditMode()) {
      return;
    }

    // Reset form to initial values với validation
    const allFields = this.visibleSections().flatMap(section => section.fields);
    const initialValues = this.config.formValues || {};
    this.formDataService.initializeForm(initialValues, allFields);

    // Show cancel message
    this.flashMessageService.info('FORM_MESSAGES.CHANGES_DISCARDED');
  }

  /**
   * Handle field value change từ field components
   */
  onFieldValueChange(event: { fieldId: string; value: FieldValue }): void {
    if (!this.isFormEditMode()) {
      return;
    }

    console.log('onFieldValueChange', event);

    // Find field để validate
    const field = this.findFieldById(event.fieldId);
    if (field) {
      this.formDataService.updateFieldValue(event.fieldId, event.value, field);

      // Emit form data change event
      const formData = this.formDataService.buildFormData();
      this.formDataChange.emit(formData);
    }
  }

  /**
   * Tìm field theo ID trong tất cả sections
   */
  private findFieldById(fieldId: string): Field | null {
    for (const section of this.visibleSections()) {
      const field = section.fields.find(f => f._id === fieldId);
      if (field) {
        return field;
      }
    }
    return null;
  }
}
