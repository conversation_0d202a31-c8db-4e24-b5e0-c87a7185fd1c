import { Injectable, signal, computed } from '@angular/core';
import { Field } from '@domain/entities/field.entity';
import {
  FieldFilter,
  FieldFiltersState,
  FilterChangeEvent,
  FilterValue,
  FIELD_OPERATORS,
  OperatorConfig
} from '../models/view/field-filter-view.model';
import { SavedFilter } from '@shared/models/view/field-filters.model';
import { filterFieldsBySearchTerm } from '../utils/search.utils';

@Injectable({
  providedIn: 'root'
})
export class FieldFiltersService {
  // Signal để quản lý state của filters
  private readonly _filtersState = signal<FieldFiltersState>({ filters: [] });

  // Signal để quản lý saved filters
  private readonly _savedFilters = signal<SavedFilter[]>([]);

  // Signal để track current saved filter being applied
  private readonly _currentSavedFilterId = signal<string | null>(null);

  // Signal để quản lý search input
  private readonly _searchInput = signal<string>('');

  // Computed signals để expose data
  readonly filtersState = this._filtersState.asReadonly();
  readonly savedFilters = this._savedFilters.asReadonly();
  readonly currentSavedFilterId = this._currentSavedFilterId.asReadonly();
  readonly searchInput = this._searchInput.asReadonly();

  readonly activeFilters = computed(() =>
    this._filtersState().filters.filter(filter => filter.isActive)
  );
  readonly hasActiveFilters = computed(() => this.activeFilters().length > 0);

  // Computed signal để lọc fields dựa trên search input
  readonly filteredFields = computed(() => {
    const allFields = this._filtersState().filters.map(filter => filter.field);
    const searchTerm = this._searchInput();
    return filterFieldsBySearchTerm(allFields, searchTerm);
  });

  // Computed để kiểm tra xem filter hiện tại có thay đổi so với saved filter không
  readonly hasUnsavedChanges = computed(() => {
    const currentSavedFilterId = this._currentSavedFilterId();
    if (!currentSavedFilterId) return false;

    const savedFilter = this._savedFilters().find(f => f.id === currentSavedFilterId);
    if (!savedFilter) return false;

    return !this.compareFilters(this.getActiveFiltersData(), savedFilter.filters);
  });

  /**
   * Khởi tạo filters từ danh sách fields
   * @param fields - Danh sách các field để tạo filter
   */
  initializeFilters(fields: Field[]): void {
    const filters: FieldFilter[] = fields.map(field => ({
      field,
      isActive: false,
      filterValue: undefined
    }));

    this._filtersState.set({ filters });
  }

  /**
   * Cập nhật trạng thái của một filter
   * @param fieldId - ID của field
   * @param isActive - Trạng thái active
   * @param filterValue - Giá trị filter (optional)
   */
  updateFilter(fieldId: string, isActive: boolean, filterValue?: FilterValue): void {
    const currentState = this._filtersState();
    const updatedFilters = currentState.filters.map(filter => {
      if (filter.field._id === fieldId) {
        return {
          ...filter,
          isActive,
          filterValue: isActive ? filterValue : undefined
        };
      }
      return filter;
    });

    this._filtersState.set({ filters: updatedFilters });
  }

  /**
   * Lấy filter theo field ID
   * @param fieldId - ID của field
   * @returns FieldFilter hoặc undefined
   */
  getFilterByFieldId(fieldId: string): FieldFilter | undefined {
    return this._filtersState().filters.find(filter => filter.field._id === fieldId);
  }

  /**
   * Lấy danh sách operators cho một field type
   * @param fieldType - Loại field
   * @returns Danh sách operators
   */
  getOperatorsForFieldType(fieldType: string): OperatorConfig[] {
    return FIELD_OPERATORS[fieldType] || [];
  }

  /**
   * Kiểm tra xem operator có cần input không
   * @param fieldType - Loại field
   * @param operator - Operator
   * @returns true nếu cần input
   */
  operatorRequiresInput(fieldType: string, operator: string): boolean {
    const operators = this.getOperatorsForFieldType(fieldType);
    const operatorConfig = operators.find(op => op.value === operator);
    return operatorConfig?.requiresInput || false;
  }

  /**
   * Lấy input type cho operator
   * @param fieldType - Loại field
   * @param operator - Operator
   * @returns Input type
   */
  getInputTypeForOperator(fieldType: string, operator: string): string | undefined {
    const operators = this.getOperatorsForFieldType(fieldType);
    const operatorConfig = operators.find(op => op.value === operator);
    return operatorConfig?.inputType;
  }

  /**
   * Xóa tất cả filters
   */
  clearAllFilters(): void {
    const currentState = this._filtersState();
    const clearedFilters = currentState.filters.map(filter => ({
      ...filter,
      isActive: false,
      filterValue: undefined
    }));

    this._filtersState.set({ filters: clearedFilters });
  }

  /**
   * Lấy filter value mặc định cho field type và operator
   * @param fieldType - Loại field
   * @param operator - Operator
   * @returns Default filter value
   */
  getDefaultFilterValue(fieldType: string, operator: string): FilterValue | undefined {
    // Tạo default value dựa trên field type và operator
    switch (fieldType) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
      case 'textarea':
        return { operator: operator as any, value: '' };

      case 'number':
      case 'decimal':
      case 'currency':
      case 'percent':
        if (operator === 'between' || operator === 'not_between') {
          return { operator: operator as any, minValue: 0, maxValue: 0 };
        }
        return { operator: operator as any, value: 0 };

      case 'date':
      case 'datetime':
        if (operator === 'age_in' || operator === 'due_in' || operator === 'previous' || operator === 'next') {
          return { operator: operator as any, timeValue: 1, timeUnit: 'days' };
        }
        if (operator === 'between' || operator === 'not_between') {
          return { operator: operator as any, minValue: '', maxValue: '' };
        }
        if (operator === 'on' || operator === 'before' || operator === 'after') {
          return { operator: operator as any, value: '' };
        }
        return { operator: operator as any };

      case 'picklist':
      case 'multi-picklist':
        return { operator: operator as any, values: [] };

      case 'checkbox':
        return { operator: operator as any, value: 'selected' };

      default:
        return undefined;
    }
  }

  /**
   * Validate filter value
   * @param fieldType - Loại field
   * @param filterValue - Giá trị filter
   * @returns true nếu valid
   */
  validateFilterValue(fieldType: string, filterValue: FilterValue): boolean {
    if (!filterValue) return false;

    const operator = filterValue.operator;
    const requiresInput = this.operatorRequiresInput(fieldType, operator);

    if (!requiresInput) return true;

    // Validate dựa trên field type và operator
    switch (fieldType) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
      case 'textarea':
        const textValue = (filterValue as any).value;
        return typeof textValue === 'string' && textValue.trim().length > 0;

      case 'number':
      case 'decimal':
      case 'currency':
      case 'percent':
        if (operator === 'between' || operator === 'not_between') {
          const numFilter = filterValue as any;
          return typeof numFilter.minValue === 'number' &&
                 typeof numFilter.maxValue === 'number' &&
                 numFilter.minValue <= numFilter.maxValue;
        }
        const numValue = (filterValue as any).value;
        return typeof numValue === 'number';

      case 'date':
      case 'datetime':
        if (operator === 'age_in' || operator === 'due_in' || operator === 'previous' || operator === 'next') {
          const dateFilter = filterValue as any;
          return typeof dateFilter.timeValue === 'number' &&
                 dateFilter.timeValue > 0 &&
                 ['days', 'weeks', 'months', 'years'].includes(dateFilter.timeUnit);
        }
        if (operator === 'between' || operator === 'not_between') {
          const dateRangeFilter = filterValue as any;
          return typeof dateRangeFilter.minValue === 'string' &&
                 typeof dateRangeFilter.maxValue === 'string' &&
                 dateRangeFilter.minValue.length > 0 &&
                 dateRangeFilter.maxValue.length > 0;
        }
        if (operator === 'on' || operator === 'before' || operator === 'after') {
          const dateValueFilter = filterValue as any;
          return typeof dateValueFilter.value === 'string' && dateValueFilter.value.length > 0;
        }
        return true;

      case 'picklist':
      case 'multi-picklist':
        const picklistFilter = filterValue as any;
        return Array.isArray(picklistFilter.values) && picklistFilter.values.length > 0;

      case 'checkbox':
        const checkboxFilter = filterValue as any;
        return ['selected', 'not_selected'].includes(checkboxFilter.value);

      default:
        return false;
    }
  }

  /**
   * Tạo event khi filter thay đổi
   * @param fieldId - ID của field
   * @param isActive - Trạng thái active
   * @param filterValue - Giá trị filter
   * @returns FilterChangeEvent
   */
  createFilterChangeEvent(fieldId: string, isActive: boolean, filterValue?: FilterValue): FilterChangeEvent {
    return {
      fieldId,
      isActive,
      filterValue
    };
  }

  // ==================== SEARCH FUNCTIONALITY ====================

  /**
   * Cập nhật search input
   * @param searchTerm - Từ khóa tìm kiếm
   */
  updateSearchInput(searchTerm: string): void {
    this._searchInput.set(searchTerm.trim());
  }

  /**
   * Xóa search input
   */
  clearSearchInput(): void {
    this._searchInput.set('');
  }

  /**
   * Lấy danh sách filters đã được lọc theo search input
   * @returns Danh sách FieldFilter đã lọc
   */
  getFilteredFilters(): FieldFilter[] {
    const allFilters = this._filtersState().filters;
    const searchTerm = this._searchInput();

    if (!searchTerm) {
      return allFilters;
    }

    const filteredFields = filterFieldsBySearchTerm(
      allFilters.map(f => f.field),
      searchTerm
    );

    return allFilters.filter(filter =>
      filteredFields.some(field => field._id === filter.field._id)
    );
  }

  // ==================== SAVED FILTERS MANAGEMENT ====================

  /**
   * Khởi tạo saved filters
   * @param savedFilters - Danh sách saved filters
   */
  initializeSavedFilters(savedFilters: SavedFilter[]): void {
    this._savedFilters.set(savedFilters);
  }

  /**
   * Lưu filter mới
   * @param name - Tên filter
   * @param filters - Danh sách filter data
   * @returns ID của saved filter mới
   */
  saveFilter(name: string, filters?: Array<{fieldId: string; filterValue?: FilterValue}>): string {
    const filtersData = filters || this.getActiveFiltersData();
    const newFilter: SavedFilter = {
      id: this.generateUUID(),
      name,
      filters: filtersData
    };

    const currentSavedFilters = this._savedFilters();
    this._savedFilters.set([...currentSavedFilters, newFilter]);
    this._currentSavedFilterId.set(newFilter.id);

    return newFilter.id;
  }

  /**
   * Cập nhật saved filter hiện tại
   * @param filterId - ID của filter cần cập nhật
   * @param filters - Danh sách filter data mới (optional)
   */
  updateSavedFilter(filterId: string, filters?: Array<{fieldId: string; filterValue?: FilterValue}>): void {
    const filtersData = filters || this.getActiveFiltersData();
    const currentSavedFilters = this._savedFilters();

    const updatedFilters = currentSavedFilters.map(filter =>
      filter.id === filterId
        ? { ...filter, filters: filtersData }
        : filter
    );

    this._savedFilters.set(updatedFilters);
  }

  /**
   * Cập nhật tên của saved filter
   * @param filterId - ID của filter cần cập nhật tên
   * @param newName - Tên mới cho filter
   */
  updateSavedFilterName(filterId: string, newName: string): void {
    const currentSavedFilters = this._savedFilters();

    const updatedFilters = currentSavedFilters.map(filter =>
      filter.id === filterId
        ? { ...filter, name: newName.trim() }
        : filter
    );

    this._savedFilters.set(updatedFilters);
  }

  /**
   * Xóa saved filter
   * @param filterId - ID của filter cần xóa
   */
  deleteSavedFilter(filterId: string): void {
    const currentSavedFilters = this._savedFilters();
    const updatedFilters = currentSavedFilters.filter(filter => filter.id !== filterId);

    this._savedFilters.set(updatedFilters);

    // Reset current saved filter nếu đang được chọn
    if (this._currentSavedFilterId() === filterId) {
      this._currentSavedFilterId.set(null);
    }
  }

  /**
   * Áp dụng saved filter
   * @param filterId - ID của saved filter
   */
  applySavedFilter(filterId: string): void {
    const savedFilter = this._savedFilters().find(f => f.id === filterId);
    if (!savedFilter) return;

    // Clear tất cả filters hiện tại
    this.clearAllFilters();

    // Áp dụng filters từ saved filter
    savedFilter.filters.forEach(filterData => {
      if (filterData.filterValue) {
        this.updateFilter(filterData.fieldId, true, filterData.filterValue);
      }
    });

    // Set current saved filter
    this._currentSavedFilterId.set(filterId);
  }

  /**
   * Lấy dữ liệu filters hiện tại đang active
   * @returns Array filter data
   */
  getActiveFiltersData(): Array<{fieldId: string; filterValue?: FilterValue}> {
    return this.activeFilters()
      .filter(filter => filter.field._id) // Lọc ra những filter có _id
      .map(filter => ({
        fieldId: filter.field._id!,
        filterValue: filter.filterValue
      }));
  }

  /**
   * So sánh hai danh sách filters
   * @param filters1 - Danh sách filter thứ nhất
   * @param filters2 - Danh sách filter thứ hai
   * @returns true nếu giống nhau
   */
  compareFilters(
    filters1: Array<{fieldId: string; filterValue?: FilterValue}>,
    filters2: Array<{fieldId: string; filterValue?: FilterValue}>
  ): boolean {
    if (filters1.length !== filters2.length) return false;

    // Sort theo fieldId để so sánh
    const sorted1 = [...filters1].sort((a, b) => a.fieldId.localeCompare(b.fieldId));
    const sorted2 = [...filters2].sort((a, b) => a.fieldId.localeCompare(b.fieldId));

    return sorted1.every((filter1, index) => {
      const filter2 = sorted2[index];
      return filter1.fieldId === filter2.fieldId &&
             JSON.stringify(filter1.filterValue) === JSON.stringify(filter2.filterValue);
    });
  }

  /**
   * Reset current saved filter tracking
   */
  resetCurrentSavedFilter(): void {
    this._currentSavedFilterId.set(null);
  }

  /**
   * Lấy saved filter theo ID
   * @param filterId - ID của saved filter
   * @returns SavedFilter hoặc undefined
   */
  getSavedFilterById(filterId: string): SavedFilter | undefined {
    return this._savedFilters().find(f => f.id === filterId);
  }

  /**
   * Generate UUID đơn giản
   * @returns UUID string
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}
