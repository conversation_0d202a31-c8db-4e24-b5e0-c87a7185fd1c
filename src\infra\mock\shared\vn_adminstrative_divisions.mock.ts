import { ProvinceList, DistrictList, WardList } from 'salehub_shared_contracts';
import { normalizeVietnameseStr } from '@shared/utils';

// Dữ liệu đầu vào (giả sử đã được định nghĩa trước)
// https://github.com/ThangLeQuoc/vietnamese-provinces-database/tree/master
const { vnProvincesData } = require('../raw_data/vn_provinces_db');

// Hàm convert sang định dạng mong muốn
function convertAdministrativeData(data: any) {
  // Khởi tạo các danh sách
  const provinceList: ProvinceList = [];
  const districtList: DistrictList = {};
  const wardList: WardList = {};

  // Duyệt qua từng tỉnh/thành phố
  data.forEach((province: any) => {
    // Thêm vào provinceList
    provinceList.push({
      code: province.Code,
      name: province.FullName,
      asciiName: normalizeVietnameseStr(province.FullName.toLowerCase()) as string
    });

    // Khởi tạo danh sách quận/huyện cho tỉnh này
    districtList[province.Code] = [];

    // Duyệt qua từng quận/huyện
    province.District.forEach((district: any) => {
      // Thêm vào districtList
      districtList[province.Code].push({
        code: district.Code,
        name: district.FullName,
        asciiName: normalizeVietnameseStr(district.FullName.toLowerCase()) as string,
        provinceCode: district.ProvinceCode
      });

      if(district.Ward?.length > 0) {
        // Khởi tạo danh sách phường/xã cho quận/huyện này
        wardList[district.Code] = [];

        // Duyệt qua từng phường/xã
        district.Ward.forEach((ward: any) => {
          // Thêm vào wardList
          wardList[district.Code].push({
            code: ward.Code,
            name: ward.FullName,
            asciiName: normalizeVietnameseStr(ward.FullName.toLowerCase()) as string,
            districtCode: ward.DistrictCode
          });
        });
      }
    });
  });

  return {
    provinceList,
    districtList,
    wardList
  };
}

export const { provinceList, districtList, wardList } = convertAdministrativeData(vnProvincesData);
