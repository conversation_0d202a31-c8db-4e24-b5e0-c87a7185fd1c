#!/usr/bin/env node
import { join, resolve } from 'path';
import { findUnusedTranslations } from '../find-unused-translations.js';

// Hàm ch<PERSON>h
async function main() {
  const args = process.argv.slice(2);
  const projectPath = args[0] && !args[0].startsWith('--') ? resolve(args[0]) : process.cwd();
  const i18nPath = join(projectPath, 'src/infra/i18n');
  const outputUnusedTranslationKeyFile = join(projectPath, 'scripts/translation-validator/exports/unused_translation_keys.js');
  const remove = args.includes('--remove');

  console.log(`Quét key translation không được sử dụng trong dự án: ${projectPath}`);
  console.log(`Thư mục i18n: ${i18nPath}`);
  if (remove) {
    console.log('Tùy chọn --remove: Sẽ xóa key translation không được sử dụng.');
  }

  const success = await findUnusedTranslations(projectPath, i18nPath, outputUnusedTranslationKeyFile, remove);

  process.exit(success ? 0 : 1);
}

// Chạy script
main().catch(error => {
  console.error(`Lỗi không xác định: ${error.message}`);
  process.exit(1);
});