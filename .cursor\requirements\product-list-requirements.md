<PERSON><PERSON><PERSON> yêu cầu cơ bản:  
- Luôn tuân thủ rules của [angular.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/angular.mdc) và [general.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/general.mdc).  
- Sử dụng MCP server từ [mcp.json](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/mcp.json) khi cần thiết để debug lỗi và view trên browser.  
- Tr<PERSON>ớ<PERSON> khi tự động chạy, nếu có gì cần làm rõ thì hỏi lại để rõ ràng các ý chính trước khi tự động chạy, không phải sửa đi sửa lại nhiều lần vì không hiểu ý.  

Interface sử dụng: [create_order.d.ts](mdc:frontend/frontend/frontend/frontend/frontend/frontend/shared_contracts/js/dist/requests/sales/create_order.d.ts)  
Mock data sử dụng: [create_order.ts](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/src/app/mock/sales/create_order.ts)  

Tôi muốn bạn tạo một component Angular với các yêu cầu sau:  

**Tên component:** `product-selection`  
**Đường dẫn:** `src/app/features/sales/components/product-selection`  
**Mục đích:**  
- Tạo một block "Chọn sản phẩm" giống như trong các hệ thống Point of Sale (POS):  
  - Dùng để chọn sản phẩm mới hoặc chỉnh sửa sản phẩm đã chọn.  
  - Thiết kế vuốt ngang (horizontal swipe) phù hợp với hệ thống POS trên máy tính để bàn cảm ứng chiều ngang.  

**@Input:**  
- Nhận 3 tham số:  
  - `list`: Lấy từ `mockProductList` trong file [text](../../src/app/mock/sales/create_order.ts) (`ProductList` chứa các sản phẩm với `productId`, `variants: Array<{ variantId: string, attributes: Array<{ name: string, value: string }> }>` và thêm field `nameAscii` được sinh ra từ `name` bằng cách bỏ dấu, lowercase, ví dụ: "Quần áo trẻ em" → `nameAscii: "quan ao tre em"`).  
  - `data`: Một mảng `Array<OrderItemDetails>` từ file [text](../../../shared_contracts/js/dist/entities/oms/order/order_components/order_item.d.ts).  
  - `settings`: Một object chứa `allowSellWhenOutOfStock: boolean` để kiểm soát việc bán khi hết hàng.  

**Logic khởi tạo:**  
- Khi component khởi tạo:  
  - Với các mảng `ProductList`, `mockBrandList`, `mockCategoryList`, thêm field `nameAscii` được sinh từ field `name` bằng cách bỏ dấu (ví dụ: "Quần áo trẻ em" → `nameAscii: "quan ao tre em"`) để hỗ trợ tìm kiếm không dấu.  
  - Kiểm tra `data` (`Array<OrderItemDetails>`). Với mỗi `OrderItemDetails`:  
    - Nếu `OrderItemDetails.product` có `variant.variantId` hoặc `OrderItemDetails.unit`:  
      - Tìm sản phẩm trong `list` (`ProductList`) có `productId` khớp với `OrderItemDetails.product._id` hoặc có `variants` chứa `variant.variantId` khớp với `OrderItemDetails.product.variant.variantId`.  
      - Nếu tìm thấy sản phẩm khớp:  
        - Đánh dấu sản phẩm trong `list` là `selected`.  
        - Clone sản phẩm gốc từ `list`.  
        - Thêm sản phẩm đã chỉnh sửa (với giá, `variant`, hoặc `unit` từ `OrderItemDetails`) vào `list` ngay bên cạnh sản phẩm gốc, cũng set class `selected`.  
        - Khi render giao diện, hiển thị thông tin `variant` (dựa trên `variantId` và `attributes`) và `unit` (dựa trên `OrderItemDetails.unit`) bên dưới tên sản phẩm.  
    - Nếu không có `variant` hoặc `unit`, giữ nguyên sản phẩm trong `list` mà không clone, nhưng đánh dấu `selected` nếu khớp với `OrderItemDetails.product._id`.  

**Logic chọn và clone product item:**  
- Khi người dùng nhấn vào một `product item`:  
  - Nếu sản phẩm có `variants` hoặc `units`:  
    - Mở `MAT_BOTTOM_SHEET` từ component `variant-selector-bottom-sheet.component.ts` trong [text](../../src/app/features/sales/dialogs/variant-selector-bottom-sheet/variant-selector-bottom-sheet.component.ts).  
    - Truyền dữ liệu vào `MAT_BOTTOM_SHEET`:  
      - `variants`: `productItem.variants`  
      - `currentValue`: `{ variant: selectedVariant, unit: selectedUnit }` (nếu đã chọn trước đó từ `data`, nếu không thì để trống)  
      - `units`: `productItem.units`  
    - Sau khi người dùng xác nhận (từ `MAT_BOTTOM_SHEET`), nhận output:  
      ```typescript
      {
        variant: { _id: string, attributes: Array<{ name: string, value: string }> } | null,
        unit: { unitName: string, conversionRate: number, isBaseUnit: boolean, price: number, cost?: number, barcode?: string }
      }
      ```  
    - Nếu `variant.variantId` khớp với một `variantId` trong `productItem.variants`, hoặc `unit` được chọn:  
      - Tạo một bản sao (clone) của `product item` gốc ngay bên cạnh trong `list` (vị trí index kế tiếp), giữ nguyên thông tin gốc và không đánh dấu `selected`.  
      - Cập nhật `product item` đã chọn với thông tin từ `variant` và `unit` (ví dụ: cập nhật giá dựa trên `unit.price`, thêm thông tin biến thể vào tên nếu có), set class `selected`.  
      - Khi render giao diện, hiển thị thông tin `variant` (dựa trên `variant.attributes`) và `unit` (dựa trên `unit.unitName`) bên dưới tên sản phẩm.  
  - Nếu sản phẩm không có `variants` hoặc `units`, đánh dấu sản phẩm là `selected` và hiển thị nút "+/-" như bình thường.  
- Hàm clone viết riêng để có thể gọi từ bên ngoài, vì component `ProductSelection` chỉ là 1 component con của component `CreateOrderComponent`. Sau này khi chỉnh sửa product item từ `CreateOrderComponent` sẽ có sự kiện gửi đến component `ProductSelection` để tạo clone product item.  

**Logic chọn variants và units:**  
- Khi người dùng nhấn vào một `product item`:  
  - Nếu sản phẩm có `variants` hoặc `units`:  
    - Mở `MAT_BOTTOM_SHEET` từ component `variant-selector-bottom-sheet.component.ts`.  
    - Truyền vào `MAT_BOTTOM_SHEET`:  
      - `variants`: `product.variants` (nếu có).  
      - `units`: `product.units` (nếu có).  
      - `currentValue`: Biến thể hoặc đơn vị hiện tại (nếu đang chỉnh sửa, lấy từ `data`).  
    - Sau khi người dùng chọn biến thể hoặc đơn vị tính và xác nhận:  
      - Nếu `variant.variantId` khớp với một `variantId` trong `product.variants`, hoặc `unit` được chọn:  
        - Cập nhật thông tin sản phẩm (tên, giá, v.v.) dựa trên lựa chọn.  
        - Clone sản phẩm gốc ngay bên cạnh để giữ nguyên tùy chọn ban đầu, không đánh dấu `selected`.  
        - Đánh dấu sản phẩm đã chọn là `selected`.  
        - Hiển thị thông tin `variant` (dựa trên `variant.attributes`) và `unit` (dựa trên `unit.unitName`) bên dưới tên sản phẩm.  
  - Ví dụ:  
    - "Áo thun" (`productId: "1"`) có `variants: [{variantId: "v1", attributes: [{size: "L", color: "Xanh"}]}, {variantId: "v2", attributes: [{size: "XL", color: "Đỏ"}]}]`, người dùng chọn "XL-Đỏ" → Tên cập nhật thành "Áo thun (XL-Đỏ)", giá theo biến thể, hiển thị "Size: XL, Color: Đỏ" bên dưới tên, đánh dấu `selected`, và clone "Áo thun" gốc.  
    - "Thuốc" (`productId: "2"`) có `units: [{unitName: "viên", price: 5000}, {unitName: "hộp", price: 400000}]`, người dùng chọn "hộp" → Giá cập nhật thành 400000, hiển thị "Unit: hộp" bên dưới tên, đánh dấu `selected`, và clone "Thuốc" gốc.  

**Logic tìm kiếm:**  
- Thêm một ô input tìm kiếm trong row header.  
- Khi người dùng nhập query:  
  - Chuyển query thành dạng không dấu, lowercase (ví dụ: "quần áo" → "quan ao").  
  - So sánh với `nameAscii` của sản phẩm trong `ProductList`.  
  - Hiển thị danh sách sản phẩm khớp với kết quả tìm kiếm, giữ nguyên các logic chọn/clone như trên.  
  - Ví dụ: Tìm "quan ao" hoặc "quần áo" đều trả về sản phẩm có `nameAscii: "Quan ao tre em"`.  

**Giao diện:**  
1. **Row trên cùng (Navigation):**  
   - Có 2 nút mũi tên: ">" (sang phải) và "<" (sang trái) để điều hướng danh sách sản phẩm.  
   - Sử dụng module `swiperjs` để hỗ trợ vuốt ngang (swipe left/right) thay cho nút ">" và "<".  
   - Chia danh sách sản phẩm thành các trang (pager), mỗi trang hiển thị tối đa số lượng sản phẩm dựa trên kích thước viewport của người dùng.  
   - Mỗi sản phẩm có 3 cột:  
     - **Cột 1:** Hình ảnh sản phẩm.  
     - **Cột 2:**  
       - Bên trên: Tên sản phẩm (nếu có `variant`, thêm thông tin như "Phở bò [Size: L]").  
       - Bên dưới: Giá sản phẩm (dựa trên `unit.price` nếu có).  
       - Dòng phụ: Hiển thị thông tin `variant` (ví dụ: "Size: L, Color: Xanh") và `unit` (ví dụ: "Unit: hộp") nếu đã chọn.  
       - Nếu `trackInventory = true`: Hiển thị số lượng tồn kho theo kho đã chọn (ví dụ: "Tồn kho: 20") bên dưới giá.  
     - **Cột 3:** Các nút điều khiển (bên phải).  
   - Chỉ render 3 danh sách tại một thời điểm: `prev`, `current`, `next` để tối ưu hiệu suất và hỗ trợ animation.  
   - Nút ">" bị disable ở trang cuối cùng, nút "<" bị disable ở trang đầu tiên.  

2. **Row header:**  
   - **Bên trái (Filter):**  
     - Ô input tìm kiếm: Hỗ trợ tìm kiếm không dấu dựa trên `nameAscii`.  
     - Bộ lọc:  
       - "Tất cả" (mặc định).  
       - `select form`: Lọc theo kho hàng, dữ liệu lấy từ `mockWarehouseList`.  
       - `select form`: Lọc theo danh mục, dữ liệu lấy từ `mockCategoryList` (thêm `nameAscii`), sử dụng `ngx-mat-select-search` với searchbox và virtual scroll để tối ưu khi danh sách dài.  
       - `select form`: Lọc theo thương hiệu, dữ liệu lấy từ `mockBrandList` (thêm `nameAscii`), sử dụng `ngx-mat-select-search` với searchbox và virtual scroll để tối ưu khi danh sách dài.  
   - **Bên phải:**  
     - Nút "Thêm mới sản phẩm":  
       - Khi nhấn, mở dialog từ component `product-form` trong [text](../../src/app/features/product/pages/product-form/product-form.component.ts).  
       - Sau khi hoàn thành form và sản phẩm mới được tạo, thêm sản phẩm đó vào `mockProductList` với `nameAscii` được sinh tự động.  

3. **Các row sản phẩm:**  
   - Mỗi row hiển thị vài `product item` (số lượng tùy thuộc vào viewport).  
   - Mỗi `product item` có 3 cột:  
     - **Cột 1:** Hình ảnh sản phẩm.  
     - **Cột 2:**  
       - Tên sản phẩm (kèm thông tin `variant` nếu có).  
       - Dòng phụ: Hiển thị thông tin `variant` (ví dụ: "Size: L, Color: Xanh") và `unit` (ví dụ: "Unit: hộp") nếu đã chọn.  
       - Giá (dựa trên `unit.price` nếu có).  
       - Nếu `trackInventory = true`: Hiển thị "Tồn kho: <số lượng>" theo kho đã chọn.  
     - **Cột 3:** Các nút điều khiển (bên phải):  
       - Nút "+" và "-" để tăng/giảm số lượng, ở giữa hiển thị tổng số lượng đã chọn.  
       - Nếu `trackInventory = true` và `settings.allowSellWhenOutOfStock = false`: Disable nút "+" khi số lượng chọn đạt giới hạn tồn kho.  
       - Khi sản phẩm được chọn, thêm class `selected` và hiển thị các nút "+/-".  
       - Ban đầu các nút này ẩn, chỉ hiện khi người dùng nhấn chọn sản phẩm.  
       - Khi nhấn "-" đến khi số lượng = 0, ẩn các nút và xóa class `selected`.  
       - Nếu sản phẩm có `linkedModifierGroupIds`:  
         - Hiển thị thêm một nút "Thêm tùy chọn".  
         - Khi nhấn nút này, mở `MAT_BOTTOM_SHEET` từ component `product-modifiers-sheet` trong [text](../../src/app/features/sales/components/product-modifiers-sheet/product-modifiers-sheet.component.ts).  
         - Sau khi chọn tùy chọn xong, cập nhật tên sản phẩm bên ngoài thành: `<Tên gốc> và <N> sản phẩm khác`.  
           - Ví dụ: "Phở bò" → "Phở bò và 2 sản phẩm khác" (nếu thêm gà và CocaCola).  
         - Clone sản phẩm gốc ngay bên cạnh, không đánh dấu `selected`.  
       - Nếu sản phẩm có `variants` hoặc `units`:  
         - Khi nhấn vào sản phẩm:  
           - Mở `MAT_BOTTOM_SHEET` từ `variant-selector-bottom-sheet.component.ts`.  
         - Sau khi chọn, cập nhật tên, giá, hiển thị thông tin `variant` và `unit` bên dưới tên, đánh dấu `selected`, rồi clone sản phẩm gốc.  

4. **Row cuối cùng:**  
   - Chứa 2 nút:  
     - **Nút "Chọn lại":** Reset toàn bộ lựa chọn hiện tại để người dùng bắt đầu chọn lại từ đầu.  
     - **Nút "Thêm vào đơn":** Hiển thị tổng số tiền của các món đã chọn (tính dựa trên số lượng và giá của các sản phẩm trong `data`, ưu tiên `unit.price` nếu có). Ví dụ: "Thêm vào đơn (150k)". Khi nhấn, thêm các sản phẩm đã chọn vào đơn hàng.  

**Ghi chú:**  
- Tham khảo bố cục giao diện trong file ![alt text](Untitled.png) để hiểu cách sắp xếp các nút và row.  
- Đảm bảo tối ưu hiệu suất khi danh sách sản phẩm rất dài.  
- Khi hiển thị tên và giá sản phẩm, ưu tiên sử dụng thông tin từ `variant` và `unit` nếu đã chọn.  
- Để phân biệt desktop và mobile, sử dụng breakpoint (ví dụ: < 768px là mobile).  

### QUAN TRỌNG:  
- Luôn dùng `ng build` để kiểm tra lỗi sau khi làm xong mỗi task.  
- Mỗi khi làm xong 1 task nào, cần ghi vào [text](../context/product-list-context.md) để sau đó làm không bị quên context cũ.  
