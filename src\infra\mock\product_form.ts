/**
 * Mock data cho form sản phẩm
 * Bao gồm dữ liệu mẫu cho ProductCategory, ProductBrand, ProductVariant, Supplier, Warehouse
 */

// Interface cho Category
export interface ProductCategory {
  _id: string;
  name: string;
  description?: string;
  parentId?: string;
  imageUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Interface cho Brand
export interface ProductBrand {
  _id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Interface cho Variant
export interface ProductVariant {
  _id: string;
  name: string;
  values: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}


// Interface cho Product
export interface Product {
  _id: string;
  name: string;
  sku: string;
  description?: string;
  imageUrl?: string;
  price: number;
  cost?: number;
  weight?: number;
  categories: string[] | ProductCategory[];
  brand?: string | ProductBrand;
  tags?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  variants?: {
    variantId: string;
    values: string[];
  }[];
  stock?: {
    warehouseId: string;
    supplierId?: string;
    locationId?: string;
    quantity: number;
  }[];
  units?: {
    baseUnit: string;
    additionalUnits?: {
      name: string;
      conversionValue: number;
      retailPrice?: number;
      wholesalePrice?: number;
      cost?: number;
      sku?: string;
    }[];
  };
  materials?: {
    productId: string;
    quantity: number;
    cost: number;
  }[];
}

// Interface cho Warehouse
export interface Warehouse {
  _id: string;
  name: string;
  address?: string;
  isDefault?: boolean;
  isActive: boolean;
}

// Interface cho Warehouse Location
export interface WarehouseLocation {
  _id: string;
  warehouseId: string;
  name: string;
  parentId?: string;
  level: number;
  path: string[];
  isActive: boolean;
}

// Mock data danh mục sản phẩm
export const mockProductCategories: ProductCategory[] = [
  {
    _id: 'cat1',
    name: 'Thực phẩm',
    description: 'Các sản phẩm thực phẩm',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'cat2',
    name: 'Đồ uống',
    description: 'Các loại đồ uống',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'cat3',
    name: 'Bánh kẹo',
    description: 'Các loại bánh kẹo',
    parentId: 'cat1',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'cat4',
    name: 'Rau củ quả',
    description: 'Các loại rau củ quả',
    parentId: 'cat1',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'cat5',
    name: 'Nước giải khát',
    description: 'Các loại nước giải khát',
    parentId: 'cat2',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  }
];

// Mock data thương hiệu
export const mockProductBrands: ProductBrand[] = [
  {
    _id: 'brand1',
    name: 'Coca-Cola',
    imageUrl: 'assets/images/brands/coca-cola.png',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'brand2',
    name: 'Pepsi',
    imageUrl: 'assets/images/brands/pepsi.png',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'brand3',
    name: 'Orion',
    imageUrl: 'assets/images/brands/orion.png',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'brand4',
    name: 'Vinamilk',
    imageUrl: 'assets/images/brands/vinamilk.png',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'brand5',
    name: 'TH True Milk',
    imageUrl: 'assets/images/brands/th-true-milk.png',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  }
];

// Mock data thuộc tính sản phẩm
export const mockProductVariants: ProductVariant[] = [
  {
    _id: 'var1',
    name: 'Màu sắc',
    values: ['Trắng', 'Đen', 'Đỏ', 'Xanh', 'Vàng'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'var2',
    name: 'Kích thước',
    values: ['S', 'M', 'L', 'XL', 'XXL'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'var3',
    name: 'Dung tích',
    values: ['250ml', '330ml', '500ml', '1.5L', '2L'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'var4',
    name: 'Hương vị',
    values: ['Vani', 'Socola', 'Dâu', 'Cam', 'Chanh'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  }
];



// Mock data kho hàng
export const mockWarehouses: Warehouse[] = [
  {
    _id: 'wh1',
    name: 'Kho tổng',
    address: 'Số 123, Đường Lê Lợi, Quận 1, TP.HCM',
    isDefault: true,
    isActive: true
  },
  {
    _id: 'wh2',
    name: 'Kho A',
    address: 'Số 456, Đường Nguyễn Huệ, Quận 1, TP.HCM',
    isDefault: false,
    isActive: true
  },
  {
    _id: 'wh3',
    name: 'Kho B',
    address: 'Số 789, Đường Hai Bà Trưng, Quận 3, TP.HCM',
    isDefault: false,
    isActive: true
  }
];

// Mock data sản phẩm
export const mockProducts: Product[] = [
  {
    _id: 'prod1',
    name: 'Coca-Cola lon',
    sku: 'CC-001',
    description: 'Nước ngọt Coca-Cola lon 330ml',
    imageUrl: 'assets/images/products/coca-cola.png',
    price: 10000,
    cost: 7000,
    categories: ['cat2', 'cat5'],
    brand: 'brand1',
    tags: ['nước ngọt', 'coca cola', 'giải khát'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    stock: [
      {
        warehouseId: 'wh1',
        supplierId: 'sup1',
        locationId: 'loc2',
        quantity: 100
      }
    ],
    units: {
      baseUnit: 'lon',
      additionalUnits: [
        {
          name: 'thùng',
          conversionValue: 24,
          retailPrice: 230000,
          wholesalePrice: 220000,
          cost: 168000,
          sku: 'CC-001-T'
        }
      ]
    }
  },
  {
    _id: 'prod2',
    name: 'Bánh Orion Chocopie',
    sku: 'OCP-001',
    description: 'Bánh Chocopie hộp 12 cái',
    imageUrl: 'assets/images/products/chocopie.png',
    price: 55000,
    cost: 45000,
    categories: ['cat1', 'cat3'],
    brand: 'brand3',
    tags: ['bánh', 'chocopie', 'orion'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    stock: [
      {
        warehouseId: 'wh1',
        supplierId: 'sup3',
        locationId: 'loc3',
        quantity: 50
      }
    ],
    units: {
      baseUnit: 'hộp',
      additionalUnits: [
        {
          name: 'cái',
          conversionValue: 1 / 12,
          retailPrice: 5000,
          cost: 3750,
          sku: 'OCP-001-C'
        },
        {
          name: 'thùng',
          conversionValue: 24,
          retailPrice: 1250000,
          wholesalePrice: 1200000,
          cost: 1080000,
          sku: 'OCP-001-T'
        }
      ]
    }
  },
  // Thêm các sản phẩm làm nguyên liệu
  {
    _id: 'ing1',
    name: 'Bột mì đa dụng',
    sku: 'BM-001',
    description: 'Bột mì đa dụng loại 1',
    price: 15000,
    cost: 12000, // giá vốn/kg
    categories: ['cat1'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    units: {
      baseUnit: 'kg'
    }
  },
  {
    _id: 'ing2',
    name: 'Đường trắng tinh luyện',
    sku: 'DT-001',
    description: 'Đường trắng tinh luyện loại A',
    price: 22000,
    cost: 18000, // giá vốn/kg
    categories: ['cat1'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    units: {
      baseUnit: 'kg'
    }
  },
  {
    _id: 'ing3',
    name: 'Trứng gà',
    sku: 'TG-001',
    description: 'Trứng gà tươi loại 1',
    price: 3500,
    cost: 3000, // giá vốn/quả
    categories: ['cat1'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    units: {
      baseUnit: 'quả'
    }
  },
  {
    _id: 'ing4',
    name: 'Sữa tươi thanh trùng',
    sku: 'ST-001',
    description: 'Sữa tươi thanh trùng nguyên chất',
    price: 30000,
    cost: 25000, // giá vốn/lít
    categories: ['cat1'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    units: {
      baseUnit: 'lít'
    }
  },
  {
    _id: 'ing5',
    name: 'Bơ lạt',
    sku: 'BL-001',
    description: 'Bơ lạt đóng gói',
    price: 45000,
    cost: 38000, // giá vốn/kg
    categories: ['cat1'],
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    units: {
      baseUnit: 'kg'
    }
  }
];
