<!-- Number Field Container -->
@if (isVisible()) {
  <div class="number-field-container" [class.read-only]="isReadOnly()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        {{ config.field.label }}
        @if (config.field.isRequired || config.field.required) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      <!-- Read-only icon for form mode with read permission -->
      @if (isReadOnlyState()) {
        <mat-icon
          class="read-only-icon ms-2"
          [matTooltip]="'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP' | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display formatted mock data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <mat-icon class="field-type-icon me-2">{{ getFieldIcon() }}</mat-icon>
          <span class="mock-value formatted-number">{{ mockValue() }}</span>
        </div>
      }
      
      <!-- FORM MODE: Display number input control -->
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <!-- Field icon prefix -->
          <mat-icon matIconPrefix class="field-type-icon">{{ getFieldIcon() }}</mat-icon>
          
          <!-- Number input element -->
          <input
            matInput
            type="number"
            [placeholder]="(config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.NUMBER') | translate"
            [formControl]="formControl()"
            [readonly]="isReadOnlyState()"
            [class.read-only-cursor]="isReadOnlyState()">
          
          <!-- Error messages -->
          @if (formControl().invalid && formControl().touched) {
            <mat-error>
              @if (formControl().hasError('required')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.REQUIRED' | translate }}
              }
              @else if (formControl().hasError('min')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MIN_VALUE' | translate: {min: formControl().getError('min').min} }}
              }
              @else if (formControl().hasError('max')) {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MAX_VALUE' | translate: {max: formControl().getError('max').max} }}
              }
              @else {
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_NUMBER' | translate }}
              }
            </mat-error>
          }
        </mat-form-field>
      }
    </div>

    <!-- Field Tooltip/Description -->
    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }


  </div>
}
