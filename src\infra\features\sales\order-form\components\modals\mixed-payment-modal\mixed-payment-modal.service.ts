import { Injectable } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { OrderPayment } from 'salehub_shared_contracts/entities/oms/order/order_components/order_payment';
import {
  MixedPaymentModalComponent,
  MixedPaymentModalData,
  MixedPaymentModalResult
} from './mixed-payment-modal.component';

@Injectable({
  providedIn: 'root'
})
export class MixedPaymentModalService {
  constructor(private responsiveModalService: ResponsiveModalService) {}

  /**
   * Mở modal thanh toán hỗn hợp
   * @param data Dữ liệu thanh toán ban đầu
   * @returns Promise<OrderPayment | undefined> Thông tin thanh toán sau khi cập nhật
   */
  async open(data: MixedPaymentModalData): Promise<MixedPaymentModalResult | undefined> {
    return this.responsiveModalService.open<
      MixedPaymentModalData,
      MixedPaymentModalResult,
      MixedPaymentModalComponent
    >(MixedPaymentModalComponent, {
      data,
      width: '800px',
      maxWidth: '95vw',
      panelClass: 'mixed-payment-modal'
    });
  }
}
