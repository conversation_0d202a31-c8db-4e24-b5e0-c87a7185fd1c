import { Injectable } from '@angular/core';
import { ExternalToast, toast, ToastT } from 'ngx-sonner';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class FlashMessageService {
  constructor(private router: Router) {};

  #msg(msg: string, type: 'error' | 'success' | 'info' | 'warning' | 'message', opts?: Opts) {
    const options: ExternalToast = {
      description: opts?.description, // Optional description
      duration: opts?.timeout ?? 10000, // Default timeout of 10 seconds
      closeButton: true, // Show close button
      action: opts?.action
    };

    if(opts?.actionLink && opts.actionLink?.url) {
      options.action = {
        label: opts.actionLink.text,
        onClick: (e) => {
          this.router.navigateByUrl(opts.actionLink!.url);
        }
      }
    }

    toast[type](msg, options);
  }

  error(msg: string, opts?: Opts) {
    this.#msg(msg, 'error', opts);
  }

  success(msg: string, opts?: Opts) {
    this.#msg(msg, 'success', opts);
  }

  warning(msg: string, opts?: Opts) {
    this.#msg(msg, 'warning', opts);
  }

  info(msg: string, opts?: Opts) {
    this.#msg(msg, 'info', opts);
  }

  message(msg: string, opts?: Opts) {
    this.#msg(msg, 'message', opts);
  }
}

type Opts = {
  description?: string,
  timeout?: number,
  disableTimeOut?: boolean,
  action?: ToastT['action'],
  actionLink?: {
    url: string,
    text: string
  }
};
