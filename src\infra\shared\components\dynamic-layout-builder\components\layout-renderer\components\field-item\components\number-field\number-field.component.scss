// Number Field Component Styles
.number-field-container {
  width: 100%;
  margin-bottom: 1rem;

  // Field label styling
  .field-label-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    .field-label {
      font-weight: 500;
      color: var(--bs-body-color);
      margin: 0;
      font-size: 0.875rem;

      .required-asterisk {
        color: var(--bs-danger);
        margin-left: 0.25rem;
      }
    }

    .read-only-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: var(--bs-warning);
    }
  }

  // Field value container
  .field-value-container {
    width: 100%;

    // View mode styling
    .field-view-value {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      background-color: var(--bs-light);
      border: 1px solid var(--bs-border-color);
      border-radius: 0.375rem;
      min-height: 3.5rem;

      .field-type-icon {
        color: var(--bs-secondary);
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }

      .mock-value {
        color: var(--bs-body-color);
        font-size: 0.875rem;

        &.formatted-number {
          font-weight: 500;
          font-family: 'Courier New', monospace;
        }
      }
    }

    // Form mode styling
    ::ng-deep .mat-mdc-form-field {
      width: 100%;

      .field-type-icon {
        color: var(--bs-secondary);
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }

      // Number suffix styling
      .number-suffix {
        color: var(--bs-secondary);
        font-weight: 500;
        font-size: 0.875rem;
      }

      // Read-only input styling
      .read-only-cursor {
        cursor: not-allowed;
        background-color: var(--bs-light);
      }

      // Error state styling
      &.mat-form-field-invalid {
        .field-type-icon {
          color: var(--bs-danger);
        }

        .number-suffix {
          color: var(--bs-danger);
        }
      }

      input[type="number"] {
        // Hide number input spinners
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        &[type=number] {
          -moz-appearance: textfield;
        }
      }
    }
  }

  // Field tooltip styling
  .field-tooltip {
    margin-top: 0.25rem;
    
    small {
      font-size: 0.75rem;
      line-height: 1.2;
    }
  }

  // Field hints styling
  .field-hints {
    margin-top: 0.25rem;
    
    small {
      font-size: 0.75rem;
      line-height: 1.2;
      color: var(--bs-secondary);
      font-style: italic;
    }
  }

  // Read-only container styling
  &.read-only {
    opacity: 0.8;

    .field-label {
      color: var(--bs-secondary);
    }
  }
}

// Currency specific styling
.number-field-container {
  &[data-field-type="currency"] {
    .field-view-value .mock-value {
      color: var(--bs-success);
      font-weight: 600;
    }

    ::ng-deep .mat-mdc-form-field {
      .number-suffix {
        color: var(--bs-success);
        font-weight: 600;
      }
    }
  }
}

// Percent specific styling
.number-field-container {
  &[data-field-type="percent"] {
    .field-view-value .mock-value {
      color: var(--bs-info);
    }

    ::ng-deep .mat-mdc-form-field {
      .number-suffix {
        color: var(--bs-info);
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .number-field-container {
    margin-bottom: 0.75rem;

    .field-label-container {
      margin-bottom: 0.375rem;

      .field-label {
        font-size: 0.8125rem;
      }
    }

    .field-value-container {
      .field-view-value {
        padding: 0.625rem;
        min-height: 3rem;

        .field-type-icon {
          font-size: 1.125rem;
          width: 1.125rem;
          height: 1.125rem;
        }

        .mock-value {
          font-size: 0.8125rem;
        }
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .number-field-container {
    .field-value-container {
      .field-view-value {
        background-color: var(--bs-dark);
        border-color: var(--bs-border-color-translucent);
      }
    }
  }
}
