import { CustomerAutocompleteRecord, Place, PosInvoice, PosProduct } from 'salehub_shared_contracts';
import { getDeliveryFullAddress } from '@features/cashier/utils/order.util';
import { InitDataStore } from '@core/store/init_data.store';
import { CashierOrderService } from './cashier-order.service';

export class CashierOrderCustomerService {
  service!: CashierOrderService;
  initStore!: InitDataStore;

  constructor(service: CashierOrderService, initStore: InitDataStore) {
    this.service = service;
    this.initStore = initStore;
  }

  updateInvoiceAddress(place: Place) {
    this.service.order.update(order => {
      order.customer.address = place;

      order.delivery.distance = place.distance?.distance;

      if(order.customer?.address?.fullAddress) {
        order.delivery.displayAddress = getDeliveryFullAddress(order.customer.address);
      }
      return order;
    });
  }

  removeInvoiceAddress() {
    this.service.order.update(order => {
      order.customer.address = undefined;
      order.delivery.distance = undefined;
      order.delivery.displayAddress = undefined;
      return order;
    });
  }

  clearCustomerValue(name: keyof PosInvoice['customer']) {
    this.service.order.update(order => {
      order.customer[name] = undefined;
      return order;
    });
  }

  cleanCustomerValue() {
    this.service.order.update(order => {
      if(order?.customer?.gender === 'none') {
        delete order.customer.gender;
      }
      return order;
    });
  }

  selectCustomer(record: CustomerAutocompleteRecord) {
    this.service.order.update(order => {
      if(record._id || record.phoneNumber) {
        order.customer = record;
        const storePlaceId = this.initStore.getData().store?.addressInfo?.placeId;
        if(storePlaceId) {
          if(record.address?.distances?.[storePlaceId]) {
            order.delivery ||= {} as any;
            order.delivery.distance = record.address?.distances?.[storePlaceId]?.distance;
          }
        }
      } else {
        switch(record.createNewUserWith) {
        case 'PHONE':
          order.customer = {
            phoneNumber: record.value
          };
          break;
        case 'ADDRESS':
          order.customer = {
            address: {
              fullAddress: record.value as string
            }
          };
          break;
        case 'NAME':
          order.customer = {
            name: record.value
          };
          break;
        }
      }
      return order;
    });
  }

  clearCustomer() {
    this.service.order.update(order => {
      order.customer = {};
      return order;
    });
  }
}
