Bạn là một chuyên gia về Angular 19, TypeScript, và Clean Architecture, tập trung vào việc tạo ra các ứng dụng web hiệu su<PERSON>t cao, d<PERSON> bảo tr<PERSON>, và tuân thủ các thực tiễn tốt nhất của Angular. Dưới đây là yêu cầu cập nhật để xây dựng `DynamicLayoutBuilderComponent`, một giao diện quản lý layout khách hàng tương tự Zoho Canvas Builder, sử dụng Angular Material CDK cho kéo-thả và Golden Layout để quản lý các section. Giao diện phục vụ các doanh nghiệp bán lẻ online trên các sàn TMĐT (Shopee, TikTok Shop, Lazada), tập trung vào các ngành: thời trang, mỹ phẩm, mẹ và bé, đồ gia dụng, công nghệ, th<PERSON> thao, s<PERSON>ch, thự<PERSON> ph<PERSON>, và dịch vụ số.

### Y<PERSON>u cầu chung
- **<PERSON>ôn ngữ trả lời**: <PERSON><PERSON><PERSON> trả lời bằng tiếng Việt.
- **Cấu trúc folder**: Tuân thủ cấu trúc trong [FOLDER_STRUCTURE.md](mdc:frontend/frontend/frontend/FOLDER_STRUCTURE.md).
- **Angular 19**: Sử dụng **standalone components**, **Signals** cho trạng thái, **OnPush Change Detection**, và **lazy loading**.
- **i18n**: Tích hợp `ngx-translate` theo [i18n.mdc](mdc:.cursor/rules/i18n.mdc), thêm key vào `src/infra/i18n/shared/dynamic-layout-builder` với format `SCREAMING_SNAKE_CASE`, chạy `compare-translations.js` để kiểm tra key trùng lặp và chuẩn hóa bản dịch.
- **Thư viện**: Sử dụng Angular Material CDK (`@angular/cdk/drag-drop`), Golden Layout, Angular Material (`@angular/material`), PrimeNG, Bootstrap, Tailwind CSS (đã import trong `styles.scss`).
- **Giao diện**: Responsive, sử dụng CSS Grid/Flexbox, dựa trên theme Metronic (https://keenthemes.com/metronic/tailwind/demo1/).
- **Hiệu suất**: Sử dụng `trackBy` trong `*ngFor`, `OnPush`, tối ưu Golden Layout (https://golden-layout.github.io/golden-layout/).

- Luôn tuân thủ rules của .cursor/rules/angular.mdc, .cursor/rules/requirements.mdc
- Sử dụng MCP server khi cần thiết để debug lỗi và view trên browser.
- Trước khi tự động chạy, nếu có gì cần làm rõ thì hỏi lại để rõ ràng các ý chính trước khi tự động chạy, không phải sửa đi sửa lại nhiều lần vì không hiểu ý.
- Bạn phải đọc toàn bộ rules trong .cursor/rules/*
- Bạn phải đọc toàn bộ docs trong docs/*
- Bạn phải đọc @FOLDER_STRUCTURE.md để hiểu rõ cấu trúc folder
- Kết nối với các MCP server hiện tại để tự động debug lỗi và xem preview trên chrome.
- Khi không còn lỗi nào, xem trên giao diện bằng MCP, xem có lỗi gì không và sửa. Tôi đã dựng sẵn server và watch changes rồi, bạn chỉ cần vào thông qua cổng 4200 và kiểm tra.
- File translate đã được cấu hình nằm ở public/assets/i18n nhưng url ra không có /public là tiền tố. KHÔNG ĐƯỢC PHÉP TẠO TRONG src/assets/i18n/*. Không được phép ghi đè lên các field có sẵn trong file i18n, chỉ được phép append thêm field vào.
- Bạn luôn tự thêm i18n vào file json, không phải tôi thêm bằng tay.
- Luôn luôn thêm 2 ngôn ngữ i18n vào @vi.json @en.json
- Không viết thêm các function để xử lý i18n, đọc @i18n-guide.md @ngx-translate-integration.md để tích hợp vào.
- Luôn thêm chi tiết comment trong code để giải thích logic bằng tiếng việt.
- Giao diện nên được viết bằng bootstrap và bắt buộc phải responsive
- Khi import vào angular, dùng các paths ngắn được định nghĩa trong @tsconfig.json như @core, @shared, @mock...
- KHÔNG ĐƯỢC PHÉP sửa các file quan trọng như app.component, layout/*... Tất cả logic của component con phải được định nghĩa trong folder của component con. Như  product thì chỉ gói gọn trong features/product. Tất cả router phải được export vào router con như product-routing.ts nằm trong features/product/product-routing.ts. Không export vào app.routing.ts. KHÔNG ĐƯỢC PHÉP đụng đến logic và giao diện của toàn ứng dụng.
- Từng bước step phải lưu lại vào .cursor/context/[taskname].md để tôi theo dõi và để bạn nhớ rõ những task đã làm, phục vụ cho bạn khi làm 1 task quá dài, vượt quá context dẫn đến việc bạn bị quên context. Các lần tiếp theo bạn phải đọc lại context xem đã làm những task nào, đang làm đến đâu để nạp lại context vào bộ nhớ.
- Không được viết 1 component quá dài, nên phân định rõ cái nào xử lý trong component và cái nào xử lý trong service. Nếu 1 component bao gồm nhiều thành phần, chia nó thành các component con và gọi đến các component con từ component cha. Component chỉ dùng để tương tác với UI, logic nghiệp vụ phải được đẩy xuống service. Nên chia service ngay trong folder component, ví dụ product/product.component.ts, product/product.service.ts
- Khi muốn test 1 chức năng, 1 dialog nào đó, viết thẳng vào @test-theme.component.ts, không được phép tạo component mới. Path để test là http://localhost:4200/#/test
- QUAN TRỌNG: Sau khi viết xong, Chạy ng build xem còn lỗi gì không và sửa.

  Interface:
  - [text](../../src/infra/shared/models/view/dynamic-layout-builder.model.ts)

  Modals:
  - [ConfirmModalComponent](../../src/infra/shared/modals/common/confirm-modal/confirm-modal.service.ts)
  - [FieldPermissionModalComponent](../../src/infra/shared/modals/field-permission/field-permission-modal.service.ts)
  - [FieldPropertiesModalComponent](../../src/infra/shared/modals/field-properties/field-properties-modal.service.ts)

  Viết tất cả vào [text](../../src/infra/shared/components/dynamic-layout-builder/*).
  thêm vào routing [text](../../src/infra/app.routes.ts): test-layout-builder. Test tại http://localhost:4200/#/test-layout-builder

### Modals
1. **ConfirmModalComponent**  
   - Path: `modals/common/confirm-modal/`  
   - Chức năng: Xác nhận xóa section/field.  

2. **FieldPermissionModalComponent**  
   - Path: `modals/field-permission/`  
   - Chức năng: Cấu hình quyền truy cập field.  

3. **FieldPropertiesModalComponent**  
   - Path: `modals/field-properties/`  
   - Chức năng: Chỉnh sửa thuộc tính field.  

### Yêu cầu cụ thể cho `DynamicLayoutBuilderComponent`
1. **Tổng quan giao diện**
   - **Mục tiêu**: Giao diện kéo-thả để tạo và quản lý layout khách hàng, tích hợp với các ngành bán lẻ online và TMĐT.
   - **Cấu trúc giao diện**:
     - **Bên trái (New Fields)**:
       - Block lớn `New Section` (button/block màu xanh, kích thước lớn) để kéo-thả tạo section mới.
       - Danh sách button cho **Field Types** với constraints:
         - `Text`: 
         - `Integer`: 
         - `Decimal`: 
         - `Percent`:
         - `Currency`: 
         - `Date`: 
         - `DateTime`: 
         - `Email`:
         - `Phone`: 
         - `Picklist`:
         - `MultiPicklist`:
         - `URL`: 
         - `TextArea`: 
         - `Checkbox`: 
         - `LongInteger`: 
     - **Bên phải (Layout hiện tại)**:
       - Nhiều section (panel trong Golden Layout), ví dụ: "Thông tin cơ bản", "Thông tin doanh nghiệp".
       - **Section header**:
         - **Bên trái**: Tên section (ví dụ: "Thông tin cơ bản"). Khi click vào tên hoặc nút pencil bên phải, tên chuyển thành `mat-form-field` để chỉnh sửa inline.
         - **Bên phải**:
           - Nút pencil (`mat-icon: edit`) để kích hoạt chỉnh sửa inline tên section.
           - Nút handle (`cdkDragHandle`) để kéo-thả sắp xếp section.
           - Icon gear mở `mat-menu` với tùy chọn:
             - **Sửa tên**: dùng chỉnh sửa inline  tên section.
             - **Xóa**: Xóa section và fields bên trong.
       - **Fields trong section**:
         - Mỗi field hiển thị dạng bảng với 3 cột:
           - **Cột 1 (Label)**: Tên field (ví dụ: "Tên khách hàng"). Khi click vào tên hoặc nút pencil bên phải, tên chuyển thành `mat-form-field` để chỉnh sửa inline. Field nào isRequired thì thêm dấu * màu đỏ bên cạnh
           - **Cột 2 (Loại field)**: Hiển thị loại field (text, picklist, v.v.).
           - **Cột 3 (Menu)**: Nút ba chấm (`mat-icon: more_vert`) mở `mat-menu` với:
             - **Đánh dấu trường bắt buộc**: Nhấn vào set field là isRequired = true
             - **Sửa thuộc tính**: Mở modal FieldPropertiesComponent
             - **Set Permission**: Mở modal FieldPermissionModalComponent
             - **Xóa trường**: Mở modal ConfirmModalComponent, sau khi xác nhận Xóa field khỏi section.
   - **Responsive**: Sử dụng CSS Grid, đảm bảo hoạt động tốt trên desktop và mobile.

2. **Tính năng chính**
   - **Kéo-thả**:
     - Sử dụng Angular Material CDK DragDrop để:
       - Kéo `New Section` để tạo section mới ở vị trí trên cùng, giữa, hoặc cuối.
       - Kéo **Field Types** vào section để thêm field mới.
       - Kéo-thả fields trong section để sắp xếp thứ tự.
       - Kéo-thả section (qua `cdkDragHandle`) để sắp xếp thứ tự sections.
     - Sử dụng Golden Layout để quản lý sections (panels), hỗ trợ kéo-thả và thay đổi kích thước. (https://golden-layout.github.io/golden-layout/)
   - **Chỉnh sửa inline**:
     - **Tên section**: Click vào tên hoặc nút pencil để chuyển thành `mat-form-field`. Nhấn Enter để lưu, Escape để hủy.
     - **Label field**: Click vào label hoặc nút pencil để chuyển thành `mat-form-field`. Nhấn Enter để lưu, Escape để hủy.
   - **Thêm section**:
     - Kéo `New Section` để tạo section mới với tiêu đề mặc định (ví dụ: "Section 1").
   - **Thêm custom field**:
     - Kéo **Field Type** vào section để tạo field mới với thuộc tính mặc định.
     - Chỉnh sửa thuộc tính qua `mat-menu` hoặc inline (cho label).
   - **Template ngành**:
     - Dropdown để chọn template ngành (thời trang, mỹ phẩm, mẹ và bé, v.v.).
     - Ví dụ template:
       - **Thời trang**: Section "Thông tin cơ bản" (Tên, Kích cỡ, Màu sắc), "Thông tin liên hệ" (Email, Số điện thoại).
       - **Mỹ phẩm**: Section "Thông tin cơ bản" (Tên, Loại da), "Sản phẩm yêu thích" (Picklist).
   - **Lưu và khôi phục layout**:
     - Lưu cấu hình (sections, fields, bố cục Golden Layout) vào `localStorage`.
     - Cung cấp nút gửi cấu hình qua API (mô phỏng bằng `console.log` hoặc HTTP POST).
   - **Xem trước dữ liệu**:
     - Một section (panel) trong Golden Layout hiển thị preview dữ liệu khách hàng dựa trên fields.

3. **Cấu trúc dữ liệu**: [text](../../src/infra/shared/models/view/dynamic-layout-builder.model.ts)
 
4. **Cấu trúc thư mục và phân chia component**

src/
├── infra/
│   ├── shared/
│   │   ├── components/
│   │   │   └── dynamic-layout-builder/
│   │   │       ├── dynamic-layout-builder.component.ts
│   │   │       ├── dynamic-layout-builder.component.html
│   │   │       ├── dynamic-layout-builder.component.scss
│   │   │       ├── dynamic-layout-builder.service.ts
│   │   │       ├── field-list/
│   │   │       │   ├── field-list.component.ts
│   │   │       │   ├── field-list.component.html
│   │   │       │   ├── field-list.component.scss
│   │   │       ├── field-item/
│   │   │       │   ├── field-item.component.ts
│   │   │       │   ├── field-item.component.html
│   │   │       │   ├── field-item.component.scss
│   │   │       ├── section/
│   │   │       │   ├── section.component.ts
│   │   │       │   ├── section.component.html
│   │   │       │   ├── section.component.scss
│   │   │       │   ├── section.service.ts
│   │   │       ├── new-section/
│   │   │       │   ├── new-section.component.ts
│   │   │       │   ├── new-section.component.html
│   │   │       │   ├── new-section.component.scss
│   │   │       ├── field-type-selector/
│   │   │       │   ├── field-type-selector.component.ts
│   │   │       │   ├── field-type-selector.component.html
│   │   │       │   ├── field-type-selector.component.scss
│   │   │       ├── template-selector/
│   │   │       │   ├── template-selector.component.ts
│   │   │       │   ├── template-selector.component.html
│   │   │       │   ├── template-selector.component.scss
│   │   │       ├── preview-panel/
│   │   │       │   ├── preview-panel.component.ts
│   │   │       │   ├── preview-panel.component.html
│   │   │       │   ├── preview-panel.component.scss
│   │   │       ├── golden-layout/
│   │   │       │   ├── golden-layout-adapter.component.ts
│   │   │       │   ├── golden-layout-adapter.component.html
│   │   │       │   ├── golden-layout-adapter.component.scss
│   │   │       │   ├── golden-layout.service.ts
│   │   ├── models/
│   │   │   └── view/
│   │   │       └── dynamic-layout-builder.model.ts
│   │   ├── modals/
│   │   │   ├── common/
│   │   │   │   └── confirm-modal/
│   │   │   │       ├── confirm-modal.component.ts
│   │   │   │       ├── confirm-modal.component.html
│   │   │   │       ├── confirm-modal.component.scss
│   │   │   │       ├── confirm-modal.service.ts
│   │   │   ├── field-permission/
│   │   │   │   ├── field-permission-modal.component.ts
│   │   │   │   ├── field-permission-modal.component.html
│   │   │   │   ├── field-permission-modal.component.scss
│   │   │   │   ├── field-permission-modal.service.ts
│   │   │   ├── field-properties/
│   │   │       ├── field-properties-modal.component.ts
│   │   │       ├── field-properties-modal.component.html
│   │   │       ├── field-properties-modal.component.scss
│   │   │       ├── field-properties-modal.service.ts
│   ├── app.routes.ts

4.1 Phân chia component
4.1.1. **DynamicLayoutBuilderComponent**  
   - Path: `dynamic-layout-builder.component.ts`  
   - Chức năng: Component cha, quản lý layout tổng thể, tích hợp Golden Layout, và điều phối kéo-thả.  

4.1.2. **FieldListComponent**  
   - Path: `field-list/field-list.component.ts`  
   - Chức năng: Hiển thị danh sách field types (Text, Picklist, v.v.) để kéo vào section.  

4.1.3. **FieldItemComponent**  
   - Path: `field-item/field-item.component.ts`  
   - Chức năng: Hiển thị một field trong section (3 cột: label, loại, menu), hỗ trợ chỉnh sửa inline và menu tùy chọn.  

4.1.4. **SectionComponent**  
   - Path: `section/section.component.ts`  
   - Chức năng: Quản lý một section (header, danh sách fields), hỗ trợ kéo-thả fields và chỉnh sửa tên inline.  

4.1.5. **NewSectionComponent**  
   - Path: `new-section/new-section.component.ts`  
   - Chức năng: Button/block để kéo-thả tạo section mới.  

4.1.6. **FieldTypeSelectorComponent**  
   - Path: `field-type-selector/field-type-selector.component.ts`  
   - Chức năng: Dropdown/button để chọn field type khi thêm field mới.  

4.1.7. **TemplateSelectorComponent**  
   - Path: `template-selector/template-selector.component.ts`  
   - Chức năng: Dropdown để chọn template ngành (thời trang, mỹ phẩm, v.v.).  

4.1.8. **PreviewPanelComponent**  
   - Path: `preview-panel/preview-panel.component.ts`  
   - Chức năng: Hiển thị preview dữ liệu khách hàng dựa trên fields.  

4.1.9. **GoldenLayoutAdapterComponent**  
   - Path: `golden-layout/golden-layout-adapter.component.ts`  
   - Chức năng: Tích hợp Golden Layout để quản lý sections/panels, hỗ trợ kéo-thả và resize.  

4.2 Services
4.2.1. **DynamicLayoutBuilderService**  
   - Path: `dynamic-layout-builder.service.ts`  
   - Chức năng: Xử lý logic nghiệp vụ (lưu/khôi phục layout, gọi API).  

4.2.2. **SectionService**  
   - Path: `section/section.service.ts`  
   - Chức năng: Quản lý logic section (thêm, xóa, sắp xếp fields).  

4.2.3. **GoldenLayoutService**  
   - Path: `golden-layout/golden-layout.service.ts`  
   - Chức năng: Quản lý cấu hình và trạng thái Golden Layout.  





7. **Hiệu suất và tối ưu**
   - Sử dụng **Signals** để quản lý trạng thái sections và fields.
   - Sử dụng `trackBy` trong `*ngFor`:
     ```typescript
     trackBySection(index: number, section: Section): string {
       return section.id;
     }
     trackByField(index: number, field: CustomField): number {
       return field.id;
     }
     ```
   - Components sử dụng **OnPush Change Detection**.
   - Tối ưu Golden Layout để xử lý nhiều section.

8. **Yêu cầu bổ sung**
   - **Section header**:
     - Tên section có thể chỉnh sửa inline khi click vào tên hoặc nút pencil (`mat-icon: edit`). Chuyển thành `mat-form-field` với input, lưu khi nhấn Enter, hủy khi nhấn Escape.
   - **Fields trong section**:
     - Hiển thị dạng bảng với 3 cột:
       - **Cột 1 (Label)**: Hiển thị label, click vào label hoặc nút pencil (`mat-icon: edit`) để chuyển thành `mat-form-field` chỉnh sửa inline. Lưu khi nhấn Enter, hủy khi nhấn Escape.
       - **Cột 2 (Loại field)**: Hiển thị loại field (text, picklist, v.v.).
       - **Cột 3 (Menu)**: Nút ba chấm (`mat-icon: more_vert`) mở `mat-menu` với tùy chọn sửa thuộc tính và xóa field.

9. **Test**
   - Test giao diện tại `http://localhost:4200/#/test-layout-builder`.
   - Chạy `ng build` để kiểm tra lỗi.
   - Kiểm tra responsive trên Chrome DevTools (desktop và mobile).

