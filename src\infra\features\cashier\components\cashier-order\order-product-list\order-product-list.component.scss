@use '../../../../../shared/styles/_variable' as *;

$sheetMaxHeight: 95vh;
$menuNavigationHeight: 80;
$catHeight: 50;
$toppingNameHeight: 70;


.menu {
  position: relative;
  width: 100%;
  padding-bottom: ($menuNavigationHeight + $mobileBottomNavHeight + 10)+px;
}
.menu-btn {
  position: fixed;
  height: $menuNavigationHeight+px;
  bottom: $mobileBottomNavHeight+px;
  width: inherit;
  background-color: #fff;
  padding: 10px;

  transition: bottom .3s;

  .total {
    font-size: 13px;
    font-weight: 500;
  }
}

.invoice-header {
  padding: 5px 20px 0 20px;

  &-container {
    height: $catHeight+px;
    overflow: hidden;
    position: sticky;
    top: 0;
    border-bottom: 2px solid #565a5d;
  }

  li {
    margin-right: 10px;
    padding: 10px 15px;
    white-space: nowrap;
    cursor: pointer;
    border-bottom: 3px solid #d7e0e5;

    &.active {
      background-color: #607d8b;
      border-color: #607d8b;
      color: #fff;
    }
  }
}

.invoice-items {
  font-size: 16px;
  padding: 10px;
  white-space: nowrap;
  overflow: hidden;

  li {
    user-select: none;
    background-color: #fefefe;
    width: calc(100% - 12px);
    margin: 6px;
    padding: 7px 10px;
    border-radius: 3px;
    border: 1px solid #c2c3c3;
    min-height: 65px;

    .info {
      width: calc(100% - 170px);
      white-space: break-spaces;
    }

    .price, .instruction {
      font-size: 12px;
    }

    .instruction {
      white-space: normal;
    }

    &.selected {
      background-color: #ed8d00;
      border-color: #ed8d00;
      color: #fff;

      .quantity {
        width: 165px;

        .count {
        }
        .icon {
          border: 1px solid #ffe1b5;
          color: #fff1dd;
          padding: 5px;
        }
      }
    }
    b {
      font-weight: 500;
    }

    .edit {
      font-size: 18px;
      margin-left: 5px;
      cursor: pointer;
    }

    .quantity {
      .count {
        font-weight: 700;
        font-size: 15px;
      }
      .icon {
        border: 1px solid #bbc6d1;
        cursor: pointer;
        color: #879db6;
      }
    }
  }
}

.navbar-hidden {
  .invoice {
    .menu {
      padding-bottom: ($menuNavigationHeight + 10)+px;
    }
    .menu-btn {
      bottom: 0;
    }
  }
}



.invoice-topping {
  .topping-name {
    height: $toppingNameHeight+px;
    font-size: 17px;
    padding-top: 20px;
    white-space: nowrap;
  }
  .close-dialog {
    position: absolute;
    top: 5px;
    right: 5px;

    .icon {
      font-size: 32px;
    }
  }
  .mat-bottom-sheet-container {
    max-height: $sheetMaxHeight;
    font-size: inherit;
    padding: 0;
  }

  .menu {
    padding-bottom: $menuNavigationHeight+px;
  }
  .menu-btn {
    width: inherit;
    bottom: 0;
  }
}

.invoice-items-container {
  &.topping {
    .invoice-items {
      max-height: calc($sheetMaxHeight - #{$menuNavigationHeight + $catHeight + $toppingNameHeight}px);
      overflow: auto;
    }
  }
}

@media screen and (min-width: #{$breakpointMinHorizontalWidth}px) {
  .menu {
    padding-bottom: $menuNavigationHeight+px;
  }

  .menu-btn {
    width: calc(100% - 300px);
    bottom: 0;
  }

  .invoice-topping {
    .menu-btn {
      width: inherit;
    }
  }
}
