#### 1. Trang hiển thị danh sách layout (Product Layouts)

**<PERSON>ục tiêu**:  
Tạo trang hiển thị danh sách các layout từ `mockProductLayoutTemplate` dưới dạng các card, mỗi card hiển thị thông tin cơ bản của layout (tên template, số section, số field). Khi click vào card, điều hướng đến trang sửa layout (`product/layouts/edit`) với dữ liệu tương ứng.

**Folder path**:  
`src/infra/features/product/product-layout`

**Path URL**:  
`product/layouts`

**Y<PERSON>u cầu chi tiết**:  
- **Component**: `ProductLayoutComponent`  
  - Path: `src/infra/features/product/product-layout/product-layout.component.ts`  
  - Tạo component standalone, sử dụng **Angular 19**, **Signals** cho trạng thái, **OnPush Change Detection**.  
  - <PERSON>ad dữ liệu từ `mockProductLayoutTemplate` (import từ `@shared/models/view/dynamic-layout-builder.model`).  
  - <PERSON><PERSON><PERSON> thị danh sách dưới dạng card sử dụng **CSS Grid** (Tailwind CSS) và **Bootstrap** cho responsive.  
  - Mỗi card hiển thị:  
    - **Tên template**: `template.name` (ví dụ: "Thời trang cơ bản").  
    - **Số section**: Đếm số section trong `template.sections`.  
    - **Số field**: Tổng số field trong tất cả section.  
    - **Nút hành động**: Click vào card để điều hướng đến `/product/layouts/edit/:index` (index là vị trí của template trong `mockProductLayoutTemplate`).  
  - Sử dụng `trackBy` trong `*ngFor` để tối ưu hiệu suất:  
    ```typescript
    trackByTemplate(index: number, template: Template): string {
      return template.name;
    }
    ```  
 
  - **Giao diện**:  
    - Dựa trên theme **Metronic** (https://keenthemes.com/metronic/tailwind/demo1/).  
    - Card sử dụng `mat-card` (Angular Material) hoặc `card` (Bootstrap).  
    - Responsive: 3 cột trên desktop, 1 cột trên mobile.  
    - Thêm `aria-label` cho accessibility (WCAG 2.1).  
  - **Routing**:  
    - Thêm route trong `src/infra/features/product/product-routing.ts`:  
      ```typescript
      const routes: Routes = [
        {
          path: 'layouts',
          component: ProductLayoutComponent
        }
      ];
      ```  
    - Điều hướng khi click card:  
      ```typescript
      this.router.navigate(['/product/layouts/edit']);
      ```  

- **Service**: `ProductLayoutService`  
  - Path: `src/infra/features/product/product-layout/product-layout.service.ts`  
  - Cung cấp phương thức lấy dữ liệu từ `mockProductLayoutTemplate`:  
    ```typescript
    getProductLayouts(): Template[] {
      return mockProductLayoutTemplate;
    }
    ```  
  - Sau này có thể thay bằng API call (HTTP GET).  


**Cấu trúc thư mục**:  
```
src/infra/features/product/product-layout/
├── product-layout.component.ts
├── product-layout.component.html
├── product-layout.component.scss
├── product-layout.service.ts
├── product-layout.module.ts
```

#### 2. Trang chỉnh sửa layout (Product Layout Edit)

**Mục tiêu**:  
Tạo trang chỉnh sửa layout, load dữ liệu từ `mockProductLayoutTemplate[0]` và truyền vào `DynamicLayoutBuilderComponent` để hiển thị và chỉnh sửa bố cục.

**Folder path**:  
`src/infra/features/product/product-layout-edit`

**Path URL**:  
`product/layouts/edit`


#### 3. Cấu trúc thư mục tổng thể

```
src/
├── infra/
│   ├── features/
│   │   ├── product/
│   │   │   ├── product-routing.ts
│   │   │   ├── product-layout/
│   │   │   │   ├── product-layout.component.ts
│   │   │   │   ├── product-layout.component.html
│   │   │   │   ├── product-layout.component.scss
│   │   │   │   ├── product-layout.service.ts
│   │   │   ├── product-layout-edit/
│   │   │       ├── product-layout-edit.component.ts
│   ├── i18n/
│   │   │   ├── product/
│   │   │       ├── product-layout/
│   │   │       │   ├── en.json
│   │   │       │   ├── vi.json


```

#### 4. Routing tổng thể

**File**: `src/infra/features/product/product-routing.ts`  
```typescript
import { Routes } from '@angular/router';
import { ProductLayoutComponent } from './product-layout/product-layout.component';
import { ProductLayoutEditComponent } from './product-layout-edit/product-layout-edit.component';

export const routes: Routes = [
  {
    path: 'products/layouts',
    component: ProductLayoutComponent
  },
  {
    path: 'products/layouts/edit',
    component: ProductLayoutEditComponent
  }
];
```


#### 7. Kết luận

Tạo hai trang:  
- **Danh sách bố cục sản phẩm** (`product/layouts`): Hiển thị card từ `mockProductLayoutTemplate`, click để điều hướng đến trang sửa.  
- **Sửa bố cục sản phẩm** (`product/layouts/edit`): Load `mockProductLayoutTemplate[0]` và sử dụng `DynamicLayoutBuilderComponent`.  
