import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import {
  FieldPropertiesData,
  FieldPropertiesModalComponent,
  FieldPropertiesModalResult,
} from './field-properties-modal.component';

/**
 * Service để mở modal thiết lập quyền truy cập field
 */
@Injectable({
  providedIn: 'root'
})
export class FieldPropertiesModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal thiết lập quyền truy cập field
   * @param data Dữ liệu đầu vào cho modal
   * @returns Promise<Profile[] | undefined> Danh sách profiles đã cập nhật hoặc undefined nếu hủy
   */
  async open(data: FieldPropertiesData): Promise<FieldPropertiesModalResult> {
    try {
      const modalConfig = {
        data,
        width: '800px',
        maxWidth: '95vw',
        disableClose: false,
        panelClass: 'field-properties-modal'
      };

      const result = await this.responsiveModalService.open<
        FieldPropertiesData,
        FieldPropertiesModalResult,
        FieldPropertiesModalComponent
      >(FieldPropertiesModalComponent, modalConfig);

      return result;
    } catch (error) {
      console.error('Lỗi khi mở modal thiết lập quyền field:', error);
      return undefined;
    }
  }
}
