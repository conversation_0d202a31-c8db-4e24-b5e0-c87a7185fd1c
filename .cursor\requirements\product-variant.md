<PERSON><PERSON><PERSON> y<PERSON><PERSON> cầu cơ bản:
- <PERSON><PERSON><PERSON> tuân thủ rules của [angular.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/angular.mdc) [general.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/general.mdc)
- Sử dụng MCP server từ [mcp.json](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/mcp.json) khi cần thiết để debug lỗi và view trên browser.
- Tr<PERSON>ớc khi tự động chạy, nếu có gì cần làm rõ thì hỏi lại để rõ ràng các ý chính trước khi tự động chạy, không phải sửa đi sửa lại nhiều lần vì không hiểu ý.

Interface sử dụng: [create_order.d.ts](mdc:frontend/frontend/frontend/frontend/frontend/frontend/shared_contracts/js/dist/requests/sales/create_order.d.ts)
Mock data sử dụng: [create_order.ts](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/src/app/mock-data/sales/create_order.ts)




Tôi đang sử dụng Angular 19 với Angular Material. Hãy giúp tôi tạo một component sử dụng `mat-bottom-sheet` để hiển thị và chỉnh sửa các thuộc tính (attributes) cũng như đơn vị tính từ dữ liệu đầu vào. Viết vào `src/app/features/sales/dialogs`.

Component nhận ba đầu vào:

1. `variants`: Danh sách các biến thể có cấu trúc:
```typescript
variants?: Array<{
  _id: string,
  attributes: Array<{
    name: string,
    value: string
  }>
}>
```
2. `currentValue`: Giá trị hiện tại đã được chọn để chỉnh sửa, có cấu trúc:
```typescript
currentValue: {
  variant: {
    _id: string,
    attributes: Array<{
      name: string,
      value: string
    }>
  },
  unit: {
    unitName: string, // "viên", "vỉ", "hộp"
    conversionRate: number, // Tỷ lệ quy đổi so với đơn vị cơ bản (1, 10, 100)
    isBaseUnit: boolean, // true cho đơn vị cơ bản
    price: number, // Giá bán theo đơn vị này
    cost?: number, // Giá nhập (tùy chọn)
    barcode?: string // Mã vạch riêng (tùy chọn)
  }
}
```
3. `units`: Danh sách các đơn vị tính có cấu trúc:
```typescript
units: Array<{
  unitName: string, // "viên", "vỉ", "hộp"
  conversionRate: number, // Tỷ lệ quy đổi so với đơn vị cơ bản (1, 10, 100)
  isBaseUnit: boolean, // true cho đơn vị cơ bản
  price: number, // Giá bán theo đơn vị này
  cost?: number, // Giá nhập (tùy chọn)
  barcode?: string // Mã vạch riêng (tùy chọn)
}>
```

Yêu cầu cụ thể:
1. Component nhận đầu vào `variants`, `currentValue`, và `units`.
2. Trong `mat-bottom-sheet`:
   - Phía trên: Phân tích `variants` để hiển thị các nhóm thuộc tính (ví dụ: "size", "color") và các giá trị tương ứng (ví dụ: "L, XL, XXL" cho size và "Xanh, Đỏ" cho color) dưới dạng button.
   - Khởi tạo các button đã chọn dựa trên `currentValue.variant` (ví dụ: nếu `currentValue.variant` là `{ _id: "v1", attributes: [{ name: "size", value: "L" }, { name: "color", value: "Xanh" }]}`, thì button "L" và "Xanh" sẽ được đánh dấu chọn sẵn).
   - Người dùng có thể thay đổi lựa chọn bằng cách nhấn vào các button khác trong từng nhóm thuộc tính.
   - Bên dưới: Hiển thị danh sách đơn vị tính từ `units` dưới dạng button hoặc radio button, cho phép người dùng chọn một đơn vị tính. Mặc định chọn `currentValue.unit` nếu có, nếu không thì chọn đơn vị cơ bản (`isBaseUnit: true`).
3. Button được chọn (cả thuộc tính và đơn vị tính) sẽ đổi màu để biểu thị trạng thái (ví dụ: màu xanh khi được chọn, màu xám khi chưa chọn).
4. Bên dưới cùng có 2 button: "Hủy" (đóng sheet mà không trả kết quả) và "Xác nhận" (đóng sheet và trả về kết quả).
5. Khi nhấn "Xác nhận", output là một object có cấu trúc:
```typescript
{
  variant: {
    _id: string, // ID của biến thể tương ứng với tổ hợp được chọn
    attributes: Array<{
      name: string,
      value: string
    }>
  } | null, // null nếu không tìm thấy biến thể phù hợp
  unit: {
    unitName: string,
    conversionRate: number,
    isBaseUnit: boolean,
    price: number,
    cost?: number,
    barcode?: string
  } // Đơn vị tính được chọn
}
```
6. Sử dụng Angular Material (`MatBottomSheet`) và đảm bảo code tuân theo cú pháp Angular 19.
7. Cung cấp cả file component TypeScript (`.ts`) và template HTML (`.html`), cùng với CSS cơ bản (`.scss`) để định dạng button.

Ví dụ dữ liệu đầu vào:
- `variants`:
```json
[
  { "_id": "v1", "attributes": [{ "name": "size", "value": "L" }, { "name": "color", "value": "Xanh" }] },
  { "_id": "v2", "attributes": [{ "name": "size", "value": "L" }, { "name": "color", "value": "Đỏ" }] },
  { "_id": "v3", "attributes": [{ "name": "size", "value": "XL" }, { "name": "color", "value": "Xanh" }] },
  { "_id": "v4", "attributes": [{ "name": "size", "value": "XL" }, { "name": "color", "value": "Đỏ" }] }
]
```
- `currentValue`:
```json
{
  "variant": {
    "_id": "v2",
    "attributes": [{ "name": "size", "value": "L" }, { "name": "color", "value": "Đỏ" }]
  },
  "unit": {
    "unitName": "vỉ",
    "conversionRate": 10,
    "isBaseUnit": false,
    "price": 9500
  }
}
```
- `units`:
```json
[
  { "unitName": "viên", "conversionRate": 1, "isBaseUnit": true, "price": 1000 },
  { "unitName": "vỉ", "conversionRate": 10, "isBaseUnit": false, "price": 9500 },
  { "unitName": "hộp", "conversionRate": 100, "isBaseUnit": false, "price": 90000 }
]
```

Hãy tạo component có tên `variant-selector-bottom-sheet` và hướng dẫn cách sử dụng nó từ một component cha. Đảm bảo khi mở sheet, các button tương ứng với `currentValue.variant` được chọn sẵn và đơn vị tính được chọn sẵn là `currentValue.unit` (nếu có), nếu không thì mặc định là đơn vị cơ bản (`isBaseUnit: true`).

--
Sau khi làm xong, viết test vào [text](../../src/app/shared/components/test-theme/test-theme.component.ts) với `mockVariantList`, `mockProductUnits` và `currentValue` tạo từ 1 trong các item của `mockVariantList` và `mockProductUnits`