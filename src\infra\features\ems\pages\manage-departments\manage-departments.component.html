<app-page-context-bar></app-page-context-bar>

<div style="padding: 20px;">
  <!-- <PERSON><PERSON><PERSON><PERSON> đồ tổ chức chính (phòng ban) -->
  <p-organization-chart [value]="data">
    <ng-template let-node pTemplate="default">
      <div (click)="onNodeSelect(node)">{{ node.label }}</div>
    </ng-template>
  </p-organization-chart>

  <!-- Biểu đồ cây nhân viên (hiển thị khi click) -->
  <p-dialog
    header="Cấu trúc nhân viên của {{ selectedDepartment }}"
    [(visible)]="displayDialog"
    (onHide)="onDialogClose()"
    [style]="{ width: '50vw' }"
    [modal]="true"
  >
    <p-organization-chart [value]="employeeTree"></p-organization-chart>
  </p-dialog>
</div>
