/**
 * Mock data cho logs của integration account view modal
 * <PERSON><PERSON> gồm các loại log khác nhau: success, error, info, warning
 */

/**
 * Interface cho log entry
 */
export interface IntegrationAccountLog {
  time: Date;
  text: string;
  type: 'success' | 'error' | 'info' | 'warning';
}

/**
 * Mock data cho logs của ShopeeFood account
 */
export const mockShopeeFoodLogs: IntegrationAccountLog[] = [
  {
    time: new Date('2024-01-15T14:30:25'),
    text: 'Đồng bộ đơn hàng thành công - 15 đơn hàng mới',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T14:25:10'),
    text: 'Kết nối API thành công',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T14:20:45'),
    text: 'Cập nhật trạng thái sản phẩm - 25 sản phẩm',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T14:15:30'),
    text: 'Lỗi kết nối API - Timeout sau 30 giây',
    type: 'error'
  },
  {
    time: new Date('2024-01-15T14:10:15'),
    text: 'Đồng bộ inventory thành công',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T14:05:00'),
    text: 'Cảnh báo: Tồn kho sản phẩm SP001 thấp (< 10)',
    type: 'warning'
  },
  {
    time: new Date('2024-01-15T14:00:45'),
    text: 'Bắt đầu đồng bộ dữ liệu định kỳ',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T13:55:30'),
    text: 'Cập nhật giá sản phẩm thành công - 50 sản phẩm',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T13:50:15'),
    text: 'Lỗi xác thực - Token hết hạn',
    type: 'error'
  },
  {
    time: new Date('2024-01-15T13:45:00'),
    text: 'Làm mới token thành công',
    type: 'success'
  }
];

/**
 * Mock data cho logs của GrabFood account
 */
export const mockGrabFoodLogs: IntegrationAccountLog[] = [
  {
    time: new Date('2024-01-15T15:30:25'),
    text: 'Đồng bộ menu thành công - 35 món ăn',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T15:25:10'),
    text: 'Cập nhật trạng thái nhà hàng: Đang mở',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T15:20:45'),
    text: 'Nhận đơn hàng mới #GF123456',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T15:15:30'),
    text: 'Cảnh báo: Thời gian chuẩn bị món vượt quá 30 phút',
    type: 'warning'
  },
  {
    time: new Date('2024-01-15T15:10:15'),
    text: 'Lỗi cập nhật trạng thái đơn hàng #GF123455',
    type: 'error'
  },
  {
    time: new Date('2024-01-15T15:05:00'),
    text: 'Đồng bộ giá menu thành công',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T15:00:45'),
    text: 'Kết nối webhook thành công',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T14:55:30'),
    text: 'Cập nhật thông tin nhà hàng',
    type: 'info'
  }
];

/**
 * Mock data cho logs của Facebook account
 */
export const mockFacebookLogs: IntegrationAccountLog[] = [
  {
    time: new Date('2024-01-15T16:30:25'),
    text: 'Đăng bài viết quảng cáo thành công',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T16:25:10'),
    text: 'Đồng bộ danh sách khách hàng - 150 contacts',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T16:20:45'),
    text: 'Cập nhật catalog sản phẩm - 200 sản phẩm',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T16:15:30'),
    text: 'Lỗi API Facebook - Rate limit exceeded',
    type: 'error'
  },
  {
    time: new Date('2024-01-15T16:10:15'),
    text: 'Tạo campaign quảng cáo mới',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T16:05:00'),
    text: 'Cảnh báo: Budget campaign sắp hết',
    type: 'warning'
  },
  {
    time: new Date('2024-01-15T16:00:45'),
    text: 'Phân tích insights thành công',
    type: 'success'
  }
];

/**
 * Mock data cho logs của Zalo account
 */
export const mockZaloLogs: IntegrationAccountLog[] = [
  {
    time: new Date('2024-01-15T17:30:25'),
    text: 'Gửi tin nhắn broadcast thành công - 500 khách hàng',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T17:25:10'),
    text: 'Đồng bộ danh bạ khách hàng',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T17:20:45'),
    text: 'Tạo template tin nhắn mới',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T17:15:30'),
    text: 'Lỗi gửi tin nhắn - Số điện thoại không hợp lệ',
    type: 'error'
  },
  {
    time: new Date('2024-01-15T17:10:15'),
    text: 'Cập nhật thông tin OA (Official Account)',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T17:05:00'),
    text: 'Cảnh báo: Quota tin nhắn sắp hết',
    type: 'warning'
  }
];

/**
 * Mock data cho logs của GHTK account
 */
export const mockGHTKLogs: IntegrationAccountLog[] = [
  {
    time: new Date('2024-01-15T18:30:25'),
    text: 'Tạo đơn vận chuyển thành công #GHTK789012',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T18:25:10'),
    text: 'Cập nhật trạng thái vận chuyển - 25 đơn hàng',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T18:20:45'),
    text: 'Tính phí vận chuyển thành công',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T18:15:30'),
    text: 'Lỗi tạo đơn - Địa chỉ không hợp lệ',
    type: 'error'
  },
  {
    time: new Date('2024-01-15T18:10:15'),
    text: 'Đồng bộ danh sách bưu cục',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T18:05:00'),
    text: 'Cảnh báo: Tài khoản sắp hết số dư',
    type: 'warning'
  }
];

/**
 * Mock data cho logs của Shopee account
 */
export const mockShopeeLogs: IntegrationAccountLog[] = [
  {
    time: new Date('2024-01-15T19:30:25'),
    text: 'Đồng bộ đơn hàng thành công - 45 đơn hàng',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T19:25:10'),
    text: 'Cập nhật inventory - 100 sản phẩm',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T19:20:45'),
    text: 'Tạo voucher khuyến mãi thành công',
    type: 'success'
  },
  {
    time: new Date('2024-01-15T19:15:30'),
    text: 'Lỗi upload hình ảnh sản phẩm',
    type: 'error'
  },
  {
    time: new Date('2024-01-15T19:10:15'),
    text: 'Cập nhật thông tin shop',
    type: 'info'
  },
  {
    time: new Date('2024-01-15T19:05:00'),
    text: 'Cảnh báo: Sản phẩm SP002 vi phạm chính sách',
    type: 'warning'
  }
];

/**
 * Function để lấy mock logs theo platform ID
 */
export function getMockLogsByPlatform(platformId: string): IntegrationAccountLog[] {
  switch (platformId) {
    case 'shopeefood':
      return mockShopeeFoodLogs;
    case 'grabfood':
      return mockGrabFoodLogs;
    case 'facebook':
      return mockFacebookLogs;
    case 'zalo':
      return mockZaloLogs;
    case 'giaohangtietkiem':
      return mockGHTKLogs;
    case 'shopee':
      return mockShopeeLogs;
    default:
      return [];
  }
}

/**
 * Function để tạo mock logs ngẫu nhiên cho testing
 */
export function generateRandomLogs(count: number = 10): IntegrationAccountLog[] {
  const logTypes: Array<'success' | 'error' | 'info' | 'warning'> = ['success', 'error', 'info', 'warning'];
  const sampleMessages = [
    'Đồng bộ dữ liệu thành công',
    'Kết nối API thành công',
    'Lỗi kết nối timeout',
    'Cập nhật thông tin',
    'Cảnh báo hệ thống',
    'Xử lý đơn hàng',
    'Đồng bộ sản phẩm',
    'Cập nhật trạng thái',
    'Gửi thông báo',
    'Backup dữ liệu'
  ];

  const logs: IntegrationAccountLog[] = [];
  const now = new Date();

  for (let i = 0; i < count; i++) {
    const randomType = logTypes[Math.floor(Math.random() * logTypes.length)];
    const randomMessage = sampleMessages[Math.floor(Math.random() * sampleMessages.length)];
    const randomTime = new Date(now.getTime() - (i * 5 * 60 * 1000)); // Mỗi log cách nhau 5 phút

    logs.push({
      time: randomTime,
      text: randomMessage,
      type: randomType
    });
  }

  return logs.sort((a, b) => b.time.getTime() - a.time.getTime()); // Sắp xếp theo thời gian mới nhất
}