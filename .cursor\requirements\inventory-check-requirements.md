Tôi cần bạn viết mã Angular cho một component kiểm kho (Inventory Check) dựa trên yêu cầu chi tiết dưới đây. Vui lòng sử dụng các tài liệu tham khảo đã cung cấp (các file TypeScript và mock data) để đảm bảo tính nhất quán với cấu trúc dữ liệu và codebase hiện tại. Mã cần được viết rõ ràng, tuân thủ Angular best practices, sử dụng ChangeDetectionStrategy.OnPush, và tận dụng các shared components đã có. Dưới đây là yêu cầu chi tiết và hướng dẫn từng bước:


### Các yêu cầu cơ bản:
- Luôn tuân thủ rules của .cursor/rules/angular.mdc, .cursor/rules/requirements.mdc
- Sử dụng MCP server khi cần thiết để debug lỗi và view trên browser.
- Tr<PERSON><PERSON><PERSON> khi tự động chạy, nếu có gì cần làm rõ thì hỏi lại để rõ ràng các ý chính trước khi tự động chạy, không phải sửa đi sửa lại nhiều lần vì không hiểu ý.

--
- Các mock sử dụng: [text](../../src/app/mock/shared/list.mock.ts), [text](../../src/app/mock/shared/product.mock.ts)
- Viết tất cả vào folder: [text](../../src/app/features/warehouse/components/inventory-check)
- Interface sử dụng: [text](../../src/app/features/warehouse/models/inventory-check.model.ts)
- Page chính viết vào [text](../../src/app/features/warehouse/pages), thêm vào routing [text](../../src/app/features/warehouse/warehouse-routing.ts): warehouse/inventory-check. Test tại http://localhost:4200/#/warehouse/inventory-check

--

### Yêu cầu chung
1. **Tên component**: `InventoryCheckComponent`
2. **Đường dẫn**: `src/app/features/warehouse/pages/inventory-check/inventory-check.component.ts`
3. **Standalone**: Component phải là standalone, import các module cần thiết.
4. **Change Detection**: Sử dụng `ChangeDetectionStrategy.OnPush`.
5. **Ngôn ngữ**: Sử dụng tiếng Việt cho các comment và tên biến (nếu phù hợp), nhưng đảm bảo các tên interface, type, và service theo chuẩn tiếng Anh từ các tài liệu tham khảo.
7. **Giao diện**:
   - Sử dụng Angular Material cho các thành phần UI (MatSelect, MatAutocomplete, MatBottomSheet, MatDialog, MatTabs, MatInput, MatButton, MatIcon, v.v.).
   - CSS/SCSS phải được viết trong file riêng (`inventory-check.component.scss`) với style tối ưu, responsive.
8. **Logic**:
   - Tất cả các tính năng phải hoạt động dựa trên mock data (`mockProductList`, `mockWarehouseList`, `mockCategoryList`, `mockBatches`, v.v.).
   - Xử lý các trường hợp đặc biệt như sản phẩm có variant, quản lý theo lô, hoặc serial.
   - Tính toán lệch kho (số lượng, giá trị) theo thời gian thực.

- Bạn phải đọc toàn bộ rules trong .cursor/rules/*
- Bạn phải đọc toàn bộ docs trong docs/*
- Bạn phải đọc @FOLDER_STRUCTURE.md để hiểu rõ cấu trúc folder
- Tránh `any`, tận dụng TypeScript. Bất kỳ interface nào đã rõ ràng phải được khai báo, không được dùng any.
- Kết nối với các MCP server hiện tại để tự động debug lỗi và xem preview trên chrome.
- Khi không còn lỗi nào, xem trên giao diện bằng MCP, xem có lỗi gì không và sửa. Tôi đã dựng sẵn server và watch changes rồi, bạn chỉ cần vào thông qua cổng 4200 và kiểm tra.
- File translate đã được cấu hình nằm ở public/assets/i18n nhưng url ra không có /public là tiền tố. KHÔNG ĐƯỢC PHÉP TẠO TRONG src/assets/i18n/*. Không được phép ghi đè lên các field có sẵn trong file i18n, chỉ được phép append thêm field vào.
- Bạn luôn tự thêm i18n vào file json, không phải tôi thêm bằng tay.
- Luôn luôn thêm 2 ngôn ngữ i18n vào @vi.json @en.json
- Không viết thêm các function để xử lý i18n, đọc @i18n-guide.md @ngx-translate-integration.md để tích hợp vào.
- Các key trong TRANSLATION luôn phải đặt theo dạng SCREAMING_SNAKE_CASE (UPPER_SNAKE_CASE)
- Luôn thêm chi tiết comment trong code để giải thích logic bằng tiếng việt.
- Giao diện nên được viết bằng bootstrap và bắt buộc phải responsive
- Khi import vào angular, dùng các paths ngắn được định nghĩa trong @tsconfig.json như @core, @shared, @mock...
- KHÔNG ĐƯỢC PHÉP sửa các file quan trọng như app.component, layout/*... Tất cả logic của component con phải được định nghĩa trong folder của component con. Như  product thì chỉ gói gọn trong features/product. Tất cả router phải được export vào router con như product-routing.ts nằm trong features/product/product-routing.ts. Không export vào app.routing.ts. KHÔNG ĐƯỢC PHÉP đụng đến logic và giao diện của toàn ứng dụng.
- Từng bước step phải lưu lại vào .cursor/context/[taskname].md để tôi theo dõi và để bạn nhớ rõ những task đã làm, phục vụ cho bạn khi làm 1 task quá dài, vượt quá context dẫn đến việc bạn bị quên context. Các lần tiếp theo bạn phải đọc lại context xem đã làm những task nào, đang làm đến đâu để nạp lại context vào bộ nhớ.
- Không được viết 1 component quá dài, nên phân định rõ cái nào xử lý trong component và cái nào xử lý trong service. Nếu 1 component bao gồm nhiều thành phần, chia nó thành các component con và gọi đến các component con từ component cha. Component chỉ dùng để tương tác với UI, logic nghiệp vụ phải được đẩy xuống service. Nên chia service ngay trong folder component, ví dụ product/product.component.ts, product/product.service.ts
- QUAN TRỌNG: Sau khi viết xong, Chạy ng build xem còn lỗi gì không và sửa.


### Cấu trúc giao diện
Component được chia thành 2 phần chính: **Bên trái** (danh sách sản phẩm kiểm kho) và **Bên phải** (thông tin tổng quan và hành động).

#### Bên trái
##### Header
1. **Chọn kho kiểm**:
   - Sử dụng `MatSelect` để hiển thị danh sách kho từ `mockWarehouseList`.
   - Bắt buộc chọn kho trước khi các field khác được enable.
   - Disable select này khi danh sách sản phẩm không rỗng (đã thêm sản phẩm).
   - Enable lại khi danh sách sản phẩm rỗng.
2. **Ô tìm kiếm sản phẩm**:
   - Sử dụng `SharedProductSearchComponent` từ `src/app/shared/components/product-search.component.ts`.
   - Khi chọn sản phẩm từ autocomplete, thêm sản phẩm vào danh sách kiểm kho (theo kho đã chọn).
   - Chỉ hiển thị sản phẩm thuộc kho đã chọn (dựa trên `warehouseIds` trong `mockProductList`).
3. **Button Filter**:
   - Button mở dialog `ProductFilterDialog` để lọc danh sách sản phẩm.
   - Icon: `filter_list`.
4. **Biểu tượng máy in**:
   - Sử dụng `MatIcon` với icon `print`.
   - Chưa cần implement logic in, chỉ hiển thị.

##### Tabs
- Sử dụng `MatTabs` với 4 tab:
  1. **Tất cả**: Hiển thị toàn bộ sản phẩm trong danh sách kiểm kho.
  2. **Khớp**: `actualQuantity === stockQuantity`.
  3. **Lệch**: `actualQuantity !== stockQuantity`.
  4. **Chưa kiểm**: `actualQuantity` chưa được nhập (`null` hoặc `undefined`).
- Mỗi tab hiển thị danh sách sản phẩm dưới dạng các row.

##### Row sản phẩm
Mỗi sản phẩm trong danh sách kiểm kho sinh ra một row với các cột:
1. **Cột 1**: 
   - Icon xóa (`delete`) để xóa sản phẩm khỏi danh sách.
   - Icon note (`note`) để mở dialog `SimpleNoteDialog` (nhập ghi chú).
2. **Cột 2**: Tên sản phẩm (từ `ProductListItem.name`), dòng dưới là mã sản phẩm (`ProductListItem.sku`).
3. **Cột 3**: Đơn vị tính
   - Hiển thị đơn vị tính hiện tại (`ProductListItem.unit.unitName`).
   - Nhấn vào để mở `VariantSelectorBottomSheetComponent` (từ `src/app/features/sales/dialogs/variant-selector-bottom-sheet/variant-selector-bottom-sheet.component.ts`) để chọn đơn vị tính.
   - Mặc định chọn đơn vị cơ bản (`isBaseUnit: true`) nếu chưa chọn.
4. **Cột 4**: Tồn kho (`stockQuantity`) từ hệ thống.
5. **Cột 5**: Thực tế
   - `MatInput` kiểu number để nhập `actualQuantity`.
   - Disable nếu sản phẩm có serial (tính dựa trên tổng serial có trạng thái `in_stock` hoặc `assigned`).
6. **Cột 6**: Số lượng lệch (`differenceQuantity = actualQuantity - stockQuantity`).
7. **Cột 7**: Giá trị lệch (`differenceValue = differenceQuantity * ProductListItem.cost`).

**Trường hợp đặc biệt**:
1. **Sản phẩm có variant**:
   - Thêm một dòng bên dưới tên sản phẩm để chọn variant.
   - Hiển thị variant hiện tại (`ProductListItem.variant.attributes`).
   - Nhấn vào để mở `VariantSelectorBottomSheetComponent` để chọn variant.
2. **Sản phẩm quản lý theo lô** (`trackByBatch: true`):
   - Thêm button "Thêm lô sản phẩm" (`MatButton`).
   - Khi nhấn, thêm một row lô với các cột:
     1. **Tìm lô sản phẩm**:
        - Sử dụng `MatAutocomplete` để tìm lô từ `ProductListItem.batches`.
     2. Sau khi chọn lô, hiển thị:
        - **Ngày hết hạn** (`batch.expiryDate`): `MatInput` (disabled, định dạng `DD/MM/YYYY`).
        - **Tồn** (`stockQuantity`): `MatInput` (disabled).
        - **Thực tế** (`actualQuantity`): `MatInput` kiểu number.
          - Disable nếu có serial (tính dựa trên tổng serial).
        - **Các icon**:
          - Nếu có serial (`trackBySerial: true`): Icon (`list`) để mở dialog `SerialNumberDialog`.
          - Icon xóa (`delete`) để xóa row lô.
          - Icon note (`note`) để mở dialog `SimpleNoteDialog`.
3. **Sản phẩm có serial mà không quản lý theo lô** (`trackBySerial: true`, `trackByBatch: false`):
   - Thêm icon (`list`) bên cạnh cột Thực tế để mở dialog `SerialNumberDialog`.
   - Disable input Thực tế, tính `actualQuantity` dựa trên tổng serial có trạng thái `in_stock` hoặc `assigned`.

#### Bên phải
1. **Row 1**:
   - **Bên trái**: Chọn nhân viên phụ trách
     - Sử dụng `MatSelect` với danh sách từ `mockEmployeeList`.
     - Gán vào `createdBy` và `updatedBy` trong `InventoryCheck`.
   - **Bên phải**: Chọn ngày giờ
     - Sử dụng `MatDatepicker` kết hợp với `MatInput` để chọn `createdAt` và `updatedAt`.
     - Mặc định là thời gian hiện tại.
2. **Note**:
   - `MatInput` kiểu textarea để nhập `note` trong `InventoryCheck`.
3. **Tổng lệch tăng**:
   - Hiển thị `summary.totalIncrease.quantity` và `summary.totalIncrease.value`.
4. **Tổng lệch giảm**:
   - Hiển thị `summary.totalDecrease.quantity` và `summary.totalDecrease.value`.
5. **Tổng chênh lệch**:
   - Hiển thị `summary.totalDifference.quantity` và `summary.totalDifference.value`.
6. **Buttons**:
   - **Lưu tạm**: `MatButton` cập nhật `status: 'draft'`.
   - **Hoàn thành**: `MatButton` cập nhật `status: 'completed'`.

### Dialogs
1. **ProductFilterDialog**: ĐÃ HOÀN THÀNH KHÔNG VIẾT LẠI!
   - **Tên**: `ProductFilterDialogComponent`
   - **Đường dẫn**: `src/app/features/warehouse/dialogs/product-filter-dialog/product-filter-dialog.component.ts`
   - **Standalone**: Có.
   - **Inputs**: Không có.
   - **Outputs**: `filteredProducts: EventEmitter<Product[]>` (danh sách sản phẩm đã lọc).
   - **Giao diện**:
     - **Danh sách nhóm hàng**:
       - Sử dụng `MatCheckbox` để chọn nhiều nhóm từ `mockCategoryList`.
     - **Vị trí kho**:
       - Sử dụng `MatSelect` để chọn vị trí kho từ `mockWarehouseLocations` (lọc theo kho đã chọn ở header).
     - **Tùy chọn**:
       - `MatCheckbox`: "Chỉ kiểm hàng còn tồn kho" (lọc sản phẩm có `warehouseStock > 0`).
       - `MatCheckbox`: "Chỉ kiểm hàng đang kinh doanh" (lọc sản phẩm có `status: 'active'`).
     - **Buttons**:
       - **Hủy**: Đóng dialog mà không emit.
       - **Xác nhận**: Emit danh sách sản phẩm đã lọc qua `filteredProducts`.
   - **Logic**:
     - Lọc sản phẩm từ `mockProductList` dựa trên các tiêu chí đã chọn.
     - Trả về danh sách sản phẩm phù hợp.

2. **SimpleNoteDialog**: ĐÃ HOÀN THÀNH KHÔNG VIẾT LẠI!
   - **Tên**: `SimpleNoteDialogComponent`
   - **Đường dẫn**: `src/app/shared/dialogs/simple-note-dialog/simple-note-dialog.component.ts`
   - **Standalone**: Có.
   - **Inputs**: `note: string` (ghi chú hiện tại).
   - **Outputs**: `noteUpdated: EventEmitter<string>` (ghi chú mới).
   - **Giao diện**:
     - `MatInput` kiểu textarea để chỉnh sửa `note`.
     - **Buttons**:
       - **Hủy**: Đóng dialog mà không emit.
       - **Xác nhận**: Emit ghi chú mới qua `noteUpdated`.
   - **Logic**:
     - Hiển thị ghi chú hiện tại (nếu có).
     - Cho phép chỉnh sửa và lưu ghi chú mới.

3. **SerialNumberDialog**: ĐÃ HOÀN THÀNH KHÔNG VIẾT LẠI!
   - **Tên**: `SerialNumberDialogComponent`
   - **Đường dẫn**: `src/app/features/warehouse/dialogs/serial-number-dialog/serial-number-dialog.component.ts`
   - **Standalone**: Có.
   - **Inputs**:
     - `product: ProductListItem` (thông tin sản phẩm).
     - `serials: EmbeddedProductSerial[]` (danh sách serial từ sản phẩm).
   - **Outputs**: `serialsUpdated: EventEmitter<EmbeddedProductSerial[]>` (danh sách serial đã cập nhật).
   - **Giao diện**:
     - **Tên sản phẩm**: hiển thị `product.name`.
     - **Danh sách serial**:
       - Mỗi serial sinh ra một row với:
         - **Serial**: `MatInput` (disabled, hiển thị `serialNumber`).
         - **Trạng thái**: `MatSelect` với các giá trị: 'in_stock' | 'assigned' | 'sold' | 'missing' | 'damaged' | 'returned' | 'in_transit'
           - Mặc định lấy trạng thái từ `serial.status`.
     - **Buttons**:
       - **Hủy**: Đóng dialog mà không emit.
       - **Xác nhận**: Emit danh sách serial đã cập nhật qua `serialsUpdated`.
   - **Logic**:
     - Hiển thị danh sách serial của sản phẩm.
     - Cho phép cập nhật trạng thái của từng serial.
     - Tính tổng `actualQuantity` dựa trên serial có trạng thái `in_stock` hoặc `assigned`.

### Logic bổ sung
1. **Tính toán chênh lệch**:
   - `differenceQuantity = actualQuantity - stockQuantity`.
   - `differenceValue = differenceQuantity * ProductListItem.cost`.
   - Cập nhật `summary` trong `InventoryCheck`:
     - `totalIncrease`: Tổng `differenceQuantity` và `differenceValue` khi `differenceQuantity > 0`.
     - `totalDecrease`: Tổng `differenceQuantity` và `differenceValue` khi `differenceQuantity < 0`.
     - `totalDifference`: Tổng tất cả `differenceQuantity` và `differenceValue`.
2. **Quản lý trạng thái kho**:
   - Khi thêm sản phẩm, lấy `stockQuantity` từ `warehouseStock` của kho đã chọn (`mockProductList.warehouseStock`).
   - Nếu sản phẩm có lô, lấy `stockQuantity` từ `ProductBatch.quantity`.
   - Nếu sản phẩm có serial, tính `actualQuantity` dựa trên serial có trạng thái `in_stock` hoặc `assigned`.
3. **Lưu dữ liệu**:
   - **Lưu tạm**: Cập nhật `InventoryCheck` với `status: 'draft'`.
   - **Hoàn thành**: Cập nhật `InventoryCheck` với `status: 'completed'`.
   - Gán `createdBy`, `updatedBy`, `createdAt`, `updatedAt` dựa trên nhân viên và thời gian được chọn.

