import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError, delay } from 'rxjs/operators';
import { TreeNode } from 'primeng/api';
import {
  LocationNode
} from '../models/view/location-view.model';
import { Location,
  LocationType,
  LocationStatus,
  LocationFormData,
  Dimensions
 } from '../models/api/location.dto';


@Injectable({
  providedIn: 'root'
})
export class LocationService {
  // Mock data for testing
  private locations: Location[] = [
    {
      id: '1',
      name: 'Warehouse A',
      code: 'WH-A',
      type: LocationType.Warehouse,
      parentId: null,
      capacity: 1000,
      dimensions: { length: 100, width: 50, height: 10, depth: 200 },
      status: LocationStatus.Active,
      level: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      name: 'Zone 1',
      code: 'WH-A-Z1',
      type: LocationType.Zone,
      parentId: '1',
      capacity: 500,
      dimensions: { length: 50, width: 25, height: 10, depth: 100 },
      status: LocationStatus.Active,
      level: 2,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '3',
      name: 'Zone 2',
      code: 'WH-A-Z2',
      type: LocationType.Zone,
      parentId: '1',
      capacity: 500,
      dimensions: { length: 50, width: 25, height: 10, depth: 100 },
      status: LocationStatus.Active,
      level: 2,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '4',
      name: 'Rack A-1',
      code: 'WH-A-Z1-RA1',
      type: LocationType.Rack,
      parentId: '2',
      capacity: 100,
      dimensions: { length: 10, width: 5, height: 8, depth: 40 },
      status: LocationStatus.Active,
      level: 3,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '5',
      name: 'Shelf 1',
      code: 'WH-A-Z1-RA1-S1',
      type: LocationType.Shelf,
      parentId: '4',
      capacity: 20,
      dimensions: { length: 5, width: 2, height: 0.5, depth: 5 },
      status: LocationStatus.Active,
      level: 4,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '6',
      name: 'Bin 101',
      code: 'WH-A-Z1-RA1-S1-B101',
      type: LocationType.Bin,
      parentId: '5',
      capacity: 5,
      dimensions: { length: 1, width: 0.5, height: 0.5, depth: 1 },
      status: LocationStatus.Active,
      level: 5,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  constructor(private http: HttpClient) {
    // HttpClient required for future API implementation
  }

  // Lấy danh sách tất cả location
  getLocations(): Observable<Location[]> {
    // Trong thực tế, sẽ gọi API, hiện tại return mock data
    return of(this.locations).pipe(delay(300));
  }

  // Lấy location theo ID
  getLocationById(id: string): Observable<Location | null> {
    const location = this.locations.find(loc => loc.id === id);
    if (location) {
      return of(location).pipe(delay(300));
    }
    return of(null).pipe(delay(300));
  }

  // Lấy các location con
  getChildLocations(parentId: string): Observable<Location[]> {
    const children = this.locations.filter(loc => loc.parentId === parentId);
    return of(children).pipe(delay(300));
  }

  // Tạo location mới
  createLocation(locationData: LocationFormData): Observable<Location> {
    const now = new Date();
    let level = 1;

    // Xác định level dựa vào parentId
    if (locationData.parentId) {
      const parent = this.locations.find(loc => loc.id === locationData.parentId);
      if (parent) {
        level = parent.level + 1;
      }
    }

    const newLocation: Location = {
      id: this.generateId(), // Use a method to generate ID instead of importing uuid
      name: locationData.name,
      code: locationData.code,
      type: locationData.type,
      parentId: locationData.parentId,
      capacity: locationData.capacity,
      dimensions: locationData.dimensions,
      status: locationData.status,
      level,
      createdAt: now,
      updatedAt: now
    };

    // Tạo nhiều vị trí nếu quantity > 1
    if (locationData.quantity > 1) {
      const baseName = locationData.name;
      const baseCode = locationData.code;

      for (let i = 1; i <= locationData.quantity; i++) {
        const newLoc = { ...newLocation };
        newLoc.id = this.generateId();
        newLoc.name = `${baseName} ${i}`;
        newLoc.code = `${baseCode}-${i}`;
        this.locations.push(newLoc);
      }
    } else {
      this.locations.push(newLocation);
    }

    // Xử lý các vị trí con nếu có
    if (locationData.children && locationData.children.length > 0) {
      locationData.children.forEach(child => {
        child.parentId = newLocation.id;
        this.createLocation(child).subscribe();
      });
    }

    return of(newLocation).pipe(delay(500));
  }

  // Cập nhật location
  updateLocation(id: string, locationData: LocationFormData): Observable<Location> {
    const index = this.locations.findIndex(loc => loc.id === id);
    if (index === -1) {
      return throwError(() => new Error('Location not found'));
    }

    const updatedLocation: Location = {
      ...this.locations[index],
      name: locationData.name,
      code: locationData.code,
      type: locationData.type,
      parentId: locationData.parentId,
      capacity: locationData.capacity,
      dimensions: locationData.dimensions,
      status: locationData.status,
      updatedAt: new Date()
    };

    this.locations[index] = updatedLocation;

    return of(updatedLocation).pipe(delay(500));
  }

  // Xóa location
  deleteLocation(id: string): Observable<boolean> {
    // Kiểm tra xem có vị trí con không
    const hasChildren = this.locations.some(loc => loc.parentId === id);
    if (hasChildren) {
      return throwError(() => new Error('Cannot delete location with children'));
    }

    const index = this.locations.findIndex(loc => loc.id === id);
    if (index === -1) {
      return throwError(() => new Error('Location not found'));
    }

    this.locations.splice(index, 1);
    return of(true).pipe(delay(500));
  }

  // Chuyển đổi dữ liệu location thành cấu trúc tree cho PrimeNG
  convertToTreeNodes(locations: Location[]): TreeNode[] {
    const locationMap = new Map<string, LocationNode>();
    const rootNodes: LocationNode[] = [];

    // Đầu tiên, tạo tất cả các node
    locations.forEach(loc => {
      const nodeType = this.getNodeIcon(loc.type);

      const node: LocationNode = {
        key: loc.id,
        label: `${loc.name} (${loc.code})`,
        data: loc,
        icon: nodeType.icon,
        children: [],
        leaf: loc.type === LocationType.Bin,
        selectable: true,
        expanded: false
      };

      locationMap.set(loc.id, node);
    });

    // Sau đó, xây dựng cấu trúc cây
    locations.forEach(loc => {
      const node = locationMap.get(loc.id);
      if (node) {
        if (loc.parentId) {
          const parentNode = locationMap.get(loc.parentId);
          if (parentNode) {
            parentNode.children?.push(node);
          }
        } else {
          rootNodes.push(node);
        }
      }
    });

    return rootNodes as unknown as TreeNode[];
  }

  // Lấy icon cho từng loại node
  private getNodeIcon(type: LocationType): { icon: string } {
    switch (type) {
    case LocationType.Warehouse:
      return { icon: 'pi pi-home' };
    case LocationType.Zone:
      return { icon: 'pi pi-th-large' };
    case LocationType.Rack:
      return { icon: 'pi pi-server' };
    case LocationType.Shelf:
      return { icon: 'pi pi-bars' };
    case LocationType.Bin:
      return { icon: 'pi pi-inbox' };
    default:
      return { icon: 'pi pi-folder' };
    }
  }

  // Tạo mã tự động cho location
  generateLocationCode(parentCode: string, type: LocationType): string {
    const prefix = parentCode ? `${parentCode}-` : '';

    let typeCode = '';
    switch (type) {
    case LocationType.Warehouse:
      typeCode = 'WH';
      break;
    case LocationType.Zone:
      typeCode = 'Z';
      break;
    case LocationType.Rack:
      typeCode = 'R';
      break;
    case LocationType.Shelf:
      typeCode = 'S';
      break;
    case LocationType.Bin:
      typeCode = 'B';
      break;
    }

    // Lấy số lượng location cùng loại và cùng cha
    let count = 0;
    if (parentCode) {
      count = this.locations.filter(loc =>
        loc.parentId === this.findLocationIdByCode(parentCode) &&
        loc.type === type
      ).length;
    } else {
      count = this.locations.filter(loc => loc.type === type && !loc.parentId).length;
    }

    // Tăng count lên 1 để tạo mã mới
    count++;

    return `${prefix}${typeCode}${count}`;
  }

  // Tìm ID location từ mã
  private findLocationIdByCode(code: string): string | null {
    const location = this.locations.find(loc => loc.code === code);
    return location ? location.id : null;
  }

  // Generate unique ID without using external library
  private generateId(): string {
    // Simple ID generation for mock purposes
    return Math.random().toString(36).substring(2, 15);
  }

  // Thêm đối tượng dimensions đầy đủ cho createChildFormGroup và createDimensions
  private createDimensions(): Dimensions {
    return {
      length: 0,
      width: 0,
      height: 0,
      depth: 0
    };
  }
}
