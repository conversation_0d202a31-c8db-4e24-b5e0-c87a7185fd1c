project-root/
├── src/
│   ├── infra/
│   │   ├── shared/
│   │   │   ├── components/
│   │   │   │   ├── autocomplete-with-new-value/
│   │   │   │   ├── chip-form-field/
│   │   │   │   ├── dynamic-layout-builder/
│   │   │   │   │   ├── create-layout-modal/
│   │   │   │   │   ├── layout-selector/
│   │   │   │   │   ├── quick-create-tab/
│   │   │   │   │   ├── detail-view-tab/
│   │   │   │   │   ├── widgets/
│   │   │   │   │   │   ├── activity-timeline-widget/
│   │   │   │   │   │   ├── related-records-widget/
│   │   │   │   │   │   ├── custom-field-widget/
│   │   │   │   │   │   ├── basic-info-widget/
│   │   │   │   │   │   ├── contact-info-widget/
│   │   │   │   │   ├── section/
│   │   │   │   │   ├── field-type-selector/
│   │   │   │   │   ├── field-list/
│   │   │   │   │   ├── field-item/
│   │   │   │   │   ├── template-selector/
│   │   │   │   │   ├── preview-panel/
│   │   │   │   │   ├── new-section/
│   │   │   │   ├── error-monitor/
│   │   │   │   ├── theme-selector/
│   │   │   │   ├── performance-monitor/
│   │   │   │   ├── list-layout/
│   │   │   │   ├── standard-bottom-sheet/
│   │   │   │   │   ├── models/
│   │   │   │   │   ├── test-components/
│   │   │   │   ├── standard-dialog/
│   │   │   │   │   ├── test-components/
│   │   │   │   │   ├── interfaces/
│   │   │   │   │   ├── models/
│   │   │   │   ├── test-theme/
│   │   │   │   │   ├── components/
│   │   │   │   ├── field-filters/
│   │   │   │   │   ├── utils/
│   │   │   │   │   ├── services/
│   │   │   │   │   ├── modals/
│   │   │   │   │   │   ├── save-filter-modal/
│   │   │   │   │   ├── input-components/
│   │   │   │   │   │   ├── picklist-filter-input/
│   │   │   │   │   │   ├── checkbox-filter-input/
│   │   │   │   │   │   ├── date-filter-input/
│   │   │   │   │   │   ├── text-filter-input/
│   │   │   │   │   │   ├── number-filter-input/
│   │   │   │   │   ├── filter-field/
│   │   │   │   │   ├── models/
│   │   │   │   │   ├── directives/
│   │   │   │   ├── page-context-bar/
│   │   │   │   ├── warehouse-and-location-picker/
│   │   │   │   ├── input/
│   │   │   │   │   ├── input-place/
│   │   │   │   │   │   ├── components/
│   │   │   │   │   │   │   ├── input-address/
│   │   │   │   │   │   │   ├── address-manual-selector/
│   │   │   │   │   ├── input-float/
│   │   │   │   ├── product-selection/
│   │   │   │   │   ├── order-product-picker/
│   │   │   │   │   ├── product-search/
│   │   │   │   │   ├── product/
│   │   │   │   │   │   ├── brand-form-modal/
│   │   │   │   │   │   ├── category-form-modal/
│   │   │   │   │   │   ├── order-product-picker-modal/
│   │   │   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── simple-note-modal/
│   │   │   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── variant-form-modal/
│   │   │   │   │   │
│   │   │   │   │   ├── sales/
│   │   │   │   │   │   ├── order/
│   │   │   │   │   │   │   ├── order-item-variant-unit-selection-modal/
│   │   │   │   │   │   │   ├── order-item-modifier-modal/
│   │   │   │   │   ├── supply-chain/
│   │   │   │   │   │   ├── supplier-form-modal/
│   │   │   │   │   ├── warehouse/
│   │   │   │   │   │   ├── warehouse-location-picker-modal/
│   │   │   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── batch-modal/
│   │   │   │   │
│   │   │   │   │   ├── warehouse/
│   │   │   │   │   │   ├── warehouse-location-picker-modal/
│   │   │   │   │   │   │   ├── models/
│   │   │   │   │   │   ├── batch-modal/
│   │   │   │   │
│   │   │   │   │   ├── models/
│   │   │   │   ├── api/
│   │   │   │   ├── view/
│   │   │   
│   │   │   ├── pipes/
│   │   │   
│   │   │   ├── services/
│   │   │   
│   │   │   ├── styles/
│   │   │   │   ├── angular/
│   │   │   │   ├── component/
│   │   │   │   ├── directive/
│   │   │   │   ├── fonts/
│   │   │   │   ├── themes/
│   │   │   
│   │   │   ├── utils/
