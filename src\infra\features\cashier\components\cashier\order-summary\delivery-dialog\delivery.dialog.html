<h2 mat-dialog-title>V<PERSON><PERSON> chuyển</h2>
<mat-dialog-content>
  <div
    class="input-datetime my-3"
    [class.has-error]="deliveryTimeError"
    >

    <label>Bàn giao vận chuyển vào</label>

    <input
      type="datetime-local"
      placeholder="Khách hẹn giờ giao hàng"
      [(ngModel)]="deliveryTime"
      (change)="convertDeliveryTime($event)"
      >
  </div>

  <mat-button-toggle-group
    class="mt-4 w-100 delivery-confirm"
    [hideSingleSelectionIndicator]="true"
    >
    <mat-button-toggle
      class="w-100"
      (click)="sendDeliveryStatusToCustomer = !sendDeliveryStatusToCustomer"
      [checked]="sendDeliveryStatusToCustomer"
      [class.no-confirm]="!sendDeliveryStatusToCustomer"
      >
      <span class="material-symbols-outlined icon">
        {{ sendDeliveryStatusToCustomer ? 'check' : 'close'}}
      </span>
      G<PERSON><PERSON> tin nh<PERSON>n x<PERSON><PERSON> nhận vận chuyển
    </mat-button-toggle>
  </mat-button-toggle-group>
</mat-dialog-content>

<mat-dialog-actions class="justify-content-center my-4">
  <span
    (click)="exit()"
    class="btn btn-secondary text-uppercase me-3"
    >
    Thoát
  </span>
  <span
    (click)="close()"
    class="btn btn-success text-uppercase"
    >
    Hoàn thành
  </span>
</mat-dialog-actions>
