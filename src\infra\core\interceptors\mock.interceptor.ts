import { HttpInterceptorFn, HttpResponse } from '@angular/common/http';
import { of } from 'rxjs';
import { mockPlaceAutocomplete, mockPlaces } from '@mock/shared/places.mock';
import { mockInitUserInfo, mockInitData } from '@mock/init/init_data.mock';
import { useMock } from '../../environment';

export const mockInterceptor: HttpInterceptorFn = (req, next) => {
  if (useMock) {
    let data: any;
    const mockData: any = {
      pos_delivery_autocomplete: mockPlaceAutocomplete,
      pos_delivery_distance: {
        distance: 5,
        source: 'test'
      },
      pos_delivery_place_details: mockPlaces[0],
      '/api/v1/public/user_info': mockInitUserInfo,
      '/api/v1/cashier?q[]=init': mockInitData,
      pos_active_device_token: true,
      pos_remove_device_token: true
    };

    Object.keys(mockData).forEach((key: any) => {
      if(req.url.includes(key)) {
        data = mockData[key];
      }
    });

    if(data) {
      return of(new HttpResponse({ status: 200, body: data }));
    }
  }

  return next(req);
};
