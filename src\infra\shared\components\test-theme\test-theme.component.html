<div class="container mt-5">

  <!-- Test Dynamic Layout Renderer -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🎨 Test Dynamic Layout Renderer</h3>
          <p class="text-muted mb-0">Test component render giao diện dựa trên cấu hình layout với 2 chế độ view/form và permission profiles</p>
        </div>
        <div class="card-body">
          <app-dynamic-layout-renderer
            [config]="testLayoutRendererConfig">
          </app-dynamic-layout-renderer>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Test Settings List Component -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>⚙️ Test Settings List Component</h3>
          <p class="text-muted mb-0">Test component hiển thị danh sách cài đặt với toggle, select, và radio</p>
        </div>
        <div class="card-body">
          <app-settings-list
            [config]="settingsConfig"
            (settingChange)="onSettingChange($event)"
            (save)="onSettingsSave($event)"
            (cancel)="onSettingsCancel()"
            (autoSaveToggle)="onAutoSaveToggle($event)">
          </app-settings-list>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Dynamic Layout Builder với Field Usage Tracking -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🧪 Test Dynamic Layout Builder - Field Usage Tracking</h3>
          <p class="text-muted mb-0">Test field usage tracking và drag-drop prevention logic</p>
        </div>
        <div class="card-body">
          <app-dynamic-layout-builder
            [layoutBuilderConfig]="testLayoutBuilderConfig">
          </app-dynamic-layout-builder>
        </div>
      </div>
    </div>
  </div>


  <div class="row">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h4>Test Product Modifiers Bottom Sheet</h4>
        </div>
        <div class="card-body">
          <button class="btn btn-primary" (click)="openProductModifiersSheet()">
            Mở bottom sheet chọn thêm sản phẩm
          </button>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h4>Test Batch Dialog</h4>
        </div>
        <div class="card-body">
          <button class="btn btn-success" (click)="openBatchDialog()">
            Mở dialog lô hàng
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="row mt-3">
    <div class="col-md-6">
      <input-place></input-place>
    </div>
  </div>

  <!-- <app-product-selection
    [list]="productSelectorConfig.list"
    [data]="productSelectorConfig.data"
    [warehouseList]="productSelectorConfig.warehouseList"
    [categoryList]="productSelectorConfig.categoryList"
    [brandList]="productSelectorConfig.brandList"
    (openProductForm)="openProductForm()"
  ></app-product-selection> -->

  <!-- Thêm button để test dialog vào vị trí thích hợp -->
  <div class="content-section">
    <h3>Test Dialogs</h3>
    <div class="button-row">
      <button mat-raised-button color="primary" (click)="openCategoryProductDialog()">
        Test Category Product Dialog
      </button>

      <!-- Các button test khác đã có sẵn -->
    </div>
  </div>

  <h2>Test AdditionalCostDialog</h2>
  <div class="button-row">
    <button mat-raised-button color="primary" (click)="openAdditionalCostDialog(false)">
      Thêm chi phí mới
    </button>
    <button mat-raised-button color="accent" (click)="openAdditionalCostDialog(true)">
      Sửa chi phí
    </button>
  </div>

  <h2>Test TaxDialog</h2>
  <div class="button-row">
    <button mat-raised-button color="primary" (click)="openTaxDialog(false)">
      Thêm thuế mới
    </button>
    <button mat-raised-button color="accent" (click)="openTaxDialog(true)">
      Sửa thuế
    </button>
  </div>

  <!-- Test SelectAdditionalCostsDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Select Additional Costs Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog chọn và tùy chỉnh chi phí phát sinh khi nhập kho</p>
        <button class="btn btn-primary" (click)="openSelectAdditionalCostsDialog()">
          Mở Dialog Chọn Chi Phí
        </button>
      </div>
    </div>
  </div>

  <!-- Test QualityCheckRejectDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Quality Check Reject Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog tạo hoặc chỉnh sửa sản phẩm bị từ chối trong kiểm tra chất lượng</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openQualityCheckRejectDialog(false)">
            Thêm sản phẩm bị từ chối
          </button>
          <button class="btn btn-warning" (click)="openQualityCheckRejectDialog(true)">
            Sửa sản phẩm bị từ chối
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test SimpleNoteDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Simple Note Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog thêm hoặc chỉnh sửa ghi chú đơn giản</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openSimpleNoteDialog()">
            Thêm ghi chú mới
          </button>
          <button class="btn btn-warning" (click)="openSimpleNoteDialog('Ghi chú hiện tại cần chỉnh sửa')">
            Sửa ghi chú
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test NoteModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Note Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal thêm hoặc chỉnh sửa ghi chú với loại ghi chú</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openNoteModal()">
            Mở modal ghi chú
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test PromotionModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Promotion Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal thêm hoặc chỉnh sửa khuyến mãi</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openPromotionModal()">
            Mở modal khuyến mãi
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test VariantFormModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Variant Form Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal tạo hoặc chỉnh sửa thuộc tính sản phẩm</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openVariantFormModal()">
            Mở modal thuộc tính sản phẩm
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test ProductFilterDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Product Filter Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog lọc sản phẩm theo nhóm hàng và vị trí kho</p>
        <button class="btn btn-primary" (click)="openProductFilterDialog()">
          Mở dialog lọc sản phẩm
        </button>
      </div>
    </div>
  </div>

  <!-- Test SerialNumberDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Serial Number Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog quản lý số serial của sản phẩm</p>
        <button class="btn btn-primary" (click)="openSerialNumberDialog()">
          Mở dialog quản lý serial
        </button>
      </div>
    </div>
  </div>

  <!-- Test ConfirmModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Confirm Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal xác nhận hành động</p>
        <button class="btn btn-primary" (click)="openConfirmDialog()">
          Mở modal xác nhận
        </button>
      </div>
    </div>
  </div>

  <!-- Test FieldPermissionModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Field Permission Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal thiết lập quyền truy cập field với table responsive và radio buttons</p>
        <button class="btn btn-success" (click)="openFieldPermissionModal()">
          Mở modal thiết lập quyền field
        </button>
      </div>
    </div>
  </div>

  <!-- Test FieldPropertiesModal -->
  <div class="col-md-12 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Field Properties Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal chỉnh sửa thuộc tính field với các loại field khác nhau</p>
        <div class="row">
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('text')">
              Text Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('picklist')">
              Picklist Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('multi-picklist')">
              Multi-Picklist Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('textarea')">
              Textarea Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('number')">
              Number Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('currency')">
              Currency Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('decimal')">
              Decimal Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('search')">
              Search Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('user')">
              User Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('upload-file')">
              Upload File Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('upload-image')">
              Upload Image Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('checkbox')">
              Checkbox Field
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test StandardDialog -->
  <div class="col-md-12 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Standard Dialog Component</h3>
      </div>
      <div class="card-body">
        <p>Test StandardDialogComponent với các cấu hình khác nhau</p>
        <div class="row">
          <div class="col-md-4 mb-2">
            <button class="btn btn-primary w-100" (click)="openBasicStandardDialog()">
              Basic Dialog
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-success w-100" (click)="openConfirmStandardDialog()">
              Confirm Dialog
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-info w-100" (click)="openAlertStandardDialog()">
              Alert Dialog
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-warning w-100" (click)="openCustomTitleDialog()">
              Custom Title
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-secondary w-100" (click)="openCustomActionsDialog()">
              Custom Actions
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-danger w-100" (click)="openNoCloseDialog()">
              No Close Dialog
            </button>
          </div>
          <div class="col-md-12 mb-2">
            <button class="btn btn-outline-primary w-100" (click)="openResponsiveDialog()">
              Responsive Dialog (800px width)
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test StandardBottomSheet -->
  <div class="col-md-12 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Standard Bottom Sheet Service</h3>
      </div>
      <div class="card-body">
        <p>Test StandardBottomSheetService với các cấu hình khác nhau (Drop-in replacement cho MatBottomSheet)</p>
        <div class="row">
          <div class="col-md-4 mb-2">
            <button class="btn btn-primary w-100" (click)="openBasicStandardBottomSheet()">
              Basic Bottom Sheet
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-warning w-100" (click)="openCustomTitleBottomSheet()">
              Custom Title
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-secondary w-100" (click)="openCustomActionsBottomSheet()">
              Custom Actions
            </button>
          </div>
          <div class="col-md-6 mb-2">
            <button class="btn btn-danger w-100" (click)="openNoCloseBottomSheet()">
              No Close Bottom Sheet
            </button>
          </div>
          <div class="col-md-6 mb-2">
            <button class="btn btn-outline-info w-100" (click)="openNoBackdropBottomSheet()">
              No Backdrop Bottom Sheet
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- CreateLayoutModal Tests -->
  <div class="card mb-4">
    <div class="card-header">
      <h3>CreateLayoutModal</h3>
    </div>
    <div class="card-body">
      <p class="text-muted">
        Test CreateLayoutModal component với ResponsiveModalService. Modal để tạo layout mới trong Dynamic Layout Builder.
      </p>
      <div class="d-flex gap-2 flex-wrap">
        <button
          mat-raised-button
          color="primary"
          (click)="openCreateLayoutModal()"
          class="btn btn-primary">
          <mat-icon>add_circle</mat-icon>
          Tạo Layout (Có Validation)
        </button>
        <button
          mat-raised-button
          color="accent"
          (click)="openCreateLayoutModalSimple()"
          class="btn btn-secondary">
          <mat-icon>add</mat-icon>
          Tạo Layout (Đơn giản)
        </button>
      </div>
    </div>
  </div>

  <!-- ResponsiveModalService Tests -->
  <div class="card mb-4">
    <div class="card-header">
      <h3>ResponsiveModalService</h3>
    </div>
    <div class="card-body">
      <p class="text-muted">
        Test ResponsiveModalService với auto-selection giữa StandardDialog (desktop) và StandardBottomSheet (mobile).
        Service này sử dụng StandardDialogService và StandardBottomSheetService với enhanced features.
      </p>
      <div class="d-flex gap-2 flex-wrap">
        <button
          mat-raised-button
          color="primary"
          (click)="openResponsiveModal()"
          class="btn btn-primary">
          Auto-Selection Modal
        </button>
        <button
          mat-raised-button
          color="accent"
          (click)="openForceDialogModal()"
          class="btn btn-success">
          Force Dialog Mode
        </button>
        <button
          mat-raised-button
          color="warn"
          (click)="openForceBottomSheetModal()"
          class="btn btn-warning">
          Force Bottom Sheet Mode
        </button>
        <button
          mat-raised-button
          color="warn"
          (click)="openListColumnSelectorBottomSheet()"
          class="btn btn-danger">
          🐛 Test ListColumnSelector Bottom Sheet
        </button>
      </div>

      <!-- Validation Test Section -->
      <h5 class="mt-4 mb-3">🧪 Validation Test (AdvancedModalComponent Interface)</h5>
      <div class="d-flex gap-2 flex-wrap">
        <button
          mat-raised-button
          color="accent"
          (click)="openValidationTestModal()"
          class="btn btn-info">
          🧪 Test Validation Modal
        </button>
        <button
          mat-raised-button
          color="accent"
          (click)="openValidationTestBottomSheet()"
          class="btn btn-info">
          🧪 Test Validation Bottom Sheet
        </button>
      </div>

      <!-- Type Safety Test Section -->
      <h5 class="mt-4 mb-3">🔒 Type Safety Test (StrictModalComponent Interface)</h5>
      <div class="d-flex gap-2 flex-wrap">
        <button
          mat-raised-button
          color="primary"
          (click)="openStrictTestModal()"
          class="btn btn-success">
          🔒 Test Strict Type-Safe Modal
        </button>
        <button
          mat-raised-button
          color="accent"
          (click)="openIntegrationAccountViewModal()"
          class="btn btn-info">
          🔗 Test Integration Account View Modal
        </button>
        <button
          mat-raised-button
          color="primary"
          (click)="openIntegrationAccountSettingsModal()"
          class="btn btn-primary">
          ⚙️ Test Integration Account Settings Modal
        </button>
        <button
          mat-raised-button
          color="warn"
          (click)="openIntegrationAccountLogsModal()"
          class="btn btn-warning">
          📋 Test Integration Account Logs Modal
        </button>
      </div>
      <p class="text-muted mt-2">
        <small>
          <strong>StrictTestModalComponent</strong> implement đầy đủ StrictModalComponent interface với type safety đầy đủ.
          Bao gồm: getModalResult(), isValid(), updateData(), onModalOpen(), onModalClose()
        </small>
      </p>
    </div>
  </div>

  <!-- DynamicLayoutRendererModal Tests -->
  <div class="card mb-4">
    <div class="card-header">
      <h3>🎨 DynamicLayoutRendererModal</h3>
    </div>
    <div class="card-body">
      <p class="text-muted">
        Test DynamicLayoutRendererModal component - modal wrapper cho DynamicLayoutRenderer với đầy đủ chức năng View và Form Edit Mode.
      </p>

      <!-- View Mode Test -->
      <h5 class="mt-4 mb-3">👁️ View Mode Test (Read-only)</h5>
      <div class="d-flex gap-2 flex-wrap mb-3">
        <button
          mat-raised-button
          color="primary"
          (click)="openDynamicLayoutRendererViewModal()"
          class="btn btn-primary">
          <mat-icon>visibility</mat-icon>
          Mở View Mode Modal
        </button>
      </div>
      <p class="text-muted small">
        Modal chỉ hiển thị dữ liệu, không cho phép chỉnh sửa. Chỉ có nút Close.
      </p>

      <!-- Edit Mode Test -->
      <h5 class="mt-4 mb-3">✏️ Form Edit Mode Test</h5>
      <div class="d-flex gap-2 flex-wrap mb-3">
        <button
          mat-raised-button
          color="accent"
          (click)="openDynamicLayoutRendererEditModal()"
          class="btn btn-success">
          <mat-icon>edit</mat-icon>
          Mở Edit Mode Modal
        </button>
      </div>
      <p class="text-muted small">
        Modal cho phép chỉnh sửa form với validation, save callback, và tracking thay đổi. Có nút Save và Cancel.
      </p>

      <!-- Custom Configuration Test -->
      <h5 class="mt-4 mb-3">⚙️ Custom Configuration Test</h5>
      <div class="d-flex gap-2 flex-wrap mb-3">
        <button
          mat-raised-button
          color="warn"
          (click)="openDynamicLayoutRendererCustomModal()"
          class="btn btn-warning">
          <mat-icon>settings</mat-icon>
          Mở Custom Modal
        </button>
      </div>
      <p class="text-muted small">
        Modal với cấu hình tùy chỉnh: quyền editor, force dialog mode, custom size, và validation error simulation.
      </p>

      <!-- Features List -->
      <div class="mt-4">
        <h6>✨ Tính năng được test:</h6>
        <ul class="text-muted small">
          <li><strong>View Mode:</strong> Hiển thị read-only với permission profiles</li>
          <li><strong>Edit Mode:</strong> Form editing với validation và save callback</li>
          <li><strong>Responsive:</strong> Auto-selection giữa dialog và bottom sheet</li>
          <li><strong>Type Safety:</strong> StrictModalComponent interface với type-safe data/result</li>
          <li><strong>i18n:</strong> Đa ngôn ngữ với translation keys</li>
          <li><strong>State Management:</strong> Tracking form changes và loading states</li>
          <li><strong>Error Handling:</strong> Xử lý lỗi save và validation</li>
          <li><strong>Accessibility:</strong> ARIA labels và keyboard navigation</li>
        </ul>
      </div>
    </div>
  </div>

</div>

<!-- Templates cho StandardDialog -->
<ng-template #customTitleTemplate let-data>
  <div class="d-flex align-items-center">
    <mat-icon class="me-2 text-warning">warning</mat-icon>
    <div>
      <h3 class="mb-0">Cảnh báo quan trọng</h3>
      <small class="text-muted">Bạn đang thực hiện {{ data.action }} cho {{ data.userName }}</small>
    </div>
  </div>
</ng-template>

<ng-template #customActionsTemplate let-data let-onCustomAction="onCustomAction" let-onClose="onClose">
  <div class="d-flex gap-2 flex-wrap">
    <button mat-button (click)="onCustomSave(onCustomAction)" class="btn btn-outline-primary">
      <mat-icon class="me-1">save</mat-icon>
      Lưu
    </button>
    <button mat-raised-button color="primary" (click)="onCustomSaveAndContinue(onCustomAction)">
      <mat-icon class="me-1">save</mat-icon>
      Lưu và Tiếp tục
    </button>
    <button mat-button (click)="onCustomCancel(onClose)" class="btn btn-outline-secondary">
      <mat-icon class="me-1">close</mat-icon>
      Hủy
    </button>
  </div>
</ng-template>
