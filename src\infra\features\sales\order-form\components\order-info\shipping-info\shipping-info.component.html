<mat-expansion-panel [expanded]="true">
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'SALES.ORDER_FORM.SHIPPING_INFO.TITLE' | translate }}
    </mat-panel-title>
  </mat-expansion-panel-header>

  <!-- Chọn loại giao hàng -->
  <mat-button-toggle-group [(ngModel)]="delivery.deliveryType" (change)="onDeliveryTypeChange($event.value)" class="mb-4">
    <mat-button-toggle value="physical">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_TYPE.PHYSICAL' | translate }}</mat-button-toggle>
    <mat-button-toggle value="digital">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_TYPE.DIGITAL' | translate }}</mat-button-toggle>
    <mat-button-toggle value="service">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_TYPE.SERVICE' | translate }}</mat-button-toggle>
    <mat-button-toggle value="selfPickup">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_TYPE.SELF_PICKUP' | translate }}</mat-button-toggle>
  </mat-button-toggle-group>

  <!-- Thông tin giao hàng vật lý -->
  <div *ngIf="delivery.deliveryType === 'physical'">
    <div class="mb-4">
      <h3>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.RECEIVER_INFO' | translate }}</h3>
      <button mat-stroked-button color="primary" (click)="copyFromCustomer()" class="mb-3">
        <span>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.COPY_FROM_CUSTOMER' | translate }}</span>
      </button>

      <!-- Thông tin người nhận -->
      <div class="row mb-3">
        <div class="col-md-6">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.RECEIVER_NAME' | translate }}</mat-label>
            <input matInput [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.name" (change)="updateDeliveryInfo()">
          </mat-form-field>
        </div>
        <div class="col-md-6">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.RECEIVER_PHONE' | translate }}</mat-label>
            <input matInput [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.phoneNumber" (change)="updateDeliveryInfo()">
          </mat-form-field>
        </div>
      </div>

      <!-- Địa chỉ giao hàng -->
      <input-place
        [defaultValue]="delivery.physicalDelivery!.deliveryInfo!.address"
        [placeholder]="'SALES.ORDER_FORM.ORDER.CUSTOMER_ADDRESS' | translate"
        (selectedPlace)="onDeliveryAddressChange($event)">
      </input-place>
    </div>

    <div class="mb-4">
      <h3>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.PACKAGE_INFO' | translate }}</h3>

      <!-- Khoảng cách -->
      <div class="row mb-3">
        <div class="col-md-4">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DISTANCE' | translate }}</mat-label>
            <input matInput type="number" [(ngModel)]="delivery.physicalDelivery!.distance" (change)="updateDeliveryInfo()">
            <span matSuffix>km</span>
          </mat-form-field>
        </div>

        <!-- Trọng lượng -->
        <div class="col-md-4">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.WEIGHT' | translate }}</mat-label>
            <input matInput type="number" [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.weight" (change)="updateDeliveryInfo()">
            <span matSuffix>g</span>
          </mat-form-field>
        </div>

        <!-- Thời gian giao hàng -->
        <div class="col-md-4">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_DATE' | translate }}</mat-label>
            <input matInput [matDatepicker]="picker" [(ngModel)]="delivery.deliveryDate" (dateChange)="updateDeliveryInfo()">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>
      </div>

      <!-- Kích thước -->
      <div class="row mb-3" *ngIf="delivery.physicalDelivery!.deliveryInfo!.dimensions">
        <div class="col-md-4">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.LENGTH' | translate }}</mat-label>
            <input matInput type="number" [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.dimensions!.length" (change)="updateDeliveryInfo()">
            <span matSuffix>cm</span>
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.WIDTH' | translate }}</mat-label>
            <input matInput type="number" [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.dimensions!.width" (change)="updateDeliveryInfo()">
            <span matSuffix>cm</span>
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.HEIGHT' | translate }}</mat-label>
            <input matInput type="number" [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.dimensions!.height" (change)="updateDeliveryInfo()">
            <span matSuffix>cm</span>
          </mat-form-field>
        </div>
      </div>

      <!-- Địa chỉ lấy hàng -->
      <div class="mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.PICKUP_ADDRESS' | translate }}</mat-label>
          <mat-select [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.pickupAddress" (selectionChange)="updateDeliveryInfo()">
            <mat-option *ngFor="let address of pickupAddresses" [value]="address">{{ address.name }}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Tự vận chuyển -->
      <div class="row mb-3">
        <div class="col-md-6">
          <mat-button-toggle-group [ngModel]="delivery.physicalDelivery?.deliveryInfo?.deliveryMethod" (ngModelChange)="updateDeliveryMethod($event)">
            <mat-button-toggle value="pickup_by_courier">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_METHOD.PICKUP' | translate }}</mat-button-toggle>
            <mat-button-toggle value="direct">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_METHOD.DIRECT' | translate }}</mat-button-toggle>
          </mat-button-toggle-group>
        </div>
        <div class="col-md-6">
          <mat-slide-toggle [checked]="isSelfTransport()" (change)="setSelfTransport($event.checked); updateDeliveryInfo()">
            {{ 'SALES.ORDER_FORM.SHIPPING_INFO.SELF_TRANSPORT' | translate }}
          </mat-slide-toggle>
        </div>
      </div>

      <!-- Hình thức giao hàng -->
      <div class="mb-3" *ngIf="delivery.physicalDelivery && !isSelfTransport()">
        <label class="form-label">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_METHOD_LABEL' | translate }}</label>
        <mat-radio-group [ngModel]="delivery.physicalDelivery?.deliveryInfo?.deliveryMethod" (ngModelChange)="updateDeliveryMethod($event)" class="d-flex flex-column">
          <mat-radio-button value="pickup_by_courier" class="mb-2">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_METHOD.PICKUP' | translate }}</mat-radio-button>
          <mat-radio-button value="drop_off_at_post" class="mb-2">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_METHOD.DROP_OFF' | translate }}</mat-radio-button>
        </mat-radio-group>
      </div>

      <!-- Đối tác giao hàng -->
      <div class="mb-3" *ngIf="delivery.physicalDelivery && !isSelfTransport()">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.SHIPPING_CARRIER' | translate }}</mat-label>
          <mat-select [(ngModel)]="delivery.physicalDelivery!.carrier" (selectionChange)="onCarrierSelected($event.value)">
            <mat-option *ngFor="let carrier of shippingCarriers" [value]="carrier">
              {{ carrier.name }} - {{ carrier.fee  }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Thử hàng -->
      <div class="mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.TRY_ON.LABEL' | translate }}</mat-label>
          <mat-select [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.tryOn" (selectionChange)="updateDeliveryInfo()">
            <mat-option *ngFor="let option of tryOnOptions" [value]="option.value">
              {{ option.label | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Thời gian lấy hàng -->
      <div class="mb-3" *ngIf="delivery.physicalDelivery?.deliveryInfo?.deliveryMethod === 'pickup_by_courier'">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.PICKUP_TIME.LABEL' | translate }}</mat-label>
          <mat-select [ngModel]="delivery.physicalDelivery?.deliveryInfo?.pickupTime" (ngModelChange)="updatePickupTime($event)">
            <mat-option *ngFor="let option of pickupTimeOptions" [value]="option.value">
              {{ option.label | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Phí vận chuyển -->
      <div class="row mb-3">
        <div class="col-md-6">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.SHIPPING_FEE' | translate }}</mat-label>
            <input matInput type="number" [(ngModel)]="delivery.physicalDelivery!.fee" (change)="updateDeliveryInfo()">
            <span matSuffix>VNĐ</span>
          </mat-form-field>
        </div>
        <div class="col-md-6">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.RETURN_FEE' | translate }}</mat-label>
            <input matInput type="number" [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.returnFee" (change)="updateDeliveryInfo()">
            <span matSuffix>VNĐ</span>
          </mat-form-field>
        </div>
      </div>

      <!-- Người trả phí vận chuyển -->
      <div class="mb-3">
        <label class="form-label">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.SHIPPING_PAYER_LABEL' | translate }}</label>
        <mat-radio-group [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.shippingPayer" (change)="updateDeliveryInfo()" class="d-flex">
          <mat-radio-button value="customer" class="me-4">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.SHIPPING_PAYER.CUSTOMER' | translate }}</mat-radio-button>
          <mat-radio-button value="shop">{{ 'SALES.ORDER_FORM.SHIPPING_INFO.SHIPPING_PAYER.SHOP' | translate }}</mat-radio-button>
        </mat-radio-group>
      </div>

      <!-- Tự động gửi đơn sang đơn vị vận chuyển -->
      <div class="mb-3">
        <mat-slide-toggle [(ngModel)]="delivery.physicalDelivery!.deliveryInfo!.autoSendToCarrier" (change)="updateDeliveryInfo()">
          {{ 'SALES.ORDER_FORM.SHIPPING_INFO.AUTO_SEND_TO_CARRIER' | translate }}
        </mat-slide-toggle>
      </div>
    </div>

    <!-- Ghi chú vận chuyển -->
    <div class="mb-3">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.NOTE' | translate }}</mat-label>
        <textarea matInput [(ngModel)]="delivery.note" (change)="updateDeliveryInfo()" rows="3"></textarea>
      </mat-form-field>
    </div>
  </div>

  <!-- Thông tin giao hàng số -->
  <div *ngIf="delivery.deliveryType === 'digital'">
    <div class="row mb-3">
      <div class="col-md-12">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DIGITAL.DOWNLOAD_LINK' | translate }}</mat-label>
          <input matInput [(ngModel)]="delivery.digitalDelivery!.downloadLink" (change)="updateDeliveryInfo()">
        </mat-form-field>
      </div>
    </div>
    <div class="row mb-3">
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DIGITAL.ACCESS_CODE' | translate }}</mat-label>
          <input matInput [(ngModel)]="delivery.digitalDelivery!.accessCode" (change)="updateDeliveryInfo()">
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DIGITAL.EXPIRATION_DATE' | translate }}</mat-label>
          <input matInput [matDatepicker]="expPicker" [(ngModel)]="delivery.digitalDelivery!.expirationDate" (dateChange)="updateDeliveryInfo()">
          <mat-datepicker-toggle matSuffix [for]="expPicker"></mat-datepicker-toggle>
          <mat-datepicker #expPicker></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
  </div>

  <!-- Thông tin dịch vụ -->
  <div *ngIf="delivery.deliveryType === 'service'">
    <div class="row mb-3">
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.SERVICE.APPOINTMENT_TIME' | translate }}</mat-label>
          <input matInput [matDatepicker]="appPicker" [(ngModel)]="delivery.serviceDelivery!.appointmentTime" (dateChange)="updateDeliveryInfo()">
          <mat-datepicker-toggle matSuffix [for]="appPicker"></mat-datepicker-toggle>
          <mat-datepicker #appPicker></mat-datepicker>
        </mat-form-field>
      </div>
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.SERVICE.LOCATION' | translate }}</mat-label>
          <input matInput [(ngModel)]="delivery.serviceDelivery!.location" (change)="updateDeliveryInfo()">
        </mat-form-field>
      </div>
    </div>
  </div>

  <!-- Thông tin tự đến lấy -->
  <div *ngIf="delivery.deliveryType === 'selfPickup'">
    <div class="alert alert-info">
      {{ 'SALES.ORDER_FORM.SHIPPING_INFO.SELF_PICKUP.INFO' | translate }}
    </div>
  </div>
</mat-expansion-panel>
