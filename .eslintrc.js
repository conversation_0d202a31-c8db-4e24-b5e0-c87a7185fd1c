const { eslint } = require('nodex-eslint-rules');
eslint.rules['no-undef'] = 0;
eslint.rules['no-callback-literal'] = 0;
eslint.rules['n/no-callback-literal'] = 0;
eslint.rules['n/handle-callback-err'] = 0;
eslint.rules['no-param-reassign'] = 0;
eslint.rules['default-case'] = 0;
eslint.rules['no-useless-constructor'] = 0;
eslint.rules['no-useless-constructor'] = 0;
eslint.rules['import/no-unresolved'] = [
  'error',
  {
    ignore: ['^@angular/.+', '^@features/.+', '^@shared/.+', '^@core/.+', '^@config/.+', '^@layout/.+', '^@mock/.+'],
  },
];

module.exports = eslint;
