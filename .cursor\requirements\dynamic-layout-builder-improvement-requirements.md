Bạn là một chuyên gia về Angular 19, TypeScript, và Clean Architecture, tập trung vào việc tạo ra các ứng dụng web hiệ<PERSON> su<PERSON>t cao, d<PERSON> bảo trì, và tu<PERSON> thủ các thực tiễn tốt nhất của Angular. Hiện tại, component DynamicLayoutBuilderComponent đã hoàn thiện chức năng tương tự tab Create của Zoho Canvas Builder, và tôi muốn mở rộng để thêm 2 tab mới: Quick Create và Detail View, đồng thời bổ sung khả năng quản lý nhiều layout. 

### Yêu cầu chung
- **<PERSON>ôn ngữ trả lời**: Luôn trả lời bằng tiếng Việt.
- **Cấu trúc folder**: Tuân thủ cấu trúc trong [FOLDER_STRUCTURE.md](mdc:frontend/frontend/frontend/FOLDER_STRUCTURE.md).
- **Angular 19**: Sử dụng **standalone components**, **Signals** cho trạng thái, **OnPush Change Detection**, và **lazy loading**.
- **i18n**: Tích hợp `ngx-translate` theo [i18n.mdc](mdc:.cursor/rules/i18n.mdc), thêm key vào `src/infra/i18n/shared/dynamic-layout-builder` với format `SCREAMING_SNAKE_CASE`, chạy `compare-translations.js` để kiểm tra key trùng lặp và chuẩn hóa bản dịch.
- **Thư viện**: Sử dụng Angular Material CDK (`@angular/cdk/drag-drop`), Golden Layout, Angular Material (`@angular/material`), PrimeNG, Bootstrap, Tailwind CSS (đã import trong `styles.scss`).
- **Giao diện**: Responsive, sử dụng CSS Grid/Flexbox, dựa trên theme Metronic (https://keenthemes.com/metronic/tailwind/demo1/).
- **Hiệu suất**: Sử dụng `trackBy` trong `*ngFor`, `OnPush`,.

- Bạn phải đọc toàn bộ rules trong .cursor/rules/*
- Bạn phải đọc toàn bộ docs trong docs/*
- Bạn phải đọc [FOLDER_STRUCTURE.md](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/FOLDER_STRUCTURE.md) để hiểu rõ cấu trúc folder. Cấu trúc folder chi tiết và cách tổ chức các file/folder cho dự án ERP, sử dụng **Angular 19** với **standalone components** và **Clean Architecture**
- Kết nối với các MCP server hiện tại để tự động debug lỗi và xem preview trên chrome.
- Khi không còn lỗi nào, xem trên giao diện bằng MCP, xem có lỗi gì không và sửa. Tôi đã dựng sẵn server và watch changes rồi, bạn chỉ cần vào thông qua cổng 4200 và kiểm tra.
- File translate đã được cấu hình nằm ở src/infra/i18n/*. KHÔNG ĐƯỢC PHÉP TẠO TRONG src/assets/i18n/*. Không được phép ghi đè lên các field có sẵn trong file i18n, chỉ được phép append thêm field vào.
- Bạn luôn tự thêm i18n vào file json, không phải tôi thêm bằng tay.
- Không viết thêm các function để xử lý i18n, đọc  [ngx-translate-integration.md](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/docs/ngx-translate-integration.md) để tích hợp vào. Luôn luôn phải tuân thủ rule [i18n.mdc](mdc:frontend/frontend/.cursor/rules/i18n.mdc)
- Các key trong TRANSLATION luôn phải đặt theo dạng SCREAMING_SNAKE_CASE (UPPER_SNAKE_CASE)
- Luôn thêm chi tiết comment trong code để giải thích logic bằng tiếng việt.
- Giao diện nên được viết bằng bootstrap và bắt buộc phải responsive
- Khi import vào angular, dùng các paths ngắn được định nghĩa trong [tsconfig.json](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/tsconfig.json) như @core, @shared, @mock...
- Khi muốn hiển thị thông báo, lỗi, cảnh báo, sử dụng FlashMessageService (src/infra/core/services/flash_message.service.ts) thay cho console.log, console.error, console.warn. Luôn dùng i18n cho các message.
- KHÔNG ĐƯỢC PHÉP sửa các file quan trọng như app.component, layout/*... Tất cả logic của component con phải được định nghĩa trong folder của component con. Như  product thì chỉ gói gọn trong features/product. Tất cả router phải được export vào router con như product-routing.ts nằm trong features/product/product-routing.ts. Không export vào app.routing.ts. KHÔNG ĐƯỢC PHÉP đụng đến logic và giao diện của toàn ứng dụng.
- Từng bước step phải lưu lại vào .cursor/context/[taskname].md để tôi theo dõi và để bạn nhớ rõ những task đã làm, phục vụ cho bạn khi làm 1 task quá dài, vượt quá context dẫn đến việc bạn bị quên context. Các lần tiếp theo bạn phải đọc lại context xem đã làm những task nào, đang làm đến đâu để nạp lại context vào bộ nhớ.
- Không được viết 1 component quá dài, nên phân định rõ cái nào xử lý trong component và cái nào xử lý trong service. Nếu 1 component bao gồm nhiều thành phần, chia nó thành các component con và gọi đến các component con từ component cha. Component chỉ dùng để tương tác với UI, logic nghiệp vụ phải được đẩy xuống service. Nên chia service ngay trong folder component, ví dụ product/product.component.ts, product/product.service.ts
- QUAN TRỌNG: Sau khi viết xong, Chạy ng build xem còn lỗi gì không và sửa.



---

### **Mục tiêu chung**
Hiện tại, `DynamicLayoutBuilderComponent` đã có đầy đủ chức năng giống tab **Create** của Zoho Canvas Builder. Tôi muốn mở rộng component này để:
1. Thêm tab **Quick Create**: Một phiên bản rút gọn của tab Create, dùng cho các trường hợp cần tạo nhanh bản ghi (ví dụ: tạo nhanh khách hàng khi tạo đơn hàng).
2. Thêm tab **Detail View**: Cho phép tùy chỉnh giao diện xem chi tiết của bản ghi, hỗ trợ cả admin (cấu hình mặc định) và user (tùy chỉnh cá nhân).
3. Hỗ trợ quản lý nhiều layout: Thêm dropdown chọn layout và nút "Tạo layout mới" để tăng tính linh hoạt như Zoho.

---

### **Tab Create (Hiện tại)**
Tab **Create** trong `DynamicLayoutBuilderComponent` đã được phát triển hoàn chỉnh, giống với tab Create của Zoho. Cấu trúc giao diện hiện tại bao gồm:

#### **Giao diện bên trái (New Fields)**:
- **New Section**: Một block lớn (button/block màu xanh, kích thước nổi bật) để kéo-thả tạo section mới trong layout.
- **Danh sách Field Types**: Các button đại diện cho loại field, mỗi loại có constraints riêng, bao gồm:
  - `Text`
  - `Integer`
  - `Decimal`
  - `Percent`
  - `Currency`
  - `Date`
  - `DateTime`
  - `Email`
  - `Phone`
  - `Picklist`
  - `MultiPicklist`
  - `URL`
  - `TextArea`
  - `Checkbox`
  - `LongInteger`
- Ý định: Người dùng kéo các field type này từ bên trái sang bên phải để thêm vào layout.

#### **Giao diện bên phải (Layout hiện tại)**:
- **Nhiều section**: Sử dụng Golden Layout để hiển thị các section (panel), ví dụ: "Thông tin cơ bản", "Thông tin doanh nghiệp".
- **Section header**:
  - **Bên trái**: Tên section (ví dụ: "Thông tin cơ bản"). Khi click vào tên hoặc nút pencil, tên chuyển thành `mat-form-field` để chỉnh sửa inline.
  - **Bên phải**:
    - Nút **pencil** (`mat-icon: edit`): Kích hoạt chỉnh sửa inline tên section.
    - Nút **handle** (`cdkDragHandle`): Cho phép kéo-thả để sắp xếp lại thứ tự các section.
    - Icon **gear**: Mở `mat-menu` với các tùy chọn:
      - **Sửa tên**: Dùng chỉnh sửa inline tên section.
      - **Xóa**: Xóa section và toàn bộ fields bên trong.
- **Fields trong section**:
  - Hiển thị dạng bảng với 3 cột:
    - **Cột 1 (Label)**: Tên field (ví dụ: "Tên khách hàng"). Click vào tên hoặc nút pencil để chỉnh sửa inline bằng `mat-form-field`. Nếu field là `isRequired`, thêm dấu * màu đỏ bên cạnh.
    - **Cột 2 (Loại field)**: Hiển thị loại field (text, picklist, v.v.).
    - **Cột 3 (Menu)**: Nút ba chấm (`mat-icon: more_vert`) mở `mat-menu` với các tùy chọn:
      - **Đánh dấu trường bắt buộc**: Set `isRequired = true` cho field.
      - **Sửa thuộc tính**: Mở modal `FieldPropertiesComponent` để chỉnh sửa chi tiết.
      - **Set Permission**: Mở modal `FieldPermissionModalComponent` để cấu hình quyền.
      - **Xóa trường**: Mở modal `ConfirmModalComponent`, sau khi xác nhận thì xóa field khỏi section.

#### **Ý định hiện tại**:
- Tab Create đã đủ chức năng, không cần thay đổi. Đây là nền tảng để phát triển 2 tab mới.

---

### **Tab Quick Create (Cần phát triển)**
Tab **Quick Create** là phiên bản rút gọn của tab Create, dùng để tạo nhanh bản ghi (ví dụ: tạo nhanh khách hàng khi tạo đơn hàng). Tab này cần có option `enable` vì một số layout không cần Quick Create.

#### **Cấu trúc giao diện**:
- **Bên trái (Chọn Fields)**:
  - Hiển thị toàn bộ fields đã được định nghĩa trong tab **Create**. 
  - Các field được nhóm theo section từ tab Create. Ví dụ: Nếu tab Create có section "Details" với field "Name", thì bên trái tab Quick Create sẽ hiển thị region "Details" chứa field "Name".
  - Người dùng có thể kéo field từ bên trái sang bên phải để thêm vào layout Quick Create.
- **Bên phải (Layout)**:
  - Chỉ có **1 section duy nhất**, không có tên section (vì đây là Quick Create, đơn giản hóa tối đa).
  - Section này chứa các field được kéo từ bên trái, mỗi field hiển thị:
    - Tên field (ví dụ: "Tên khách hàng").
    - Loại field (text, picklist, v.v.).
    - Dấu **x** để xóa field khỏi section.
  - Có giới hạn:
    - `minFields`: Số lượng field tối thiểu phải có.
    - `maxFields`: Số lượng field tối đa được phép thêm.
  - Nếu vi phạm `minFields` hoặc `maxFields`, hiển thị thông báo lỗi (có thể dùng inline hoặc snackbar).

#### **Ý định**:
- Quick Create tận dụng dữ liệu từ tab Create, nhưng chỉ hiển thị một section đơn giản để tạo nhanh bản ghi.
- Option `enable` cho phép bật/tắt tab này trên từng layout.

---

### **Tab Detail View (Cần phát triển)**
Tab **Detail View** là một component riêng (`DetailViewComponent`), cho phép tùy chỉnh giao diện xem chi tiết của bản ghi. Admin có thể cấu hình layout mặc định, và sau này user trong tổ chức có thể tùy chỉnh layout cá nhân trong trang view.

#### **Cấu trúc giao diện**:
- **Nhiều section**: Mỗi section chứa nhiều **widget**.
  - Một số section **cho phép kéo-thả widget** để sắp xếp lại thứ tự hoặc vị trí.
  - Một số section **cố định**, không cho phép thay đổi (do admin định nghĩa).
- **Widget trong section**:
  - Mỗi widget hiển thị:
    - **Tên widget**: Tên ngắn gọn (ví dụ: "Thông tin liên hệ").
    - **Mô tả widget**: Chi tiết về widget (ví dụ: "Hiển thị số điện thoại và email").
    - **Dấu x**: Để xóa widget khỏi section (chỉ áp dụng cho section không cố định).
  - Widget có thể cấu hình qua modal hoặc inline (tùy chọn linh hoạt).

#### **Ý định**:
- Admin dùng tab này để thiết kế giao diện xem chi tiết mặc định cho layout.
- User có thể tùy chỉnh lại giao diện trong trang view theo nhu cầu cá nhân (nếu được phép).
- Section cố định đảm bảo một số yếu tố quan trọng không bị thay đổi, trong khi section kéo-thả tăng tính linh hoạt.

---

### **Hỗ trợ quản lý nhiều layout**
Hiện tại, `DynamicLayoutBuilderComponent` chưa linh hoạt như Zoho trong việc quản lý nhiều layout. Bạn muốn bổ sung:

#### **Dropdown chọn layout**:
- Vị trí: Phía trên component.
- Nội dung: Hiển thị danh sách tên hoặc title của các `LayoutConfig` (ví dụ: "Layout Khách hàng", "Layout Đơn hàng").
- Ý định: Khi chọn một layout từ dropdown, component sẽ hiển thị cấu hình của layout đó (bao gồm các tab Create, Quick Create, Detail View).

#### **Nút "Tạo layout mới"**:
- Vị trí: Bên cạnh dropdown.
- Chức năng: Khi nhấn nút, mở một form (modal hoặc inline) để nhập tên layout mới.
- Kết quả: Tạo một layout mới với cấu hình mặc định:
  - Có 3 tab: **Create**, **Quick Create**, **Detail View**.
  - Tab Create có 1 section rỗng.
  - Tab Quick Create và Detail View để trống, sẵn sàng cấu hình.

#### **Ý định**:
- Tăng tính linh hoạt, cho phép người dùng tạo và quản lý nhiều layout giống Zoho.
- Cấu hình mặc định giúp người dùng bắt đầu nhanh chóng.

---

### **Tóm tắt yêu cầu phát triển**
1. **Tab Quick Create**:
   - Bên trái: Hiển thị fields từ tab Create, có thể kéo sang bên phải.
   - Bên phải: 1 section duy nhất, chứa field với tên, loại, dấu x, giới hạn `minFields` và `maxFields`.
   - Thêm option `enable` để bật/tắt tab.
2. **Tab Detail View**:
   - Component riêng: `DetailViewComponent`.
   - Nhiều section, chứa widget (kéo-thả hoặc cố định).
   - Widget có tên, mô tả, dấu x (nếu xóa được).
   - Hỗ trợ admin cấu hình mặc định và user tùy chỉnh cá nhân.
3. **Quản lý nhiều layout**:
   - Thêm dropdown phía trên để chọn layout.
   - Thêm nút "Tạo layout mới" để tạo layout với cấu hình mặc định.

---

### **Kết luận**
Với các yêu cầu trên, `DynamicLayoutBuilderComponent` sẽ được mở rộng để có đầy đủ 3 tab (**Create**, **Quick Create**, **Detail View**) và khả năng quản lý nhiều layout, tương tự Zoho Canvas Builder. Tab Create giữ nguyên, Quick Create đơn giản hóa để tạo nhanh, Detail্র

Detail View linh hoạt với cấu hình admin và user, trong khi quản lý layout qua dropdown và nút tạo mới sẽ tăng tính linh hoạt. Mọi thứ cần rõ ràng, tách biệt component và logic để dễ bảo trì và tích hợp sau này.


9. **Test**
   - Test giao diện tại `http://localhost:4200/#/product/layouts/edit`.
   - Chạy `ng build` để kiểm tra lỗi.
   - Kiểm tra responsive trên Chrome DevTools (desktop và mobile).

