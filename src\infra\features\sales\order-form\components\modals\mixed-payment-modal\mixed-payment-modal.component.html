<div class="modal-container">
  <h2 class="modal-title">{{ 'SALES.MIXED_PAYMENT.TITLE' | translate }}</h2>

  <form [formGroup]="paymentForm" class="payment-form">
    <!-- Tổng tiền kh<PERSON>ch cần trả -->
    <div class="form-row">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.MIXED_PAYMENT.TOTAL_AMOUNT' | translate }}</mat-label>
        <input matInput type="number" formControlName="totalAmount" readonly>
      </mat-form-field>
    </div>

    <!-- Danh sách các phương thức thanh toán -->
    <div class="payments-container" formArrayName="payments">
      <div *ngFor="let payment of paymentsArray.controls; let i = index" class="payment-method-row" [formGroupName]="i">
        <div class="row">
          <!-- <PERSON><PERSON><PERSON><PERSON> thức thanh toán -->
          <div class="col-md-4">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>{{ 'SALES.MIXED_PAYMENT.PAYMENT_METHOD' | translate }}</mat-label>
              <mat-select formControlName="paymentMethod">
                <mat-option *ngFor="let method of paymentMethods" [value]="method.value">
                  {{ method.label | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Số tiền thanh toán -->
          <div class="col-md-4">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>{{ 'SALES.MIXED_PAYMENT.AMOUNT' | translate }}</mat-label>
              <input
                matInput
                #amountInput
                type="number"
                formControlName="amount"
                [matAutocomplete]="auto"
                (focus)="openQuickAmountSuggestions(i, auto)"
                (click)="openQuickAmountSuggestions(i, auto)"
                autocomplete="off">
              <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn" class="quick-amount-panel" panelWidth="auto">
                <mat-option *ngFor="let amount of getQuickAmountSuggestionsForIndex(i)[0]" [value]="amount" class="d-none">
                  {{ amount }}
                </mat-option>
                <div class="quick-amounts">
                  <div class="quick-amounts-row">
                    <button
                      *ngFor="let amount of getQuickAmountSuggestionsForIndex(i)[0]"
                      type="button"
                      class="quick-amount-btn"
                      (click)="selectQuickAmount(amount, i)">
                      {{ amount | number }}
                    </button>
                  </div>
                  <div class="quick-amounts-row">
                    <button
                      *ngFor="let amount of getQuickAmountSuggestionsForIndex(i)[1]"
                      type="button"
                      class="quick-amount-btn"
                      (click)="selectQuickAmount(amount, i)">
                      {{ amount | number }}
                    </button>
                  </div>
                </div>
              </mat-autocomplete>
            </mat-form-field>
          </div>

          <!-- Nút xóa phương thức thanh toán -->
          <div class="col-md-4 d-flex align-items-center">
            <button mat-icon-button color="warn" type="button" (click)="removePayment(i)"
                    [disabled]="paymentsArray.length <= 1">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>

        <!-- Chi tiết chuyển khoản -->
        <div class="payment-details" *ngIf="payment.get('paymentMethod')?.value === 'bank_transfer'" formGroupName="details">
          <div class="row">
            <div class="col-md-6">
              <!-- QR Code -->
              <div class="qr-code-container">
                <img [src]="qrCodeImage" alt="QR Code" class="qr-code-image">
                <p class="mt-2 text-center">{{ 'SALES.MIXED_PAYMENT.SCAN_QR' | translate }}</p>
              </div>
            </div>
            <div class="col-md-6" formGroupName="bankTransfer">
              <!-- Thông tin tài khoản ngân hàng -->
              <div formGroupName="account">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>{{ 'SALES.MIXED_PAYMENT.BANK' | translate }}</mat-label>
                  <mat-select formControlName="bankName">
                    <mat-option *ngFor="let bank of bankAccounts" [value]="bank.name">
                      {{ bank.name }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="w-100" *ngIf="payment.get('details.bankTransfer.account.bankName')?.value">
                  <mat-label>{{ 'SALES.MIXED_PAYMENT.ACCOUNT_NUMBER' | translate }}</mat-label>
                  <input matInput [value]="getSelectedBank(i)?.accountNumber" readonly>
                </mat-form-field>

                <mat-form-field appearance="outline" class="w-100" *ngIf="payment.get('details.bankTransfer.account.bankName')?.value">
                  <mat-label>{{ 'SALES.MIXED_PAYMENT.ACCOUNT_NAME' | translate }}</mat-label>
                  <input matInput [value]="getSelectedBank(i)?.accountName" readonly>
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Nút thêm phương thức thanh toán -->
    <div class="add-payment-btn-container">
      <button mat-flat-button color="primary" type="button" (click)="addPayment()">
        <mat-icon>add</mat-icon>
        {{ 'SALES.MIXED_PAYMENT.ADD_PAYMENT_METHOD' | translate }}
      </button>
    </div>

    <!-- Thông tin tổng -->
    <div class="payment-summary">
      <div class="form-row">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.MIXED_PAYMENT.CUSTOMER_PAID' | translate }}</mat-label>
          <input matInput type="number" [value]="paidAmount()" readonly>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'SALES.MIXED_PAYMENT.REMAINING_AMOUNT' | translate }}</mat-label>
          <input matInput type="number" [value]="remainingAmount()" readonly>
        </mat-form-field>
      </div>
    </div>

    <!-- Nút bấm -->
    <div class="modal-actions">
      <button mat-button type="button" (click)="cancel()">
        {{ 'COMMON.CANCEL' | translate }}
      </button>
      <button mat-flat-button color="primary" type="button" (click)="save()" [disabled]="paymentForm.invalid">
        {{ 'COMMON.SAVE' | translate }}
      </button>
    </div>
  </form>
</div>
