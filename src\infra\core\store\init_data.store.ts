import { Injectable, signal } from '@angular/core';
import { PosInitData } from 'salehub_shared_contracts';

@Injectable({
  providedIn: 'root'
})
export class InitDataStore {
  #_data = signal<PosInitData>({} as any);
  #_selectedStore!: {
    storeId: string,
    branchId: string,
    token: string
  } | null;

  getData() {
    return this.#_data();
  }

  getDataSignal() {
    return this.#_data;
  }

  updateInitData(data: PosInitData | null) {
    /**
     * update từ component thì chung 1 object
     */
    if(data !== this.getData()) {
      this.#_data.set({
        ...(this.getData() ?? {} as any),
        ...(data ?? {} as any)
      });
    }
  }

  updateStore(store: {
    storeId: string,
    branchId: string,
    token: string
  }) {
    this.#_selectedStore = store;
  }

  getSelectedStore() {
    return this.#_selectedStore;
  }

  destroy() {
    this.#_data.set({} as any);
    this.#_selectedStore = null;
  }
}
