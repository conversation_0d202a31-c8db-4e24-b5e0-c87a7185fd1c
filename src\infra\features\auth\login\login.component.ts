import { Component, OnDestroy } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '@core/services/auth.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    FormsModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent implements OnDestroy {
  input = {
    email: '',
    password: ''
  };

  err!: string;

  // Memory management: Container để quản lý tất cả subscriptions
  private readonly subscriptions = new Subscription();

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnDestroy(): void {
    // Memory management: Unsubscribe tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }

  resetError() {
    if(this.err) {
      this.err = '';
    }
  }

  submit() {
    // Memory management: Thêm subscription vào container để tự động cleanup
    this.subscriptions.add(
      this.authService.login(this.input)
        .subscribe({
          next: async () => {
            let redirectTo = this.route.snapshot.queryParams['redirect'] || '/';
            if(redirectTo === '/logout') {
              redirectTo = '/';
            }

            this.router.navigateByUrl(redirectTo);
          },
          error: (e) => {
            console.log(e);
            this.err = e.error?.message ?? e.error?.code ?? e.message ?? `Lỗi máy chủ: ${e.status}`;
          }
        })
    );
  }
}
