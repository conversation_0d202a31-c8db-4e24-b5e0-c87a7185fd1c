import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormatStringPipe } from '@shared/pipes/format_str.pipe';
import { FormatDatePipe } from '@shared/pipes/format_date.pipe';
import { PosInvoice } from 'salehub_shared_contracts';

@Component({
  selector: 'app-cashier-order-bill',
  standalone: true,
  imports: [
    FormatStringPipe,
    FormatDatePipe
  ],
  templateUrl: './order-bill.component.html',
  styleUrl: './order-bill.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CashierOrderBillComponent {
  @ViewChild('bill', { static: false }) element!: ElementRef;
  @Input() order!: PosInvoice;
  @Output() renderedEvent = new EventEmitter<boolean>();

  ngAfterViewInit() {
    const images: HTMLImageElement[] = this.element.nativeElement.querySelectorAll('img');
    const countImages = images.length;

    if(countImages > 0) {
      let loaded = 0;

      const check = () => {
        loaded++;

        if(loaded >= countImages) {
          this.renderedEvent.emit(true);
        }
      };

      for(let i = 0; i < images.length; i++) {
        if(images[i].complete && images[i].naturalWidth > 0) {
          check();
        } else {
          images[i].onload = check;
        }
      }
    } else {
      this.renderedEvent.emit(true);
    }
  }
}
