<!-- Modal container -->
<div class="connection-create-modal">
  
  <!-- Header với logo và tên platform -->
  <div class="modal-header mb-4">
    <div class="d-flex align-items-center">
      <div class="platform-logo me-3">
        <img [src]="platform()?.logo" [alt]="platform()?.name" class="img-fluid rounded" width="48">
      </div>
      <div class="platform-info">
        <h5 class="mb-1">{{ 'CONNECTION_CREATE_TITLE' | translate }}</h5>
        <p class="text-muted mb-0">
          {{ 'CONNECTION_CREATE_SUBTITLE' | translate: { platformName: platform()?.name } }}
        </p>
      </div>
    </div>
  </div>
  
  <!-- Content area -->
  <div class="modal-body">
    
    <!-- Loading overlay -->
    <div class="loading-overlay d-flex justify-content-center align-items-center" *ngIf="isLoading()">
      <mat-spinner diameter="40"></mat-spinner>
    </div>
    
    <!-- Account Login Form -->
    <div class="account-login-form" *ngIf="connectionType() === 'account_login'">
      <form [formGroup]="accountLoginForm" class="d-flex flex-column gap-3">
        
        <!-- Username/Email field -->
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'USERNAME_EMAIL' | translate }}</mat-label>
          <input 
            matInput 
            formControlName="username" 
            type="text" 
            autocomplete="username"
            [placeholder]="'USERNAME' | translate">
          <mat-error *ngIf="accountLoginForm.get('username')?.hasError('required')">
            {{ 'USERNAME_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
        
        <!-- Password field -->
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'PASSWORD' | translate }}</mat-label>
          <input 
            matInput 
            formControlName="password" 
            type="password" 
            autocomplete="current-password"
            [placeholder]="'PASSWORD' | translate">
          <mat-error *ngIf="accountLoginForm.get('password')?.hasError('required')">
            {{ 'PASSWORD_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
        
      </form>
    </div>
    
    <!-- API Authentication Form -->
    <div class="api-auth-form" *ngIf="connectionType() === 'api_auth'">
      <form [formGroup]="apiAuthForm" class="d-flex flex-column gap-3">
        
        <!-- Auth Type Selection -->
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ 'AUTH_TYPE' | translate }}</mat-label>
          <mat-select formControlName="authType">
            <mat-option value="api_key">{{ 'AUTH_TYPE_API_KEY' | translate }}</mat-option>
            <mat-option value="oauth">{{ 'AUTH_TYPE_OAUTH' | translate }}</mat-option>
          </mat-select>
        </mat-form-field>
        
        <!-- API Key field (shown only when authType is 'api_key') -->
        <mat-form-field appearance="outline" class="w-100" *ngIf="apiAuthForm.get('authType')?.value === 'api_key'">
          <mat-label>{{ 'API_KEY' | translate }}</mat-label>
          <input 
            matInput 
            formControlName="apiKey" 
            type="text" 
            [placeholder]="'API_KEY' | translate">
          <mat-error *ngIf="apiAuthForm.get('apiKey')?.hasError('required')">
            {{ 'API_KEY_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
        
        <!-- OAuth Connect button (shown only when authType is 'oauth') -->
        <div *ngIf="apiAuthForm.get('authType')?.value === 'oauth'" class="mt-2">
          <button 
            mat-raised-button 
            color="primary" 
            class="w-100 py-2"
            [disabled]="isLoading()"
            (click)="handleOAuthConnect()">
            <mat-icon class="me-2">link</mat-icon>
            {{ 'CONNECT_OAUTH' | translate }}
          </button>
        </div>
        
      </form>
    </div>
    
    <!-- Cookie Authentication Form -->
    <div class="cookie-auth-form" *ngIf="connectionType() === 'cookie_auth'">
      <form [formGroup]="cookieAuthForm" class="d-flex flex-column gap-3">
        
        <!-- Browser Auth Description -->
        <div class="browser-auth-info p-3 bg-light rounded border mb-3">
          <p class="mb-0">
            {{ 'BROWSER_AUTH_DESC' | translate: { platformName: platform()?.name } }}
          </p>
        </div>
        
        <!-- Browser Connect Button -->
        <button 
          mat-raised-button 
          color="primary" 
          class="w-100 py-2"
          [disabled]="isLoading() || cookieAuthForm.get('browserAuth')?.value === true"
          (click)="handleBrowserConnect()">
          <mat-icon class="me-2">open_in_browser</mat-icon>
          {{ 'CONNECT_BROWSER' | translate }}
        </button>
        
        <!-- Success message when connected -->
        <div *ngIf="cookieAuthForm.get('browserAuth')?.value === true" class="mt-2 text-success text-center">
          <mat-icon>check_circle</mat-icon>
          {{ 'CONNECTION_SUCCESS' | translate }}
        </div>
        
      </form>
    </div>
    
  </div>
  
  <!-- Footer với action buttons -->
  <div class="modal-footer d-flex justify-content-end gap-2 mt-4">
    <button 
      mat-button 
      type="button" 
      [disabled]="isLoading()"
      data-dismiss="modal">
      {{ 'CANCEL' | translate }}
    </button>
    
    <button 
      mat-raised-button 
      color="primary" 
      type="button"
      [disabled]="!isValid() || isLoading()">
      <span *ngIf="!isLoading()">{{ 'CONNECT_NOW' | translate }}</span>
      <span *ngIf="isLoading()">{{ 'CONNECTING' | translate }}</span>
    </button>
  </div>
  
</div>
