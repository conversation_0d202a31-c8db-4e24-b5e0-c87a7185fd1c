---
description:
globs:
alwaysApply: true
---
B<PERSON><PERSON> cảnh hiện tại:
- Tôi đang dùng Angular Material (https://material.angular.io/) và PrimeNG (https://primeng.org/)
- Tất cả global style đã được import vào index.html từ file [styles.scss](mdc:frontend/src/infra/shared/styles/styles.scss) bao gồm: Bootstrap, tailwind, angular material. Các icon font như: keen-icon, FontAwesome Pro, LineAwesome, Bootstrap icon, Material Icons.
- Tôi đang dùng theme metronic: https://keenthemes.com/metronic/tailwind/demo1/
- Angular 19 KHÔNG SỬ DỤNG module, chỉ sử dụng standalone components.
- Tất cả interface đều đã được định nghĩa trong module 'salehub_shared_contracts', bạn chỉ import interface để dùng, không được phép tự định nghĩa interface. Vì interface này định nghĩa data giữa frontend và backend, nếu bạn tự định nghĩa, backend sẽ sinh data không chính xác. Nếu bạn không tìm thấy interface, phải dừng lại để hỏi. Ví dụ câu lệnh import interface `Product` là: import { Product } from 'salehub_shared_contracts'. Không tự định nghĩa lại interface `Product`. Nếu bạn không biết cấu trúc của `Product`, hãy dừng lại và hỏi tôi để tôi cung cấp.
- Đã include module swiperjs để dùng cho các gesture, slide...
