import { Injectable } from '@angular/core';
import { Template } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.model';
import { simpleMockLayouts } from '@mock/product/simple_layout.mock';

/**
 * ProductLayoutService - Service quản lý dữ liệu layout sản phẩm
 * 
 * Chức năng:
 * - L<PERSON>y danh sách templates layout
 * - T<PERSON>h to<PERSON> thống kê (số section, số field)
 * - Quản lý dữ liệu layout theo Clean Architecture
 */
@Injectable({
  providedIn: 'root'
})
export class ProductLayoutService {

  /**
   * Lấy danh sách tất cả product layouts
   * @returns Mảng Template từ simple mock data
   */
  getProductLayouts(): Template[] {
    // Tạm thời return empty array vì simpleMockLayouts không có Template
    return [];
  }

  /**
   * Lấy layout theo index
   * @param index Vị trí của template trong mảng
   * @returns Template hoặc null nếu không tìm thấy
   */
  getProductLayoutByIndex(index: number): Template | null {
    const layouts = this.getProductLayouts();
    if (index >= 0 && index < layouts.length) {
      return layouts[index];
    }
    return null;
  }

  /**
   * Tính tổng số section trong một template
   * @param template Template cần tính
   * @returns Số lượng section
   */
  getSectionCount(template: Template): number {
    return template.sections?.length || 0;
  }

  /**
   * Tính tổng số field trong tất cả section của template
   * @param template Template cần tính
   * @returns Tổng số field
   */
  getTotalFieldCount(template: Template): number {
    if (!template.sections) {
      return 0;
    }
    
    return template.sections.reduce((total, section) => {
      return total + (section.fields?.length || 0);
    }, 0);
  }

  /**
   * Lấy thống kê chi tiết của một template
   * @param template Template cần thống kê
   * @returns Object chứa thông tin thống kê
   */
  getTemplateStats(template: Template): {
    sectionCount: number;
    fieldCount: number;
    requiredFieldCount: number;
    optionalFieldCount: number;
  } {
    const sectionCount = this.getSectionCount(template);
    const fieldCount = this.getTotalFieldCount(template);
    
    let requiredFieldCount = 0;
    let optionalFieldCount = 0;

    if (template.sections) {
      template.sections.forEach(section => {
        if (section.fields) {
          section.fields.forEach(field => {
            if (field.required || field.isRequired) {
              requiredFieldCount++;
            } else {
              optionalFieldCount++;
            }
          });
        }
      });
    }

    return {
      sectionCount,
      fieldCount,
      requiredFieldCount,
      optionalFieldCount
    };
  }

  /**
   * Kiểm tra template có hợp lệ không
   * @param template Template cần kiểm tra
   * @returns true nếu hợp lệ
   */
  isValidTemplate(template: Template): boolean {
    return !!(
      template &&
      template.name &&
      template.sections &&
      template.sections.length > 0
    );
  }

  /**
   * Tìm kiếm template theo tên
   * @param searchTerm Từ khóa tìm kiếm
   * @returns Mảng Template phù hợp
   */
  searchTemplates(searchTerm: string): Template[] {
    if (!searchTerm.trim()) {
      return this.getProductLayouts();
    }

    const term = searchTerm.toLowerCase().trim();
    return this.getProductLayouts().filter(template =>
      template.name.toLowerCase().includes(term)
    );
  }
}
