import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatDatepickerModule, MatDatepickerInputEvent } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { MixedPaymentModalService } from '@features/sales/order-form/components/modals/mixed-payment-modal/mixed-payment-modal.service';
import { CreateOrderRequest } from 'salehub_shared_contracts/requests/sales/create_order';
import { OrderPayment } from 'salehub_shared_contracts/entities/oms/order/order_components/order_payment';

@Component({
  selector: 'app-summary-rows',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatRadioModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule
  ],
  templateUrl: './summary-rows.component.html',
  styleUrls: ['./summary-rows.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SummaryRowsComponent {
  @Input() order: CreateOrderRequest;
  @Input() summary: {
    totalItemsAmount: number;
    discountAmount: number;
    deliveryFee: number;
    surchargeFee: number;
    finalAmount: number;
    paidAmount: number;
    change: number;
    debt: number;
    currentDebt: number;
  } = {
      totalItemsAmount: 0,
      discountAmount: 0,
      deliveryFee: 0,
      surchargeFee: 0,
      finalAmount: 0,
      paidAmount: 0,
      change: 0,
      debt: 0,
      currentDebt: 0
    };

  @Output() summaryUpdated = new EventEmitter<any>();

  // Mock data
  mockBankList = [
    { id: '1', name: 'Vietcombank - *********', bankName: 'Vietcombank', accountNumber: '*********' },
    { id: '2', name: 'MB Bank - *********', bankName: 'MB Bank', accountNumber: '*********' },
    { id: '3', name: 'Techcombank - *********', bankName: 'Techcombank', accountNumber: '*********' }
  ];

  mockOrderStatuses = [
    { id: 'pending', name: 'Đang xử lý' },
    { id: 'confirmed', name: 'Đã xác nhận' },
    { id: 'delivering', name: 'Đang giao hàng' },
    { id: 'completed', name: 'Đã giao' },
    { id: 'cancelled', name: 'Hủy' }
  ];

  // Quick value options for payment amount
  quickPaymentOptions: number[] = [];
  paymentMethod: 'cash' | 'bank_transfer' | 'credit_card' | 'ewallet' = 'cash';
  isMixedPayment = false;
  selectedBank = '';
  deliveryOption: 'asap' | 'scheduled' = 'asap';
  scheduledTime: Date | null = null;

  constructor(
    private dialog: MatDialog,
    private mixedPaymentModalService: MixedPaymentModalService
  ) {}

  ngOnChanges(): void {
    this.updateQuickPaymentOptions();
  }

  // Update quick payment options based on final amount
  updateQuickPaymentOptions(): void {
    if (this.summary && this.summary.finalAmount > 0) {
      const baseAmount = Math.ceil(this.summary.finalAmount / 1000) * 1000;
      this.quickPaymentOptions = [
        baseAmount,
        baseAmount + 5000,
        baseAmount + 10000,
        baseAmount + 20000,
        baseAmount + 50000,
        baseAmount + 100000,
        baseAmount + 200000,
        baseAmount + 500000
      ];
    } else {
      this.quickPaymentOptions = [0, 5000, 10000, 20000, 50000, 100000, 200000, 500000];
    }
  }

  // Xử lý khi cập nhật phụ thu
  onSurchargeFeeChange(value: number): void {
    if (!this.order.customerFare) {
      this.order.customerFare = {};
    }
    this.order.customerFare.surchargeFee = value;
    this.summaryUpdated.emit({ surchargeFee: value });
  }

  // Xử lý khi cập nhật số tiền khách thanh toán
  onPaidAmountChange(value: number): void {
    if (!this.order.payment) {
      this.order.payment = {
        totalAmount: this.summary.finalAmount,
        paidAmount: value,
        remainingAmount: this.summary.finalAmount - value,
        payments: [],
        paymentStatus: value >= this.summary.finalAmount ? 'paid' : value > 0 ? 'partially_paid' : 'unpaid'
      };
    } else {
      this.order.payment.paidAmount = value;
      this.order.payment.remainingAmount = this.summary.finalAmount - value;
      this.order.payment.paymentStatus = value >= this.summary.finalAmount ? 'paid' : value > 0 ? 'partially_paid' : 'unpaid';
    }

    // Cập nhật phương thức thanh toán nếu chưa có
    if (!this.order.payment.payments || this.order.payment.payments.length === 0) {
      this.order.payment.payments = [{
        paymentMethod: this.paymentMethod,
        amount: value,
        paidAt: new Date()
      }];
    } else {
      // Cập nhật phương thức thanh toán hiện tại
      this.order.payment.payments[0].amount = value;
    }

    this.summaryUpdated.emit({ payment: this.order.payment });
  }

  // Xử lý khi chọn phương thức thanh toán
  onPaymentMethodChange(method: 'cash' | 'bank_transfer' | 'credit_card' | 'ewallet'): void {
    this.paymentMethod = method;

    if (!this.order.payment) {
      this.order.payment = {
        totalAmount: this.summary.finalAmount,
        paidAmount: 0,
        remainingAmount: this.summary.finalAmount,
        payments: [{
          paymentMethod: method,
          amount: 0,
          paidAt: new Date()
        }],
        paymentStatus: 'unpaid'
      };
    } else if (this.order.payment.payments && this.order.payment.payments.length > 0) {
      // Cập nhật phương thức thanh toán cho payment đầu tiên
      this.order.payment.payments[0].paymentMethod = method;

      // Nếu là chuyển khoản và đã chọn ngân hàng
      if (method === 'bank_transfer' && this.selectedBank) {
        const bank = this.mockBankList.find(b => b.id === this.selectedBank);
        if (bank) {
          this.order.payment.payments[0].details = {
            bankTransfer: {
              account: {
                accountNumber: bank.accountNumber,
                accountHolder: 'Shop Owner', // Placeholder
                bankName: bank.bankName
              }
            }
          };
        }
      }
    }

    this.summaryUpdated.emit({ payment: this.order.payment });
  }

  // Xử lý khi chọn ngân hàng (cho phương thức chuyển khoản)
  onBankChange(bankId: string): void {
    this.selectedBank = bankId;

    if (this.order.payment && this.order.payment.payments && this.order.payment.payments.length > 0) {
      const bank = this.mockBankList.find(b => b.id === bankId);
      if (bank) {
        this.order.payment.payments[0].details = {
          bankTransfer: {
            account: {
              accountNumber: bank.accountNumber,
              accountHolder: 'Shop Owner', // Placeholder
              bankName: bank.bankName
            }
          }
        };

        this.summaryUpdated.emit({ payment: this.order.payment });
      }
    }
  }

  // Xử lý khi chọn mở thanh toán hỗn hợp
  async openMixedPaymentDialog(): Promise<void> {
    const paymentData: OrderPayment = {
      totalAmount: this.summary.finalAmount,
      payments: this.order.payment?.payments || [],
      paidAmount: this.order.payment?.paidAmount || 0,
      remainingAmount: this.order.payment?.remainingAmount || this.summary.finalAmount,
      paymentStatus: this.order.payment?.paymentStatus || 'unpaid'
    };

    try {
      const result = await this.mixedPaymentModalService.open(paymentData);

      if (result && result.payments) {
        this.isMixedPayment = true;

        // Tính tổng số tiền đã thanh toán từ tất cả phương thức
        const totalPaid = result.payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);

        if (!this.order.payment) {
          this.order.payment = {
            totalAmount: this.summary.finalAmount,
            paidAmount: totalPaid,
            remainingAmount: this.summary.finalAmount - totalPaid,
            payments: result.payments,
            paymentStatus: totalPaid >= this.summary.finalAmount ? 'paid' : totalPaid > 0 ? 'partially_paid' : 'unpaid'
          };
        } else {
          this.order.payment.paidAmount = totalPaid;
          this.order.payment.remainingAmount = this.summary.finalAmount - totalPaid;
          this.order.payment.payments = result.payments;
          this.order.payment.paymentStatus = totalPaid >= this.summary.finalAmount ? 'paid' : totalPaid > 0 ? 'partially_paid' : 'unpaid';
        }

        this.summaryUpdated.emit({ payment: this.order.payment });
      }
    } catch (error) {
      console.error('Error opening mixed payment modal:', error);
    }
  }

  // Xử lý khi thay đổi thời gian giao hàng
  onDeliveryOptionChange(option: 'asap' | 'scheduled'): void {
    this.deliveryOption = option;

    if (option === 'scheduled') {
      // Đã chọn giao hàng theo lịch, cần thêm lịch
      if (!this.scheduledTime) {
        // Mặc định là 1 ngày sau, vào lúc 12h trưa
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(12, 0, 0, 0);
        this.scheduledTime = tomorrow;
      }

      // Cập nhật vào order
      if (!this.order.scheduledOrderInfo) {
        this.order.scheduledOrderInfo = {};
      }
      this.order.scheduledOrderInfo.expectedDeliveryTime = this.scheduledTime;
      this.order.status = 'preorder';
    } else {
      // Đã chọn giao hàng ngay, bỏ lịch đã đặt
      if (this.order.scheduledOrderInfo) {
        this.order.scheduledOrderInfo.expectedDeliveryTime = undefined;
      }
      // Nếu trạng thái là preorder, chuyển về pending
      if (this.order.status === 'preorder') {
        this.order.status = 'pending';
      }
    }

    this.summaryUpdated.emit({
      scheduledOrderInfo: this.order.scheduledOrderInfo,
      status: this.order.status
    });
  }

  // Xử lý khi thay đổi thời gian giao hàng theo lịch
  onScheduledTimeChange(event: MatDatepickerInputEvent<Date>): void {
    this.scheduledTime = event.value;

    if (this.scheduledTime) {
      if (!this.order.scheduledOrderInfo) {
        this.order.scheduledOrderInfo = {};
      }
      this.order.scheduledOrderInfo.expectedDeliveryTime = this.scheduledTime;
      // Đánh dấu là preorder nếu có lịch giao hàng
      this.order.status = 'preorder';
    } else {
      // Nếu xóa ngày, bỏ thông tin lịch và có thể quay lại trạng thái mặc định
      if (this.order.scheduledOrderInfo) {
        delete this.order.scheduledOrderInfo.expectedDeliveryTime;
      }
      // Cân nhắc: Có nên đổi status về 'pending' nếu không còn lịch?
      // if (this.order.status === 'preorder') {
      //   this.order.status = 'pending';
      // }
    }
    this.summaryUpdated.emit({ scheduledOrderInfo: this.order.scheduledOrderInfo, status: this.order.status });
  }

  // Xử lý khi thay đổi trạng thái đơn hàng
  onStatusChange(status: string): void {
    this.order.status = status as any;
    this.summaryUpdated.emit({ status });
  }

  // Chọn một giá trị nhanh từ danh sách gợi ý
  selectQuickPayment(amount: number): void {
    if (this.order && this.order.payment) {
      this.order.payment.paidAmount = amount;
      this.onPaidAmountChange(amount);
    }
  }
}
