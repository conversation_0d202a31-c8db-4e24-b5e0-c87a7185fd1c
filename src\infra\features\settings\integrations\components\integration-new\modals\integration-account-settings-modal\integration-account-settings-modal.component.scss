/* Integration Account Settings Modal Styles */

// Modal header styling
.modal-header {
  h4 {
    color: #333;
    font-weight: 600;
  }
  
  p {
    color: #6c757d;
    font-size: 0.875rem;
  }
  
  mat-icon {
    font-size: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

// Modal content styling
.modal-content-settings {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  
  // Settings container
  .settings-container {
    :deep(.settings-card) {
      box-shadow: none;
      border: 1px solid #e0e0e0;
    }
  }
}

// Modal footer styling
.modal-footer {
  .status-info {
    .small-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      vertical-align: middle;
    }
  }
  
  .settings-info {
    font-size: 0.8rem;
  }
}

// Empty state styling
.text-center {
  .display-4 {
    font-size: 3rem;
    opacity: 0.5;
  }
  
  h5 {
    margin-bottom: 0.5rem;
  }
  
  p {
    font-size: 0.875rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-header {
    h4 {
      font-size: 1.1rem;
    }
    
    p {
      font-size: 0.8rem;
    }
  }
  
  .modal-content-settings {
    min-height: 300px;
    max-height: 500px;
    padding: 1rem !important;
  }
  
  .modal-footer {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 0.5rem;
    
    .status-info,
    .settings-info {
      width: 100%;
    }
  }
}

// Dark mode support (optional)
@media (prefers-color-scheme: dark) {
  .modal-header {
    h4 {
      color: #f8f9fa;
    }
    
    p {
      color: #adb5bd;
    }
  }
  
  .modal-content-settings {
    .settings-container {
      :deep(.settings-card) {
        background-color: #495057;
        border-color: #6c757d;
      }
    }
  }
}
