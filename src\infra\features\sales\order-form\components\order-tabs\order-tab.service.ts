import { Injectable, signal } from '@angular/core';
import { CreateOrderRequest } from 'salehub_shared_contracts/requests/sales/create_order';

/**
 * Service để quản lý các tab đơn hàng
 */
@Injectable({
  providedIn: 'root'
})
export class OrderTabService {
  /**
   * Signal quản lý danh sách các tab đơn hàng
   */
  tabs = signal<CreateOrderRequest[]>([]);

  /**
   * Signal lưu giữ tab active hiện tại
   */
  activeTabIndex = signal<number>(0);

  constructor() {
    // Khôi phục trạng thái từ localStorage nếu có
    this.restoreStateFromLocalStorage();
  }

  /**
   * Khôi phục trạng thái từ localStorage
   */
  private restoreStateFromLocalStorage(): void {
    try {
      const savedTabs = localStorage.getItem('order-tabs');
      if (savedTabs) {
        const parsedTabs = JSON.parse(savedTabs) as CreateOrderRequest[];
        this.tabs.set(parsedTabs);
      } else {
        // Nếu không có dữ liệu, tạo tab mặc định
        this.addTab();
      }

      const savedActiveIndex = localStorage.getItem('order-tabs-active-index');
      if (savedActiveIndex) {
        const parsedIndex = parseInt(savedActiveIndex, 10);
        if (!Number.isNaN(parsedIndex) && parsedIndex >= 0 && parsedIndex < this.tabs().length) {
          this.activeTabIndex.set(parsedIndex);
        }
      }
    } catch (error) {
      console.error('Lỗi khi khôi phục trạng thái từ localStorage:', error);
      // Nếu có lỗi, tạo tab mặc định
      this.addTab();
    }
  }

  /**
   * Lưu trạng thái vào localStorage
   */
  private saveStateToLocalStorage(): void {
    try {
      localStorage.setItem('order-tabs', JSON.stringify(this.tabs()));
      localStorage.setItem('order-tabs-active-index', this.activeTabIndex().toString());
    } catch (error) {
      console.error('Lỗi khi lưu trạng thái vào localStorage:', error);
    }
  }

  /**
   * Thêm tab mới với đơn hàng trống
   */
  addTab(): void {
    const newTabs = [...this.tabs(), {} as CreateOrderRequest];
    this.tabs.set(newTabs);
    this.activeTabIndex.set(newTabs.length - 1);
    this.saveStateToLocalStorage();
  }

  /**
   * Xóa tab tại vị trí index
   * @param index Vị trí tab cần xóa
   */
  removeTab(index: number): void {
    if (index < 0 || index >= this.tabs().length) {
      return;
    }

    const newTabs = [...this.tabs()];
    newTabs.splice(index, 1);
    this.tabs.set(newTabs);

    // Cập nhật activeTabIndex sau khi xóa
    if (newTabs.length === 0) {
      // Nếu không còn tab nào, tạo tab mới
      this.addTab();
    } else if (this.activeTabIndex() >= newTabs.length) {
      // Nếu activeTabIndex vượt quá số lượng tab mới, đặt lại thành tab cuối cùng
      this.activeTabIndex.set(newTabs.length - 1);
    } else if (this.activeTabIndex() === index) {
      // Nếu tab đang active bị xóa, đặt lại activeTabIndex
      this.activeTabIndex.set(Math.max(0, index - 1));
    }

    this.saveStateToLocalStorage();
  }

  /**
   * Lấy đơn hàng của tab đang active
   * @returns Đơn hàng của tab đang active
   */
  getActiveTab(): CreateOrderRequest {
    return this.tabs()[this.activeTabIndex()] || {};
  }

  /**
   * Đặt tab tại vị trí index làm tab active
   * @param index Vị trí tab cần active
   */
  setActiveTab(index: number): void {
    if (index >= 0 && index < this.tabs().length) {
      this.activeTabIndex.set(index);
      this.saveStateToLocalStorage();
    }
  }

  /**
   * Cập nhật đơn hàng tại tab đang active
   * @param order Đơn hàng cần cập nhật
   */
  updateActiveTab(order: CreateOrderRequest): void {
    const index = this.activeTabIndex();
    const newTabs = [...this.tabs()];
    newTabs[index] = order;
    this.tabs.set(newTabs);
    this.saveStateToLocalStorage();
  }
}
