// Field Item Component Styles
.field-item-container {
  margin-bottom: 1rem;
  
  // Read-only state styling
  &.read-only {
    .field-label-container {
      opacity: 0.8;
    }
  }
}

// Field Label Styles
.field-label-container {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  
  .field-label {
    font-weight: 600;
    color: #333;
    margin: 0;
    font-size: 0.9rem;
    
    .required-asterisk {
      color: #dc3545;
      margin-left: 0.25rem;
    }
  }
  
  .read-only-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #6c757d;
    cursor: help;
  }
}

// Field Value Styles
.field-value-container {
  .field-view-value {
    padding: 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    color: #495057;
    font-size: 0.9rem;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
  }
  
  // Form field styling
  .mat-mdc-form-field {
    width: 100%;
    
    // Read-only input styling
    .read-only-cursor {
      cursor: not-allowed !important;
      background-color: #f8f9fa;
      
      &:focus {
        background-color: #f8f9fa;
      }
    }
  }
  
  // Checkbox specific styling
  .field-checkbox {
    margin-top: 0.5rem;
    
    &.mat-mdc-checkbox-disabled {
      opacity: 0.6;
    }
  }
}

// Field Tooltip Styles
.field-tooltip {
  margin-top: 0.25rem;
  
  small {
    font-size: 0.8rem;
    line-height: 1.3;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .field-item-container {
    margin-bottom: 0.75rem;
    
    .field-label-container {
      margin-bottom: 0.4rem;
      
      .field-label {
        font-size: 0.85rem;
      }
      
      .read-only-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }
    }
    
    .field-value-container {
      .field-view-value {
        padding: 0.6rem;
        font-size: 0.85rem;
        min-height: 2.2rem;
      }
    }
  }
}

@media (max-width: 576px) {
  .field-item-container {
    margin-bottom: 0.5rem;
    
    .field-label-container {
      margin-bottom: 0.3rem;
      
      .field-label {
        font-size: 0.8rem;
      }
    }
    
    .field-value-container {
      .field-view-value {
        padding: 0.5rem;
        font-size: 0.8rem;
        min-height: 2rem;
      }
    }
    
    .field-tooltip {
      margin-top: 0.2rem;
      
      small {
        font-size: 0.75rem;
      }
    }
  }
}

// Animation for state changes
.field-item-container {
  transition: opacity 0.2s ease-in-out;
  
  &.read-only {
    .field-value-container {
      transition: background-color 0.2s ease-in-out;
    }
  }
}
