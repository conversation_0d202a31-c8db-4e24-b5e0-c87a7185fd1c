import { I18nService } from '@core/services/i18n.service';
import { CdkMenu, CdkMenuTrigger } from '@angular/cdk/menu';
import { ChangeDetectionStrategy, Component, ViewEncapsulation } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { Popover } from 'primeng/popover';
import { CdkAccordionModule } from '@angular/cdk/accordion';


@Component({
  selector: 'app-user-menu',
  imports: [
    MatMenuModule,
    CdkAccordionModule
  ],
  templateUrl: './user-menu.component.html',
  styleUrl: './user-menu.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserMenuComponent {
  currentLang = 'vi';

  constructor(private i18nService: I18nService) {}

  ngOnInit(): void {
    // Lấy ngôn ngữ hiện tại từ service
    this.currentLang = this.i18nService.getCurrentLang();
  }

  switchLanguage(lang: string): void {
    // Chuyển đổi ngôn ngữ thông qua service
    this.i18nService.switchLanguage(lang);
    // Cập nhật ngôn ngữ hiện tại
    this.currentLang = lang;
  }

}
