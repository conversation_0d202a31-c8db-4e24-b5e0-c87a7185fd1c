import { ChangeDetectionStrategy, Component, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { StrictModalComponent } from '@shared/components/standard-dialog/interfaces/modal-component.interface';
import { FlashMessageService } from '@core/services/flash_message.service';
import { 
  ConnectionCreateModalData, 
  ConnectionCreateModalResult,
  AccountLoginFormData,
  ApiAuthFormData,
  CookieAuthFormData
} from './connection-create-modal.interfaces';

/**
 * Modal component để tạo kết nối mới với integration platform
 * Hỗ trợ 3 loại authentication: account_login, api_auth, cookie_auth
 */
@Component({
  selector: 'app-connection-create-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  templateUrl: './connection-create-modal.component.html',
  styleUrls: ['./connection-create-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConnectionCreateModalComponent implements StrictModalComponent<ConnectionCreateModalData, ConnectionCreateModalResult> {
  
  // Dữ liệu đầu vào từ modal service
  data = signal<ConnectionCreateModalData | null>(null);
  
  // Trạng thái loading
  isLoading = signal<boolean>(false);
  
  // Form groups cho các loại authentication khác nhau
  accountLoginForm!: FormGroup;
  apiAuthForm!: FormGroup;
  cookieAuthForm!: FormGroup;
  
  // Computed properties
  platform = computed(() => this.data()?.platform);
  connectionType = computed(() => this.platform()?.connectionMethod.type);
  
  constructor(
    private fb: FormBuilder,
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {
    // Khởi tạo forms
    this.initializeForms();
  }
  
  /**
   * Khởi tạo các form groups cho từng loại authentication
   */
  private initializeForms(): void {
    // Form cho account login
    this.accountLoginForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
    
    // Form cho API authentication
    this.apiAuthForm = this.fb.group({
      authType: ['api_key', Validators.required],
      apiKey: ['', Validators.required]
    });
    
    // Form cho cookie authentication
    this.cookieAuthForm = this.fb.group({
      browserAuth: [false]
    });
    
    // Watch authType changes để toggle API key field
    this.apiAuthForm.get('authType')?.valueChanges.subscribe(authType => {
      const apiKeyControl = this.apiAuthForm.get('apiKey');
      if (authType === 'api_key') {
        apiKeyControl?.setValidators([Validators.required]);
        apiKeyControl?.enable();
      } else {
        apiKeyControl?.clearValidators();
        apiKeyControl?.disable();
      }
      apiKeyControl?.updateValueAndValidity();
    });
  }
  
  /**
   * Lấy form hiện tại dựa trên connection type
   */
  getCurrentForm(): FormGroup {
    switch (this.connectionType()) {
      case 'account_login':
        return this.accountLoginForm;
      case 'api_auth':
        return this.apiAuthForm;
      case 'cookie_auth':
        return this.cookieAuthForm;
      default:
        return this.accountLoginForm;
    }
  }
  
  /**
   * Xử lý kết nối OAuth
   */
  async handleOAuthConnect(): Promise<void> {
    this.isLoading.set(true);
    
    try {
      // Simulate OAuth flow
      await this.simulateOAuthFlow();
      
      this.flashMessageService.success(
        this.translateService.instant('CONNECTION_SUCCESS')
      );
      
    } catch (error) {
      this.flashMessageService.error(
        this.translateService.instant('CONNECTION_ERROR')
      );
    } finally {
      this.isLoading.set(false);
    }
  }
  
  /**
   * Xử lý kết nối browser authentication
   */
  async handleBrowserConnect(): Promise<void> {
    this.isLoading.set(true);
    
    try {
      // Simulate browser authentication
      await this.simulateBrowserAuth();
      
      this.cookieAuthForm.patchValue({ browserAuth: true });
      
      this.flashMessageService.success(
        this.translateService.instant('CONNECTION_SUCCESS')
      );
      
    } catch (error) {
      this.flashMessageService.error(
        this.translateService.instant('CONNECTION_ERROR')
      );
    } finally {
      this.isLoading.set(false);
    }
  }
  
  /**
   * Simulate OAuth authentication flow
   */
  private async simulateOAuthFlow(): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Simulate success/failure
        if (Math.random() > 0.2) {
          resolve();
        } else {
          reject(new Error('OAuth failed'));
        }
      }, 2000);
    });
  }
  
  /**
   * Simulate browser authentication
   */
  private async simulateBrowserAuth(): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Simulate success/failure
        if (Math.random() > 0.1) {
          resolve();
        } else {
          reject(new Error('Browser auth failed'));
        }
      }, 1500);
    });
  }
  
  /**
   * Validate form dựa trên connection type
   */
  private validateCurrentForm(): boolean {
    const currentForm = this.getCurrentForm();
    
    if (!currentForm.valid) {
      // Mark all fields as touched để hiển thị errors
      Object.keys(currentForm.controls).forEach(key => {
        currentForm.get(key)?.markAsTouched();
      });
      return false;
    }
    
    return true;
  }
  
  // Implementation của StrictModalComponent interface
  
  /**
   * Cập nhật dữ liệu cho modal
   */
  updateData(data: ConnectionCreateModalData): void {
    this.data.set(data);
  }
  
  /**
   * Kiểm tra modal có valid không
   */
  isValid(): boolean {
    if (this.isLoading()) return false;
    
    const connectionType = this.connectionType();
    
    switch (connectionType) {
      case 'account_login':
        return this.accountLoginForm.valid;
      case 'api_auth':
        return this.apiAuthForm.valid;
      case 'cookie_auth':
        return this.cookieAuthForm.get('browserAuth')?.value === true;
      default:
        return false;
    }
  }
  
  /**
   * Lấy kết quả từ modal
   */
  getModalResult(): ConnectionCreateModalResult {
    if (!this.validateCurrentForm()) {
      return {
        success: false,
        error: this.translateService.instant('INVALID_CREDENTIALS')
      };
    }
    
    const connectionType = this.connectionType();
    const result: ConnectionCreateModalResult = { success: true };
    
    switch (connectionType) {
      case 'account_login':
        const accountData = this.accountLoginForm.value as AccountLoginFormData;
        result.connectionData = {
          username: accountData.username,
          password: accountData.password
        };
        break;
        
      case 'api_auth':
        const apiData = this.apiAuthForm.value as ApiAuthFormData;
        result.connectionData = {
          authType: apiData.authType,
          apiKey: apiData.authType === 'api_key' ? apiData.apiKey : undefined
        };
        break;
        
      case 'cookie_auth':
        const cookieData = this.cookieAuthForm.value as CookieAuthFormData;
        result.connectionData = {
          browserAuth: cookieData.browserAuth
        };
        break;
    }
    
    return result;
  }
  
  /**
   * Được gọi khi modal mở
   */
  onModalOpen(): void {
    // Reset forms khi modal mở
    this.accountLoginForm.reset();
    this.apiAuthForm.reset({ authType: 'api_key' });
    this.cookieAuthForm.reset({ browserAuth: false });
  }
  
  /**
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Cho phép đóng modal nếu không đang loading
    return !this.isLoading();
  }
}
