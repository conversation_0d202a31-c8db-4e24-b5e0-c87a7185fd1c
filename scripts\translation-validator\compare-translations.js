#!/usr/bin/env node
import { readFile, stat, readdir } from 'fs/promises';
import { join, resolve, dirname } from 'path';

// Từ điển chuẩn cho bản dịch
const translationDictionary = {
  Save: "<PERSON><PERSON><PERSON>",
  Cancel: "<PERSON><PERSON><PERSON>",
  Submit: "<PERSON><PERSON>c <PERSON>",
  Create: "<PERSON>ạo",
  Order: "Đơn Hàng",
  Title: "Ti<PERSON><PERSON>",
  Confirm: "Xác Nhận",
  Panel: "Bảng Điều Khiển",
  Dialog: "Hộp Thoại"
};

// Hàm chuyển JSON phân cấp thành danh sách key phẳng
function flattenKeys(obj, prefix = '') {
  const keys = [];
  for (const [key, value] of Object.entries(obj)) {
    const newKey = prefix ? `${prefix}.${key}` : key;
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...flattenKeys(value, newKey));
    } else {
      keys.push(newKey);
    }
  }
  return keys;
}

// Hàm kiểm tra key trùng lặp trong một file
function checkDuplicateKeysInFile(keys, filePath) {
  const keyCount = {};
  const duplicates = [];

  for (const key of keys) {
    keyCount[key] = (keyCount[key] || 0) + 1;
    if (keyCount[key] > 1) {
      duplicates.push({ key, count: keyCount[key] });
    }
  }

  if (duplicates.length > 0) {
    console.error(`Lỗi: Phát hiện key trùng lặp trong ${filePath}:`);
    for (const { key, count } of duplicates) {
      console.error(`  - Key "${key}" xuất hiện ${count} lần.`);
      console.error(`    Đề xuất: Xóa hoặc hợp nhất các key trùng lặp trong ${filePath}.`);
    }
    return false;
  }
  return true;
}

// Hàm kiểm tra key trùng lặp trên toàn bộ src/infra/i18n/
async function checkDuplicateKeysGlobally(folderPath) {
  const i18nPath = resolve(folderPath, '../../..'); // Lên 3 cấp từ global/ hoặc feature/sub-feature/
  const allKeys = new Map(); // Lưu key và file chứa key
  let hasError = false;

  async function scanDirectory(dir) {
    try {
      const entries = await readdir(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = join(dir, entry.name);
        if (entry.isDirectory()) {
          await scanDirectory(fullPath);
        } else if (entry.isFile() && entry.name === 'en.json') {
          const json = await readJsonFile(fullPath);
          if (json) {
            const keys = flattenKeys(json);
            for (const key of keys) {
              if (allKeys.has(key)) {
                console.error(`Lỗi: Key "${key}" trùng lặp:`);
                console.error(`  - Xuất hiện trong ${allKeys.get(key)}`);
                console.error(`  - Xuất hiện trong ${fullPath}`);
                console.error(`    Đề xuất: Sử dụng key khác (ví dụ: ${key}_NEW) hoặc kiểm tra ngữ nghĩa.`);
                hasError = true;
              } else {
                allKeys.set(key, fullPath);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error(`Lỗi khi duyệt thư mục ${dir}: ${error.message}`);
      hasError = true;
    }
  }

  // Kiểm tra thư mục i18nPath tồn tại
  try {
    const stats = await stat(i18nPath);
    if (!stats.isDirectory()) {
      console.error(`Lỗi: Thư mục i18n không tồn tại: ${i18nPath}`);
      return false;
    }
  } catch (error) {
    console.error(`Lỗi: Không thể truy cập thư mục i18n: ${i18nPath}`);
    return false;
  }

  await scanDirectory(i18nPath);
  return !hasError;
}

// Hàm đọc và phân tích file JSON
async function readJsonFile(filePath) {
  try {
    const content = await readFile(filePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.error(`Lỗi: File không tồn tại: ${filePath}`);
    } else {
      console.error(`Lỗi khi đọc file ${filePath}: ${error.message}`);
    }
    return null;
  }
}

// Hàm kiểm tra ngữ nghĩa bản dịch
function checkTranslationSemantics(enJson, viJson, filePath) {
  const enKeys = flattenKeys(enJson);
  const viKeys = flattenKeys(viJson);
  let hasError = false;

  for (const key of enKeys) {
    if (viKeys.includes(key)) {
      const enValue = getValueByKey(enJson, key);
      const viValue = getValueByKey(viJson, key);
      const expectedVi = translationDictionary[enValue];
      if (expectedVi && viValue !== expectedVi) {
        console.error(`Lỗi: Bản dịch không nhất quán trong ${filePath}:`);
        console.error(`  - Key: ${key}`);
        console.error(`  - en.json: "${enValue}"`);
        console.error(`  - vi.json: "${viValue}"`);
        console.error(`    Đề xuất: Sửa vi.json thành "${expectedVi}".`);
        hasError = true;
      }
    }
  }

  return !hasError;
}

// Hàm lấy giá trị theo key phẳng
function getValueByKey(obj, key) {
  const parts = key.split('.');
  let current = obj;
  for (const part of parts) {
    current = current[part];
    if (!current) return null;
  }
  return current;
}

// Hàm so sánh key giữa hai file JSON
async function compareTranslations(folderPath) {
  const enFile = join(folderPath, 'en.json');
  const viFile = join(folderPath, 'vi.json');

  // Kiểm tra folder tồn tại
  try {
    const stats = await stat(folderPath);
    if (!stats.isDirectory()) {
      console.error(`Lỗi: Đường dẫn không phải thư mục: ${folderPath}`);
      return false;
    }
  } catch (error) {
    console.error(`Lỗi: Thư mục không tồn tại: ${folderPath}`);
    return false;
  }

  // Đọc file en.json và vi.json
  const enJson = await readJsonFile(enFile);
  const viJson = await readJsonFile(viFile);

  if (!enJson || !viJson) {
    console.error('Lỗi: Không thể đọc cả hai file en.json và vi.json.');
    return false;
  }

  // Chuyển JSON thành danh sách key phẳng
  const enKeys = flattenKeys(enJson);
  const viKeys = flattenKeys(viJson);

  let hasError = false;

  // Kiểm tra key trùng lặp trong từng file
  if (!checkDuplicateKeysInFile(enKeys, enFile)) {
    hasError = true;
  }
  if (!checkDuplicateKeysInFile(viKeys, viFile)) {
    hasError = true;
  }

  // Kiểm tra key trùng lặp toàn cục
  if (!(await checkDuplicateKeysGlobally(folderPath))) {
    hasError = true;
  }

  // Kiểm tra ngữ nghĩa bản dịch
  if (!checkTranslationSemantics(enJson, viJson, folderPath)) {
    hasError = true;
  }

  // So sánh số lượng key
  if (enKeys.length !== viKeys.length) {
    console.error(`Lỗi: Số lượng key không khớp trong ${folderPath}:`);
    console.error(`- en.json: ${enKeys.length} key`);
    console.error(`- vi.json: ${viKeys.length} key`);
    hasError = true;
  }

  // Tìm key thiếu trong mỗi file
  const missingInVi = enKeys.filter(key => !viKeys.includes(key));
  const missingInEn = viKeys.filter(key => !enKeys.includes(key));

  // Báo lỗi nếu có key thiếu
  if (missingInVi.length > 0) {
    hasError = true;
    console.error(`Lỗi: Các key có trong en.json nhưng thiếu trong vi.json:`);
    for (const key of missingInVi) {
      console.error(`  - ${key}`);
      console.error(`    Đề xuất: Thêm key "${key}" vào ${viFile} với bản dịch phù hợp (tham khảo từ điển: ${translationDictionary[getValueByKey(enJson, key)] || 'chưa có trong từ điển'}).`);
    }
  }
  if (missingInEn.length > 0) {
    hasError = true;
    console.error(`Lỗi: Các key có trong vi.json nhưng thiếu trong en.json:`);
    for (const key of missingInEn) {
      console.error(`  - ${key}`);
      console.error(`    Đề xuất: Thêm key "${key}" vào ${enFile} với bản dịch phù hợp.`);
    }
  }

  // Báo thành công nếu không có lỗi
  if (!hasError) {
    console.log(`Thành công: en.json và vi.json trong ${folderPath} có cùng key, không có key trùng lặp, và bản dịch nhất quán.`);
  }

  return !hasError;
}

export { compareTranslations };