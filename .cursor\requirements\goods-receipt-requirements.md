## **Giới thiệu và Mục tiêu**
Bạn sẽ phát triển một module frontend hoàn chỉnh cho chức năng **<PERSON><PERSON><PERSON><PERSON> (Goods Receipt)** trong hệ thống ERP, sử dụng **Angular 19** và thư viện **Angular Material**. <PERSON><PERSON><PERSON> này cho phép người dùng quản lý quá trình nhận hàng vào kho từ nhà cung cấp, bao gồm việc chọn sản phẩm, quản lý lô hàng (batches), chọn đơn vị tính (units), xử lý biến thể (variants), và ghi nhận thông tin tài chính (tổng tiền, thanh toán, công nợ). Giao diện phải **responsive**, trự<PERSON> quan, và tích hợp **mock data** đ<PERSON> mô phỏng dữ liệu thực tế mà không cần backend.

<PERSON><PERSON><PERSON> tiêu:
- <PERSON><PERSON><PERSON> một giao di<PERSON> đẹp, d<PERSON> sử dụng với các thành phần Angular Material như `mat-autocomplete`, `mat-chip-list`, `mat-dialog`, `mat-bottom-sheet`.
- Đảm bảo tính tương tác real-time (ví dụ: cập nhật tổng tiền khi thêm sản phẩm hoặc thay đổi số lượng).
- Viết code sạch, có cấu trúc thư mục rõ ràng, sử dụng TypeScript interfaces, và tuân thủ best practices của Angular.

---

Các mock sử dụng: [text](../../src/app/mock/shared/list.mock.ts)
Interface:
- [text](../../../shared_contracts/js/src/entities/ims/inventory/goods_receipt.ts)
- [text](../../../shared_contracts/js/src/requests/shared/list.ts)

InputWarehouseLocationComponent: [text](../../src/app/shared/components/input/input-warehouse-location/input-warehouse-location.component.ts)
---


Viết tất cả vào [text](../../src/app/features/warehouse/*).
Page chính viết vào [text](../../src/app/features/warehouse/pages), thêm vào routing [text](../../src/app/features/warehouse/warehouse-routing.ts): warehouse/goods-receipt. Test tại http://localhost:4200/#/warehouse/goods-receipt

--


## **Yêu cầu chung**
- **Framework**: Angular 19.
- **Thư viện UI**: Angular Material (v19.x.x).
- **Ngôn ngữ lập trình**: TypeScript.
- **Tính năng chính**:
  - Tìm kiếm và thêm sản phẩm vào danh sách nhận hàng.
  - Quản lý lô hàng và biến thể (nếu có).
  - Ghi nhận thông tin kho, nhà cung cấp, nhân viên, và tài chính.
- **Responsive**: Hoạt động tốt trên desktop và tablet (ít nhất 768px).

- Bạn phải đọc toàn bộ rules trong .cursor/rules/*
- Bạn phải đọc toàn bộ docs trong docs/*
- Bạn phải đọc @FOLDER_STRUCTURE.md để hiểu rõ cấu trúc folder
- Kết nối với các MCP server hiện tại để tự động debug lỗi và xem preview trên chrome.
- Khi không còn lỗi nào, xem trên giao diện bằng MCP, xem có lỗi gì không và sửa. Tôi đã dựng sẵn server và watch changes rồi, bạn chỉ cần vào thông qua cổng 4200 và kiểm tra.
- File translate đã được cấu hình nằm ở public/assets/i18n nhưng url ra không có /public là tiền tố. KHÔNG ĐƯỢC PHÉP TẠO TRONG src/assets/i18n/*. Không được phép ghi đè lên các field có sẵn trong file i18n, chỉ được phép append thêm field vào.
- Bạn luôn tự thêm i18n vào file json, không phải tôi thêm bằng tay.
- Luôn luôn thêm 2 ngôn ngữ i18n vào @vi.json @en.json
- Không viết thêm các function để xử lý i18n, đọc @i18n-guide.md @ngx-translate-integration.md để tích hợp vào.
- Luôn thêm chi tiết comment trong code để giải thích logic bằng tiếng việt.
- Giao diện nên được viết bằng bootstrap và bắt buộc phải responsive
- Khi import vào angular, dùng các paths ngắn được định nghĩa trong @tsconfig.json như @core, @shared, @mock...
- KHÔNG ĐƯỢC PHÉP sửa các file quan trọng như app.component, layout/*... Tất cả logic của component con phải được định nghĩa trong folder của component con. Như  product thì chỉ gói gọn trong features/product. Tất cả router phải được export vào router con như product-routing.ts nằm trong features/product/product-routing.ts. Không export vào app.routing.ts. KHÔNG ĐƯỢC PHÉP đụng đến logic và giao diện của toàn ứng dụng.
- Từng bước step phải lưu lại vào .cursor/context/[taskname].md để tôi theo dõi và để bạn nhớ rõ những task đã làm, phục vụ cho bạn khi làm 1 task quá dài, vượt quá context dẫn đến việc bạn bị quên context. Các lần tiếp theo bạn phải đọc lại context xem đã làm những task nào, đang làm đến đâu để nạp lại context vào bộ nhớ.
- Không được viết 1 component quá dài, nên phân định rõ cái nào xử lý trong component và cái nào xử lý trong service. Nếu 1 component bao gồm nhiều thành phần, chia nó thành các component con và gọi đến các component con từ component cha. Component chỉ dùng để tương tác với UI, logic nghiệp vụ phải được đẩy xuống service. Nên chia service ngay trong folder component, ví dụ product/product.component.ts, product/product.service.ts
- Khi muốn test 1 chức năng, 1 dialog nào đó, viết thẳng vào @test-theme.component.ts, không được phép tạo component mới. Path để test là http://localhost:4200/#/test
- QUAN TRỌNG: Sau khi viết xong, Chạy ng build xem còn lỗi gì không và sửa.


---


## **Dialogs** QUAN TRỌNG: TẤT CẢ DIALOGS ĐÃ LÀM XONG! Cần đọc để hiểu rõ cấu trúc dialog, input, output
  ### **Dialog Lô hàng (BatchDialog)**: [text](../../src/app/features/warehouse/dialogs/batch-dialog/batch-dialog.component.ts)

  ### **Dialog Thêm hàng hóa từ nhóm hàng**: [text](../../src/app/features/warehouse/dialogs/category-product-dialog/category-product-dialog.component.ts)

  ### **Dialog: AdditionalCostDialog**: [text](../../src/app/features/warehouse/dialogs/additional-cost-dialog/additional-cost-dialog.component.ts)

  ### **Dialog: TaxDialog**: [text](../../src/app/features/warehouse/dialogs/tax-dialog/tax-dialog.component.ts)
  ### **Dialog: SelectAdditionalCostsDialog**: [text](../../src/app/features/warehouse/dialogs/select-additional-costs-dialog/select-additional-costs-dialog.component.ts)
  ### **Dialog: QualityCheckRejectDialog**: [text](../../src/app/features/warehouse/dialogs/quality-check-reject-dialog/quality-check-reject-dialog.component.ts)



## **Cấu trúc giao diện**
Giao diện được chia thành **hai phần chính**: **bên trái** (danh sách sản phẩm nhận hàng) và **bên phải** (thông tin tổng quan và tài chính).

### **Bên trái**
#### **0. Thanh tiêu đề (Row Header)**
- **Ô tìm kiếm sản phẩm**:
  - Sử dụng `mat-autocomplete` để tìm kiếm sản phẩm từ `mockProductList`.
  - Gõ từ khóa để lọc sản phẩm theo `name` hoặc `sku`.
  - Khi chọn một sản phẩm, tự động thêm vào danh sách bên dưới với các giá trị mặc định (đơn giá, đơn vị tính cơ bản).
- **Nút "Thêm từ nhóm hàng"**:
  - Button với icon `add_circle` (Angular Material).
  - Khi nhấn, mở **Dialog Thêm hàng hóa từ nhóm hàng**.
- **Biểu tượng in**:
  - Icon `print` (Angular Material).
  - Khi nhấn, gọi hàm mock `printReceipt()` với `console.log('Đang in phiếu nhập kho...')`.

#### **1. Expansion Panel: Kiểm tra Chất lượng**

##### **Mô tả**

Một `mat-expansion-panel` để nhập và chỉnh sửa thông tin kiểm tra chất lượng (`QualityCheck`), bao gồm bảng hiển thị sản phẩm bị từ chối, hiển thị trong giao diện chính của phiếu nhập kho, mặc định đóng.

##### **Giao diện**

- **Tiêu đề panel**:
  - "Kiểm tra chất lượng" (hiển thị trong `mat-expansion-panel-header`).
  - Icon `verified` (Angular Material) bên cạnh tiêu đề.
  - Hiển thị trạng thái:
    - Nếu `qualityCheck.status = 'pending'`, badge màu xám: "Chưa kiểm tra".
    - Nếu `status = 'passed'`, badge màu xanh: "Đạt".
    - Nếu `status = 'failed'`, badge màu đỏ: "Không đạt".
    - Nếu `status = 'partial'`, badge màu vàng: "Một phần" + số lượng sản phẩm bị từ chối (VD: "2 sản phẩm từ chối").
- **Nội dung panel** (khi mở):
  - Form với các trường:
    - **Trạng thái**:
      - `mat-select` với các tùy chọn:
        - "Chưa kiểm tra" (`pending`).
        - "Đạt" (`passed`).
        - "Không đạt" (`failed`).
        - "Một phần" (`partial`).
      - Bắt buộc, mặc định là "Chưa kiểm tra".
    - **Người kiểm tra**:
      - `mat-select` hiển thị danh sách từ `EmployeeList`.
      - Hiển thị: `name` của nhân viên.
      - Tùy chọn, chỉ hiển thị nếu `status != 'pending'`.
    - **Ngày kiểm tra**:
      - `mat-form-field` với `mat-datepicker`.
      - Placeholder: "Chọn ngày kiểm tra".
      - Tùy chọn, định dạng ngày: DD/MM/YYYY.
      - Chỉ hiển thị nếu `status != 'pending'`.
    - **Ghi chú**:
      - `mat-form-field` với textarea.
      - Placeholder: "Nhập ghi chú kiểm tra".
      - Tùy chọn, tối đa 1000 ký tự.
      - Chiều cao cố định: 100px.
  - **Bảng sản phẩm bị từ chối**:
    - Hiển thị `rejectedItems`.
    - **Cột**:
      - **Sản phẩm**:
        - Hiển thị `EmbeddedProduct.name` (ánh xạ từ `itemId` sang `GoodsReceiptItem.product.name`).
        - Dòng phụ: "SKU: `product.sku`".
      - **Số lượng**:
        - Hiển thị `quantity` (định dạng số, VD: "5").
      - **Lý do**:
        - Hiển thị `reason` (cắt ngắn nếu dài hơn 50 ký tự, thêm tooltip để xem đầy đủ).
      - **Hành động**:
        - Icon `delete` (`mat-icon-button`) để xóa sản phẩm bị từ chối.
        - Icon `edit` (`mat-icon-button`) để sửa sản phẩm bị từ chối. Nhấn vào hiện QualityCheckRejectDialog với input rejectedItem
    - **Nút thêm**:
      - `mat-raised-button` với label "Thêm từ chối" và icon `add`.
      - Vị trí: Góc trên bên phải của bảng.
      - Khi nhấn, mở **QualityCheckRejectDialog** (đã triển khai) để thêm sản phẩm bị từ chối.
  - **Nút lưu**:
    - `mat-raised-button` màu primary với label "Lưu".
    - Vị trí: Góc dưới bên phải của panel.
    - Chỉ hiển thị khi panel mở và form hoặc bảng có thay đổi.

##### **Logic**

- **Validation**:
  - **Trạng thái**: Bắt buộc.
  - **Người kiểm tra** và **Ngày kiểm tra**:
    - Bắt buộc nếu `status = 'passed'`, `'failed'`, hoặc `'partial'`.
    - Tùy chọn nếu `status = 'pending'`.
  - **Ghi chú**: Tùy chọn, tối đa 1000 ký tự.
  - **Sản phẩm bị từ chối**:
    - Bắt buộc có ít nhất một sản phẩm nếu `status = 'partial'`.
    - Mỗi sản phẩm: `quantity` phải ≤ `quantityReceived` của `GoodsReceiptItem` tương ứng.
  - Hiển thị lỗi bằng `mat-error` (VD: "Vui lòng chọn người kiểm tra").
  - Disable nút "Lưu" nếu form không hợp lệ hoặc không có thay đổi.
- **Dữ liệu đầu vào**:
  - Nhận `qualityCheck` từ `GoodsReceipt.qualityCheck` (qua `@Input` hoặc service).
  - Nhận `items` từ `GoodsReceipt.items` để ánh xạ `itemId` sang `product.name` và kiểm tra `quantityReceived`.
  - Nhận `EmployeeList` qua service để điền `mat-select` người kiểm tra.
  - Nếu `qualityCheck` tồn tại, điền form và bảng với giá trị hiện tại.
  - Nếu không, để form trống với `status = 'pending'` và bảng rỗng.
- **Cập nhật dữ liệu**:
  - Khi nhấn "Lưu", cập nhật `GoodsReceipt.qualityCheck` với dữ liệu từ form và bảng.
  - Nếu `status = 'pending'` và không có dữ liệu khác, gán `qualityCheck = undefined`.
  - Sử dụng `FormGroup` để theo dõi thay đổi trong form.
  - Theo dõi thay đổi trong bảng `rejectedItems` bằng cách so sánh với dữ liệu ban đầu.
- **Thêm sản phẩm bị từ chối**:
  - Khi nhấn "Thêm từ chối", mở **QualityCheckRejectDialog** với `GoodsReceipt.items` làm dữ liệu đầu vào.
  - Khi dialog đóng và trả về một object `{ _id, quantity, reason }`:
    - Thêm object này vào mảng `rejectedItems` của bảng.
    - Tự động cập nhật `status = 'partial'` nếu `rejectedItems` không rỗng và `status != 'failed'`.
- **Xóa sản phẩm bị từ chối**:
  - Khi nhấn icon `delete`, xóa sản phẩm khỏi mảng `rejectedItems`.
  - Nếu `rejectedItems` rỗng, kiểm tra lại `status`:
    - Nếu không có ghi chú hoặc người kiểm tra, đặt `status = 'pending'`.
- **UX**:
  - Form bố trí theo cột dọc trên desktop, với bảng `rejectedItems` ở dưới cùng.
  - Trên tablet, bảng có thanh cuộn ngang nếu cột quá dài.
  - Trường `Người kiểm tra` và `Ngày kiểm tra` ẩn khi `status = 'pending'` để giảm lộn xộn.
  - Badge trạng thái trên tiêu đề panel giúp người dùng nhận biết nhanh tình trạng kiểm tra.
  - Tooltip trên cột "Lý do" hiển thị đầy đủ nội dung nếu bị cắt ngắn.
  - Nút "Thêm từ chối" nổi bật với icon `add` để dễ nhận diện.

##### **Ánh xạ interface**

- `QualityCheck`:
  - `status`: Giá trị từ `mat-select` (Trạng thái).
  - `checkedBy`: Giá trị từ `mat-select` (Người kiểm tra, ánh xạ `_id` và `name`).
  - `checkedAt`: Giá trị từ `mat-datepicker` (Ngày kiểm tra).
  - `notes`: Giá trị từ textarea (Ghi chú).
  - `rejectedItems`: Mảng từ bảng, mỗi item chứa:
    - `_id`: ID của `GoodsReceiptItem`.
    - `quantity`: Số lượng bị từ chối.
    - `reason`: Lý do từ chối.


#### **2. Danh sách sản phẩm đã chọn**
#### **2.1. Cấu trúc bảng**

- **Component**:.
- **UI**: Hiển thị các cột sau:
  1. **Xóa**:
     - `mat-icon` với biểu tượng `delete`.
     - Khi click, xóa `GoodsReceiptItem` khỏi danh sách và emit sự kiện `itemRemoved`.
  2. **Sản phẩm**:
     - Hiển thị `EmbeddedProduct.name` (dòng trên, font bold, 16px).
     - Hiển thị `EmbeddedProduct.sku` (dòng dưới, font italic, 12px, màu xám).
  3. **Đơn vị tính**:
     - Text clickable hiển thị `ProductUnit.unitName`.
     - Khi click, mở `VariantSelectorBottomSheetComponent` từ `src/app/features/sales/variant-selector-bottom-sheet/variant-selector-bottom-sheet.component.ts` để chọn `ProductUnit`.
     - Mặc định chọn đơn vị có `isBaseUnit: true`.
  4. **Số lượng đặt hàng**:
     - Hiển thị `quantityOrdered` (readonly, định dạng số nguyên).
     - Nếu không có `quantityOrdered`, hiển thị "N/A".
  5. **Số lượng nhận**:
     - `mat-form-field` với input type number cho `quantityReceived`.
     - Disable nếu sản phẩm có `batches` (tổng số lượng lấy từ lô).
     - Validation: `quantityReceived >= 0`.
  6. **Số lượng chấp nhận**:
     - `mat-form-field` với input type number cho `quantityAccepted`.
     - Mặc định bằng `quantityReceived`.
     - Chỉ enable nếu `GoodsReceipt.qualityCheck.status = 'checked'`.
     - Validation: `0 <= quantityAccepted <= quantityReceived`.
  7. **Đơn giá**:
     - `mat-form-field` với input type number cho `price`.
     - Mặc định lấy từ `ProductUnit.price`.
     - Validation: `price >= 0`.
  8. **Giảm giá**:
     - `mat-form-field` với input type number cho `discount`.
     - Mặc định là 0.
     - Validation: `0 <= discount <= (quantityAccepted * price)`.
  9. **Thành tiền**:
     - Hiển thị `total = (quantityAccepted * price) - discount`.
     - Định dạng tiền tệ (VD: "1,234,567 VND") sử dụng pipe `currency` của Angular.
  10. **Vị trí lưu trữ**:
      - Sử dụng component `input-warehouse-location` từ `src/app/shared/components/input/input-warehouse-location/input-warehouse-location.component.ts`.
      - Cấu hình:
        ```html
        <input-warehouse-location
          [locations]="warehouseLocations"
          [warehouseId]="warehouseId"
          [disabled]="!warehouseId"
          formControlName="locationId"
          (locationSelected)="onLocationSelected($event)">
        </input-warehouse-location>
        ```
      - Hiển thị `EmbeddedWarehouseLocation.name` (VD: "Khu vực 1 / Kệ 01").
      - Nếu chưa chọn, hiển thị "Chọn vị trí".



#### **2.2. Quản lý biến thể (Variants)**

- **Điều kiện**: Nếu `EmbeddedProduct.variants` tồn tại.
- **UI**:
  - Hiển thị text clickable:
    - Nếu chưa chọn: "Chọn biến thể".
    - Nếu đã chọn: Hiển thị `attributes` (VD: "Màu: Xanh, Kích thước: L").
  - Khi click, mở `VariantSelectorBottomSheetComponent` để chọn `ProductSimpleVariant`.
- **Logic**:
  - Khi chọn biến thể, cập nhật:
    - `EmbeddedProduct.name` (thêm thông tin biến thể, VD: "Áo thun - Xanh").
    - `EmbeddedProduct.sku` (nếu biến thể có SKU riêng).
    - `price` (từ biến thể nếu có).
  - Emit sự kiện `variantUpdated` để thông báo thay đổi.

#### **2.3. Quản lý lô hàng (Batches)**

- **Điều kiện**: Nếu `EmbeddedProduct.batches` tồn tại.
- **UI**:
  - Thêm sub-row với `mat-chip-list`.
  - Mỗi chip hiển thị:
    - `batchNumber` (VD: "BATCH2025-001").
    - `expiryDate` (định dạng `DD/MM/YYYY`).
    - `quantity` (VD: "50 kg").
    - Icon `edit` (mở `BatchDialog` với các Input từ row).
    - Icon `delete` (xóa lô, emit `batchRemoved`).
  - Chip "Thêm lô" (`mat-chip` với icon `add`) mở `BatchDialog`.
- **Logic**:
  - Tổng `quantity` từ các lô (`ProductBatchInfo.quantity`) cập nhật `quantityReceived` (disabled).
  - Khi thêm/sửa/xóa lô, emit sự kiện `batchUpdated` và cập nhật `quantityReceived`.
  - Validation: Tổng `quantity` của lô phải <= `quantityOrdered` (nếu có).

#### **2.4. Chi phí phân bổ (Allocated Costs)**

- **Điều kiện**: Nếu `allocatedCosts` tồn tại.
- **UI**:
  - Thêm sub-row với text readonly:
    - "Chi phí phân bổ: X VND" (tổng `amount` từ `allocatedCosts`).
    - Icon `info` mở `mat-tooltip` liệt kê chi tiết (VD: "Phí vận chuyển: 5000 VND, Phí hải quan: 2000 VND").
- **Logic**:
  - `allocatedCosts` được tính tự động từ `GoodsReceipt.additionalCosts` có `allocateToItems: true`.
  - Không cho phép chỉnh sửa trực tiếp trong bảng, chỉ hiển thị thông tin.

#### **2.5. Tích hợp input-warehouse-location**

- **Component**: `input-warehouse-location` từ `src/app/shared/components/input/input-warehouse-location/input-warehouse-location.component.ts`.
- **Cấu hình**:
  - Input `locations`: Danh sách `mockWarehouseLocations` từ mock data hoặc service.
  - Input `warehouseId`: ID kho được chọn từ `GoodsReceipt` (để lọc vị trí).
  - Input `disabled`: Disable nếu `warehouseId` chưa được chọn.
  - Output `locationSelected`: Emit `EmbeddedWarehouseLocation` khi chọn vị trí.
  - Form control `locationId`: Bind với `GoodsReceiptItem.warehouseLocation._id`.
- **Logic**:
  - Khi chọn vị trí, cập nhật `GoodsReceiptItem.warehouseLocation` và emit sự kiện `locationUpdated`.
  - Hiển thị `name` hoặc `code` của vị trí (VD: "Khu vực 1 / Kệ 01").

#### **2.6. Tương tác và Real-time**

- **Cập nhật real-time**:
  - Khi thay đổi `quantityReceived`, `quantityAccepted`, `price`, hoặc `discount`, tính lại `subTotal = quantityAccepted * price` và `total = subTotal - discount`.
  - Emit sự kiện `itemUpdated` để thông báo thay đổi cho parent component.
- **Validation**:
  - Hiển thị lỗi bằng `mat-error` nếu:
    - `quantityReceived < 0`.
    - `quantityAccepted > quantityReceived`.
    - `discount > subTotal`.
    - `price < 0`.
  - Disable `quantityAccepted` nếu `qualityCheck.status != 'checked'`.
- **Sử dụng RxJS**:
  - Dùng `BehaviorSubject` để quản lý danh sách `items` và tự động cập nhật bảng khi có thay đổi.
  - Gọi `ChangeDetectorRef.detectChanges()` nếu cần refresh UI.




#### **3. Expansion Panel: Thông tin Vận chuyển**

##### **Mô tả**

Một `mat-expansion-panel` để nhập và chỉnh sửa thông tin vận chuyển (`TransportInfo`), hiển thị trong giao diện chính của phiếu nhập kho, mặc định đóng.

##### **Giao diện**

- **Tiêu đề panel**:
  - "Thông tin vận chuyển" (hiển thị trong `mat-expansion-panel-header`).
  - Icon `local_shipping` (Angular Material) bên cạnh tiêu đề để trực quan.
  - Hiển thị trạng thái: Nếu `transportInfo` có ít nhất một trường được điền (VD: `transportMethod`), thêm badge nhỏ màu xanh với text "Đã nhập".
- **Nội dung panel** (khi mở):
  - Form với các trường:
    - **Phương thức vận chuyển**:
      - `mat-select` với các tùy chọn:
        - "Đường bộ" (`road`).
        - "Đường biển" (`sea`).
        - "Đường hàng không" (`air`).
        - "Khác" (`other`).
      - Placeholder: "Chọn phương thức vận chuyển".
      - Tùy chọn, không bắt buộc.
    - **Đơn vị vận chuyển**:
      - `mat-form-field` với input text.
      - Placeholder: "Nhập đơn vị vận chuyển (VD: Viettel Post)".
      - Tùy chọn, tối đa 100 ký tự.
    - **Số vận đơn**:
      - `mat-form-field` với input text.
      - Placeholder: "Nhập số vận đơn (VD: VT123456)".
      - Tùy chọn, tối đa 50 ký tự.
    - **Ngày giao dự kiến**:
      - `mat-form-field` với `mat-datepicker`.
      - Placeholder: "Chọn ngày giao dự kiến".
      - Tùy chọn, định dạng ngày: DD/MM/YYYY.
    - **Ngày giao thực tế**:
      - `mat-form-field` với `mat-datepicker`.
      - Placeholder: "Chọn ngày giao thực tế".
      - Tùy chọn, định dạng ngày: DD/MM/YYYY.
  - **Nút lưu**:
    - `mat-raised-button` màu primary với label "Lưu".
    - Vị trí: Góc dưới bên phải của panel.
    - Chỉ hiển thị khi panel mở và form có thay đổi.

##### **Logic**

- **Validation**:
  - Tất cả trường đều tùy chọn, không bắt buộc.
  - Nếu nhập:
    - **Đơn vị vận chuyển**: Tối đa 100 ký tự.
    - **Số vận đơn**: Tối đa 50 ký tự.
    - **Ngày giao dự kiến** và **Ngày giao thực tế**: Phải là ngày hợp lệ, không nhỏ hơn ngày hiện tại trừ khi chỉnh sửa dữ liệu cũ.
  - Hiển thị lỗi bằng `mat-error` (VD: "Số vận đơn quá dài").
- **Dữ liệu đầu vào**:
  - Nhận `transportInfo` từ `GoodsReceipt.transportInfo` (qua `@Input` hoặc service).
  - Nếu `transportInfo` tồn tại, điền các trường với giá trị hiện tại.
  - Nếu không, để form trống.
- **Cập nhật dữ liệu**:
  - Khi nhấn "Lưu", cập nhật `GoodsReceipt.transportInfo` với dữ liệu từ form.
  - Nếu tất cả trường trống, gán `transportInfo = undefined` để tiết kiệm lưu trữ.
  - Sử dụng `FormGroup` để theo dõi thay đổi và chỉ hiển thị nút "Lưu" khi form có thay đổi (`form.dirty`).
- **UX**:
  - Form bố trí theo lưới 2 cột trên desktop (VD: Phương thức + Đơn vị ở hàng 1, Số vận đơn + Ngày dự kiến ở hàng 2), chuyển thành 1 cột trên tablet.
  - Trường `mat-datepicker` có nút lịch rõ ràng để dễ chọn.
  - Badge "Đã nhập" giúp người dùng biết panel có dữ liệu mà không cần mở.
  - Khi mở panel, tự động focus vào trường "Phương thức vận chuyển".

##### **Ánh xạ interface**

- `TransportInfo`:
  - `transportMethod`: Giá trị từ `mat-select` (Phương thức vận chuyển).
  - `carrier`: Giá trị từ input text (Đơn vị vận chuyển).
  - `trackingNumber`: Giá trị từ input text (Số vận đơn).
  - `estimatedDeliveryDate`: Giá trị từ `mat-datepicker` (Ngày giao dự kiến).
  - `actualDeliveryDate`: Giá trị từ `mat-datepicker` (Ngày giao thực tế).





### **Bên phải**
#### **1. Row 1**
- **Nhân viên phụ trách**: Dropdown (`mat-select`) để chọn từ `mockEmployeeList`.
- **Ngày nhận hàng**: `mat-datepicker` kết hợp input thời gian (24h format).

#### **2. Row 2**
- **Kho hàng**: Dropdown (`mat-select`) để chọn từ `mockWarehouseList`.

#### **3. Row 3**
- **Nhà cung cấp**: `mat-autocomplete` để tìm kiếm và chọn từ `mockSuppliers`.

#### **4. Row 4**
- **Ghi chú**: `mat-form-field` với `textarea`, giới hạn 500 ký tự.

#### **5. Row 5 - Thông tin tài chính**
- **5.1: Mã phiếu**: `mat-form-field` với placeholder "Tự động tạo khi hoàn thành".
- **5.2: Tổng tiền hàng**: Hiển thị dạng tiền tệ, tính từ tổng `Thành tiền` của tất cả sản phẩm.
- **5.3: Giảm giá tổng**: Input number, mặc định 0.
- **5.4: Row: Chi Phí Khác (Trả cho NCC)**
  - **Row chính**:
    - **Bên trái (Label)**:
      - Text: "Chi phí khác (trả cho NCC)".
      - Font: Bold, clickable (cursor: pointer).
      - Khi nhấn, mở **SelectAdditionalCostsDialog** với dữ liệu đầu vào:
        - `items`: Lọc `mockImportAdditionalCosts` chỉ lấy các item có `paidToSupplier = true`.
        - `current`: Lọc `GoodsReceipt.additionalCosts` có `paidToSupplier = true`.
        - `subTotal`: Truyền `GoodsReceipt.summary.subTotal` để tính chi phí phần trăm.
    - **Bên phải (Giá trị)**:
      - Text readonly: Tổng tiền của tất cả chi phí trong `GoodsReceipt.additionalCosts` có `paidToSupplier = true`, định dạng tiền tệ (VD: "1,500,000 VND").
      - Ánh xạ: `summary.totalSupplierAdditionalCost`.
      - Font: Bold, clickable (cursor: pointer).
      - Khi nhấn, mở **SelectAdditionalCostsDialog** với dữ liệu đầu vào giống bên trái.
      - Nếu `totalSupplierAdditionalCost = 0`, hiển thị: "0 VND".
    - **Badge**:
      - Nếu `totalSupplierAdditionalCost > 0`, hiển thị badge màu xanh bên cạnh giá trị: "X chi phí" (X là số chi phí có `paidToSupplier = true`).
  - **Row phụ** (cho mỗi chi phí trong `GoodsReceipt.additionalCosts` có `paidToSupplier = true`):
    - **Bên trái**:
      - Text: `cost.name` (VD: "Phí vận chuyển nội địa").
    - **Bên phải**:
      - Text readonly: Tổng `cost.costValue.value + (cost.tax?.amount || 0)`, định dạng tiền tệ (VD: "550,000 VND").
      - Nếu `cost.tax` không tồn tại, chỉ hiển thị `cost.costValue.value`.
    - **Số lượng row phụ**:
      - Tạo một row phụ cho mỗi chi phí trong `GoodsReceipt.additionalCosts` có `paidToSupplier = true`.
      - Nếu không có chi phí, không hiển thị row phụ.
  - **Logic**:
    - **Lọc chi phí**:
      - Trong **SelectAdditionalCostsDialog**, chỉ hiển thị các chi phí từ `mockImportAdditionalCosts` có `paidToSupplier = true`.
      - Truyền `current` để hiển thị các chi phí đã chọn từ `GoodsReceipt.additionalCosts`.
      - Khi dialog đóng và emit mảng `ImportAdditionalCost[]`, cập nhật `GoodsReceipt.additionalCosts` với các chi phí được chọn (giữ các chi phí `paidToSupplier = false` không bị ảnh hưởng).
    - **Tính tổng**:
      - Tính `summary.totalSupplierAdditionalCost` bằng cách duyệt `GoodsReceipt.additionalCosts`:
        - Với `costValue.type = 'fixed'`: Cộng `costValue.value`.
        - Với `costValue.type = 'percentage'`: Cộng `costValue.value * summary.subTotal / 100`.
        - Chỉ tính các chi phí có `paidToSupplier = true`.
      - Cập nhật `summary.totalAdditionalCost = totalSupplierAdditionalCost + totalNonSupplierAdditionalCost`.
      - Cập nhật `summary.total = subTotal - totalDiscount + totalAdditionalCost + totalTax`.
    - **Hiển thị row phụ**:
      - Sử dụng `*ngFor` để tạo row phụ cho mỗi chi phí `paidToSupplier = true`.
      - Tính tổng chi phí mỗi row phụ: `cost.costValue.value + (cost.tax?.amount || 0)`.
    - **Validation**: Không yêu cầu validation vì giá trị là readonly và chỉnh sửa qua dialog.
    - **Cập nhật dữ liệu**:
      - Khi dialog trả về chi phí, cập nhật `GoodsReceipt.additionalCosts`.
      - Tái tính `summary.totalSupplierAdditionalCost`, `summary.totalAdditionalCost`, và `summary.total`.
    - **UX**:
      - Label và giá trị clickable của row chính được gạch chân khi hover.
      - Row phụ thụt lề 16px, font nhỏ hơn (14px) để phân biệt với row chính.
      - Badge hiển thị số chi phí, tooltip khi hover row chính: "Nhấn để xem và chỉnh sửa chi phí trả cho nhà cung cấp".
      - Nếu không có chi phí, hiển thị row chính với giá trị "0 VND" và không có row phụ.

  - **Ánh xạ interface**:
    - `summary.totalSupplierAdditionalCost`: Tổng tiền chi phí trả NCC.
    - `additionalCosts`: Danh sách chi phí, lọc `paidToSupplier = true` khi mở dialog và hiển thị row phụ.

- **5.5: Row: Chi Phí Nhập Khác**
  - **Row chính**:
    - **Bên trái (Label)**:
      - Text: "Chi phí nhập khác".
      - Font: Bold, clickable (cursor: pointer).
      - Khi nhấn, mở **SelectAdditionalCostsDialog** với dữ liệu đầu vào:
        - `items`: Lọc `mockImportAdditionalCosts` chỉ lấy các item có `paidToSupplier = false`.
        - `current`: Lọc `GoodsReceipt.additionalCosts` có `paidToSupplier = false`.
        - `subTotal`: Truyền `GoodsReceipt.summary.subTotal`.
    - **Bên phải (Giá trị)**:
      - Text readonly: Tổng tiền của tất cả chi phí trong `GoodsReceipt.additionalCosts` có `paidToSupplier = false`, định dạng tiền tệ (VD: "800,000 VND").
      - Ánh xạ: `summary.totalNonSupplierAdditionalCost`.
      - Font: Bold, clickable (cursor: pointer).
      - Khi nhấn, mở **SelectAdditionalCostsDialog** với dữ liệu đầu vào giống bên trái.
      - Nếu `totalNonSupplierAdditionalCost = 0`, hiển thị: "0 VND".
    - **Badge**:
      - Nếu `totalNonSupplierAdditionalCost > 0`, hiển thị badge màu xanh: "X chi phí" (X là số chi phí có `paidToSupplier = false`).
  - **Row phụ** (cho mỗi chi phí trong `GoodsReceipt.additionalCosts` có `paidToSupplier = false`):
    - **Bên trái**:
      - Text: `cost.name` (VD: "Phí hải quan").
      - Font: Regular, thụt lề 16px.
    - **Bên phải**:
      - Text readonly: Tổng `cost.costValue.value + (cost.tax?.amount || 0)`, định dạng tiền tệ (VD: "220,000 VND").
      - Nếu `cost.tax` không tồn tại, chỉ hiển thị `cost.costValue.value`.
      - Font: Regular.
    - **Số lượng row phụ**:
      - Tạo một row phụ cho mỗi chi phí trong `GoodsReceipt.additionalCosts` có `paidToSupplier = false`.
      - Nếu không có chi phí, không hiển thị row phụ.
  - **Logic**:
    - **Lọc chi phí**:
      - Trong **SelectAdditionalCostsDialog**, chỉ hiển thị các chi phí từ `mockImportAdditionalCosts` có `paidToSupplier = false`.
      - Truyền `current` để hiển thị các chi phí đã chọn.
      - Khi dialog đóng, cập nhật `GoodsReceipt.additionalCosts` (giữ các chi phí `paidToSupplier = true`).
    - **Tính tổng**:
      - Tính `summary.totalNonSupplierAdditionalCost` bằng cách duyệt `GoodsReceipt.additionalCosts`:
        - Với `costValue.type = 'fixed'`: Cộng `costValue.value`.
        - Với `costValue.type = 'percentage'`: Cộng `costValue.value * summary.subTotal / 100`.
        - Chỉ tính các chi phí có `paidToSupplier = false`.
      - Cập nhật `summary.totalAdditionalCost` và `summary.total` như trên.
    - **Hiển thị row phụ**:
      - Sử dụng `*ngFor` để tạo row phụ cho mỗi chi phí `paidToSupplier = false`.
      - Tính tổng chi phí mỗi row phụ: `cost.costValue.value + (cost.tax?.amount || 0)`.
    - **Validation**: Không yêu cầu validation.
    - **Cập nhật dữ liệu**:
      - Cập nhật `GoodsReceipt.additionalCosts` từ dialog.
      - Tái tính `summary.totalNonSupplierAdditionalCost`, `summary.totalAdditionalCost`, và `summary.total`.
    - **UX**:
      - Label và giá trị clickable của row chính được gạch chân khi hover.
      - Row phụ thụt lề, font nhỏ hơn.
      - Badge hiển thị số chi phí, tooltip: "Nhấn để xem và chỉnh sửa chi phí không trả cho nhà cung cấp".
      - Nếu không có chi phí, hiển thị row chính với "0 VND" và không có row phụ.

  - **Ánh xạ interface**:
    - `summary.totalNonSupplierAdditionalCost`: Tổng tiền chi phí không trả NCC.
    - `additionalCosts`: Danh sách chi phí, lọc `paidToSupplier = false` khi mở dialog và hiển thị row phụ.

- **5.6: Row: Thuế**
  - **Row chính**:
    - **Bên trái (Label)**:
      - Text: "Thuế".
      - Font: Bold.
      - Kèm nút `mat-icon-button` với icon `add` (label: "Thêm thuế").
      - Khi nhấn nút `add`, mở **TaxDialog** với dữ liệu đầu vào:
        - `subTotal`: Truyền `GoodsReceipt.summary.subTotal` để tính thuế tự động.
    - **Bên phải (Giá trị)**:
      - Text readonly: Tổng tiền của tất cả thuế trong `GoodsReceipt.taxes`, định dạng tiền tệ (VD: "200,000 VND").
      - Ánh xạ: `summary.totalTax`.
      - Font: Bold, clickable (cursor: pointer).
      - Khi nhấn, mở **TaxDialog** với dữ liệu đầu vào:
        - `taxes`: Truyền `GoodsReceipt.taxes` để hiển thị danh sách thuế hiện tại.
        - `subTotal`: Truyền `GoodsReceipt.summary.subTotal`.
      - Nếu `totalTax = 0`, hiển thị: "0 VND".
    - **Badge**:
      - Nếu `totalTax > 0`, hiển thị badge màu xanh: "X thuế" (X là số khoản thuế).
  - **Row phụ** (cho mỗi thuế trong `GoodsReceipt.taxes`):
    - **Bên trái**:
      - **Nút Edit**: `mat-icon-button` với icon `edit`, khi nhấn mở **TaxDialog** với dữ liệu:
        - `tax`: Object `TaxInfo` hiện tại (`type`, `rate`, `amount`).
        - `subTotal`: Truyền `GoodsReceipt.summary.subTotal`.
      - **Nút Delete**: `mat-icon-button` với icon `delete`, khi nhấn xóa thuế khỏi `GoodsReceipt.taxes`.
      - **Text**: `tax.type` (VD: "VAT", "import_tax", "other").
      - Font: Regular, thụt lề 16px.
      - Bố cục: Nút `edit` và `delete` nằm trước `tax.type`, cách nhau 8px.
    - **Bên phải**:
      - Text readonly: `tax.amount` kèm tỷ lệ trong ngoặc (VD: "200,000 VND (10%)").
      - Định dạng tiền tệ cho `amount`, phần trăm cho `rate`.
      - Font: Regular.
    - **Số lượng row phụ**:
      - Tạo một row phụ cho mỗi `TaxInfo` trong `GoodsReceipt.taxes`.
      - Nếu không có thuế, không hiển thị row phụ.
  - **Logic**:
    - **Mở dialog**:
      - **TaxDialog** (từ nút `add` hoặc text clickable):
        - Nhận `subTotal` để tính `amount` tự động (`amount = subTotal * rate / 100`).
        - Nếu từ text clickable, truyền `taxes` để hiển thị danh sách thuế.
        - Emit `TaxInfo`, thêm vào `GoodsReceipt.taxes`.
      - **TaxDialog** (từ nút Edit):
        - Nhận `tax` (object `TaxInfo` hiện tại) và `subTotal`.
        - Emit `TaxInfo`, thay thế thuế tương ứng trong `GoodsReceipt.taxes`.
    - **Xóa thuế**:
      - Khi nhấn nút `delete`, xóa `TaxInfo` khỏi `GoodsReceipt.taxes` dựa trên index hoặc `_id`.
    - **Tính tổng**:
      - Tính `summary.totalTax = sum(taxes.amount)`.
      - Cập nhật `summary.total = subTotal - totalDiscount + totalAdditionalCost + totalTax`.
    - **Hiển thị row phụ**:
      - Sử dụng `*ngFor` để tạo row phụ cho mỗi `TaxInfo`.
      - Hiển thị `tax.type` và `tax.amount (tax.rate%)`.
    - **Validation**: Không yêu cầu validation vì giá trị là readonly và chỉnh sửa qua dialog.
    - **Cập nhật dữ liệu**:
      - Khi dialog trả về `TaxInfo`, thêm hoặc thay thế trong `GoodsReceipt.taxes`.
      - Khi xóa thuế, cập nhật `GoodsReceipt.taxes`.
      - Tái tính `summary.totalTax` và `summary.total`.
    - **UX**:
      - Nút `add`, `edit`, `delete` có kích thước nhỏ (24px), màu xám, hover đổi màu primary.
      - Label clickable của row chính được gạch chân khi hover.
      - Row phụ thụt lề, font nhỏ hơn.
      - Badge hiển thị số thuế, tooltip khi hover row chính: "Nhấn để xem và chỉnh sửa các khoản thuế".
      - Nếu không có thuế, hiển thị row chính với "0 VND" và không có row phụ.

  - **Ánh xạ interface**:
    - `summary.totalTax`: Tổng tiền thuế.
    - `taxes`: Danh sách thuế, cập nhật khi **TaxDialog** emit hoặc xóa.


  - **Logic Tổng Quát**

    - **Cập nhật summary**:
      - Khi `additionalCosts` thay đổi (từ **SelectAdditionalCostsDialog**):
        - Tái tính `summary.totalSupplierAdditionalCost` (cho `paidToSupplier = true`).
        - Tái tính `summary.totalNonSupplierAdditionalCost` (cho `paidToSupplier = false`).
        - Tính `summary.totalAdditionalCost = totalSupplierAdditionalCost + totalNonSupplierAdditionalCost`.
        - Cập nhật `summary.total = subTotal - totalDiscount + totalAdditionalCost + totalTax`.
      - Khi `taxes` thay đổi (từ **TaxDialog** hoặc xóa):
        - Tái tính `summary.totalTax = sum(taxes.amount)`.
        - Cập nhật `summary.total`.
    - **Hiển thị row phụ**:
      - **Chi phí**: Sử dụng `*ngFor` để tạo row phụ dựa trên `GoodsReceipt.additionalCosts`, lọc theo `paidToSupplier`.
      - **Thuế**: Sử dụng `*ngFor` để tạo row phụ dựa trên `GoodsReceipt.taxes`.
    - **Dialog**:
      - **SelectAdditionalCostsDialog**:
        - Nhận `items` (lọc `mockImportAdditionalCosts`) và `current` (lọc `GoodsReceipt.additionalCosts`).
        - Truyền `subTotal` để tính chi phí phần trăm.
        - Emit mảng `ImportAdditionalCost[]`, hợp nhất vào `GoodsReceipt.additionalCosts`.
      - **TaxDialog**:
        - Nhận `subTotal` (cho thêm thuế) hoặc `tax` (cho chỉnh sửa).
        - Emit `TaxInfo`, thêm hoặc thay thế trong `GoodsReceipt.taxes`.
    - **Xóa thuế**:
      - Xóa `TaxInfo` khỏi `GoodsReceipt.taxes` dựa trên index hoặc `_id`.
      - Tái tính `summary.totalTax` và `summary.total`.
    - **UX**:
  - Row phụ thụt lề, font nhỏ để phân biệt.
  - Nút `edit`, `delete` nhỏ gọn, hover đổi màu.
  - Badge số lượng chi phí/thuế hiển thị khi giá trị > 0.
  - Tooltip trên label clickable, giá trị clickable, và nút hành động.

- **5.7: Cần trả nhà cung cấp**: Tính bằng `Tổng tiền hàng - Giảm giá tổng`.
- **5.8: Đã thanh toán**: Input number, có nút "100%" để điền bằng `Cần trả nhà cung cấp`.
- **5.9: Phương thức thanh toán**: 
  - `mat-radio` với các tùy chọn: "Tiền mặt", "Chuyển khoản".
  - Disable nếu `Đã thanh toán = 0`.
  - Nếu chọn "Chuyển khoản", hiển thị thêm `mat-radio` để chọn tài khoản từ `mockBankList`.
- **5.10: Ngày hẹn thanh toán**: 
  - **Bên trái (Label)**:
    - Text: "Ngày hẹn thanh toán".
  - **Bên phải (Giá trị)**:
    - `mat-form-field` với `mat-datepicker`.
    - Placeholder: "Chọn ngày hẹn thanh toán".
    - Định dạng ngày: DD/MM/YYYY.
    - Tùy chọn, không bắt buộc.
    - Nếu `payment.debt.dueDate` có giá trị, hiển thị ngày hiện tại.
    - Khi chọn ngày, cập nhật `payment.debt.dueDate`.
  - **Logic**:
    - **Validation**: Nếu nhập, ngày phải hợp lệ, không nhỏ hơn ngày hiện tại.
    - **Cập nhật dữ liệu**: Cập nhật `GoodsReceipt.payment.debt.dueDate` khi chọn ngày.
    - **UX**: 
      - Nút lịch rõ ràng để dễ chọn ngày.
      - Hiển thị `mat-error` nếu ngày không hợp lệ (VD: "Ngày không hợp lệ").
      - Nếu `payment.debt.debtAmount = 0`, có thể disable trường này với text nhỏ: "Không có công nợ".
  - **Ánh xạ interface**:
    - `payment.debt.dueDate`: Giá trị từ `mat-datepicker`.
- **5.11: Công nợ**: Tính bằng `Cần trả nhà cung cấp - Đã thanh toán`, hiển thị âm nếu còn nợ (VD: -500,000 VND).

#### **6. Row 6**
- **Nút "Lưu tạm"**: Button `mat-raised-button`, đặt `status = 'draft'` và log dữ liệu vào console.
- **Nút "Hoàn thành"**: Button `mat-raised-button` màu primary, đặt `status = 'completed'` và log dữ liệu.

---


