import { Injectable, Type, TemplateRef } from '@angular/core';
import { ViewportService } from './viewport.service';
import { StandardDialogService, StandardDialogConfig } from '@shared/components/standard-dialog';
import { StandardBottomSheetService, StandardBottomSheetConfig } from '@shared/components/standard-bottom-sheet';
import {
  StrictModalComponent,
  isStrictModalComponent
} from '@shared/components/standard-dialog/interfaces/modal-component.interface';
import { mergeDeep } from '@ngx-translate/core';

/**
 * Interface cho legacy modal config với properties cũ
 */
export interface LegacyModalConfig<TData = unknown> {
  title?: string | TemplateRef<unknown>;
  dialogTitle?: string;
  enableClose?: boolean;
  useDefault?: boolean;
  customActionsTemplate?: TemplateRef<unknown>;
  actions?: {
    useDefault?: boolean;
    customActionsTemplate?: TemplateRef<unknown>;
  };
  data?: TData;
  panelClass?: string | string[];
  width?: string;
  height?: string;
  maxWidth?: string;
  maxHeight?: string;
  minWidth?: string;
  minHeight?: string;
  disableClose?: boolean;
  hasBackdrop?: boolean;
  backdropClass?: string | string[];
  position?: {
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
  };
  direction?: 'ltr' | 'rtl';
  autoFocus?: boolean;
  restoreFocus?: boolean;
  closeOnNavigation?: boolean;
}

/**
 * Interface cho responsive modal config kết hợp StandardDialogConfig và StandardBottomSheetConfig
 */
export interface ResponsiveModalConfig<TData = unknown> extends LegacyModalConfig<TData> {
  // Từ StandardDialogConfig - support both string and TemplateRef
  title?: string | TemplateRef<unknown>;
  enableClose?: boolean;
  actions?: {
    useDefault?: boolean;
    customActionsTemplate?: TemplateRef<unknown>;
  };

  // Common properties
  data?: TData;
}

/**
 * ResponsiveModalService - Unified modal service với auto-selection
 *
 * Chức năng chính:
 * - Tự động chọn StandardDialog (desktop) hoặc StandardBottomSheet (mobile) dựa trên breakpoint
 * - Maintain API tương tự MatDialog để dễ migration
 * - Sử dụng StandardDialogService và StandardBottomSheetService với enhanced features
 * - Backward compatibility với existing code
 *
 * Sử dụng:
 * - Thay thế ResponsiveModalService cũ mà không cần thay đổi code
 * - API tương tự nhưng với enhanced features từ Standard services
 */
@Injectable({
  providedIn: 'root'
})
export class ResponsiveModalService {
  constructor(
    private standardDialogService: StandardDialogService,
    private standardBottomSheetService: StandardBottomSheetService,
    private viewportService: ViewportService
  ) {}

  /**
   * Mở modal với auto-selection giữa dialog và bottom sheet
   * Type Safety: Component phải implement StrictModalComponent interface
   *
   * @param component - Component class phải extends StrictModalComponent<TData, TResult>
   * @param modalConfig - Cấu hình modal (StandardDialogConfig hoặc StandardBottomSheetConfig)
   * @param forceMode - Force sử dụng dialog hoặc bottom-sheet (optional)
   * @returns Promise<TResult | undefined> - Kết quả khi modal đóng
   */
  async open<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    modalConfig: ResponsiveModalConfig<TData> = {},
    forceMode?: 'dialog' | 'bottom-sheet'
  ): Promise<TResult | undefined> {
    // Runtime validation: Kiểm tra component có implement StrictModalComponent interface không
    try {
      // Cố gắng tạo instance với empty constructor trước
      let tempInstance: any;
      try {
        tempInstance = new component();
      } catch (constructorError) {
        // Nếu constructor có dependencies, tạo instance với undefined values
        tempInstance = Object.create(component.prototype);
      }

      if (!isStrictModalComponent(tempInstance)) {
        console.warn(
          `ResponsiveModalService: Component ${component.name} không implement StrictModalComponent interface.`,
          'Recommended: Implement getModalResult(), isValid(), updateData?(), onModalOpen?(), onModalClose?() methods để có type safety đầy đủ.'
        );
      }
    } catch (error) {
      // Ignore validation errors, component vẫn có thể hoạt động
      console.debug(`ResponsiveModalService: Skipped validation for component ${component.name} (có dependencies injection)`);
    }


    const useBottomSheet =
      forceMode === 'bottom-sheet' || (forceMode !== 'dialog' && this.viewportService.isMobile());


    if (useBottomSheet) {
      // Convert ResponsiveModalConfig to StandardBottomSheetConfig
      const standardConfig: StandardBottomSheetConfig<TData> = mergeDeep({
        enableClose: true,
        panelClass: 'responsive-bottom-sheet',
        disableClose: false,
        hasBackdrop: true,
        actions: {
          useDefault: true
        },
      }, modalConfig);

      // Mở bottom sheet với StandardBottomSheetService
      return this.standardBottomSheetService.openAsync<TData, TResult, TComponent>(component, standardConfig);
    } else {
      // Convert ResponsiveModalConfig to StandardDialogConfig
      const standardConfig: StandardDialogConfig<TData> = mergeDeep({
        enableClose: true,
        panelClass: 'responsive-dialog',
        disableClose: false,
        hasBackdrop: true,
        actions: {
          useDefault: true
        },
      }, modalConfig);


      // Mở dialog với StandardDialogService
      return this.standardDialogService.openAsync<TData, TResult, TComponent>(component, standardConfig);
    }
  }

  /**
   * Kiểm tra xem có modal nào đang mở không
   * Backward compatibility method
   *
   * @returns boolean - true nếu có modal đang mở
   */
  hasOpenModals(): boolean {
    return this.standardDialogService.hasOpenDialogs();
  }

  /**
   * Đóng tất cả modals đang mở
   * Backward compatibility method
   */
  closeAll(): void {
    this.standardDialogService.closeAll();
    this.standardBottomSheetService.dismiss();
  }
}
