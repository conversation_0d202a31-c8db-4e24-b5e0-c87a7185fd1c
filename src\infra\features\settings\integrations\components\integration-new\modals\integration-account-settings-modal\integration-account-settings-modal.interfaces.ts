import { SettingsListConfig } from '@shared/components/settings/settings-list.component';

/**
 * Interface cho dữ liệu đầu vào của modal cài đặt tài khoản tích hợp
 */
export interface IntegrationAccountSettingsModalData {
  /**
   * C<PERSON>u hình cho SettingsListComponent
   */
  config: SettingsListConfig;
  
  /**
   * Tên platform để hiển thị trong title modal
   */
  platformName: string;
  
  /**
   * Tên account để hiển thị trong title modal
   */
  accountName?: string;
}

/**
 * Interface cho kết quả trả về từ modal cài đặt tài khoản tích hợp
 */
export interface IntegrationAccountSettingsModalResult {
  /**
   * Có thay đổi settings hay không
   */
  settingsChanged: boolean;
  
  /**
   * Dữ liệu settings đã thay đổi (nếu có)
   */
  changedSettings?: Record<string, any>;
  
  /**
   * Action được thực hiện
   */
  action: 'close' | 'save' | 'cancel';
}
