import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { SelectFieldTypes, FieldItemConfig, FieldValidationResult } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

/**
 * Component chuyên bi<PERSON>t xử lý các select-based fields
 * 
 * Supported field types:
 * - picklist: Dropdown chọn một giá trị
 * - multi-picklist: Multi-select chọn nhiều giá trị
 * - select: Standard select dropdown
 * - radio: Radio button group (rendered as select for consistency)
 * 
 * Features:
 * - View mode: Hiển thị mock data với chips cho multi-select
 * - Form mode: Select controls với search functionality
 * - Multi-select với chips display
 * - Mock options cho demo
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-select-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatChipsModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './select-field.component.html',
  styleUrls: ['./select-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SelectFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitters để gửi dữ liệu lên parent component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();
  @Output() validationChange = new EventEmitter<{ fieldId: string; validation: FieldValidationResult }>();

  // Component-specific state
  mockValues = signal<string[]>([]);
  fieldOptions = signal<Array<{value: string, label: string}>>([]);

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    const supportedTypes: SelectFieldTypes[] = ['picklist', 'multi-picklist', 'select', 'radio'];
    if (!supportedTypes.includes(this.config.field.type as SelectFieldTypes)) {
      console.warn(`SelectFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate mock value dựa trên field type
   */
  protected override generateMockValue(): void {
    const fieldType = this.config.field.type as any;

    if (this.isMultiSelect()) {
      // Generate array of mock values for multi-select
      const mockArray = this.mockDataService.generateArrayMockData(fieldType, 2).split(', ');
      this.mockValues.set(mockArray);
      this.mockValue.set(mockArray.join(', '));
    } else {
      // Generate single mock value
      this.mockValue.set(this.mockDataService.generateMockData(fieldType));
    }
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected override getValidators(): any[] {
    const validators = this.getCommonValidators();

    // Select fields thường không cần validators đặc biệt
    // Validation chủ yếu dựa trên options available

    return validators;
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    const fieldType = this.config.field.type as any;
    switch (fieldType) {
      case 'multi-picklist':
        return 'checklist';
      case 'radio':
        return 'radio_button_checked';
      case 'picklist':
      case 'select':
      default:
        return 'arrow_drop_down';
    }
  }

  // ===== IMPLEMENT INTERFACE METHODS =====

  /**
   * Handle khi field.value thay đổi từ bên ngoài
   */
  override handleFieldValueChange(): void {
    super.handleFieldValueChange();
    this.loadFieldOptions();
  }

  override initializeField(): void {
    super.initializeField();
    this.loadFieldOptions();
  }

  /**
   * Lấy giá trị hiện tại của field
   */
  getFieldValue(): FieldValue {
    return this.getCurrentValue();
  }

  /**
   * Override onValueChange để emit event
   */
  override onValueChange(value: FieldValue): void {
    super.onValueChange!(value);

    // Emit change event để Form Management Service track
    const fieldId = this.config.field._id || '';
    this.valueChange.emit({ fieldId, value });
  }

  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Load field options từ field constraints
   */
  private loadFieldOptions(): void {
    const constraints = this.config.field.constraints;
    let options: Array<{value: string, label: string}> = [];

    // Đọc từ picklistOptions nếu có
    if ((constraints as any)?.picklistOptions && Array.isArray((constraints as any).picklistOptions)) {
      options = (constraints as any).picklistOptions.map((option: any) => ({
        value: option.value.toString(),
        label: option.label
      }));
    }

    this.fieldOptions.set(options);
  }

  /**
   * Kiểm tra xem có phải multi-select không
   */
  isMultiSelect(): boolean {
    return this.config.field.type === 'multi-picklist';
  }

  /**
   * Lấy options cho select field
   */
  getSelectOptions(): Array<{ value: any; label: string }> {
    return this.fieldOptions();
  }

  /**
   * Lấy mock values cho multi-select fields
   */
  getMockValues(): string[] {
    return this.mockValues();
  }

  /**
   * Lấy label của option dựa trên value
   */
  getOptionLabel(value: string): string {
    const option = this.fieldOptions().find(opt => opt.value === value);
    return option?.label || value || '';
  }

  /**
   * Xử lý khi selection thay đổi
   */
  onSelectionChange(event: any): void {
    const fieldId = this.config.field._id || '';
    const value = event.value;

    // Emit value change event
    this.valueChange.emit({ fieldId, value });
  }

  /**
   * Remove chip trong multi-select mode
   */
  removeChip(chipValue: string): void {
    if (this.isReadOnly()) return;

    const currentValue = this.formControl().value || [];
    const newValue = currentValue.filter((value: string) => value !== chipValue);
    this.formControl().setValue(newValue);
  }
}
