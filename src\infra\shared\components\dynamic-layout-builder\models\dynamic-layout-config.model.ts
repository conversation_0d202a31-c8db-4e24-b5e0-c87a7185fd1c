import { FieldType, FieldPermissionProfile } from "@domain/entities/field.entity";
import { DynamicLayoutConfigDto } from "./dynamic-layout-config.dto";
import { FieldPropertiesData } from "../modals/field-properties/field-properties-modal.component";

/**
 * các field bên dưới set từ frontend
 * DynamicLayoutConfigDto có thể set từ server/frontend
 */
export interface DynamicLayoutConfig extends DynamicLayoutConfigDto {
  /** Tiêu đề của layout builder */
  title: string;
  /** Tên hiển thị của layout (thay thế LayoutMetadata.name) */
  name?: string;
  /** Mô tả ngắn gọn về layout */
  shortDescription?: string;


  // Field types configuration - các loại field có thể sử dụng
  /** Danh sách các FieldType được hỗ trợ - sử dụng để filter DEFAULT_FIELD_TYPES */
  supportedFieldTypes?: FieldType[];

  // UI Configuration - cấu hình giao diện
  /** <PERSON><PERSON> hiển thị panel template selector không */
  // showTemplateSelector?: boolean;
  /** <PERSON><PERSON> hiển thị panel preview không */
  showPreviewPanel?: boolean;
  /** Có cho phép chế độ preview không */
  enablePreviewMode?: boolean;
  /** Có hiển thị toolbar actions không */
  showToolbarActions?: boolean;

  // Behavior Configuration - cấu hình hành vi
  /** Có tự động lưu layout không */
  autoSave?: boolean;
  /** Thời gian tự động lưu (milliseconds) */
  autoSaveInterval?: number;
  /** Có cho phép kéo thả không */
  enableDragDrop?: boolean;
  /** Có cho phép chỉnh sửa inline không */
  enableInlineEdit?: boolean;

  // Validation Configuration - cấu hình validation
  /** Số lượng section tối thiểu */
  minSections?: number;
  /** Số lượng section tối đa */
  maxSections?: number;
  /** Số lượng field tối thiểu trong mỗi section */
  minFieldsPerSection?: number;
  /** Số lượng field tối đa trong mỗi section */
  maxFieldsPerSection?: number;

  // Tab Configuration - cấu hình tabs
  /** Có hiển thị Quick Create tab không */
  enableQuickCreate?: boolean;
}

export interface DynamicLayoutBuilderConfig  {
  /**
   * layoutId set từ frontend như 'product-layout'..
   * nhằm lưu vào localStorage
   * không sync với server
   */
  layoutId: string;
  /**
   * Danh sách các layout có sẵn
   * Layout mặc định sẽ được lấy từ layout có isDefault = true hoặc layouts[0]
   */
  layouts: DynamicLayoutConfig[];
  /**
   * mục đích để sử dụng trong trường hợp tạo layout mới
   */
  defaultLayoutConfig: DynamicLayoutConfig;
}

/**
 * Helper function để lấy layout mặc định từ array layouts
 * Ưu tiên layout có isDefault = true, nếu không có thì lấy layouts[0]
 */
export function getDefaultLayout(layouts: DynamicLayoutConfig[]): DynamicLayoutConfig | null {
  if (!layouts || layouts.length === 0) {
    return null;
  }

  // Tìm layout có isDefault = true
  const defaultLayout = layouts.find(layout => layout.isDefault === true);
  if (defaultLayout) {
    return defaultLayout;
  }

  // Nếu không có layout nào có isDefault = true, lấy layout đầu tiên
  return layouts[0];
}
