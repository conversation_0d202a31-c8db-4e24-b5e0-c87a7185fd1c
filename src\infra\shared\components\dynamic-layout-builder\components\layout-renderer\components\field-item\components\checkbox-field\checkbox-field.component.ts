import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

/**
 * Component chuyên biệt xử lý checkbox fields
 * 
 * Features:
 * - View mode: Hiển thị mock data với check/uncheck icon
 * - Form mode: Checkbox control
 * - Permission-based visibility và read-only state
 * - i18n support
 * - Accessibility support
 */
@Component({
  selector: 'app-checkbox-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './checkbox-field.component.html',
  styleUrls: ['./checkbox-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CheckboxFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  @Input({ required: true }) config!: FieldItemConfig;
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    if (this.config.field.type !== 'checkbox') {
      console.warn(`CheckboxFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate mock value dựa trên field type
   */
  protected override generateMockValue(): void {
    const mockData = this.mockDataService.generateMockData('checkbox');
    this.mockValue.set(Boolean(mockData) ? 'true' : 'false');
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected override getValidators(): any[] {
    return this.getCommonValidators();
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    return 'check_box';
  }

  // ===== IMPLEMENT INTERFACE METHODS =====

  /**
   * Handle khi field.value thay đổi từ bên ngoài
   */
  override handleFieldValueChange(): void {
    super.handleFieldValueChange();
  }

  /**
   * Lấy giá trị hiện tại của field
   */
  getFieldValue(): FieldValue {
    return this.getCurrentValue();
  }

  /**
   * Override onValueChange để emit event
   */
  override onValueChange(value: FieldValue): void {
    super.onValueChange!(value);

    // Emit change event để Form Management Service track
    const fieldId = this.config.field._id || '';
    this.valueChange.emit({ fieldId, value });
  }
}
