import { Routes } from '@angular/router';
import { RouterResolverService } from '@core/services/router.resolver.service';
import { canActivateCashier } from '@core/guards/auth.guard';
import { ROUTER_LINKS } from 'salehub_shared_contracts';

export const CashierRoutes: Routes = [
  {
    path: ROUTER_LINKS.cashier.order,
    data: {
      resolverNavigationBlock: 'cashier',
      resolverPermission: 'cashier',
      resolverRequestQuery: ['cashier']
    },
    resolve: RouterResolverService,
    loadComponent: () => import('./pages/cashier-order/cashier-order.component')
      .then((m) => m.CashierOrderComponent),

    canActivate: [canActivateCashier]
  }
  // {
  //   path: CASHIER_ROUTER_LINKS.invoices,
  //   data: {
  //     resolverNavigationBlock: 'cashier',
  //     resolverPermission: 'cashier',
  //     resolverRequestQuery: ['pos_list_invoices']
  //   },
  //   runGuardsAndResolvers: 'paramsOrQueryParamsChange',
  //   resolve: RouterResolverService,
  //   loadComponent: () => import('../components/invoice/invoice.component')
  //     .then((m) => m.InvoiceComponent),

  //   canActivate: [canActivateCashier]
  // },
  // {
  //   path: CASHIER_ROUTER_LINKS.purchases,
  //   data: {
  //     resolverNavigationBlock: 'cashier',
  //     resolverPermission: 'cashier',
  //     resolverRequestQuery: ['pos_purchase_data']
  //   },
  //   resolve: RouterResolverService,
  //   loadComponent: () => import('../components/purchases/purchases.component')
  //     .then((m) => m.PurchasedComponent),

  //   canActivate: [canActivateCashier]
  // },
  // {
  //   path: CASHIER_ROUTER_LINKS.reports,
  //   data: {
  //     resolverNavigationBlock: 'cashier',
  //     resolverPermission: 'cashier',
  //     resolverRequestQuery: ['pos_report']
  //   },
  //   resolve: RouterResolverService,
  //   loadComponent: () => import('../components/report/report.component')
  //     .then((m) => m.ReportComponent),

  //   canActivate: [canActivateCashier]
  // },
  // {
  //   path: CASHIER_ROUTER_LINKS.shift_change,
  //   data: {
  //     resolverNavigationBlock: 'cashier',
  //     resolverPermission: 'cashier',
  //     resolverRequestQuery: ['get_end_shift_data']
  //   },
  //   resolve: RouterResolverService,
  //   loadComponent: () => import('../components/shift-change/shift-change.component')
  //     .then((m) => m.ShiftChangeComponent),

  //   canActivate: [canActivateCashier]
  // },
  // {
  //   path: CASHIER_ROUTER_LINKS.calc_delivery_fee,
  //   resolve: RouterResolverService,
  //   data: {
  //     resolverNavigationBlock: 'cashier',
  //     resolverPermission: 'cashier',
  //     resolverRequestQuery: ['pos_calc_delivery_fee']
  //   },
  //   loadComponent: () => import('../components/calc-delivery-fee/calc-delivery-fee.component')
  //     .then((m) => m.CalcDeliveryFeeComponent),

  //   canActivate: [canActivateCashier]
  // },
  // {
  //   path: CASHIER_ROUTER_LINKS.reviews,
  //   resolve: RouterResolverService,
  //   data: {
  //     resolverNavigationBlock: 'cashier',
  //     resolverPermission: 'cashier',
  //     resolverRequestQuery: ['reviews', 'review_summary']
  //   },
  //   loadComponent: () => import('../components/review/review.component')
  //     .then((m) => m.ReviewComponent),

  //   canActivate: [canActivateCashier]
  // }
];
