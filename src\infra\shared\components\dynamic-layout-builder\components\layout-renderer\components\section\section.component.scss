// Section Component Styles
.section-card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  
  // Section Header Styles
  .section-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    margin: 0;
    
    .section-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #495057;
      margin: 0;
      display: flex;
      align-items: center;
      
      .section-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
        color: #6c757d;
      }
    }
  }
  
  // Section Content Styles
  .section-content {
    padding: 1.5rem;
  }
}

// Dynamic CSS-based Column Layout Container
.section-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;

  // Base field item styling
  .field-item {
    flex: 1 1 auto;
    min-width: 0; // Cho phép field shrink khi cần
  }

  // Single Column Layout - mỗi field chiếm full width
  &.single-column {
    flex-direction: column;

    .field-item {
      flex: 1 1 100%;
      width: 100%;
    }
  }

  // Double Column Layout - fields tự động chia đều 2 cột
  &.double-column {
    flex-direction: row;

    .field-item {
      flex: 1 1 calc(50% - 0.75rem); // 50% width trừ đi gap
      max-width: calc(50% - 0.75rem);
    }
  }
}

// Responsive Design
@media (max-width: 992px) {
  .section-card {
    .section-header {
      padding: 0.875rem 1.25rem;

      .section-title {
        font-size: 1rem;

        .section-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }

    .section-content {
      padding: 1.25rem;
    }
  }

  // Force single column layout on tablet và nhỏ hơn
  .section-fields-container.double-column {
    flex-direction: column;
    gap: 1.5rem;

    .field-item {
      flex: 1 1 100%;
      max-width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .section-card {
    margin-bottom: 1rem;
    border-radius: 0.375rem;

    .section-header {
      padding: 0.75rem 1rem;

      .section-title {
        font-size: 0.95rem;

        .section-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }

    .section-content {
      padding: 1rem;
    }
  }

  // Ensure single column on mobile với gap nhỏ hơn
  .section-fields-container {
    gap: 1rem;

    &.double-column {
      flex-direction: column;

      .field-item {
        flex: 1 1 100%;
        max-width: 100%;
      }
    }
  }
}

@media (max-width: 576px) {
  .section-card {
    margin-bottom: 0.75rem;
    border-radius: 0.25rem;

    .section-header {
      padding: 0.625rem 0.875rem;

      .section-title {
        font-size: 0.9rem;

        .section-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
        }
      }
    }

    .section-content {
      padding: 0.875rem;
    }
  }

  // Single column with reduced gap on small mobile
  .section-fields-container {
    gap: 0.75rem;

    &.double-column {
      flex-direction: column;

      .field-item {
        flex: 1 1 100%;
        max-width: 100%;
      }
    }
  }
}

// Animation for layout changes
.section-fields-container {
  transition: flex-direction 0.3s ease-in-out, gap 0.3s ease-in-out;

  .field-item {
    transition: flex 0.3s ease-in-out, max-width 0.3s ease-in-out;
  }
}

// Empty state styling
.section-card:empty {
  display: none;
}

// Field item spacing within sections
.field-item {
  &:last-child {
    margin-bottom: 0;
  }
}
