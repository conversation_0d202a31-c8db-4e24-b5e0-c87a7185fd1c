import { Component, ChangeDetectionStrategy, OnInit, On<PERSON><PERSON>roy, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

// Core services
import { FlashMessageService } from '@core/services/flash_message.service';

// Shared components
import { DynamicLayoutRendererComponent } from '../../components/layout-renderer/dynamic-layout-renderer.component';

// Interfaces
import { StrictModalComponent } from '@shared/components/standard-dialog/interfaces/modal-component.interface';
import { 
  DynamicLayoutRendererModalData, 
  DynamicLayoutRendererModalResult,
  DynamicLayoutRendererFormData 
} from '../../models/dynamic-layout-renderer.model';

/**
 * DynamicLayoutRendererModal - Modal wrapper cho DynamicLayoutRendererComponent
 * 
 * Chức năng:
 * - Wrap DynamicLayoutRendererComponent trong modal layout
 * - Hỗ trợ cả View mode và Form Edit mode
 * - Tích hợp với form validation và save functionality
 * - Handle modal close events (Cancel/Save/X button)
 * - Responsive design (desktop dialog / mobile bottom sheet)
 * 
 * Sử dụng:
 * - Implement StrictModalComponent interface cho type safety
 * - Sử dụng với ResponsiveModalService.open()
 * - Return Observable với kết quả form data khi save
 */
@Component({
  selector: 'app-dynamic-layout-renderer-modal',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    DynamicLayoutRendererComponent
  ],
  templateUrl: './dynamic-layout-renderer-modal.component.html',
  styleUrls: ['./dynamic-layout-renderer-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DynamicLayoutRendererModalComponent implements 
  StrictModalComponent<DynamicLayoutRendererModalData, DynamicLayoutRendererModalResult>,
  OnInit, 
  OnDestroy {

  // ===== DEPENDENCY INJECTION =====
  
  private modalData = inject(MAT_DIALOG_DATA) as DynamicLayoutRendererModalData | undefined;
  private translateService = inject(TranslateService);
  private flashMessageService = inject(FlashMessageService);

  // ===== COMPONENT STATE =====
  
  /** Cấu hình cho DynamicLayoutRendererComponent */
  rendererConfig = signal<DynamicLayoutRendererModalData | null>(null);
  
  /** Trạng thái loading khi save */
  isSaving = signal(false);
  
  /** Có thay đổi dữ liệu hay không */
  hasChanges = signal(false);
  
  /** Dữ liệu form hiện tại */
  currentFormData = signal<DynamicLayoutRendererFormData | null>(null);
  
  /** Subscription management */
  private subscriptions = new Subscription();

  // ===== LIFECYCLE HOOKS =====

  constructor() {
    // Khởi tạo dữ liệu từ modalData nếu có
    if (this.modalData) {
      this.updateData(this.modalData);
    }
  }

  ngOnInit(): void {
    console.log('DynamicLayoutRendererModal initialized with data:', this.modalData);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  // ===== STRICT MODAL COMPONENT INTERFACE =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): DynamicLayoutRendererModalResult {
    return {
      action: this.hasChanges() ? 'save' : 'close',
      formData: this.currentFormData() || undefined,
      hasChanges: this.hasChanges()
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    // Nếu không có thay đổi, luôn valid (cho phép đóng modal)
    if (!this.hasChanges()) {
      return true;
    }

    // Nếu có thay đổi, kiểm tra form data có hợp lệ không
    const formData = this.currentFormData();
    return formData !== null && formData.values.length > 0;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: DynamicLayoutRendererModalData): void {
    console.log('DynamicLayoutRendererModal.updateData() called with:', data);

    // Tạo config cho DynamicLayoutRendererComponent với onFormSave callback
    const rendererConfig: DynamicLayoutRendererModalData = {
      ...data,
      onFormSave: this.handleFormSave.bind(this)
    };

    this.rendererConfig.set(rendererConfig);
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen?(): void {
    console.log('DynamicLayoutRendererModal opened');
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose?(): boolean {
    console.log('DynamicLayoutRendererModal closing, hasChanges:', this.hasChanges());

    // Nếu có thay đổi chưa lưu, hiển thị cảnh báo
    if (this.hasChanges() && !this.isSaving()) {
      // TODO: Có thể hiển thị confirm dialog ở đây
      this.flashMessageService.warning(
        this.translateService.instant('DYNAMIC_LAYOUT_RENDERER_MODAL.WARNINGS.UNSAVED_CHANGES')
      );
    }

    return true; // Cho phép đóng modal
  }

  // ===== EVENT HANDLERS =====

  /**
   * Public method để save form - được gọi từ template
   */
  async onSave(): Promise<void> {
    const formData = this.currentFormData();
    if (formData) {
      await this.handleFormSave(formData);
    }
  }

  /**
   * Xử lý khi DynamicLayoutRenderer có form save
   * Được gọi từ onFormSave callback
   */
  private async handleFormSave(data: DynamicLayoutRendererFormData): Promise<void> {
    console.log('DynamicLayoutRendererModal.handleFormSave() called with:', data);

    try {
      this.isSaving.set(true);
      
      // Lưu form data vào state
      this.currentFormData.set(data);
      this.hasChanges.set(true);

      // Gọi original onFormSave callback nếu có
      const originalCallback = this.modalData?.onFormSave;
      if (originalCallback) {
        await originalCallback(data);
      }

      // Hiển thị thông báo thành công
      this.flashMessageService.success(
        this.translateService.instant('DYNAMIC_LAYOUT_RENDERER_MODAL.SUCCESS.FORM_SAVED')
      );

    } catch (error) {
      console.error('Error saving form:', error);
      this.flashMessageService.error(
        this.translateService.instant('DYNAMIC_LAYOUT_RENDERER_MODAL.ERRORS.SAVE_FAILED')
      );
    } finally {
      this.isSaving.set(false);
    }
  }

  /**
   * Xử lý khi có thay đổi trong form
   * Được gọi từ DynamicLayoutRenderer formDataChange event
   */
  onFormChange(formData: DynamicLayoutRendererFormData): void {
    // Cập nhật current form data
    this.currentFormData.set(formData);

    // Đánh dấu có thay đổi nếu form data có values
    const hasChanges = formData && formData.values && formData.values.length > 0;
    this.hasChanges.set(hasChanges);
  }

  // ===== COMPUTED PROPERTIES =====

  /**
   * Tiêu đề modal - sử dụng từ config hoặc default
   */
  get modalTitle(): string {
    const configTitle = this.rendererConfig()?.title;
    if (configTitle) {
      return this.translateService.instant(configTitle);
    }
    
    // Default title dựa trên mode
    const isEditMode = this.rendererConfig()?.enableEditMode;
    const titleKey = isEditMode 
      ? 'DYNAMIC_LAYOUT_RENDERER_MODAL.TITLES.EDIT_FORM'
      : 'DYNAMIC_LAYOUT_RENDERER_MODAL.TITLES.VIEW_LAYOUT';
    
    return this.translateService.instant(titleKey);
  }

  /**
   * Có đang trong edit mode hay không
   */
  get isEditMode(): boolean {
    return this.rendererConfig()?.enableEditMode === true;
  }
}
