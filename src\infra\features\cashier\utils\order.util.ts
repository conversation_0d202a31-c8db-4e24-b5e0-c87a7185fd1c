import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { CashierOrderSummaryComponent } from '@features/cashier/components/cashier/order-summary/order-summary.component';
import { Place, PosInvoice, PosInvoiceItem } from 'salehub_shared_contracts';

export function placeholderOrder(shiftId?: number): PosInvoice {
  return {
    shiftId,
    items: [],
    discountWholeOrder: {
      amount: 0
    },
    customer: {},
    isFinalized: false,
    isThirdPartyInvoice: false,

    summarize: {
      countItems: 0,
      totalDiscount: 0,
      totalRevenue: 0,
      subTotal: 0
    },
    delivery: {
      driver: {},
      company: {},
      selfArrive: false
    },

    timeline: []
  };
}
export function buildFullPosOrder(invoice: PosInvoice) {
  invoice.items ||= [];

  const discountWholeOrderAmount = invoice.discountWholeOrder?.amount ?? 0;
  invoice.summarize.totalDiscount = discountWholeOrderAmount;
  invoice.summarize.countItems = 0;

  invoice.items.forEach((item, index) => buildOrderItem(invoice, item, index));

  invoice.summarize.subTotal = invoice.items
    .reduce(
      (accumulator, item) => {
        const subtotal: number = accumulator + item.subTotal;

        if(item.options && item.options?.length > 0) {
          return item.options.reduce(
            (d, e) => d + e.subTotal,
            subtotal
          );
        }

        return subtotal;
      },
      0
    );

  invoice.summarize.totalRevenue = invoice.items
    .reduce(
      (accumulator, item) => {
        const total: number = accumulator + item.total;

        if(item.options && item.options?.length > 0) {
          return item.options.reduce(
            (d, e) => d + e.total,
            total
          );
        }

        return total;
      },
      0
    );

  /**
   * totalRevenue là đã trừ item discount trong từng item rồi
   */
  invoice.summarize.totalRevenue -= discountWholeOrderAmount;

  if(invoice.customer?.address?.fullAddress) {
    invoice.delivery.displayAddress = getDeliveryFullAddress(invoice.customer.address);
  }
}

export function getDeliveryFullAddress(place: Place | undefined) {
  if(!place) {
    return '';
  }

  let text = '';
  if(place.mainAddress) {
    text += `${place.mainAddress} (${place.fullAddress})`;
  } else {
    text += place.fullAddress;
  }

  if(place.instruction) {
    text += ` (${place.instruction})`;
  }

  return text;
}

function buildOrderItem(
  invoice: PosInvoice,
  item: PosInvoiceItem,
  index?: number | undefined,
  parentItem?: PosInvoiceItem
) {
  if(item.quantity && item.price) {
    if(!parentItem) {
      item.subTotal = item.price * item.quantity;
    } else {
      item.subTotal = item.price * item.quantity * parentItem.quantity;
    }
    item.total = item.subTotal - item.discount;
  }

  if(item.quantity === 0 && index !== undefined) {
    invoice.items.splice(index as number, 1);
  }

  invoice.summarize.totalDiscount += item.discount;
  invoice.summarize.countItems += item.quantity;

  if(item.options && item.options.length > 0) {
    invoice.summarize.countItems += item.options.length;
    item.options.forEach(opt => buildOrderItem(invoice, opt, undefined, item));
  }
}

export function openOrderSummary(_bottomSheet: MatBottomSheet, invoice: PosInvoice) {
  return _bottomSheet
    .open(CashierOrderSummaryComponent, {
      data: invoice,
      panelClass: ['invoice-summarize', 'full-screen-bottom-sheet'],
      disableClose: true
    });
}

export function testCashierOrder() {
  const result: PosInvoice = {
    // invoiceId: 'XM-223',
    invoiceId: undefined,
    items: [
      {
        _id: '65f7fc48feb53781e59d3ee5',
        name: 'Xôi thập cẩm 35k',
        price: 65000,
        quantity: 5,
        total: 355000,
        subTotal: 355000,
        discount: 10000,
        grossProfit: 30000,
        grossProfitPct: 10,
        totalCost: 5000,

        options: [
          {
            _id: '65f7fc48feb53781e59d3ec4',
            name: 'Thêm xôi 5k',
            price: 10000,
            quantity: 1,
            total: 10000,
            totalCost: 5000,
            subTotal: 10000,
            discount: 5000,
            grossProfit: 30000,
            grossProfitPct: 10
          },
          {
            _id: '65f7fc48feb53781e59d3eba',
            name: 'Gà đảo nấm hương - Gọi thêm',
            price: 20000,
            totalCost: 5000,
            quantity: 1,
            total: 20000,
            subTotal: 20000,
            discount: 0,
            grossProfit: 30000,
            grossProfitPct: 10
          }
        ],

        note: '0Cho nhiều hành và đậu xanh Cho nhiều hành và đậu xanh '
      },
      {
        _id: '65f7fc48feb53781e59d3edb',
        name: 'Xôi chả',
        price: 20000,
        quantity: 5,
        total: 100000,
        subTotal: 100000,
        discount: 0,
        grossProfit: 30000,
        grossProfitPct: 10,
        totalCost: 5000
      },
      {
        _id: '65f7fc48feb53781e59d3eeb',
        name: 'Xôi sườn cay',
        price: 35000,
        quantity: 100,
        total: 3500000,
        subTotal: 3500000,
        discount: 0,
        grossProfit: 30000,
        grossProfitPct: 10,
        totalCost: 5000
      }
    ],

    store: {
      storeId: '1',
      branchName: '1',
      branchId: '1',
      address: '1',
      phone: '1',
      website: '1',
      bank: {
        bankName: '1',
        accountId: '1',
        accountName: '1',
        baseQrCode: '1'
      }
    },

    diningOption: 'takeaway',
    paymentMethod: 'cash',
    discountWholeOrder: {
      amount: 10000
    },
    summarize: {
      totalDiscount: 10000,
      subTotal: 3955000,
      totalRevenue: 3945000,
      countItems: 10
    },

    customer: {
      // _id: 'customer_id',
      // name: 'Customer name',
      // phoneNumber: '**********',
      // address: {
      //   fullAddress: '759 bà triệu',
      //   lat: 19.812827,
      //   lng: 105.7769794,
      //   instruction: 'đi thẳng rẽ phải'
      // },
      // note: 'khach vip'
    },
    isFinalized: false,
    isThirdPartyInvoice: false,
    delivery: {
      company: {},
      selfArrive: false,
      distance: 0.7,
      driver: {
        name: 'Driver name',
        phoneNumber: '**********',
        avatar: 'https://s3-ap-southeast-1.amazonaws.com/myteksi/grab-id/profile_pic/1/-1/xY4ZsqC7N2_OJM2XanhWAxB6E78=_2024/02/26_.jpg'
      }
    },
    timeline: []
  };

  return result;
}
