import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  standalone: true,
  name: 'formatDate'
})
export class FormatDatePipe implements PipeTransform {
  transform(value: number | string | Date | undefined, formatType: 'ago' | 'vietnamDateStr' | 'dmyhm'): string {
    if(!value) {
      return value as string;
    }

    const date = new Date(value);
    if(formatType === 'ago') {
      const secondsAgo = (Date.now() - date.getTime()) / 1000;

      const days = Math.floor(secondsAgo / (3600 * 24));
      const hours: any = Math.floor((secondsAgo - (days * (3600 * 24))) / 3600);
      const minutes: any = Math.floor((secondsAgo - (days * (3600 * 24)) - (hours * 3600)) / 60);
      // const seconds = Math.floor(inSeconds - (days * (3600 * 24)) - (hours * 3600) - (minutes * 60));

      if(days > 0) {
        return `${Math.round(days)} ngày trước`;
      }

      if(hours > 0) {
        return `${Math.round(hours)} tiếng trước`;
      }

      if(minutes > 0) {
        return `${minutes} phút trước`;
      }

      return 'vừa mới xong';
    }

    const d = date.getDate();
    const month = date.getMonth() + 1;
    const y = date.getFullYear();
    const h = date.getHours();
    const m = date.getMinutes();
    const day = date.getDay();
    let dayStr: string;

    if(formatType === 'vietnamDateStr') {
      if(day === 0) {
        dayStr = 'Chủ nhật';
      } else {
        dayStr = `Thứ ${day + 1}`;
      }
      return `${h < 10 ? `0${h}` : h}:${m < 10 ? `0${m}` : m} ${dayStr}, ${d}/${month}/${y}`;
    }

    if(formatType === 'dmyhm') {
      return `${d}-${month}-${y} ${h < 10 ? `0${h}` : h}:${m < 10 ? `0${m}` : m}`;
    }

    return '';
  }
}
