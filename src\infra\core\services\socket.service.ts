import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { ClientToServerEvents, ServerToClientEvents } from 'salehub_shared_contracts';
import { io, Socket, Manager } from 'socket.io-client';

@Injectable({
  providedIn: 'root'
})
export class SocketService {
  private socket: {
    [permission in SOCKET_PERMISSION]?: Socket<ServerToClientEvents, ClientToServerEvents>
  } = {};

  private manager: {
    [permission in SOCKET_PERMISSION]?: Manager
  } = {};

  private onConnectPendingFunctions: {
    [permission in SOCKET_PERMISSION]: Array<(() => void) | null>
  } = {
      cashier: [],
      manage: [],
      admin: []
    };

  constructor() {}

  init(permission: SOCKET_PERMISSION, data: { storeId: string, branchId: string, token: string }) {
    if(!this.manager[permission]) {
      this.manager[permission] = new Manager({
        autoConnect: true,
        reconnection: true,
        closeOnBeforeunload: true,
        /**
         * 3 socket khác nhau, ko dùng chung 1 socket
         * https://socket.io/docs/v4/client-options/
         */
        forceNew: true
      });

      this.socket[permission] = this.manager[permission].socket(`/${permission}`, {
        auth: data
      });

      this.socket[permission].on('connect', () => {
        // console.warn('connected socket');
        // console.log(this.onConnectPendingFunctions);

        if(this.onConnectPendingFunctions[permission]?.length > 0) {
          this.onConnectPendingFunctions[permission].forEach((func, index) => {
            if(func) {
              func();

              this.onConnectPendingFunctions[permission][index] = null;
            }
          });
        }

      // ko dc set this.onConnectPendingFunctions = []
      // nếu ko sẽ bị lỗi vì cùng lúc connect các hàm khác đang pushOnConnectFunction
      });

      this.socket[permission].on('disconnect', () => {
        console.error('disconnected socket');
      });
      /**
       * auth error hoac nhung cai khac
       * middle ware next(err)
       */
      this.socket[permission].on('connect_error', (err: any) => {
        console.error('Socket connect error');
        console.error(err.message); // not authorized
        console.error(err.data); // { content: "Please retry later" }
      });
    }
  }

  destroy() {
    (Object.keys(this.socket) as SOCKET_PERMISSION[])
      .forEach((permission) => {
        if(this.socket[permission]) {
          this.socket[permission].disconnect();
          delete this.socket[permission];
        }
      });
    (Object.keys(this.onConnectPendingFunctions) as SOCKET_PERMISSION[])
      .forEach((permission) => {
        this.onConnectPendingFunctions[permission] = [];
      });
    (Object.keys(this.manager) as SOCKET_PERMISSION[])
      .forEach((permission) => {
        if(this.manager[permission]) {
          delete this.manager[permission];
        }
      });
  }

  /**
   * khi socket bị disconnect
   * thì data trong khoảng thời gian
   * sau khi bị disconnect -> connect lại
   * và toàn bộ các sự kiện on trước khi bị disconnect
   * đều bị mất hết
   */
  pushOnConnectFunction(permission: SOCKET_PERMISSION, func: () => void) {
    this.onConnectPendingFunctions[permission].push(func);
    // console.log(this.onConnectPendingFunctions);

    return this.onConnectPendingFunctions[permission].length - 1;
  }

  removeOnConnectFunction(permission: SOCKET_PERMISSION, index: number) {
    if(typeof index === 'number' && index > -1 && this.onConnectPendingFunctions[permission][index]) {
      this.onConnectPendingFunctions[permission][index] = null;
    }
  }

  getCashierSocket() {
    return this.socket.cashier;
  }

  async join(permission: SOCKET_PERMISSION, roomName: string, callback: (e?: Error) => void) {
    if(this.socket[permission]) {
      // console.log('join', roomName);
      try {
        await this.socket[permission].timeout(5000).emitWithAck('join', roomName);
        // console.log('joined', roomName);

        callback();
      } catch(e) {
        console.error(e);
        callback(new Error('timeout'));
      }
    }
  }

  async leave(permission: SOCKET_PERMISSION, roomName: string, callback: (e?: Error) => void) {
    if(this.socket[permission]) {
      // console.log('leave', roomName);

      try {
        await this.socket[permission].timeout(5000).emitWithAck('leave', roomName);
        // console.log('leaved', roomName);

        callback();
      } catch(e) {
        callback(new Error('timeout'));
      }
    }
  }
}

export type SOCKET_PERMISSION = 'cashier' | 'manage' | 'admin';
