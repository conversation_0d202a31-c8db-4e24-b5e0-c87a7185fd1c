/**
 * D<PERSON> liệu đầu vào cho PromotionModal
 */
export interface PromotionModalData {
  totalAmount: number;
  discountType?: 'amount' | 'percent' | 'coupon';
  discountValue?: number;
  promotionName?: string;
  finalAmount?: number;
  discounts?: any; // Giữ nguyên kiểu dữ liệu từ component cũ
}

/**
 * Kết quả trả về từ PromotionModal
 */
export interface PromotionModalResult {
  totalAmount: number;
  discountType: 'amount' | 'percent' | 'coupon';
  discountValue: number;
  promotionName: string;
  finalAmount: number;
  discounts?: any; // Giữ nguyên kiểu dữ liệu từ component cũ
}
