// Select Field Component Styles
.select-field-container {
  width: 100%;
  margin-bottom: 1rem;

  // Field label styling
  .field-label-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    .field-label {
      font-weight: 500;
      color: var(--bs-body-color);
      margin: 0;
      font-size: 0.875rem;

      .required-asterisk {
        color: var(--bs-danger);
        margin-left: 0.25rem;
      }
    }

    .read-only-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: var(--bs-warning);
    }
  }

  // Field value container
  .field-value-container {
    width: 100%;

    // View mode styling
    .field-view-value {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      background-color: var(--bs-light);
      border: 1px solid var(--bs-border-color);
      border-radius: 0.375rem;
      min-height: 3.5rem;

      .field-type-icon {
        color: var(--bs-secondary);
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }

      .mock-value {
        color: var(--bs-body-color);
        font-size: 0.875rem;
      }

      // Chips container for multi-select view
      .chips-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .mock-chip {
          background-color: var(--bs-primary);
          color: white;
          font-size: 0.75rem;
          height: 1.5rem;
          line-height: 1.5rem;
          padding: 0 0.5rem;
          border-radius: 0.75rem;
        }
      }
    }

    // Form mode styling
    ::ng-deep .mat-mdc-form-field {
      width: 100%;

      .field-type-icon {
        color: var(--bs-secondary);
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }

      // Read-only select styling
      &.mat-form-field-disabled {
        .field-type-icon {
          color: var(--bs-secondary);
          opacity: 0.6;
        }
      }

      // Error state styling
      &.mat-form-field-invalid {
        .field-type-icon {
          color: var(--bs-danger);
        }
      }
    }

    // Selected chips container for form mode
    .selected-chips-container {
      margin-top: 0.5rem;
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;

      .selected-chip {
        background-color: var(--bs-info);
        color: white;
        font-size: 0.75rem;

        ::ng-deep .mat-mdc-chip-remove {
          color: white;
          opacity: 0.8;

          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }

  // Field tooltip styling
  .field-tooltip {
    margin-top: 0.25rem;
    
    small {
      font-size: 0.75rem;
      line-height: 1.2;
    }
  }

  // Field hints styling
  .field-hints {
    margin-top: 0.25rem;
    
    small {
      font-size: 0.75rem;
      line-height: 1.2;
      color: var(--bs-secondary);
      font-style: italic;
    }
  }

  // Read-only container styling
  &.read-only {
    opacity: 0.8;

    .field-label {
      color: var(--bs-secondary);
    }
  }
}

// Multi-select specific styling
.select-field-container {
  &[data-field-type="multi-picklist"] {
    .field-view-value {
      align-items: flex-start;
      padding-top: 1rem;
      padding-bottom: 1rem;

      .chips-container {
        flex: 1;
      }
    }
  }
}

// Radio field specific styling
.select-field-container {
  &[data-field-type="radio"] {
    ::ng-deep .mat-mdc-form-field {
      .field-type-icon {
        color: var(--bs-success);
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .select-field-container {
    margin-bottom: 0.75rem;

    .field-label-container {
      margin-bottom: 0.375rem;

      .field-label {
        font-size: 0.8125rem;
      }
    }

    .field-value-container {
      .field-view-value {
        padding: 0.625rem;
        min-height: 3rem;

        .field-type-icon {
          font-size: 1.125rem;
          width: 1.125rem;
          height: 1.125rem;
        }

        .mock-value {
          font-size: 0.8125rem;
        }

        .chips-container {
          gap: 0.375rem;

          .mock-chip {
            font-size: 0.6875rem;
            height: 1.375rem;
            line-height: 1.375rem;
            padding: 0 0.375rem;
          }
        }
      }

      .selected-chips-container {
        gap: 0.375rem;

        .selected-chip {
          font-size: 0.6875rem;
        }
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .select-field-container {
    .field-value-container {
      .field-view-value {
        background-color: var(--bs-dark);
        border-color: var(--bs-border-color-translucent);

        .chips-container .mock-chip {
          background-color: var(--bs-primary-dark);
        }
      }

      .selected-chips-container .selected-chip {
        background-color: var(--bs-info-dark);
      }
    }
  }
}
