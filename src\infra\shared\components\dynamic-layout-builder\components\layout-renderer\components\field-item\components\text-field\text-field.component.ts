import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { TextFieldTypes, FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';
import { getFieldIcon, getFieldIconColor, generatePlaceHolderData } from '../../../../../../utils/field.utils';

/**
 * Component chuyên biệt xử lý các text-based fields
 * 
 * Supported field types:
 * - text: Văn bản thông thường
 * - email: Email với validation
 * - phone: Số điện thoại
 * - url: URL với validation
 * - search: Tìm kiếm
 * 
 * Features:
 * - View mode: Hiển thị mock data
 * - Form mode: Input controls với validation
 * - Permission-based visibility và read-only state
 * - i18n support cho placeholders
 * - Responsive design
 */
@Component({
  selector: 'app-text-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './text-field.component.html',
  styleUrls: ['./text-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TextFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   * Tuân thủ yêu cầu: component chỉ nhận 1 input object
   */
  @Input({ required: true }) config!: FieldItemConfig;

  /**
   * Event emitter cho form value changes
   * Emit khi user thay đổi giá trị trong form mode
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    const supportedTypes: TextFieldTypes[] = ['text', 'email', 'phone', 'url', 'search'];
    if (!supportedTypes.includes(this.config.field.type as TextFieldTypes)) {
      console.error(`TextFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate placeholder value dựa trên field type
   */
  protected override generateMockValue(): void {
    const fieldType = this.config.field.type as any;
    this.mockValue.set(generatePlaceHolderData(fieldType, this.translateService));
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected override getValidators(): any[] {
    const validators = this.getCommonValidators();

    // Type-specific validators
    switch (this.config.field.type) {
      case 'email':
        validators.push(Validators.email);
        break;
      case 'url':
        validators.push(Validators.pattern(/^https?:\/\/.+/));
        break;
      case 'phone':
        validators.push(Validators.pattern(/^[\d\s\-\+\(\)]+$/));
        break;
    }

    // Length validators
    if (this.config.field.constraints && 'minLength' in this.config.field.constraints) {
      const constraints = this.config.field.constraints as { minLength?: number };
      if (constraints.minLength) {
        validators.push(Validators.minLength(constraints.minLength));
      }
    }
    if (this.config.field.constraints && 'maxLength' in this.config.field.constraints) {
      const constraints = this.config.field.constraints as { maxLength?: number };
      if (constraints.maxLength) {
        validators.push(Validators.maxLength(constraints.maxLength));
      }
    }

    return validators;
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    return getFieldIcon(this.config.field.type);
  }

  /**
   * Lấy màu sắc icon cho field type
   */
  getFieldIconColor(): string {
    return getFieldIconColor(this.config.field.type);
  }

  // ===== IMPLEMENT INTERFACE METHODS =====

  /**
   * Handle khi field.value thay đổi từ bên ngoài
   */
  override handleFieldValueChange(): void {
    // Gọi implementation từ base class
    super.handleFieldValueChange();
  }

  /**
   * Lấy giá trị hiện tại của field
   */
  getFieldValue(): FieldValue {
    return this.getCurrentValue();
  }

  /**
   * Override onValueChange để emit event
   */
  override onValueChange(value: FieldValue): void {
    super.onValueChange!(value);

    // Emit change event để Form Management Service track
    const fieldId = this.config.field._id || '';
    this.valueChange.emit({ fieldId, value });
  }

  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Lấy HTML input type phù hợp
   */
  getInputType(): string {
    switch (this.config.field.type) {
      case 'email':
        return 'email';
      case 'phone':
        return 'tel';
      case 'url':
        return 'url';
      case 'search':
        return 'search';
      default:
        return 'text';
    }
  }
}
