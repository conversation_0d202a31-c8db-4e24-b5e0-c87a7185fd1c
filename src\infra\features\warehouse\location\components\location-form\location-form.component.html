<div class="location-form-container">
  <form [formGroup]="locationForm" (ngSubmit)="onSubmit()" class="location-form">
    <div class="form-content">
      <div class="form-row">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.NAME' | translate }}</mat-label>
          <input matInput formControlName="name">
          <mat-error *ngIf="locationForm.get('name')?.hasError('required')">
            {{ 'WAREHOUSE.LOCATION.FORM.NAME' | translate }} {{ 'COMMON.REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row code-row">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.CODE' | translate }}</mat-label>
          <input matInput formControlName="code">
          <mat-error *ngIf="locationForm.get('code')?.hasError('required')">
            {{ 'WAREHOUSE.LOCATION.FORM.CODE' | translate }} {{ 'COMMON.REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
        <mat-checkbox formControlName="autoGenerateCode" class="auto-code-checkbox">
          {{ 'WAREHOUSE.LOCATION.FORM.AUTO_GENERATE' | translate }}
        </mat-checkbox>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.TYPE' | translate }}</mat-label>
          <mat-select formControlName="type">
            <mat-option *ngFor="let type of locationTypes" [value]="type">
              {{ getLocationTypeLabel(type) | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.PARENT' | translate }}</mat-label>
          <mat-select formControlName="parentId">
            <mat-option *ngFor="let parent of parentLocations" [value]="parent.id">
              {{ parent.code }} - {{ parent.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.CAPACITY' | translate }} (kg)</mat-label>
          <input matInput type="number" formControlName="capacity" min="0">
        </mat-form-field>
      </div>

      <div class="form-row dimensions-row" formGroupName="dimensions">
        <h4>{{ 'WAREHOUSE.LOCATION.FORM.DIMENSIONS' | translate }}</h4>
        <div class="dimensions-inputs">
          <mat-form-field appearance="outline">
            <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.LENGTH' | translate }} (m)</mat-label>
            <input matInput type="number" formControlName="length" min="0" step="0.1">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.WIDTH' | translate }} (m)</mat-label>
            <input matInput type="number" formControlName="width" min="0" step="0.1">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.HEIGHT' | translate }} (m)</mat-label>
            <input matInput type="number" formControlName="height" min="0" step="0.1">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.DEPTH' | translate }} (m)</mat-label>
            <input matInput type="number" formControlName="depth" min="0" step="0.1">
          </mat-form-field>
        </div>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.STATUS' | translate }}</mat-label>
          <mat-select formControlName="status">
            <mat-option *ngFor="let status of locationStatuses" [value]="status">
              {{ getLocationStatusLabel(status) | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="form-row" *ngIf="!editMode">
        <mat-form-field appearance="outline">
          <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.QUANTITY' | translate }}</mat-label>
          <input matInput type="number" formControlName="quantity" min="1">
        </mat-form-field>
      </div>

      <!-- Nút tạo con nếu chưa đạt giới hạn 5 cấp -->
      <div class="create-child-container" *ngIf="currentLevel < 5">
        <button mat-stroked-button type="button" color="primary" (click)="addChild()">
          <mat-icon>add</mat-icon>
          {{ 'WAREHOUSE.LOCATION.FORM.CREATE_CHILD' | translate }}
        </button>
      </div>

      <!-- Hiển thị các form con nếu có -->
      <div class="children-container" *ngIf="showChildren">
        <ng-container *ngFor="let i of [0, 1, 2, 3, 4]; let idx = index">
          <div *ngIf="idx < childrenFormArray.length">
            <app-location-child-form
              [formGroup]="getChildFormGroup(idx)"
              [level]="currentLevel + 1"
              [parentFormGroup]="locationForm"
              (removeChild)="removeChild(idx)">
            </app-location-child-form>
          </div>
        </ng-container>
      </div>

      <!-- Hiển thị thông tin cấp độ -->
      <div class="level-info">
        <mat-label>{{ 'WAREHOUSE.LOCATION.FORM.LEVEL' | translate }}: {{ currentLevel }}/5</mat-label>
      </div>

      <div class="form-actions">
        <button mat-stroked-button type="button" (click)="onCancelClick()">
          {{ 'COMMON.CANCEL' | translate }}
        </button>
        <button mat-raised-button color="primary" type="submit" [disabled]="locationForm.invalid">
          {{ 'COMMON.SAVE' | translate }}
        </button>
      </div>
    </div>
  </form>

  <!-- Hiển thị preview realtime của form data ở cột bên phải -->
  <div class="preview-column">
    <app-location-form-preview [formData]="locationForm.value" [currentLevel]="currentLevel"></app-location-form-preview>
  </div>
</div>
