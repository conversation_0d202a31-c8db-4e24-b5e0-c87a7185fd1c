import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { Router, Event } from '@angular/router';
import { Subject, Observable } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class RouterEventService implements OnDestroy {
  private destroy$ = new Subject<void>();
  private navigationEvent$ = new Subject<Event>();

  constructor(private router: Router) {
    this.router.events.pipe(
      takeUntil(this.destroy$)
    ).subscribe(event => {
      this.navigationEvent$.next(event);
    });
  }

  observeNavigationEvent(destroy$: Observable<void>): Observable<Event> {
    return this.navigationEvent$.asObservable().pipe(
      takeUntil(destroy$)
    );
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
