import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  ViewChild,
  signal,
  computed,
  effect,
  ViewEncapsulation
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
// Angular Material imports
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatSelectChange } from '@angular/material/select';

import {
  FieldFilter,
  FilterChangeEvent,
  FilterValue,
  OperatorConfig
} from '../models/view/field-filter-view.model';
import { FieldFiltersService } from '../services/field-filters.service';
import { DynamicFilterInputDirective } from '../directives/dynamic-filter-input.directive';
import { FilterInputChangeEvent } from '../models/view/field-filters.model';

@Component({
  selector: 'app-filter-field',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    // Angular Material modules (for checkbox and operator select)
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatCheckboxModule,
    // Dynamic directive
    DynamicFilterInputDirective
  ],
  templateUrl: './filter-field.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class FilterFieldComponent {
  // Input properties
  @Input() filter!: FieldFilter;

  // Output events
  @Output() filterChange = new EventEmitter<FilterChangeEvent>();

  // ViewChild để access dynamic directive
  @ViewChild('dynamicInput', { read: DynamicFilterInputDirective }) dynamicDirective?: DynamicFilterInputDirective;

  // Signals để quản lý state
  readonly isActive = signal(false);
  readonly selectedOperator = signal<string>('');
  readonly filterValue = signal<FilterValue | undefined>(undefined);

  // Computed properties
  readonly availableOperators = computed(() => {
    if (!this.filter?.field) return [];
    return this.fieldFiltersService.getOperatorsForFieldType(this.filter.field.type);
  });

  readonly requiresInput = computed(() => {
    const operator = this.selectedOperator();
    if (!operator || !this.filter?.field) return false;
    return this.fieldFiltersService.operatorRequiresInput(this.filter.field.type, operator);
  });

  readonly inputType = computed(() => {
    const operator = this.selectedOperator();
    if (!operator || !this.filter?.field) return undefined;
    return this.fieldFiltersService.getInputTypeForOperator(this.filter.field.type, operator);
  });

  readonly isCollapsed = computed(() => !this.isActive());



  constructor(private fieldFiltersService: FieldFiltersService) {
    // Effect để sync với service state changes
    effect(() => {
      // Track service state signal để detect changes
      const filtersState = this.fieldFiltersService.filtersState();

      if (this.filter?.field) {
        // Get current filter from service state
        const serviceFilter = filtersState.filters.find(f => f.field._id === this.filter.field._id);

        if (serviceFilter) {
          this.isActive.set(serviceFilter.isActive);

          if (serviceFilter.filterValue) {
            this.selectedOperator.set(serviceFilter.filterValue.operator);
            this.filterValue.set(serviceFilter.filterValue);
          } else {
            this.selectedOperator.set('');
            this.filterValue.set(undefined);

            // Reset dynamic component khi filter bị clear
            setTimeout(() => this.resetDynamicComponent(), 0);
          }
        }
      }
    });

    // Effect để emit changes
    effect(() => {
      const isActive = this.isActive();
      const filterValue = this.filterValue();

      if (this.filter && this.filter.field._id) {
        const event: FilterChangeEvent = {
          fieldId: this.filter.field._id,
          isActive,
          filterValue: isActive ? filterValue : undefined
        };
        this.filterChange.emit(event);
      }
    });
  }

  /**
   * Xử lý khi checkbox thay đổi
   * @param event - Event từ checkbox
   */
  handleCheckboxChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const checked = target.checked;
    this.onCheckboxChange(checked);
  }

  /**
   * Xử lý khi Material checkbox thay đổi
   * @param event - MatCheckboxChange event
   */
  handleMatCheckboxChange(event: MatCheckboxChange): void {
    this.onCheckboxChange(event.checked);
  }

  /**
   * Xử lý khi checkbox thay đổi
   * @param checked - Trạng thái checkbox
   */
  onCheckboxChange(checked: boolean): void {
    this.isActive.set(checked);

    if (checked && !this.selectedOperator()) {
      // Chọn operator đầu tiên nếu chưa có
      const operators = this.availableOperators();
      if (operators.length > 0) {
        this.onOperatorChange(operators[0].value);
      }
    } else if (!checked) {
      // Clear filter value khi uncheck
      this.filterValue.set(undefined);
      this.selectedOperator.set('');
    }
  }

  /**
   * Xử lý khi operator select thay đổi
   * @param event - Event từ select
   */
  handleOperatorChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.onOperatorChange(target.value);
  }

  /**
   * Xử lý khi Material select thay đổi
   * @param event - MatSelectChange event
   */
  handleMatOperatorChange(event: MatSelectChange): void {
    this.onOperatorChange(event.value);
  }

  /**
   * Xử lý khi operator thay đổi
   * @param operator - Operator được chọn
   */
  onOperatorChange(operator: string): void {
    this.selectedOperator.set(operator);

    // Tạo default filter value cho operator mới
    const defaultValue = this.fieldFiltersService.getDefaultFilterValue(
      this.filter.field.type,
      operator
    );
    this.filterValue.set(defaultValue);
  }

  /**
   * Xử lý khi input value thay đổi
   * @param event - Event từ input
   */
  handleValueChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value;

    const filterValue: FilterValue = {
      operator: this.selectedOperator() as any,
      value: value
    } as FilterValue;

    this.onFilterValueChange(filterValue);
  }

  /**
   * Xử lý khi Material input thay đổi
   * @param event - Event từ Material input
   */
  handleMatValueChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value;

    const filterValue: FilterValue = {
      operator: this.selectedOperator() as any,
      value: value
    } as FilterValue;

    this.onFilterValueChange(filterValue);
  }



  /**
   * Xử lý khi filter value thay đổi từ input components
   * @param value - Giá trị mới
   */
  onFilterValueChange(value: FilterValue): void {
    this.filterValue.set(value);
  }

  /**
   * Xử lý khi dynamic filter component thay đổi
   * @param event - FilterInputChangeEvent từ dynamic component
   */
  onDynamicFilterChange(event: FilterInputChangeEvent): void {
    // Convert event to FilterValue format
    const filterValue: FilterValue = {
      operator: event.operator as any,
      value: event.value
    } as FilterValue;

    // Handle special cases for different field types
    if (this.isPicklistField() && Array.isArray(event.value)) {
      (filterValue as any).values = event.value;
    }

    this.onFilterValueChange(filterValue);
  }

  /**
   * Lấy giá trị hiện tại để hiển thị trong input
   */
  getCurrentValue(): string {
    const currentFilter = this.filterValue();
    if (!currentFilter) return '';

    // Type assertion để truy cập value property
    const filterWithValue = currentFilter as any;
    return filterWithValue.value || '';
  }

  /**
   * Lấy filter value hiện tại cho dynamic components
   */
  getCurrentFilterValue(): any {
    const currentFilter = this.filterValue();
    if (!currentFilter) return undefined;

    // Type assertion để truy cập properties
    const filterWithValue = currentFilter as any;

    // Return appropriate value based on field type
    if (this.isPicklistField()) {
      return filterWithValue.values || [];
    }

    return filterWithValue.value;
  }

  /**
   * Lấy placeholder text cho operator select
   */
  getOperatorPlaceholder(): string {
    return 'FIELD_FILTERS.PLACEHOLDERS.SELECT_OPERATOR';
  }

  /**
   * Lấy input type cho Material input
   */
  getInputType(): string {
    const inputType = this.inputType();
    if (!inputType) return 'text';

    // Map input types cho Material
    switch (inputType) {
      case 'number':
        return 'number';
      case 'date':
        return 'date';
      case 'email':
        return 'email';
      case 'tel':
        return 'tel';
      default:
        return 'text';
    }
  }

  /**
   * Kiểm tra xem có nên hiển thị input component không
   */
  shouldShowInput(): boolean {
    return this.isActive() && !!this.selectedOperator() && this.requiresInput();
  }

  /**
   * Kiểm tra xem có phải là picklist field không
   */
  isPicklistField(): boolean {
    return this.filter?.field?.type === 'picklist' || this.filter?.field?.type === 'multi-picklist';
  }

  /**
   * Kiểm tra xem có phải là date field không
   */
  isDateField(): boolean {
    return this.filter?.field?.type === 'date' || this.filter?.field?.type === 'datetime';
  }

  /**
   * Reset dynamic component khi filter bị clear
   */
  private resetDynamicComponent(): void {
    if (this.dynamicDirective) {
      this.dynamicDirective.reset();
    }
  }

}
