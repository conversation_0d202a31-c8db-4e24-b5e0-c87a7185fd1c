import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ChangeDetectionStrategy,
  signal,
  computed,
  ViewEncapsulation
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Field } from '@domain/entities/field.entity';
import { FlashMessageService } from '@core/services/flash_message.service';
import { FieldFiltersService } from './services/field-filters.service';
import { FilterChangeEvent, FieldFilter } from './models/view/field-filter-view.model';
import { FilterFieldComponent } from './filter-field/filter-field.component';

import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { FieldFiltersConfig, FieldFiltersResult, SavedFilter } from '@shared/models/view/field-filters.model';
import { SaveFilterModalService } from './modals/save-filter-modal/save-filter-modal.service';
import { ConfirmModalService } from '@shared/modals/common/confirm-modal/confirm-modal.service';

@Component({
  selector: 'app-field-filters',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FilterFieldComponent,
    MatFormFieldModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    FormsModule,
    MatInputModule
  ],
  templateUrl: './field-filters.component.html',
  styleUrls: ['./field-filters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class FieldFiltersComponent implements OnInit {

  // Input properties
  @Input({ required: true }) config!: FieldFiltersConfig;

  // Output events
  @Output() filterChange = new EventEmitter<FieldFiltersResult>();
  @Output() filtersApplied = new EventEmitter<FieldFiltersResult>();
  @Output() filtersReset = new EventEmitter<void>();

  searchInputValue: string = '';

  // Signals để quản lý state
  readonly isInitialized = signal(false);
  readonly filters = computed(() => this.fieldFiltersService.filtersState().filters);
  readonly activeFilters = computed(() => this.fieldFiltersService.activeFilters());
  readonly hasActiveFilters = computed(() => this.fieldFiltersService.hasActiveFilters());

  // Search functionality signals
  readonly searchInput = computed(() => this.fieldFiltersService.searchInput());
  readonly filteredFilters = computed(() => this.fieldFiltersService.getFilteredFilters());

  // Saved filters signals
  readonly savedFilters = computed(() => this.fieldFiltersService.savedFilters());
  readonly currentSavedFilterId = computed(() => this.fieldFiltersService.currentSavedFilterId());
  readonly hasUnsavedChanges = computed(() => this.fieldFiltersService.hasUnsavedChanges());

  // Validation signals
  readonly hasValidActiveFilters = computed(() => {
    const activeFilters = this.activeFilters();
    return activeFilters.length > 0 && activeFilters.every(filter =>
      this.validateActiveFilter(filter)
    );
  });

  readonly canApplyFilters = computed(() => {
    // Sử dụng hasActiveFilters thay vì hasValidActiveFilters để đơn giản hóa
    // Validation sẽ được thực hiện trong onApplyFilters()
    return this.hasActiveFilters();
  });

  constructor(
    private fieldFiltersService: FieldFiltersService,
    private saveFilterModalService: SaveFilterModalService,
    private confirmModalService: ConfirmModalService,
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {
    // Không còn auto-emit filtersApplied trong effect
    // Chỉ emit khi user click nút "Áp dụng"
  }

  ngOnInit(): void {
    this.initializeFilters();
    this.initializeSavedFilters();
  }

  /**
   * Khởi tạo filters từ danh sách fields
   */
  private initializeFilters(): void {
    if (this.config.fields && this.config.fields.length > 0) {
      this.fieldFiltersService.initializeFilters(this.config.fields);
      this.isInitialized.set(true);
    }
  }

  /**
   * Khởi tạo saved filters từ config
   */
  private initializeSavedFilters(): void {
    if (this.config.savedFilters && this.config.savedFilters.length > 0) {
      this.fieldFiltersService.initializeSavedFilters(this.config.savedFilters);
    }
  }

  /**
   * Xử lý khi filter của một field thay đổi
   * @param event - Filter change event
   */
  onFilterChange(event: FilterChangeEvent): void {
    // Cập nhật service state
    this.fieldFiltersService.updateFilter(
      event.fieldId,
      event.isActive,
      event.filterValue
    );

    // Emit event ra ngoài
    // this.filterChange.emit({
    //   searchQuery: this.searchInput?.trim?.(),
    //   fields: activeFilters.map(f => ({
    //     fieldId: f.field._id,
    //     filterValue: f.filterValue
    //   }))
    // });
  }


  /**
   * Validate một active filter
   * @param filter - FieldFilter để validate
   * @returns true nếu filter valid
   */
  private validateActiveFilter(filter: FieldFilter): boolean {
    if (!filter.isActive || !filter.filterValue) {
      return false;
    }
    return this.fieldFiltersService.validateFilterValue(
      filter.field.type,
      filter.filterValue
    );
  }

  /**
   * Validate tất cả active filters
   * @returns true nếu tất cả active filters đều valid
   */
  private validateAllActiveFilters(): boolean {
    const activeFilters = this.activeFilters();
    if (activeFilters.length === 0) {
      return false;
    }
    return activeFilters.every(filter => this.validateActiveFilter(filter));
  }

  /**
   * Xử lý khi user click nút "Áp dụng"
   */
  onApplyFilters(): void {
    const activeFilters = this.activeFilters();
    // Xóa console.log debug không cần thiết

    // Thông báo cho người dùng khi áp dụng filters
    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.FILTERS.APPLIED')
    );

    this.filtersApplied.emit({
      fields: activeFilters
        .filter(f => f.field._id) // Lọc ra những filter có _id
        .map(f => ({
          fieldId: f.field._id!,
          filterValue: f.filterValue
        }))
    });
  }

  /**
   * Xử lý khi user click nút "Hủy" / "Reset"
   */
  onResetFilters(): void {
    this.fieldFiltersService.clearAllFilters();
    this.filtersReset.emit();
  }

  /**
   * Track function cho ngFor để tối ưu performance
   * @param _index - Index của item (không sử dụng)
   * @param filter - FieldFilter object
   * @returns Unique identifier
   */
  trackByFieldId(_index: number, filter: FieldFilter): string {
    return filter.field._id || 'unknown';
  }

  /**
   * Track function cho saved filters ngFor
   * @param _index - Index của item
   * @param savedFilter - SavedFilter object
   * @returns Unique identifier
   */
  trackBySavedFilterId(_index: number, savedFilter: SavedFilter): string {
    return savedFilter.id;
  }

  // ==================== SEARCH METHODS ====================

  /**
   * Xử lý khi search input thay đổi
   * @param searchTerm - Từ khóa tìm kiếm
   */
  onSearchInputChange(searchTerm: string): void {
    this.searchInputValue = searchTerm;
    this.fieldFiltersService.updateSearchInput(searchTerm);
  }

  /**
   * Xóa search input
   */
  onClearSearch(): void {
    this.searchInputValue = '';
    this.fieldFiltersService.clearSearchInput();
  }

  /**
   * Toggle trạng thái collapse/expand của saved filters
   */
  toggleSavedFiltersExpansion(): void {
    this._isSavedFiltersExpanded.set(!this._isSavedFiltersExpanded());
  }

  /**
   * Kiểm tra xem có search input không
   */
  readonly hasSearchInput = computed(() => {
    return this.searchInput().length > 0;
  });

  /**
   * Signal để quản lý trạng thái collapse/expand của saved filters
   */
  private readonly _isSavedFiltersExpanded = signal(true);

  /**
   * Readonly computed signal cho trạng thái expanded của saved filters
   */
  readonly isSavedFiltersExpanded = this._isSavedFiltersExpanded.asReadonly();

  /**
   * Computed signal cho class của chevron icon
   */
  readonly chevronIconClass = computed(() =>
    this.isSavedFiltersExpanded() ? 'fa-chevron-down' : 'fa-chevron-right'
  );

  // ==================== SAVED FILTERS METHODS ====================

  /**
   * Áp dụng saved filter
   * @param filterId - ID của saved filter
   */
  onApplySavedFilter(filterId: string): void {
    this.fieldFiltersService.applySavedFilter(filterId);
  }

  /**
   * Chỉnh sửa tên saved filter
   * @param event - Click event để stop propagation
   * @param filterId - ID của saved filter
   */
  async onEditSavedFilter(event: Event, filterId: string): Promise<void> {
    event.stopPropagation(); // Ngăn trigger click của parent

    const savedFilter = this.fieldFiltersService.getSavedFilterById(filterId);
    if (!savedFilter) return;

    // Lọc ra các saved filters khác (để kiểm tra trùng tên)
    const otherSavedFilters = this.savedFilters().filter(f => f.id !== filterId);

    const result = await this.saveFilterModalService.open({
      filterName: savedFilter.name,
      filters: savedFilter.filters,
      savedFilters: otherSavedFilters
    });

    if (result) {
      this.fieldFiltersService.updateSavedFilterName(filterId, result.name);
    }
  }

  /**
   * Xóa saved filter với confirmation
   * @param event - Click event để stop propagation
   * @param filterId - ID của saved filter
   */
  async onDeleteSavedFilter(event: Event, filterId: string): Promise<void> {
    event.stopPropagation(); // Ngăn trigger click của parent

    const savedFilter = this.fieldFiltersService.getSavedFilterById(filterId);
    if (!savedFilter) return;

    const confirmed = await this.confirmModalService.confirm({
      title: 'FIELD_FILTERS.DELETE_SAVED_FILTER_TITLE',
      message: 'FIELD_FILTERS.DELETE_SAVED_FILTER_MESSAGE',
      confirmText: 'COMMON.DELETE',
      cancelText: 'COMMON.CANCEL',
      confirmColor: 'warn'
    });

    if (confirmed) {
      this.fieldFiltersService.deleteSavedFilter(filterId);
    }
  }

  /**
   * Lưu filter mới
   */
  async onSaveNewFilter(): Promise<void> {
    const activeFiltersData = this.fieldFiltersService.getActiveFiltersData();
    if (activeFiltersData.length === 0) return;

    const result = await this.saveFilterModalService.openForNewFilter(
      activeFiltersData,
      this.savedFilters()
    );

    if (result) {
      this.fieldFiltersService.saveFilter(result.name, result.filters);
    }
  }

  /**
   * Lưu đè filter hiện tại
   */
  onSaveCurrentFilter(): void {
    const currentFilterId = this.currentSavedFilterId();
    if (!currentFilterId) return;

    this.fieldFiltersService.updateSavedFilter(currentFilterId);
  }

  /**
   * Lưu filter hiện tại với tên mới (Save As New)
   */
  async onSaveAsNewFilter(): Promise<void> {
    const currentFilterId = this.currentSavedFilterId();
    if (!currentFilterId) return;

    const currentFilter = this.fieldFiltersService.getSavedFilterById(currentFilterId);
    if (!currentFilter) return;

    const activeFiltersData = this.fieldFiltersService.getActiveFiltersData();

    const result = await this.saveFilterModalService.openForSaveAsNew(
      currentFilter.name,
      activeFiltersData,
      this.savedFilters()
    );

    if (result) {
      this.fieldFiltersService.saveFilter(result.name, result.filters);
    }
  }

  /**
   * Kiểm tra xem có thể hiển thị Save Filter button không
   */
  readonly canShowSaveFilter = computed(() => {
    return this.hasActiveFilters();
  });

  /**
   * Kiểm tra xem có thể hiển thị Save menu không (khi có unsaved changes)
   */
  readonly canShowSaveMenu = computed(() => {
    return this.currentSavedFilterId() !== null && this.hasUnsavedChanges();
  });
}
