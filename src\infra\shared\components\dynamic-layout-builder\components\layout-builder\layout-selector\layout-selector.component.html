<div class="layout-selector-container">
    <!-- Layout Selector Menu -->
    <div class="layout-selector-wrapper">
        <label class="form-label">
      {{ 'DYNAMIC_LAYOUT_BUILDER.LAYOUT_SELECTOR.LABEL' | translate }}
    </label>

        <div class="layout-menu-container">
            <!-- <PERSON>u Trigger Button -->
            <button mat-button class="layout-trigger-button" [matMenuTriggerFor]="layoutMenu" [disabled]="isLoading()">
        <span class="layout-name">
          @if (selectedLayout(); as layout) {
            {{ layout.name }}
            @if (layout.isDefault) {
              <span class="default-badge">({{ 'DYNAMIC_LAYOUT_BUILDER.LAYOUT_SELECTOR.DEFAULT' | translate }})</span>
            }
          } @else {
            {{ 'DYNAMIC_LAYOUT_BUILDER.LAYOUT_SELECTOR.PLACEHOLDER' | translate }}
          }
        </span>

        <!-- Loading Spinner or Dropdown Icon -->
        @if (isLoading()) {
          <mat-icon class="loading-icon">
            <i class="fas fa-spinner fa-spin"></i>
          </mat-icon>
        } @else {
          <mat-icon>arrow_drop_down</mat-icon>
        }
      </button>

            <!-- Material Menu -->
            <mat-menu #layoutMenu="matMenu" class="layout-menu">
                <!-- Layout Options -->
                @for (layout of availableLayouts(); track layout._id) {
                <button mat-menu-item (click)="onLayoutSelected(layout._id!)" [class.selected]="layout._id === selectedLayoutId()">
            <mat-icon>
              @if (layout._id === selectedLayoutId()) {
                check
              } @else {
                dashboard
              }
            </mat-icon>
            <span>
              {{ layout.name }}
              @if (layout.isDefault) {
                <span class="default-badge">({{ 'DYNAMIC_LAYOUT_BUILDER.LAYOUT_SELECTOR.DEFAULT' | translate }})</span>
              }
            </span>
          </button> }

                <!-- Divider -->
                <mat-divider></mat-divider>

                <!-- Create New Layout Option -->
                <button mat-menu-item (click)="onCreateNewLayout()" class="create-new-item">
          <mat-icon class="create-icon">add</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.LAYOUT_SELECTOR.CREATE_NEW' | translate }}</span>
        </button>
            </mat-menu>
        </div>

        <!-- Layout Info -->
        @if (selectedLayout(); as layout) {
        <div class="layout-info mt-2">
            <small class="text-muted">
          <mat-icon class="info-icon">info</mat-icon>
          @if (layout.shortDescription) {
            {{ layout.shortDescription }}
          }
          <span class="ms-2">
            <mat-icon class="clock-icon">schedule</mat-icon>
            {{ 'DYNAMIC_LAYOUT_BUILDER.LAYOUT_SELECTOR.UPDATED' | translate }}:
            {{ formatDate(layout.updatedAt) }}
          </span>
        </small>
        </div>
        }
    </div>
</div>