<div class="user-menu-block menu-default light:border-gray-300 w-screen d-flex align-items-stretch flex-column">
  <div class="flex items-center justify-between px-3 py-1.5 gap-1.5">
   <div class="flex items-center gap-2">
    <img alt="" class="size-9 rounded-full border-2 border-success" src="/assets/images/mock/300-2.png">
     <div class="flex flex-col gap-1.5">
      <span class="text-sm text-gray-800 font-semibold leading-none">
       <PERSON>
      </span>
      <a class="text-xs text-gray-600 hover:text-primary font-medium leading-none" href="/metronic/tailwind/demo1/account/home/<USER>">
       c.fishergmail.com
      </a>
     </div>

   </div>
   <span class="badge badge-xs badge-primary badge-outline">
    Pro
   </span>
  </div>
  <div class="menu-separator">
  </div>
  <div class="flex flex-col">
   <div class="menu-item">
    <a class="menu-link" href="/metronic/tailwind/demo1/public-profile/profiles/default">
     <span class="menu-icon">
      <i class="ki-filled ki-badge">
      </i>
     </span>
     <span class="menu-title">
      Public Profile
     </span>
    </a>
   </div>
   <div class="menu-item">
    <a class="menu-link" href="/metronic/tailwind/demo1/account/home/<USER>">
     <span class="menu-icon">
      <i class="ki-filled ki-profile-circle">
      </i>
     </span>
     <span class="menu-title">
      My Profile
     </span>
    </a>
   </div>
   <div class="menu-item menu-item-dropdown" data-menu-item-offset="-50px, 0" data-menu-item-placement="left-start" data-menu-item-placement-rtl="right-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:hover">
    <div class="menu-link">
     <span class="menu-icon">
      <i class="ki-filled ki-setting-2">
      </i>
     </span>
     <span class="menu-title">
      My Account
     </span>
     <span class="menu-arrow">
      <i class="ki-filled ki-right text-3xs rtl:transform rtl:rotate-180">
      </i>
     </span>
    </div>
   </div>
   <div class="menu-item">
    <a class="menu-link" href="https://devs.keenthemes.com">
     <span class="menu-icon">
      <i class="ki-filled ki-message-programming">
      </i>
     </span>
     <span class="menu-title">
      Dev Forum
     </span>
    </a>
   </div>
   <cdk-accordion class="example-accordion">
    <cdk-accordion-item #accordionItem="cdkAccordionItem" class="example-accordion-item">
      <div class="menu-item" data-menu-item-offset="-10px, 0" data-menu-item-placement="left-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:hover">
        <div class="menu-link">
        <span class="menu-icon">
          <i class="ki-filled ki-icon">
          </i>
        </span>
        <span class="menu-title">
          Language
        </span>
        <div class="flex items-center gap-1.5 rounded-md border border-gray-300 text-gray-600 p-1.5 text-2xs font-medium shrink-0">
          <button
            mat-icon-button
            aria-label="Example icon-button with a menu"
            (click)="accordionItem.toggle()"
            >
            Tiếng việt
          </button>
        </div>
        </div>
      </div>

      @if(accordionItem.expanded) {
      <div>
        <div><button (click)="switchLanguage('vi')" [class.active]="currentLang === 'vi'">Tiếng Việt</button></div>
        <div><button (click)="switchLanguage('en')" [class.active]="currentLang === 'en'">English</button></div>
      </div>
      }
    </cdk-accordion-item>
  </cdk-accordion>
  </div>
  <div class="menu-separator">
  </div>
  <div class="flex flex-col">
   <div class="menu-item px-4 py-1.5">
    <a class="btn btn-sm btn-light justify-center" href="/metronic/tailwind/demo1/authentication/classic/sign-in">
     Log out
    </a>
   </div>
  </div>
 </div>
