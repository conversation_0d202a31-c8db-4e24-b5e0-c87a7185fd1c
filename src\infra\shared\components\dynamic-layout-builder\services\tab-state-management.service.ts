import { BehaviorSubject, Observable } from 'rxjs';

import { AnyTabState } from '../models/dynamic-layout-builder.model';

/**
 * TabStateManagementService
 *
 * ✅ REFACTORED: Non-injectable service for tab state management
 * ✅ CHANGED: Removed @Injectable() decorator - instantiate with 'new TabStateManagementService()'
 * ✅ EXTRACTED FROM DynamicLayoutBuilderService: Dedicated service for tab state management
 *
 * Responsibilities:
 * - Tab state persistence to localStorage
 * - Tab state reactive management với BehaviorSubjects
 * - Tab state initialization và cleanup
 * - Tab state CRUD operations
 *
 * Architecture:
 * - Non-injectable service với instance riêng cho mỗi component
 * - Centralized tab state management
 * - Reactive state với Observables
 * - Persistent storage với localStorage
 * - Error handling và logging
 *
 * Usage:
 * - Instantiate trong component: `private tabStateService = new TabStateManagementService();`
 * - Mỗi component có instance riêng để tránh shared state conflicts
 */
export class TabStateManagementService {

  // ==================== STORAGE CONFIGURATION ====================

  /**
   * localStorage key for tab states
   * ✅ MOVED FROM DynamicLayoutBuilderService: Centralized tab state storage key
   */
  private readonly TAB_STATE_KEY = 'dynamic-layout-builder-tab-states';

  // ==================== REACTIVE STATE MANAGEMENT ====================

  /**
   * BehaviorSubject for tab states
   * ✅ MOVED FROM DynamicLayoutBuilderService: Centralized tab state management
   */
  private tabStatesSubject = new BehaviorSubject<{ [key: string]: AnyTabState }>({});

  /**
   * Observable for tab states
   * ✅ MOVED FROM DynamicLayoutBuilderService: Reactive tab state access
   */
  tabStates$ = this.tabStatesSubject.asObservable();

  // ==================== CONSTRUCTOR ====================

  constructor() {
    // console.log('🚀 TabStateManagementService: Service initialized');
    
    // Auto-initialize tab states from localStorage
    this.initializeTabStates();
  }

  // ==================== TAB STATE CRUD OPERATIONS ====================

  /**
   * Save tab state to localStorage and BehaviorSubject
   * ✅ MOVED FROM DynamicLayoutBuilderService: Enhanced với better error handling
   */
  saveTabState(tabName: string, state: AnyTabState): void {
    try {
      const currentStates = this.tabStatesSubject.value;
      const updatedStates = {
        ...currentStates,
        [tabName]: {
          ...state,
          lastUpdated: new Date().toISOString()
        }
      };

      // Update BehaviorSubject
      this.tabStatesSubject.next(updatedStates);

      // Save to localStorage
      localStorage.setItem(this.TAB_STATE_KEY, JSON.stringify(updatedStates));

      // console.log(`✅ TabStateManagementService: Tab state saved for ${tabName}:`, state);
    } catch (error) {
      console.error(`❌ TabStateManagementService: Error saving tab state for ${tabName}:`, error);
    }
  }

  /**
   * Load tab state from localStorage
   * ✅ MOVED FROM DynamicLayoutBuilderService: Enhanced với better error handling
   */
  loadTabState(tabName: string): AnyTabState | null {
    try {
      const savedStates = localStorage.getItem(this.TAB_STATE_KEY);
      if (savedStates) {
        const states = JSON.parse(savedStates);
        const tabState = states[tabName];
        // console.log(`📋 TabStateManagementService: Tab state loaded for ${tabName}:`, tabState);
        return tabState || null;
      }
    } catch (error) {
      console.error(`❌ TabStateManagementService: Error loading tab state for ${tabName}:`, error);
    }
    return null;
  }

  /**
   * Get current tab state from BehaviorSubject
   * ✅ MOVED FROM DynamicLayoutBuilderService: Real-time tab state access
   */
  getTabState(tabName: string): AnyTabState | null {
    const currentStates = this.tabStatesSubject.value;
    const tabState = currentStates[tabName] || null;
    console.log(`🔍 TabStateManagementService: Getting current tab state for ${tabName}:`, tabState);
    return tabState;
  }

  /**
   * Clear tab state
   * ✅ MOVED FROM DynamicLayoutBuilderService: Enhanced với better error handling
   */
  clearTabState(tabName: string): void {
    try {
      const currentStates = this.tabStatesSubject.value;
      const updatedStates = { ...currentStates };
      delete updatedStates[tabName];

      this.tabStatesSubject.next(updatedStates);
      localStorage.setItem(this.TAB_STATE_KEY, JSON.stringify(updatedStates));

      console.log(`🗑️ TabStateManagementService: Tab state cleared for ${tabName}`);
    } catch (error) {
      console.error(`❌ TabStateManagementService: Error clearing tab state for ${tabName}:`, error);
    }
  }

  /**
   * Initialize tab states from localStorage
   * ✅ MOVED FROM DynamicLayoutBuilderService: Enhanced với better error handling
   */
  initializeTabStates(): void {
    try {
      const savedStates = localStorage.getItem(this.TAB_STATE_KEY);
      if (savedStates) {
        const states = JSON.parse(savedStates);
        this.tabStatesSubject.next(states);
        // console.log('🚀 TabStateManagementService: Tab states initialized:', states);
      } else {
        console.log('📝 TabStateManagementService: No saved tab states found, starting with empty state');
      }
    } catch (error) {
      console.error('❌ TabStateManagementService: Error initializing tab states:', error);
      // Reset to empty state on error
      this.tabStatesSubject.next({});
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get all current tab states
   * ✅ NEW: Utility method for debugging và monitoring
   */
  getAllTabStates(): { [key: string]: AnyTabState } {
    const currentStates = this.tabStatesSubject.value;
    console.log('📊 TabStateManagementService: Getting all tab states:', currentStates);
    return currentStates;
  }

  /**
   * Clear all tab states
   * ✅ NEW: Utility method for cleanup operations
   */
  clearAllTabStates(): void {
    try {
      this.tabStatesSubject.next({});
      localStorage.removeItem(this.TAB_STATE_KEY);
      console.log('🗑️ TabStateManagementService: All tab states cleared');
    } catch (error) {
      console.error('❌ TabStateManagementService: Error clearing all tab states:', error);
    }
  }

  /**
   * Check if tab state exists
   * ✅ NEW: Utility method for conditional operations
   */
  hasTabState(tabName: string): boolean {
    const currentStates = this.tabStatesSubject.value;
    const exists = !!currentStates[tabName];
    console.log(`🔍 TabStateManagementService: Tab state exists for ${tabName}:`, exists);
    return exists;
  }

  /**
   * Get tab state count
   * ✅ NEW: Utility method for monitoring
   */
  getTabStateCount(): number {
    const currentStates = this.tabStatesSubject.value;
    const count = Object.keys(currentStates).length;
    console.log('📊 TabStateManagementService: Total tab states count:', count);
    return count;
  }

  // ==================== CLEANUP METHODS ====================

  /**
   * Cleanup service khi component bị destroy
   * ✅ NEW: Memory management để tránh memory leaks
   */
  public destroy(): void {
    // console.log('🧹 TabStateManagementService: Cleanup service');

    // Complete BehaviorSubject để tránh memory leaks
    this.tabStatesSubject.complete();
  }
}
