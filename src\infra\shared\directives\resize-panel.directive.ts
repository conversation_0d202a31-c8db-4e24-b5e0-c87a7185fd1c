import { Directive, ElementRef, Input, HostListener, OnInit, OnDestroy, inject, Injectable, Renderer2 } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

// Dịch vụ lưu trữ localStorage
@Injectable({ providedIn: 'root' })
export class StorageService {
  setItem(key: string, value: string): void {
    localStorage.setItem(key, value);
  }

  getItem(key: string): string | null {
    return localStorage.getItem(key);
  }
}

@Directive({
  selector: '[appResizePanel]',
  standalone: true
})
export class ResizePanelDirective implements OnInit, OnDestroy {
  private el = inject(ElementRef);
  private translateService = inject(TranslateService);
  private storageService = inject(StorageService);
  private renderer = inject(Renderer2);

  // Inputs
  @Input() leftPanel!: ElementRef<HTMLElement> | HTMLElement;
  @Input() rightPanel!: ElementRef<HTMLElement> | HTMLElement;
  @Input() showLeftPanel: boolean = true;
  /**
   * tắt transition khi init
   */
  @Input() disableTransitionOnInit: boolean = true;
  @Input() minWidth: number = 200;
  @Input() maxWidth: number = 600;
  @Input() additionalMarginLeft: number = 0;
  @Input() isAbsolute: boolean = false;
  @Input() panelName: string = ''; // Thêm input panelName để định danh panel

  /**
   * Vị trí top của drag button (tính bằng pixel từ trên xuống)
   * Chỉ sử dụng một trong hai: dragButtonTop hoặc dragButtonBottom
   * Ví dụ: '50px', '100px'
   */
  @Input() dragButtonTop?: string;

  /**
   * Vị trí bottom của drag button (tính bằng pixel từ dưới lên)
   * Chỉ sử dụng một trong hai: dragButtonTop hoặc dragButtonBottom
   * Ví dụ: '50px', '100px'
   */
  @Input() dragButtonBottom?: string;

  private panelWidthSubject = new BehaviorSubject<number>(this.minWidth);
  panelWidth$: Observable<number> = this.panelWidthSubject.asObservable();

  // Memory management
  private subscriptions = new Subscription();

  private get storageKey(): string {
    return this.panelName ? `panel_width_${this.panelName}` : 'panel_width'; // Tạo key duy nhất dựa trên panelName
  }

  // Trạng thái kéo thả
  private isCurrentlyDragging = false;
  private isPanelHidden = false; // Theo dõi trạng thái ẩn/hiện của panel
  private originalPanelStyles: { marginLeft: string; width: string } | null = null; // Lưu trữ styles gốc
  private isAnimationInProgress = false; // Theo dõi trạng thái animation
  private animationDurationMs = 200; // Thời gian animation tính bằng milliseconds
  private dragButtonElement: HTMLElement | null = null; // Tham chiếu đến drag button element

  ngOnInit(): void {
    // Thêm class CSS cho resize handle
    this.el.nativeElement.classList.add('panel-resize-handle');

    // Tạo drag button với biểu tượng Font Awesome
    this.createDragButton();

    if(this.showLeftPanel) {
      this.show(true);
      this.setPanelWidth(this.getLeftPanelWidth());
    } else {
      this.hide(true);
      this.setPanelWidth(0);
    }

    // Thêm tooltip dịch từ i18n cho resize handle
    this.subscriptions.add(
      this.translateService.get('RESIZE_PANEL.TOOLTIP').subscribe(tooltip => {
        this.el.nativeElement.setAttribute('title', tooltip);
      })
    );

    // Thêm tooltip cho drag button
    this.subscriptions.add(
      this.translateService.get('RESIZE_PANEL.DRAG_BUTTON_TOOLTIP').subscribe(tooltip => {
        if (this.dragButtonElement) {
          this.dragButtonElement.setAttribute('title', tooltip);
        }
      })
    );
  }

  /**
   * Tạo drag button với biểu tượng Font Awesome
   */
  private createDragButton(): void {
    // Tạo button element
    this.dragButtonElement = this.renderer.createElement('button');
    this.renderer.addClass(this.dragButtonElement, 'resize-drag-button');
    this.renderer.setAttribute(this.dragButtonElement, 'type', 'button');
    this.renderer.setAttribute(this.dragButtonElement, 'aria-label', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.TOOLTIPS.DRAG_TO_RESIZE_PANEL'));

    // Tạo icon element
    const iconElement = this.renderer.createElement('i');
    this.renderer.addClass(iconElement, 'fa-solid');
    this.renderer.addClass(iconElement, 'fa-grip-lines-vertical');

    // Thêm icon vào button
    this.renderer.appendChild(this.dragButtonElement, iconElement);

    // Đặt vị trí button dựa trên input
    this.setDragButtonPosition();

    // Thêm button vào resize handle
    this.renderer.appendChild(this.el.nativeElement, this.dragButtonElement);

    // Thêm event listeners cho button
    this.addDragButtonEventListeners();
  }

  /**
   * Đặt vị trí cho drag button dựa trên dragButtonTop hoặc dragButtonBottom
   */
  private setDragButtonPosition(): void {
    if (!this.dragButtonElement) return;

    // Kiểm tra và validate input - chỉ cho phép một trong hai
    if (this.dragButtonTop && this.dragButtonBottom) {
      console.warn('ResizePanelDirective: Chỉ nên sử dụng một trong hai: dragButtonTop hoặc dragButtonBottom. Sử dụng dragButtonTop.');
    }

    if (this.dragButtonTop) {
      // Đặt vị trí từ trên xuống
      this.renderer.setStyle(this.dragButtonElement, 'top', this.dragButtonTop);
      this.renderer.removeStyle(this.dragButtonElement, 'bottom');
    } else if (this.dragButtonBottom) {
      // Đặt vị trí từ dưới lên
      this.renderer.setStyle(this.dragButtonElement, 'bottom', this.dragButtonBottom);
      this.renderer.removeStyle(this.dragButtonElement, 'top');
    } else {
      // Mặc định ở giữa (50px từ trên)
      this.renderer.setStyle(this.dragButtonElement, 'top', '50px');
      this.renderer.removeStyle(this.dragButtonElement, 'bottom');
    }
  }

  /**
   * Thêm event listeners cho drag button
   */
  private addDragButtonEventListeners(): void {
    if (!this.dragButtonElement) return;

    // Mouse events cho drag button
    this.renderer.listen(this.dragButtonElement, 'mousedown', (event: MouseEvent) => {
      this.onMouseDown(event);
    });

    // Hover events để đồng bộ với resize handle
    this.renderer.listen(this.dragButtonElement, 'mouseenter', () => {
      this.el.nativeElement.classList.add('hover-active');
    });

    this.renderer.listen(this.dragButtonElement, 'mouseleave', () => {
      this.el.nativeElement.classList.remove('hover-active');
    });

    // Focus events để đồng bộ với resize handle
    this.renderer.listen(this.dragButtonElement, 'focus', () => {
      this.el.nativeElement.classList.add('focus-active');
    });

    this.renderer.listen(this.dragButtonElement, 'blur', () => {
      this.el.nativeElement.classList.remove('focus-active');
    });
  }

  /**
   * Bật/tắt trạng thái của drag button
   */
  private setDragButtonState(enabled: boolean): void {
    if (!this.dragButtonElement) return;

    if (enabled) {
      this.renderer.removeAttribute(this.dragButtonElement, 'disabled');
      this.renderer.setStyle(this.dragButtonElement, 'pointer-events', 'auto');
    } else {
      this.renderer.setAttribute(this.dragButtonElement, 'disabled', 'true');
      this.renderer.setStyle(this.dragButtonElement, 'pointer-events', 'none');
    }
  }

  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent): void {
    this.isCurrentlyDragging = true;
    // Disable transitions during drag for smooth performance
    this.disableTransitions();
    event.preventDefault();
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (!this.isCurrentlyDragging) return;

    const newWidth = event.clientX;
    if (newWidth >= this.minWidth && newWidth <= this.maxWidth) {
      this.setPanelWidth(newWidth);
    }
  }

  @HostListener('document:mouseup')
  onMouseUp(): void {
    if (this.isCurrentlyDragging) {
      this.isCurrentlyDragging = false;
      // Re-enable transitions after drag
      this.enableTransitions();
    }
  }

  getLeftPanelWidth(): number {
    const savedWidth = this.storageService.getItem(this.storageKey);
    const currentWidth = savedWidth ? parseInt(savedWidth, 10) : this.minWidth;
    return (currentWidth && currentWidth > this.minWidth) ? currentWidth : this.minWidth;
  }

  private setPanelWidth(leftPanelWidth: number): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    // Cập nhật style dựa trên isAbsolute
    if (this.isAbsolute) {
      // Bố cục absolute
      leftPanelEl.style.width = `${leftPanelWidth}px`;
      leftPanelEl.style.position = 'absolute';
      leftPanelEl.style.top = '0';
      leftPanelEl.style.left = '0';
      rightPanelEl.style.marginLeft = `${leftPanelWidth + this.additionalMarginLeft}px`;
      rightPanelEl.style.width = `calc(100% - ${leftPanelWidth + this.additionalMarginLeft}px)`;
    } else {
      // Bố cục flex
      leftPanelEl.style.width = `${leftPanelWidth}px`;
      leftPanelEl.style.position = ''; // Xóa position nếu có
      rightPanelEl.style.marginLeft = ''; // Xóa margin-left
      rightPanelEl.style.width = ''; // Xóa width, để flex-grow xử lý
    }

    // Lưu chiều rộng vào localStorage và cập nhật BehaviorSubject
    this.panelWidthSubject.next(leftPanelWidth);
    this.storageService.setItem(this.storageKey, leftPanelWidth.toString());
  }

  /**
   * Hide panel với CSS animation
   * Method này được gọi từ component khi cần ẩn panel
   */
  hide(isInit = false): Promise<void> {
    if (this.isPanelHidden || this.isAnimationInProgress) return Promise.resolve(); // Đã ẩn rồi hoặc đang animate

    return new Promise((resolve) => {
      this.isAnimationInProgress = true;

      // Disable drag button khi đang animate
      this.setDragButtonState(false);

      const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
      const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

      // Ensure transitions are enabled for synchronized animation
      this.enableTransitions();

      // Lưu styles hiện tại trước khi reset
      this.originalPanelStyles = {
        marginLeft: rightPanelEl.style.marginLeft || '',
        width: rightPanelEl.style.width || ''
      };

      // Start synchronized animation for both panels using requestAnimationFrame
      requestAnimationFrame(() => {
        // Animate panel collapse and adjacent panel expansion simultaneously
        leftPanelEl.style.width = '0px';
        leftPanelEl.style.opacity = '0';
        rightPanelEl.style.marginLeft = '0px';
        rightPanelEl.style.width = '100%';

        // Ẩn drag button khi panel collapse
        if (this.dragButtonElement) {
          this.renderer.setStyle(this.dragButtonElement, 'opacity', '0');
          this.renderer.setStyle(this.dragButtonElement, 'pointer-events', 'none');
        }

        // Update internal state
        this.panelWidthSubject.next(0);
      });

      // Wait for animation to complete
      setTimeout(() => {
        this.isPanelHidden = true;
        this.isAnimationInProgress = false;
        resolve();
      }, this.animationDurationMs);
    });
  }



  /**
   * Show panel với CSS animation
   * Method này được gọi từ component khi cần hiện panel
   */
  async show(isInit = false): Promise<void> {
    if (this.isAnimationInProgress) return; // Đang animate

    return new Promise((resolve) => {
      this.isAnimationInProgress = true;

      // Disable drag button khi đang animate
      this.setDragButtonState(false);

      const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
      const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

      // Restore lại panel width từ localStorage hoặc minWidth
      const leftPanelWidth = this.getLeftPanelWidth();

      if(!isInit || !this.disableTransitionOnInit) {
        // Ensure transitions are enabled for synchronized animation
        this.enableTransitions();

        // Set initial collapsed state for both panels simultaneously
        leftPanelEl.style.width = '0px';
        leftPanelEl.style.opacity = '0';
        rightPanelEl.style.marginLeft = '0px';
        rightPanelEl.style.width = '100%';
      } else {
        leftPanelEl.style.opacity = '0';
        rightPanelEl.style.opacity = '0';
      }

      // Force reflow để đảm bảo initial state được apply
      leftPanelEl.offsetHeight;

      // Start synchronized animation for both panels
      requestAnimationFrame(() => {
        this.setPanelWidth(leftPanelWidth);

        leftPanelEl.style.opacity = '1';
        rightPanelEl.style.opacity = '1';

        // Hiện drag button khi panel expand
        if (this.dragButtonElement) {
          this.renderer.setStyle(this.dragButtonElement, 'opacity', '1');
          this.renderer.setStyle(this.dragButtonElement, 'pointer-events', 'auto');
        }

        // Update internal state
        this.panelWidthSubject.next(leftPanelWidth);
      });

      // Wait for animation to complete
      setTimeout(() => {
        this.isPanelHidden = false;
        this.isAnimationInProgress = false;
        this.originalPanelStyles = null; // Clear saved styles

        // Enable drag button sau khi animation hoàn thành
        this.setDragButtonState(true);
        resolve();
      }, this.animationDurationMs);
    });
  }

  /**
   * Disable transitions temporarily (useful for instant updates during resize)
   */
  private disableTransitions(): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    leftPanelEl.style.transition = 'none';
    rightPanelEl.style.transition = 'none';
  }

  /**
   * Re-enable transitions
   */
  private enableTransitions(): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    const transitionStyle = `width ${this.animationDurationMs}ms ease-out, margin-left ${this.animationDurationMs}ms ease-out, opacity ${this.animationDurationMs}ms ease-out`;
    leftPanelEl.style.transition = transitionStyle;
    rightPanelEl.style.transition = transitionStyle;
  }

  /**
   * Public method để cập nhật vị trí drag button từ bên ngoài
   * Hữu ích khi cần thay đổi vị trí button động
   */
  public updateDragButtonPosition(): void {
    this.setDragButtonPosition();
  }

  ngOnDestroy(): void {
    // Unsubscribe tất cả subscriptions để prevent memory leaks
    this.subscriptions.unsubscribe();

    // Complete BehaviorSubject
    this.panelWidthSubject.complete();

    // Cleanup drag button
    if (this.dragButtonElement) {
      this.renderer.removeChild(this.el.nativeElement, this.dragButtonElement);
      this.dragButtonElement = null;
    }
  }
}
