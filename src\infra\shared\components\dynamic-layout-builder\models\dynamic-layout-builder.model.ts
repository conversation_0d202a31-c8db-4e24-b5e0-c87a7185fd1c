import { DetailViewConfig, Section, WidgetConfig } from "./dynamic-layout-config.dto";
import { Field, FieldValue } from '@domain/entities/field.entity';
import { DynamicLayoutConfig, DynamicLayoutBuilderConfig } from "./dynamic-layout-config.model";
import { MatTabChangeEvent } from '@angular/material/tabs';


export interface Template {
  name: string; // Tên template (ví dụ: "Thời trang")
  sections: Section[]; // Danh sách sections của template
}

// Drag and drop data interface
export interface DragData {
  type: 'field-type' | 'field' | 'section';
  fieldType?: Field;
  field?: Field;
  section?: Section;
  sourceIndex?: number;
  sourceSection?: string;
}

// Preview data interface cho preview panel
export interface PreviewData {
  [fieldId: string]: FieldValue;
}

// API response interface cho submit layout
export interface SubmitLayoutResponse {
  success: boolean;
  id: string;
  message?: string;
  errors?: string[];
}



// Track by function return type
export type TrackByFunction<T> = (index: number, item: T) => string | number;

// ==================== TYPE SAFETY INTERFACES ====================


/**
 * Interface cho tab component methods - thay thế [key: string]: any
 * ✅ REFACTORED: Tất cả methods đều optional để tương thích với implementation
 */
export interface TabComponentMethods {
  saveTabState?(): void;
  loadTabState?(): void;
  refreshData?(): void;
  ngOnDestroy?(): void;
  cleanupSortableInstances?(): void;
  // triggerAutoSave có thể private trong implementation, nên không bắt buộc public
  resetToDefaults?(): void;
}

/**
 * Interface cho tab component references trong ViewChild
 * ✅ REFACTORED: Loại bỏ [key: string]: any, sử dụng specific methods
 */
export interface TabComponentReference extends TabComponentMethods {
  // Không cần [key: string]: any nữa - chỉ sử dụng specific methods
}

/**
 * Interface cho tab change event từ Angular Material
 */
export interface TabChangeEvent {
  index: number;
  tab: {
    textLabel: string;
    position: number | null;
  };
}



/**
 * Base interface cho tab state metadata
 */
export interface TabStateMetadata {
  lastUpdated?: string;
  timestamp?: number;
  version?: number;
  layoutId?: string;
}

/**
 * Interface cho tab state được lưu trong localStorage
 * ✅ REFACTORED: Loại bỏ [key: string]: any, sử dụng specific properties
 */
export interface TabState extends TabStateMetadata {
  // Specific properties sẽ được extend trong các interface con
}

/**
 * Interface cho Quick Create tab state
 */
export interface QuickCreateTabState extends TabState {
  quickCreateSection: Section;
  fieldCount: number;
  layoutId?: string;
  timestamp?: number;
}

/**
 * Interface cho Detail View tab state
 */
export interface DetailViewTabState extends TabState {
  detailViewConfig: DetailViewConfig;
  sectionCount: number;
  widgetCount: number;
}

/**
 * Interface cho Create tab state (nếu có)
 */
export interface CreateTabState extends TabState {
  sections: Section[];
  sectionCount: number;
  fieldCount: number;
}

/**
 * Union type cho tất cả tab states - sử dụng specific interfaces thay vì any
 * ✅ REFACTORED: Loại bỏ [key: string]: any, sử dụng union của specific states
 */
export type AnyTabState = CreateTabState | QuickCreateTabState | DetailViewTabState | TabState;

/**
 * Interface cho widget reorder event data
 * UPDATED: Sử dụng WidgetConfig từ DTO
 */
export interface WidgetReorderEventData {
  sectionId: string;
  widgets: WidgetConfig[];
  oldIndex?: number;
  newIndex?: number;
}

/**
 * Interface cho field default value (thay thế any trong QuickCreateField)
 */
export type FieldDefaultValue = string | number | boolean | string[] | null | undefined;

/**
 * Interface cho section với fields - sử dụng trong reduce operations
 * ✅ REFACTORED: Thay thế any trong reduce callbacks
 */
export interface SectionWithFields {
  _id: string;
  id?: string;
  title: string;
  fields: Field[];
}

/**
 * Interface cho section với widgets - sử dụng trong detail view reduce operations
 */
export interface SectionWithWidgets {
  _id: string;
  name: string;
  widgets: import('./dynamic-layout-config.dto').WidgetConfig[];
}

/**
 * Interface cho auto-save configuration
 */
export interface AutoSaveConfig {
  enabled: boolean;
  interval: number; // milliseconds
  maxRetries: number;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

/**
 * Interface cho field usage map - sử dụng trong quick create
 */
export interface FieldUsageMap {
  [fieldId: string]: boolean;
}

/**
 * Cấu hình chính cho DynamicLayoutBuilderComponent
 * Bao gồm tất cả các thuộc tính cần thiết để khởi tạo và cấu hình component
 */
/**
 * Cấu hình cho multi-layout management
 * UPDATED: Sử dụng DynamicLayoutConfig mới từ dynamic-layout-config.model.ts
 * REMOVED: LayoutMetadata interface - sử dụng DynamicLayoutConfig thay thế
 */
export interface MultiLayoutConfig {
  /** Map các layout theo ID */
  layouts: { [key: string]: DynamicLayoutConfig };
  /** Metadata của các layout - sử dụng DynamicLayoutConfig thay vì LayoutMetadata */
  metadata: { [key: string]: DynamicLayoutConfig };
  /** ID của layout hiện tại */
  currentLayoutId: string;
  /** Có enable multi-layout mode không */
  multiLayoutMode: boolean;
}

// REMOVED: DynamicLayoutConfig interface đã được move sang dynamic-layout-config.model.ts
// Sử dụng interface mới từ dynamic-layout-config.model.ts

// REMOVED: DynamicLayoutBuilderConfig interface đã được move sang dynamic-layout-config.model.ts
// Sử dụng interface mới từ dynamic-layout-config.model.ts

// ==================== CENTRALIZED STATE MANAGEMENT INTERFACES ====================

/**
 * Enum cho các loại event state change
 */
export enum StateChangeEventType {
  // Layout events
  LAYOUT_SWITCHED = 'layout_switched',
  LAYOUT_CREATED = 'layout_created',
  LAYOUT_DELETED = 'layout_deleted',

  // Create tab events
  SECTION_ADDED = 'section_added',
  SECTION_DELETED = 'section_deleted',
  SECTION_REORDERED = 'section_reordered',
  SECTION_TITLE_CHANGED = 'section_title_changed',
  FIELD_ADDED = 'field_added',
  FIELD_DELETED = 'field_deleted',
  FIELD_REORDERED = 'field_reordered',
  FIELD_REQUIRED_TOGGLED = 'field_required_toggled',
  FIELD_PROPERTIES_EDITED = 'field_properties_edited',

  // Quick Create events
  QC_FIELD_ADDED = 'qc_field_added',
  QC_FIELD_DELETED = 'qc_field_deleted',
  QC_FIELD_REORDERED = 'qc_field_reordered',
  QC_SECTION_UPDATED = 'qc_section_updated',
  QC_AUTO_SAVE = 'qc_auto_save',

  // Detail View events
  DV_WIDGET_REORDERED = 'dv_widget_reordered',
  DV_WIDGET_DELETED = 'dv_widget_deleted',
  DV_SECTION_UPDATED = 'dv_section_updated',
  DV_CONFIG_UPDATED = 'dv_config_updated',

  // UI events
  TAB_CHANGED = 'tab_changed',
  PREVIEW_MODE_TOGGLED = 'preview_mode_toggled',
  LOADING_STATE_CHANGED = 'loading_state_changed'
}

/**
 * Union type cho các loại payload khác nhau trong state change events
 * ✅ REFACTORED: Thay thế any bằng specific types
 */
export type StateChangeEventPayload =
  | Section[]                    // For section operations
  | Section                      // For single section operations
  | Field[]                     // For field operations
  | Field                       // For single field operations
  | DetailViewConfig            // For detail view operations
  | WidgetReorderEventData      // For widget reorder operations
  | string                      // For simple string data
  | number                      // For simple number data
  | boolean                     // For simple boolean data
  | null                        // For null values
  | undefined;                  // For undefined values

/**
 * Interface cho state change event metadata
 * ✅ REFACTORED: Thay thế [key: string]: any bằng specific properties
 */
export interface StateChangeEventMetadata {
  sectionId?: string;
  fieldId?: string;
  widgetId?: string;
  oldIndex?: number;
  newIndex?: number;
  operation?: 'create' | 'update' | 'delete' | 'reorder';
  source?: 'user' | 'system' | 'auto-save';
  duration?: number;
  success?: boolean;
  errorMessage?: string;
  // Additional properties từ các trackEvent calls
  tabName?: string;
  timestamp?: string;
  version?: number;
  instanceId?: string;
  previousTabIndex?: number;
  isPreviewMode?: boolean;
  isLoading?: boolean;
}

/**
 * Interface cho state change event
 * ✅ REFACTORED: Thay thế any types bằng specific types
 */
export interface StateChangeEvent {
  type: StateChangeEventType;
  payload: StateChangeEventPayload;
  timestamp: string;
  layoutId: string;
  tabName?: string;
  metadata?: StateChangeEventMetadata;
}

/**
 * Interface cho UI state
 */
export interface UIState {
  isLoading: boolean;
  isPreviewMode: boolean;
  selectedTabIndex: number;
}

/**
 * Interface cho drag operation tracking
 */
export interface DragOperation {
  type: 'section' | 'field';
  sourceIndex: number;
  targetIndex: number;
  sectionId?: string;
  fieldId?: string;
  timestamp: string;
  success: boolean;
}

/**
 * Extended Create tab state với additional tracking
 */
export interface CreateTabStateExtended extends CreateTabState {
  lastDragOperation?: DragOperation;
  totalSectionOperations: number;
  totalFieldOperations: number;
}

/**
 * Extended Quick Create tab state với additional tracking
 */
export interface QuickCreateTabStateExtended extends TabState {
  quickCreateSection: Section;
  fieldCount: number;
  autoSaveEnabled: boolean;
  lastAutoSave?: string;
  autoSaveInterval?: number;
  totalFieldOperations: number;
}

/**
 * Extended Detail View tab state với additional tracking
 */
export interface DetailViewTabStateExtended extends DetailViewTabState {
  lastWidgetReorder?: WidgetReorderEventData;
  totalWidgetOperations: number;
  totalSectionOperations: number;
}

/**
 * Main centralized state interface
 * UPDATED: Sử dụng DynamicLayoutBuilderConfig mới từ dynamic-layout-config.model.ts
 */
export interface DynamicLayoutBuilderState {
  // Core configuration
  config: DynamicLayoutBuilderConfig;

  // Current active states
  currentLayoutId: string;
  selectedTabIndex: number;

  // Tab-specific states
  createTabState: CreateTabStateExtended;
  quickCreateTabState: QuickCreateTabStateExtended;
  detailViewTabState: DetailViewTabStateExtended;

  // UI states
  isLoading: boolean;
  isPreviewMode: boolean;

  // Metadata
  lastUpdated: string;
  version: number;
  instanceId: string;
}