.order-details-panel {
  margin-bottom: 16px;
}

.search-container {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;

  .search-input {
    flex: 1;
  }
}

.product-list {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;

  .product-list-header, .product-item {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr 0.5fr;
    align-items: center;
    padding: 8px 16px;
  }

  .product-list-header {
    background-color: #f5f5f5;
    font-weight: 500;
  }

  .product-item {
    border-top: 1px solid #e0e0e0;

    &:hover {
      background-color: #f9f9f9;
    }
  }

  .product-name {
    .product-name-main {
      font-weight: 500;
    }

    .product-name-info {
      font-size: 12px;
      color: #666;
      margin-top: 4px;

      span {
        margin-right: 8px;
      }
    }
  }

  .product-quantity, .product-price {
    input {
      width: 80px;
      padding: 4px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  }

  .product-total {
    font-weight: 500;
  }

  .no-products {
    padding: 16px;
    text-align: center;
    color: #666;
  }
}

.cdk-overlay-pane.custom-dialog {
  width: 80vw;
  height: 80vh;
  transform-origin: center center;
  transition: transform 600ms cubic-bezier(0.4, 0.0, 0.2, 1), opacity 600ms ease;

  --mat-dialog-container-max-width: 80vw;
}
// .initial {
//   transform: scale(0.1);
//   opacity: 0;
// }
// .expanded {
//   transform: scale(1);
//   opacity: 1;
//   width: 100vw;
//   height: 100vh;
//   max-width: 100vw; /* Override default max-width của MatDialog */
//   max-height: 100vh; /* Override default max-height của MatDialog */
//   margin: 0; /* Xóa margin mặc định */
//   border-radius: 0;
// }

/* Styling cho CDK Overlay */
::ng-deep {
  .cdk-overlay-pane {
    max-height: 80vh !important;
  }

  .product-selection-panel {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .product-selection-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 16px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      font-weight: 500;
    }

    app-product-selection {
      width: 100%;
      display: block;

      .product-selection-container {
        height: 100%;
        max-height: 60vh;

        .header-container {
          padding: 10px;
        }

        .product-list-container {
          max-height: calc(60vh - 200px);
          overflow: auto;
        }

        .actions-container {
          padding: 10px;
        }
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .order-details-panel {
    .search-container {
      flex-direction: column;
    }

    .product-list {
      .product-list-header {
        grid-template-columns: 2fr 1fr 1fr 1fr 0.5fr;
      }

      .product-list-items {
        .product-item {
          grid-template-columns: 2fr 1fr 1fr 1fr 0.5fr;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .order-details-panel {
    .product-list {
      .product-list-header {
        display: none;
      }

      .product-list-items {
        .product-item {
          grid-template-columns: 1fr;
          gap: 0.25rem;
          padding: 1rem 0;

          .product-name,
          .product-quantity,
          .product-price,
          .product-total,
          .product-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;

            &::before {
              content: attr(data-label);
              font-weight: 600;
              flex: 1;
            }
          }

          .product-actions {
            justify-content: flex-end;
          }
        }
      }
    }
  }
}

.product-selection-panel {
  padding: 16px;

  .product-selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    span {
      font-weight: 500;
    }
  }
}

/* Styles cho bottom sheet */
:host ::ng-deep .variant-selector-bottom-sheet {
  max-width: 600px;
  width: 100%;
}

// CSS cho modifier groups
.modifier-groups {
  margin-left: 20px; // Thụt vào so với sản phẩm chính
  border-left: 2px dashed #ddd; // Thêm đường gạch dọc bên trái để phân biệt
  padding-left: 10px;
  margin-top: 5px;
  margin-bottom: 10px;
}

.modifier-group {
  margin-bottom: 5px;

  .modifier-item {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr 0.5fr;
    align-items: center;
    padding: 4px 8px;
    background-color: rgba(0, 0, 0, 0.02); // Màu nền nhẹ để phân biệt
    border-radius: 4px;

    .quantity-btn-sm {
      width: 24px;
      height: 24px;
      line-height: 24px;

      .mat-icon {
        font-size: 14px;
        line-height: 14px;
        height: 14px;
        width: 14px;
      }
    }

    .remove-btn-sm {
      width: 24px;
      height: 24px;
      line-height: 24px;

      .mat-icon {
        font-size: 14px;
        line-height: 14px;
        height: 14px;
        width: 14px;
      }
    }
  }
}

// CSS cho note
.product-note {
  margin-left: 20px;
  margin-top: 5px;
  margin-bottom: 5px;

  .note-item {
    display: flex;
    padding: 4px 8px;
    background-color: rgba(255, 255, 150, 0.15); // Màu nền vàng nhạt cho note
    border-radius: 4px;
    border-left: 2px solid #f9c74f; // Viền màu vàng bên trái

    .product-name {
      flex: 1;

      .product-name-main {
        font-size: 0.9rem;
        font-style: italic;
        color: #666;
      }
    }
  }
}

// CSS cho product-price
.product-price {
  position: relative;

  .discount-info {
    display: flex;
    flex-direction: column;
    margin-top: 4px;
    font-size: 12px;

    .original-price {
      text-decoration: line-through;
      color: #777;
    }

    .discount-amount {
      color: #e53935; // Màu đỏ cho số tiền giảm
      font-weight: 500;
    }
  }
}

.product-quantity {
  input {
    width: 80px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .quantity-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .quantity-input {
      max-width: 60px;
      text-align: center;
      margin: 0 4px;
      padding: 0 4px;
    }

    .quantity-btn {
      width: 28px;
      height: 28px;
      line-height: 28px;

      .mat-icon {
        font-size: 16px;
        line-height: 16px;
        height: 16px;
        width: 16px;
      }
    }

    .modifier-quantity {
      width: 30px;
      text-align: center;
      margin: 0 2px;
    }
  }
}

.modifier-item {
  .remove-btn-sm {
    width: 24px;
    height: 24px;
    line-height: 24px;

    .mat-icon {
      font-size: 16px;
      line-height: 16px;
      height: 16px;
      width: 16px;
    }
  }
}

