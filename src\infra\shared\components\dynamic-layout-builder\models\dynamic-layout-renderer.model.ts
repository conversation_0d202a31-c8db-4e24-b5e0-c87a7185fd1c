import { Field, FieldPermissionProfile, FieldValue } from '@domain/entities/field.entity';
import { Section } from './dynamic-layout-config.dto';
import { DynamicLayoutConfigDto } from "./dynamic-layout-config.dto";
import { DynamicLayoutConfig } from './dynamic-layout-config.model';

// Re-export FieldValue để các component khác có thể sử dụng
export type { FieldValue } from '@domain/entities/field.entity';

// ===== FORM DATA MANAGEMENT SERVICE INTERFACE =====

/**
 * Interface cho FormDataManagementService để tránh circular import
 * Định nghĩa contract cho service quản lý form state và validation
 */
export interface FormDataManagementServiceInterface {
  // Readonly signals
  readonly isDirty: () => boolean;
  readonly isValid: () => boolean;
  readonly isLoading: () => boolean;
  readonly fieldValues: () => Record<string, FieldValue>;
  readonly formState: () => FormState;

  // Public methods
  initializeForm(initialValues: Record<string, FieldValue>, fields: Field[]): void;
  updateFieldValue(fieldId: string, value: FieldValue, field: Field): void;
  buildFormData(): DynamicLayoutRendererFormData;
  resetForm(): void;
  setLoading(loading: boolean): void;
  destroy(): void;
  isDestroyed(): boolean;
  getFieldValue(fieldId: string): FieldValue;
}

/**
 * Interface định nghĩa form state
 */
export interface FormState {
  isDirty: boolean;
  isValid: boolean;
  isLoading: boolean;
  fieldValues: Record<string, FieldValue>;
  fieldValidations: Record<string, FieldValidationResult>;
}

// ===== VALIDATION INTERFACES =====

/**
 * Interface định nghĩa kết quả validation của một field
 */
export interface FieldValidationResult {
  isValid: boolean;
  errorMessage?: string; // i18n key cho error message
}

// ===== MAIN RENDERER CONFIGURATION =====

/**
 * Interface định nghĩa cấu trúc dữ liệu form output khi save
 * Sử dụng khi enableEditMode = true và user click Save button
 */
export interface DynamicLayoutRendererFormData {
  formId?: string; // Optional - có thể không có khi tạo mới
  values: Array<{
    fieldId: string; // field._id
    value: FieldValue;
  }>;
}
export interface DynamicLayoutRendererConfig extends DynamicLayoutConfigDto {
  currentPermissionProfileId: string;
  showPermissionProfiles: boolean;
  defaultView: 'view' | 'form';
  /**
   * show cả 2 tab view/form
   */
  showAllTab: boolean;
  /**
   * Bật chế độ edit form - cho phép user chỉnh sửa và save dữ liệu
   * Khi true: hiển thị form controls, enable field editing, show Save/Cancel buttons
   * Khi false hoặc undefined: chỉ hiển thị read-only view
   */
  enableEditMode?: boolean;

  /**
   * ID của form để sync với server (optional)
   * Sử dụng khi edit existing record
   */
  formId?: string;

  /**
   * Dữ liệu ban đầu của form - key là field._id, value là FieldValue
   * Chỉ sử dụng khi enableEditMode = true
   * Ví dụ: { "field_123": "John Doe", "field_456": "<EMAIL>" }
   */
  formValues?: Record<string, FieldValue>;

  /**
   * Callback function được gọi khi user click Save button
   * Nhận DynamicLayoutRendererFormData và có thể return Promise để handle async operations
   * Component sẽ show loading state cho đến khi Promise resolve/reject
   */
  onFormSave?: (data: DynamicLayoutRendererFormData) => Promise<void> | void;
}

// ===== MODAL CONFIGURATION INTERFACES =====

/**
 * Interface cho dữ liệu đầu vào của DynamicLayoutRendererModal
 * Extends DynamicLayoutRendererConfig với các thuộc tính modal-specific
 */
export interface DynamicLayoutRendererModalData extends DynamicLayoutRendererConfig {
  /** Tiêu đề modal - sử dụng i18n key */
  title?: string;
  /** Chiều rộng modal (desktop) */
  width?: string;
  /** Chiều cao modal (desktop) */
  height?: string;
  /** Có thể đóng modal bằng ESC/backdrop click hay không */
  disableClose?: boolean;
  /** Có hiển thị nút X để đóng modal hay không */
  enableClose?: boolean;
}

/**
 * Interface cho kết quả trả về từ DynamicLayoutRendererModal
 */
export interface DynamicLayoutRendererModalResult {
  /** Hành động user thực hiện */
  action: 'save' | 'cancel' | 'close';
  /** Dữ liệu form nếu user click Save */
  formData?: DynamicLayoutRendererFormData;
  /** Có thay đổi dữ liệu hay không */
  hasChanges: boolean;
}

// ===== COMPONENT CONFIGURATION INTERFACES =====

/**
 * Configuration cho PermissionSelectorComponent
 * Component chỉ nhận 1 input object chứa tất cả dữ liệu cần thiết
 */
export interface PermissionSelectorConfig {
  /** Danh sách permission profiles có sẵn */
  permissionProfiles: FieldPermissionProfile[];
  /** ID của permission profile hiện tại được chọn */
  currentPermissionProfileId: string;
  /** Có hiển thị selector hay không */
  showSelector: boolean;
}

/**
 * Configuration cho SectionComponent
 * Component chỉ nhận 1 input object chứa tất cả dữ liệu cần thiết
 */
export interface SectionConfig {
  config: DynamicLayoutRendererConfig;
  /** Thông tin section cần render */
  section: Section;
  /** Chế độ hiển thị hiện tại (view/form) */
  currentViewMode: 'view' | 'form';
  /** Permission profile hiện tại */
  currentPermissionProfile: FieldPermissionProfile;
  /** Form data service cho form edit mode (optional) */
  formDataService?: FormDataManagementServiceInterface;
}

/**
 * Configuration cho FieldItemComponent
 * Component chỉ nhận 1 input object chứa tất cả dữ liệu cần thiết
 */
export interface FieldItemConfig {
  /** Thông tin field cần render */
  field: Field;
  /** Chế độ hiển thị hiện tại (view/form) */
  currentViewMode: 'view' | 'form';
  /** Permission của user hiện tại đối với field này */
  currentPermission: 'read_write' | 'read' | 'none';
  /** Form data service cho form edit mode (optional) */
  formDataService?: FormDataManagementServiceInterface;
}

// ===== EVENT INTERFACES =====

/**
 * Event được emit khi thay đổi permission profile
 */
export interface PermissionProfileChangeEvent {
  /** ID của permission profile mới được chọn */
  permissionProfileId: string;
  /** Thông tin đầy đủ của permission profile */
  permissionProfile: FieldPermissionProfile;
}

/**
 * Event được emit khi thay đổi view mode
 */
export interface ViewModeChangeEvent {
  /** View mode mới được chọn */
  viewMode: 'view' | 'form';
}

// ===== BASE FIELD COMPONENT INTERFACES =====

/**
 * Base interface cho tất cả field components
 * Định nghĩa contract chung mà mọi field component phải tuân thủ
 */
export interface BaseFieldComponent {
  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   * Tuân thủ yêu cầu: component chỉ nhận 1 input object
   */
  config: FieldItemConfig;

  /**
   * Kiểm tra xem có nên hiển thị field hay không
   * Dựa trên permission level
   */
  shouldShowField(): boolean;

  /**
   * Lấy placeholder text cho field
   * Hỗ trợ i18n với fallback keys
   */
  getPlaceholder(): string;

  /**
   * Lấy tooltip text cho read-only fields
   * Sử dụng i18n keys
   */
  getReadOnlyTooltip(): string;

  // ===== FORM MODE METHODS =====
  // TODO: Implement these methods in all field components

  /**
   * Lấy giá trị hiện tại của field
   * Sử dụng trong form mode để collect data
   */
  getCurrentValue?(): FieldValue;

  /**
   * Kiểm tra xem field có validation errors không
   * Sử dụng để hiển thị error state
   */
  hasValidationErrors?(): boolean;

  /**
   * Lấy danh sách validation errors
   * Trả về array of i18n keys
   */
  getValidationErrors?(): string[];
}

/**
 * Extended interface cho các field components có form control
 * Áp dụng cho các field có thể nhập liệu
 */
export interface FormFieldComponent extends BaseFieldComponent {
  /**
   * Kiểm tra xem field có ở chế độ read-only không
   * Dựa trên permission và view mode
   */
  isReadOnly(): boolean;

  /**
   * Khởi tạo form control cho form mode
   * Bao gồm validators và disabled state
   */
  initializeFormControl(): void;

  /**
   * Bind giá trị từ form data vào field
   * Sử dụng khi initialize form với existing data
   */
  bindFormValue?(value: FieldValue): void;

  /**
   * Handle khi user thay đổi giá trị field
   * Emit change event để Form Management Service track
   */
  onValueChange?(value: FieldValue): void;
}

// ===== FIELD TYPE DEFINITIONS =====

/**
 * Type union cho các field types được hỗ trợ bởi từng component
 */
export type TextFieldTypes = 'text' | 'email' | 'phone' | 'url' | 'search';
export type NumberFieldTypes = 'number' | 'decimal' | 'currency' | 'percent';
export type SelectFieldTypes = 'picklist' | 'multi-picklist' | 'select' | 'radio';
export type DateFieldTypes = 'date' | 'datetime';
export type FileFieldTypes = 'file' | 'image';