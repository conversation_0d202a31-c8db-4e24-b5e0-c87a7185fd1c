// Unsaved Changes Confirm Modal Styles

.mat-mdc-dialog-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--bs-dark);
  
  mat-icon {
    font-size: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

.mat-mdc-dialog-content {
  max-width: 500px;
  min-width: 400px;
  
  p {
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--bs-gray-700);
  }
  
  .bg-light {
    background-color: var(--bs-gray-100) !important;
    border: 1px solid var(--bs-gray-200);
    
    small {
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .fw-bold {
      font-size: 0.9rem;
      color: var(--bs-dark);
    }
  }
}

.mat-mdc-dialog-actions {
  border-top: 1px solid var(--bs-gray-200);
  background-color: var(--bs-gray-50);
  
  button {
    min-width: 120px;
    height: 40px;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 6px;
    
    mat-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }
    
    &.btn-outline-secondary {
      border: 1px solid var(--bs-gray-300);
      color: var(--bs-gray-600);
      
      &:hover:not(:disabled) {
        background-color: var(--bs-gray-100);
        border-color: var(--bs-gray-400);
      }
    }
    
    &.btn-outline-danger {
      border: 1px solid var(--bs-danger);
      color: var(--bs-danger);
      
      &:hover:not(:disabled) {
        background-color: var(--bs-danger);
        color: white;
      }
    }
    
    &.btn-primary {
      background-color: var(--bs-primary);
      border-color: var(--bs-primary);
      color: white;
      
      &:hover:not(:disabled) {
        background-color: var(--bs-primary-dark, #0056b3);
        border-color: var(--bs-primary-dark, #0056b3);
      }
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  mat-spinner {
    display: inline-block;
    vertical-align: middle;
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .mat-mdc-dialog-content {
    min-width: 300px;
    max-width: 90vw;
  }
  
  .mat-mdc-dialog-actions {
    flex-direction: column;
    gap: 8px;
    
    button {
      width: 100%;
      min-width: unset;
    }
  }
}
