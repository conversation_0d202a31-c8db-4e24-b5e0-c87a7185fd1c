import { On<PERSON>nit, <PERSON><PERSON><PERSON>es, On<PERSON><PERSON>roy, <PERSON><PERSON>hang<PERSON>, ChangeDetectorRef, inject, signal, Directive } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

import { BaseFieldComponent, FormFieldComponent, FieldItemConfig, FormDataManagementServiceInterface } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

/**
 * Abstract base class cho tất cả field components trong Dynamic Layout Builder
 * 
 * Cung cấp:
 * - Lifecycle management (ngOnInit, ngOnChanges, ngOnDestroy)
 * - Subscription management để tránh memory leaks
 * - Common logic cho việc khởi tạo FormControl từ field.value
 * - Template methods cho các field components kế thừa
 * - Consistent implementation của BaseFieldComponent và FormFieldComponent interfaces
 * 
 * Các field components con chỉ cần:
 * - Implement abstract methods: validateFieldType(), generateMockValue(), getValidators(), getFieldIcon()
 * - Override methods nếu cần custom behavior
 */
@Directive()
export abstract class AbstractFieldComponent implements OnInit, OnChanges, OnDestroy, BaseFieldComponent, FormFieldComponent {

  // ===== ABSTRACT PROPERTIES =====
  
  /**
   * Configuration object - phải được set bởi component con
   */
  abstract config: FieldItemConfig;

  // ===== INJECTED SERVICES =====

  protected translateService = inject(TranslateService);
  protected cdr = inject(ChangeDetectorRef);

  // ===== STATE MANAGEMENT =====
  
  // Subscription để quản lý form control changes
  protected subscriptions = new Subscription();

  // Signals để quản lý state
  protected mockValue = signal<string>('');
  public formControl = signal<FormControl>(new FormControl());
  protected isVisible = signal<boolean>(true);
  protected isReadOnlyState = signal<boolean>(false);

  // Form mode state
  protected currentValue = signal<FieldValue>('');
  protected validationErrors = signal<string[]>([]);

  // FormDataManagementService instance (từ config)
  protected formDataService: FormDataManagementServiceInterface | null = null;

  // ===== LIFECYCLE HOOKS =====

  ngOnInit(): void {
    // Lấy FormDataManagementService từ config nếu có
    this.formDataService = this.config.formDataService || null;

    this.initializeField();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Khi config thay đổi, cần xử lý cẩn thận để không mất dữ liệu người dùng đã nhập
    if (changes['config'] && this.config) {
      // Cập nhật FormDataManagementService reference
      this.formDataService = this.config.formDataService || null;

      this.validateFieldType();

      const configChange = changes['config'];
      const isFirstTime = !configChange.previousValue;
      const fieldIdChanged = configChange.previousValue?.field?._id !== this.config.field._id;
      const fieldValueChanged = configChange.previousValue?.field?.value !== this.config.field.value;

      if (isFirstTime || fieldIdChanged) {
        // Khởi tạo hoàn toàn mới cho field mới hoặc lần đầu
        this.initializeFormControl();
        this.initializeField();
      } else if (fieldValueChanged && !this.formControl().dirty) {
        // Chỉ cập nhật giá trị nếu form control chưa bị user thay đổi (dirty = false)
        this.formControl().setValue(this.config.field.value || null, { emitEvent: false });
        this.initializeField();
      } else {
        // Chỉ cập nhật field-specific logic, không động đến FormControl value
        this.initializeField();
      }
    }
  }

  ngOnDestroy(): void {
    // Hủy tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }

  // ===== ABSTRACT METHODS - PHẢI ĐƯỢC IMPLEMENT BỞI COMPONENT CON =====

  /**
   * Validate rằng field type được hỗ trợ bởi component
   */
  protected abstract validateFieldType(): void;

  /**
   * Generate mock value dựa trên field type cho view mode
   */
  protected abstract generateMockValue(): void;

  /**
   * Lấy validators phù hợp cho field type
   */
  protected abstract getValidators(): any[];

  /**
   * Lấy icon phù hợp cho field type
   */
  abstract getFieldIcon(): string;

  // ===== TEMPLATE METHODS - CÓ THỂ ĐƯỢC OVERRIDE BỞI COMPONENT CON =====

  /**
   * Khởi tạo field dựa trên config
   * Template method - có thể được override nếu cần custom logic
   */
  protected initializeField(): void {
    // Validate field type
    this.validateFieldType();

    // Kiểm tra visibility dựa trên permission
    this.isVisible.set(this.config.currentPermission !== 'none');

    // Kiểm tra read-only state
    this.isReadOnlyState.set(
      this.config.currentPermission === 'read' && this.config.currentViewMode === 'form'
    );

    // Generate mock data cho view mode
    if (this.config.currentViewMode === 'view') {
      this.generateMockValue();
    }

    // QUAN TRỌNG: Không gọi initializeFormControl() ở đây để tránh ghi đè user input
    // initializeFormControl() chỉ được gọi từ ngOnChanges khi thực sự cần thiết
    if (this.config.currentViewMode === 'form') {
      // Hủy subscription cũ trước khi tạo subscription mới
      this.subscriptions.unsubscribe();
      this.subscriptions = new Subscription();

      // Chỉ setup subscriptions, KHÔNG khởi tạo lại FormControl
      this.setupFormValueSubscription();
    }
  }

  /**
   * Xử lý khi field.value thay đổi từ bên ngoài
   * Template method - có thể được override nếu cần custom logic
   * CHỈ cập nhật nếu form control chưa bị user thay đổi (dirty = false)
   */
  protected handleFieldValueChange(): void {
    // Nếu đang ở form mode và có form control, cập nhật giá trị
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      if (control && !control.dirty) {
        // Chỉ cập nhật nếu form control chưa bị user thay đổi
        const newValue = this.getInitialValue();
        control.setValue(newValue, { emitEvent: false });
        this.currentValue.set(newValue);
      }
    }
  }

  /**
   * Lấy initial value từ field.value
   * Template method - có thể được override nếu cần custom logic
   */
  protected getInitialValue(): FieldValue {
    // Sử dụng field.value làm nguồn dữ liệu duy nhất
    return this.config.field.value || this.config.field.defaultValue || '';
  }

  // ===== BASEFIELCOMPONENT INTERFACE IMPLEMENTATION =====

  /**
   * Kiểm tra xem có nên hiển thị field hay không
   */
  shouldShowField(): boolean {
    return this.isVisible();
  }

  /**
   * Lấy placeholder text cho field
   */
  getPlaceholder(): string {
    return this.config.field.placeholder || 
           `DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.${this.config.field.type.toUpperCase()}`;
  }

  /**
   * Lấy tooltip text cho read-only fields
   */
  getReadOnlyTooltip(): string {
    return 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP';
  }

  // ===== FORMFIELDCOMPONENT INTERFACE IMPLEMENTATION =====

  /**
   * Kiểm tra xem field có ở chế độ read-only không
   */
  isReadOnly(): boolean {
    return this.isReadOnlyState();
  }

  /**
   * Khởi tạo form control cho form mode
   */
  initializeFormControl(): void {
    // Lấy giá trị từ FormDataManagementService trước, nếu không có thì dùng field.value
    let initialValue = this.getInitialValue();

    if (this.formDataService && this.config.field._id) {
      const serviceValue = this.formDataService.getFieldValue(this.config.field._id);
      if (serviceValue !== null && serviceValue !== undefined) {
        initialValue = serviceValue;
      }
    }

    // Cập nhật currentValue signal để đồng bộ
    this.currentValue.set(initialValue);

    const control = new FormControl({
      value: initialValue,
      disabled: this.isReadOnly()
    });

    // Thêm validators dựa trên field type và requirements
    const validators = this.getValidators();
    if (validators.length > 0) {
      control.addValidators(validators);
    }

    this.formControl.set(control);
  }

  /**
   * Bind giá trị từ form data vào field
   */
  bindFormValue?(value: FieldValue): void {
    this.currentValue.set(value);

    // Nếu đang ở form mode, cập nhật form control
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      control.setValue(value, { emitEvent: false });
    }
  }

  /**
   * Handle khi user thay đổi giá trị field
   */
  onValueChange?(value: FieldValue): void {
    this.currentValue.set(value);

    // Trigger change detection để cập nhật UI
    this.cdr.markForCheck();
  }

  // ===== UTILITY METHODS =====

  /**
   * Lấy giá trị hiện tại của field
   */
  getCurrentValue(): FieldValue {
    if (this.config.currentViewMode === 'form') {
      return this.formControl().value;
    }
    return this.currentValue();
  }

  /**
   * Kiểm tra xem field có validation errors không
   */
  hasValidationErrors(): boolean {
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      return control.invalid && (control.dirty || control.touched);
    }
    return this.validationErrors().length > 0;
  }

  /**
   * Lấy danh sách validation errors
   */
  getValidationErrors(): string[] {
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      const errors: string[] = [];

      if (control.errors) {
        if (control.errors['required']) {
          errors.push('FORM_VALIDATION.FIELD_REQUIRED');
        }
        // Các error khác sẽ được thêm bởi component con
      }

      return errors;
    }
    return this.validationErrors();
  }

  /**
   * Setup subscription để theo dõi form control value changes
   */
  protected setupFormValueSubscription(): void {
    const control = this.formControl();
    if (!control) return;

    // Subscribe to value changes
    const valueChangeSub = control.valueChanges.subscribe((value: FieldValue) => {
      // Cập nhật FormDataManagementService nếu có
      if (this.formDataService && this.config.field._id) {
        this.formDataService.updateFieldValue(this.config.field._id, value, this.config.field);
      }

      // Gọi onValueChange callback nếu có
      if (this.onValueChange) {
        this.onValueChange(value);
      }
    });

    this.subscriptions.add(valueChangeSub);
  }

  /**
   * Lấy common validators (required)
   */
  protected getCommonValidators(): any[] {
    const validators: any[] = [];

    // Required validator
    if (this.config.field.isRequired || this.config.field.required) {
      validators.push(Validators.required);
    }

    return validators;
  }
}
