import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { PromotionModalComponent } from './promotion-modal.component';
import { PromotionModalData, PromotionModalResult } from './models/promotion-modal.model';

/**
 * Service để mở modal khuyến mãi
 */
@Injectable({
  providedIn: 'root'
})
export class PromotionModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal khuyến mãi
   * @param data Dữ liệu cho modal
   * @returns Promise<PromotionModalResult | null> Kết quả khuyến mãi hoặc null nếu hủy
   */
  async open(data: PromotionModalData): Promise<PromotionModalResult | null> {
    try {
      const modalConfig = {
        data,
        width: '500px',
        maxWidth: '95vw'
      };

      const result = await this.responsiveModalService.open<
        PromotionModalData,
        PromotionModalResult,
        PromotionModalComponent
      >(PromotionModalComponent, modalConfig);

      return result || null;
    } catch (error) {
      console.error('Lỗi khi mở modal khuyến mãi:', error);
      return null;
    }
  }
}
