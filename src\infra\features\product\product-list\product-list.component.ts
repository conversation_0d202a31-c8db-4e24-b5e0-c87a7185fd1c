import { Component, signal, TemplateRef, ViewChild, ChangeDetectionStrategy, inject } from '@angular/core';
import { CommonModule, CurrencyPipe } from '@angular/common';
import { Observable, of } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { ListLayoutComponent } from '@shared/components/list-layout/list-layout.component';
import { ListConfig, Product, SortState, ActionItem } from '@shared/models/view/list-layout.model';
import { PageContextBarComponent } from '@shared/components/page-context-bar/page-context-bar.component';
import { mockProductListFetchNewItems, mockProductListPage } from '@mock/product/product_list.mock';
import { FlashMessageService } from '@core/services/flash_message.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    CommonModule,
    ListLayoutComponent,
    FormsModule,
  ],
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductListComponent {
  @ViewChild('priceRangeTemplate') priceRangeTemplate!: TemplateRef<any>;

  // Inject services
  private flashMessageService = inject(FlashMessageService);
  private translateService = inject(TranslateService);

  products$!: Observable<Product[]>;
  config = signal<ListConfig>({
    pageName: 'LIST_LAYOUT.PAGE_NAME.ALL_PRODUCTS',
    pageShortDescription: 'LIST_LAYOUT.PAGE_SHORT_DESC.ALL_PRODUCTS',

    // Enable selection and row actions
    enableSelection: true,
    enableRowActions: true,

    // Default actions (hiển thị khi không có item nào được chọn)
    defaultActions: [
      {
        _id: 'add-product',
        label: 'LIST_LAYOUT.ACTIONS.ADD_PRODUCT',
        icon: 'fa-light fa-plus',
        actionType: 'add-product',
        tooltip: 'Thêm sản phẩm mới'
      },
      {
        _id: 'import',
        label: 'LIST_LAYOUT.ACTIONS.IMPORT',
        icon: 'fa-light fa-upload',
        actionType: 'import',
        tooltip: 'Nhập dữ liệu từ file'
      }
    ],

    // Selected actions (hiển thị khi có items được chọn)
    selectedActions: [
      {
        _id: 'bulk-delete',
        label: 'LIST_LAYOUT.ACTIONS.BULK_DELETE',
        icon: 'fa-light fa-trash',
        actionType: 'bulk-delete',
        tooltip: 'Xóa các sản phẩm đã chọn'
      },
      {
        _id: 'bulk-edit',
        label: 'LIST_LAYOUT.ACTIONS.BULK_EDIT',
        icon: 'fa-light fa-edit',
        actionType: 'bulk-edit',
        tooltip: 'Chỉnh sửa hàng loạt'
      },
      {
        _id: 'bulk-export',
        label: 'LIST_LAYOUT.ACTIONS.BULK_EXPORT',
        icon: 'fa-light fa-download',
        actionType: 'bulk-export',
        tooltip: 'Xuất dữ liệu đã chọn'
      }
    ],

    // Row actions (hiển thị trong menu của từng dòng)
    rowActions: [
      {
        _id: 'edit',
        label: 'LIST_LAYOUT.ACTIONS.EDIT',
        icon: 'edit',
        actionType: 'edit',
        tooltip: 'Chỉnh sửa sản phẩm'
      },
      {
        _id: 'view-details',
        label: 'LIST_LAYOUT.ACTIONS.VIEW_DETAILS',
        icon: 'visibility',
        actionType: 'view-details',
        tooltip: 'Xem chi tiết sản phẩm'
      },
      {
        _id: 'duplicate',
        label: 'LIST_LAYOUT.ACTIONS.DUPLICATE',
        icon: 'content_copy',
        actionType: 'duplicate',
        tooltip: 'Nhân bản sản phẩm'
      },
      {
        _id: 'delete',
        label: 'LIST_LAYOUT.ACTIONS.DELETE',
        icon: 'delete',
        actionType: 'delete',
        tooltip: 'Xóa sản phẩm'
      }
    ],
    fetchNewItems: mockProductListFetchNewItems,


    ...mockProductListPage
  });
  filterValues = signal<Record<string, any>>({});
  sortState = signal<SortState>({ _id: '', direction: '' });
  currentPage = signal<number>(1);
  pageSize = signal<number>(50);
  totalItems = signal<number>(500); // Tổng số sản phẩm
  selectedItems = signal<string[]>([]); // Track selected product IDs

  constructor() {}

  ngOnInit() {
  }

  onFilterChange(filters: Record<string, any>) {
    this.filterValues.set(filters);
    this.currentPage.set(1);
  }

  onSortChange(sort: SortState) {
    this.sortState.set(sort);
    this.currentPage.set(1);
  }

  onColumnsChange({ visibleColumns, columnOrder }: { visibleColumns: string[]; columnOrder: string[] }) {
    // this.config.set({
    //   ...this.config(),
    //   columns: this.config().allColumns.filter(col => visibleColumns.includes(col._id)),
    //   columnOrder
    // });
    // this.currentPage.set(1);
  }

  onPageChange(event: { page: number; pageSize: number }) {
    this.currentPage.set(event.page);
    this.pageSize.set(event.pageSize);
  }

  handleActionClick(data: { item: Product; actionType: string; extra?: any }) {
    // Xóa console.log debug không cần thiết
    switch (data.actionType) {
      case 'view':
        // Thông báo cho người dùng về action đang thực hiện
        this.flashMessageService.info(
          this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.LOADING')
        );
        break;
      case 'edit':
        this.flashMessageService.info(
          this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.PROCESSING')
        );
        break;
      case 'delete':
        this.flashMessageService.warning(
          this.translateService.instant('FLASH_MESSAGES.WARNING.GENERAL.IRREVERSIBLE')
        );
        break;
    }
  }

  /**
   * Xử lý khi selection thay đổi
   * @param selectedIds - Array of selected item IDs
   */
  onSelectionChange(selectedIds: string[]): void {
    this.selectedItems.set(selectedIds);
  }

  /**
   * Xử lý khi action được thực thi
   * @param event - Action execution event
   */
  onActionExecute(event: { actionType: string; selectedItems: string[]; item?: any }): void {
    // Xóa console.log debug không cần thiết

    switch (event.actionType) {
      // Default actions
      case 'add-product':
        this.handleAddProduct();
        break;
      case 'import':
        this.handleImport();
        break;

      // Selected actions (bulk operations)
      case 'bulk-delete':
        this.handleBulkDelete(event.selectedItems);
        break;
      case 'bulk-edit':
        this.handleBulkEdit(event.selectedItems);
        break;
      case 'bulk-export':
        this.handleBulkExport(event.selectedItems);
        break;

      // Row actions
      case 'edit':
        this.handleEditProduct(event.item);
        break;
      case 'view-details':
        this.handleViewDetails(event.item);
        break;
      case 'duplicate':
        this.handleDuplicateProduct(event.item);
        break;
      case 'delete':
        this.handleDeleteProduct(event.item);
        break;

      default:
        // Thay thế console.warn bằng FlashMessageService với i18n
        this.flashMessageService.warning(
          this.translateService.instant('FLASH_MESSAGES.WARNING.GENERAL.INCOMPLETE_DATA'),
          {
            description: `Unknown action type: ${event.actionType}`
          }
        );
    }
  }

  // ==================== ACTION HANDLERS ====================

  private handleAddProduct(): void {
    // Thay thế console.log bằng thông báo có ý nghĩa cho người dùng
    this.flashMessageService.info(
      this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.PROCESSING')
    );
    // TODO: Navigate to add product page or open modal
  }

  private handleImport(): void {
    // Thay thế console.log bằng thông báo có ý nghĩa cho người dùng
    this.flashMessageService.info(
      this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.UPLOADING')
    );
    // TODO: Open import dialog
  }

  private handleBulkDelete(selectedIds: string[]): void {
    // Thay thế console.log bằng warning message cho bulk delete
    this.flashMessageService.warning(
      this.translateService.instant('FLASH_MESSAGES.WARNING.GENERAL.IRREVERSIBLE'),
      {
        description: `Deleting ${selectedIds.length} products`
      }
    );
    // TODO: Show confirmation dialog and delete selected products
  }

  private handleBulkEdit(selectedIds: string[]): void {
    // Thay thế console.log bằng info message cho bulk edit
    this.flashMessageService.info(
      this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.PROCESSING'),
      {
        description: `Editing ${selectedIds.length} products`
      }
    );
    // TODO: Open bulk edit dialog
  }

  private handleBulkExport(selectedIds: string[]): void {
    // Thay thế console.log bằng info message cho bulk export
    this.flashMessageService.info(
      this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.DOWNLOADING'),
      {
        description: `Exporting ${selectedIds.length} products`
      }
    );
    // TODO: Export selected products to file
  }

  private handleEditProduct(product: Product): void {
    // Thay thế console.log bằng info message
    this.flashMessageService.info(
      this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.PROCESSING')
    );
    // TODO: Navigate to edit product page or open modal
  }

  private handleViewDetails(product: Product): void {
    // Thay thế console.log bằng info message
    this.flashMessageService.info(
      this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.LOADING')
    );
    // TODO: Navigate to product details page or open modal
  }

  private handleDuplicateProduct(product: Product): void {
    // Thay thế console.log bằng success message khi duplicate
    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.COPIED')
    );
    // TODO: Create a copy of the product
  }

  private handleDeleteProduct(product: Product): void {
    // Thay thế console.log bằng warning message cho delete
    this.flashMessageService.warning(
      this.translateService.instant('FLASH_MESSAGES.WARNING.GENERAL.IRREVERSIBLE')
    );
    // TODO: Show confirmation dialog and delete product
  }
}
