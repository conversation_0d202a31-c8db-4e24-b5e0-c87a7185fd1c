import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class I18nService {
  /**
   * Constructor
   */
  constructor(private translate: TranslateService) {
    // Thiết lập ngôn ngữ mặc định
    translate.setDefaultLang('vi');

    // Debug: hiển thị đường dẫn file dịch
    console.log('I18n initialized. Assets path for i18n files: /assets/i18n/[lang].json');

    // Thiết lập ngôn ngữ hiện tại từ localStorage (nếu có)
    const savedLang = localStorage.getItem('selectedLang');
    if (savedLang && ['vi', 'en'].includes(savedLang)) {
      console.log(`Using saved language from localStorage: ${savedLang}`);
      translate.use(savedLang);
    } else {
      console.log('No saved language preference found, using default: vi');
      translate.use('vi');
    }

    // Lắng nghe sự kiện thay đổi ngôn ngữ
    translate.onLangChange.subscribe(event => {
      console.log(`Language changed to: ${event.lang}`);
    });

    // Lắng nghe sự kiện lỗi
    translate.onTranslationChange.subscribe(() => {
      console.log('Translations loaded successfully');
    });
  }

  /**
   * Lấy ngôn ngữ hiện tại
   */
  getCurrentLang(): string {
    return this.translate.currentLang || this.translate.defaultLang;
  }

  /**
   * Chuyển đổi ngôn ngữ
   * @param lang Mã ngôn ngữ cần chuyển đổi
   */
  switchLanguage(lang: string): void {
    console.log(`Switching language to: ${lang}`);
    this.translate.use(lang);
    localStorage.setItem('selectedLang', lang);
  }
}
