/* eslint-disable */
// @ts-nocheck
import { Place } from 'salehub_shared_contracts';

export const mockPlaces: Place[] = [
  {
    addressComponents: [
      {
        long_name: 'Đường Lê Lợi',
        short_name: '<PERSON><PERSON> Lê Lợi',
        types: ['route']
      },
      {
        long_name: '<PERSON><PERSON><PERSON>',
        short_name: '<PERSON><PERSON><PERSON>',
        types: ['neighborhood', 'political']
      },
      {
        long_name: 'Đông <PERSON>ả<PERSON>',
        short_name: '<PERSON>ô<PERSON>ả<PERSON>',
        types: ['political', 'sublocality', 'sublocality_level_1']
      },
      {
        long_name: '<PERSON><PERSON> Hó<PERSON>',
        short_name: '<PERSON><PERSON> Hó<PERSON>',
        types: ['locality', 'political']
      },
      {
        long_name: '<PERSON><PERSON> Hóa',
        short_name: '<PERSON><PERSON> Hóa',
        types: ['administrative_area_level_1', 'political']
      },
      {
        long_name: 'Vietnam',
        short_name: 'VN',
        types: ['country', 'political']
      }
    ],
    fullAddress: '<PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Vietnam',
    geometry: {
      bounds: {
        northeast: {
          lat: 19.8205,
          lng: 105.7901
        },
        southwest: {
          lat: 19.8150,
          lng: 105.7850
        }
      },
      location: {
        lat: 19.8178,
        lng: 105.7875
      },
      location_type: 'GEOMETRIC_CENTER',
      viewport: {
        northeast: {
          lat: 19.8205,
          lng: 105.7901
        },
        southwest: {
          lat: 19.8150,
          lng: 105.7850
        }
      }
    },
    placeId: 'EmQxMjMgxJDGsOG7nW5nIEzDqiBM4bujaSwgxJDhu5NuZyBI4bqjaSwgVGhhbmggSMOzYSBWaeG7h3QgTmFtIi4qLAoUChIJdycLU973NjERfACLqv04qCcSFAoSCSeuFe5zWDYxEb_sdcFrLPDp',
    types: ['route']
  },
  {
    addressComponents: [
      {
        long_name: 'Đường Nguyễn Trãi',
        short_name: 'Đ. Nguyễn Trãi',
        types: ['route']
      },
      {
        long_name: 'Nam Ngạn',
        short_name: 'Nam Ngạn',
        types: ['neighborhood', 'political']
      },
      {
        long_name: 'Nam Ngạn',
        short_name: 'Nam Ngạn',
        types: ['political', 'sublocality', 'sublocality_level_1']
      },
      {
        long_name: 'Thanh Hóa',
        short_name: 'Thanh Hóa',
        types: ['locality', 'political']
      },
      {
        long_name: 'Thanh Hóa',
        short_name: 'Thanh Hóa',
        types: ['administrative_area_level_1', 'political']
      },
      {
        long_name: 'Vietnam',
        short_name: 'VN',
        types: ['country', 'political']
      }
    ],
    fullAddress: 'Đ. Nguyễn Trãi, Nam Ngạn, Nam Ngạn, Thanh Hóa, Vietnam',
    geometry: {
      bounds: {
        northeast: {
          lat: 19.8300,
          lng: 105.8000
        },
        southwest: {
          lat: 19.8250,
          lng: 105.7950
        }
      },
      location: {
        lat: 19.8275,
        lng: 105.7975
      },
      location_type: 'GEOMETRIC_CENTER',
      viewport: {
        northeast: {
          lat: 19.8300,
          lng: 105.8000
        },
        southwest: {
          lat: 19.8250,
          lng: 105.7950
        }
      }
    },
    placeId: 'EmQxMjMgxJDGsOG7nW5nIE5ndXnhu4VuIFRyxqNpLCBOYW0gTmfigrFuLCBUaGFuaCBHw7NhIFZp4buHdCBOYW0iLioqLAoUChIJdycLU973NjERfACLqv04qCcSFAoSCSeuFe5zWDYxEb_sdcFrLPDp',
    types: ['route']
  },
  {
    addressComponents: [
      {
        long_name: 'Đường Quốc lộ 1A',
        short_name: 'Đ. QL 1A',
        types: ['route']
      },
      {
        long_name: 'Quảng Hưng',
        short_name: 'Quảng Hưng',
        types: ['neighborhood', 'political']
      },
      {
        long_name: 'Quảng Hưng',
        short_name: 'Quảng Hưng',
        types: ['political', 'sublocality', 'sublocality_level_1']
      },
      {
        long_name: 'Thanh Hóa',
        short_name: 'Thanh Hóa',
        types: ['locality', 'political']
      },
      {
        long_name: 'Thanh Hóa',
        short_name: 'Thanh Hóa',
        types: ['administrative_area_level_1', 'political']
      },
      {
        long_name: 'Vietnam',
        short_name: 'VN',
        types: ['country', 'political']
      }
    ],
    fullAddress: 'Đ. QL 1A, Quảng Hưng, Quảng Hưng, Thanh Hóa, Vietnam',
    geometry: {
      bounds: {
        northeast: {
          lat: 19.8500,
          lng: 105.8200
        },
        southwest: {
          lat: 19.8450,
          lng: 105.8150
        }
      },
      location: {
        lat: 19.8475,
        lng: 105.8175
      },
      location_type: 'GEOMETRIC_CENTER',
      viewport: {
        northeast: {
          lat: 19.8500,
          lng: 105.8200
        },
        southwest: {
          lat: 19.8450,
          lng: 105.8150
        }
      }
    },
    placeId: 'EmQxMjMgxJDGsOG7nW5nIFF14buRbmggbOG6rSBxQSLCIFF14bqhbmcgSMO6bmcsIFRoYW5oIEjDs2EgVmnhu4d0IE5hbSIiLioqLAoUChIJdycLU973NjERfACLqv04qCcSFAoSCSeuFe5zWDYxEb_sdcFrLPDp',
    types: ['route']
  },
  {
    addressComponents: [
      {
        long_name: 'Đường Trần Phú',
        short_name: 'Đ. Trần Phú',
        types: ['route']
      },
      {
        long_name: 'Ba Đình',
        short_name: 'Ba Đình',
        types: ['neighborhood', 'political']
      },
      {
        long_name: 'Ba Đình',
        short_name: 'Ba Đình',
        types: ['political', 'sublocality', 'sublocality_level_1']
      },
      {
        long_name: 'Thanh Hóa',
        short_name: 'Thanh Hóa',
        types: ['locality', 'political']
      },
      {
        long_name: 'Thanh Hóa',
        short_name: 'Thanh Hóa',
        types: ['administrative_area_level_1', 'political']
      },
      {
        long_name: 'Vietnam',
        short_name: 'VN',
        types: ['country', 'political']
      }
    ],
    fullAddress: 'Đ. Trần Phú, Ba Đình, Ba Đình, Thanh Hóa, Vietnam',
    geometry: {
      bounds: {
        northeast: {
          lat: 19.8100,
          lng: 105.7800
        },
        southwest: {
          lat: 19.8050,
          lng: 105.7750
        }
      },
      location: {
        lat: 19.8075,
        lng: 105.7775
      },
      location_type: 'GEOMETRIC_CENTER',
      viewport: {
        northeast: {
          lat: 19.8100,
          lng: 105.7800
        },
        southwest: {
          lat: 19.8050,
          lng: 105.7750
        }
      }
    },
    placeId: 'EmQxMjMgxJDGsOG7nW5nIFRyxqFuIFBow7ksIEJhIMSQw6xuaCwgVGhhbmggSMOzYSBWaeG7h3QgTmFtIi4qLAoUChIJdycLU973NjERfACLqv04qCcSFAoSCSeuFe5zWDYxEb_sdcFrLPDp',
    types: ['route']
  },
  {
    addressComponents: [
      {
        long_name: 'Đường Hùng Vương',
        short_name: 'Đ. Hùng Vương',
        types: ['route']
      },
      {
        long_name: 'Đông Thọ',
        short_name: 'Đông Thọ',
        types: ['neighborhood', 'political']
      },
      {
        long_name: 'Đông Thọ',
        short_name: 'Đông Thọ',
        types: ['political', 'sublocality', 'sublocality_level_1']
      },
      {
        long_name: 'Thanh Hóa',
        short_name: 'Thanh Hóa',
        types: ['locality', 'political']
      },
      {
        long_name: 'Thanh Hóa',
        short_name: 'Thanh Hóa',
        types: ['administrative_area_level_1', 'political']
      },
      {
        long_name: 'Vietnam',
        short_name: 'VN',
        types: ['country', 'political']
      }
    ],
    fullAddress: 'Đ. Hùng Vương, Đông Thọ, Đông Thọ, Thanh Hóa, Vietnam',
    geometry: {
      bounds: {
        northeast: {
          lat: 19.8400,
          lng: 105.8100
        },
        southwest: {
          lat: 19.8350,
          lng: 105.8050
        }
      },
      location: {
        lat: 19.8375,
        lng: 105.8075
      },
      location_type: 'GEOMETRIC_CENTER',
      viewport: {
        northeast: {
          lat: 19.8400,
          lng: 105.8100
        },
        southwest: {
          lat: 19.8350,
          lng: 105.8050
        }
      }
    },
    placeId: 'EmQxMjMgxJDGsOG7nW5nIEjDuG5nIFbGsMahbmcsIMSQ4buRbmcgVGjhu5ssIFRoYW5oIEjDs2EgVmnhu4d0IE5hbSIiLioqLAoUChIJdycLU973NjERfACLqv04qCcSFAoSCSeuFe5zWDYxEb_sdcFrLPDp',
    types: ['route']
  }
];

export const mockPlaceAutocomplete = [
  { fullAddress: '26 Lý Thái Tông, Đông Thọ, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '15 Lê Lợi, Nam Ngạn, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '78 Nguyễn Trãi, Ba Đình, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '102 Quốc lộ 1A, Quảng Hưng, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '45 Trần Phú, Đông Hải, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '89 Hùng Vương, Ngọc Trạo, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '12 Phạm Ngũ Lão, Điện Biên, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '33 Ngô Quyền, Lam Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '67 Lý Thường Kiệt, Quảng Thắng, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '91 Hai Bà Trưng, Tân Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '54 Lê Hoàn, Đông Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '120 Nguyễn Du, Quảng Phú, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '7 Trần Hưng Đạo, Đông Hương, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '83 Đại lộ Lê Lợi, Đông Thọ, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '19 Trường Thi, Nam Ngạn, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '136 Nguyễn Huệ, Ba Đình, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '41 Phạm Bành, Quảng Hưng, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '95 Tống Duy Tân, Đông Hải, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '28 Đinh Lễ, Ngọc Trạo, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '63 Lê Lai, Điện Biên, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '108 Triệu Quốc Đạt, Lam Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '72 Nguyễn Chí Thanh, Quảng Thắng, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '14 Phan Chu Trinh, Tân Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '87 Bùi Thị Xuân, Đông Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '39 Lê Thị Hoa, Quảng Phú, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '111 Trần Khát Chân, Đông Hương, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '56 Nguyễn Thiện Thuật, Đông Thọ, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '92 Hoàng Văn Thụ, Nam Ngạn, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '23 Lê Đại Hành, Ba Đình, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '68 Đặng Thai Mai, Quảng Hưng, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '105 Trần Nhật Duật, Đông Hải, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '31 Nguyễn Khang, Ngọc Trạo, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '77 Lê Nin, Điện Biên, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '18 Phạm Hồng Thái, Lam Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '84 Nguyễn Văn Cừ, Quảng Thắng, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '49 Trần Quang Khải, Tân Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '113 Lý Nam Đế, Đông Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '27 Nguyễn Công Trứ, Quảng Phú, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '71 Đỗ Hành, Đông Hương, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '96 Lê Quý Đôn, Đông Thọ, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '42 Nguyễn Đình Chiểu, Nam Ngạn, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '88 Trần Cao Vân, Ba Đình, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '13 Đặng Tất, Quảng Hưng, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '59 Nguyễn Hoàng, Đông Hải, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '104 Phạm Đình Hổ, Ngọc Trạo, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '35 Lê Hồng Phong, Điện Biên, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '80 Trần Bình Trọng, Lam Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '16 Nguyễn Tất Thành, Quảng Thắng, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '62 Đinh Công Tráng, Tân Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '107 Lê Thánh Tông, Đông Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '43 Nguyễn Đức Cảnh, Quảng Phú, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '98 Trần Đại Nghĩa, Đông Hương, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '24 Lý Tự Trọng, Đông Thọ, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '70 Nguyễn Thị Minh Khai, Nam Ngạn, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '11 Đặng Thái Thân, Ba Đình, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '57 Nguyễn Văn Trỗi, Quảng Hưng, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '103 Phạm Văn Đồng, Đông Hải, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '38 Lê Văn Hưu, Ngọc Trạo, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '85 Trần Quốc Toản, Điện Biên, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '20 Nguyễn Trung Trực, Lam Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '66 Đặng Văn Ngữ, Quảng Thắng, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '112 Lê Anh Xuân, Tân Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '47 Nguyễn Khoa Văn, Đông Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '93 Trần Nhân Tông, Quảng Phú, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '29 Lê Đình Chinh, Đông Hương, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '74 Nguyễn Chích, Đông Thọ, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '10 Đặng Minh Khiêm, Nam Ngạn, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '55 Nguyễn Xuân Nguyên, Ba Đình, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '101 Phạm Sư Mạnh, Quảng Hưng, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '36 Lê Thế Anh, Đông Hải, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '82 Trần Văn Thời, Ngọc Trạo, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '17 Nguyễn Siêu, Điện Biên, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '63 Đặng Xuân Bảng, Lam Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '109 Nguyễn Thái Học, Quảng Thắng, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '44 Lê Đức Thọ, Tân Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '90 Trần Hữu Dực, Đông Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '25 Nguyễn Duy Hiệu, Quảng Phú, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '71 Đặng Tiến Đông, Đông Hương, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '106 Lê Bá Trinh, Đông Thọ, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '40 Nguyễn Biểu, Nam Ngạn, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '86 Trần Nguyên Hãn, Ba Đình, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '21 Đặng Huy Trứ, Quảng Hưng, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '67 Nguyễn Nhữ Lãm, Đông Hải, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '112 Phạm Ngọc Thạch, Ngọc Trạo, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '48 Lê Phụng Hiểu, Điện Biên, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '94 Trần Xuân Soạn, Lam Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '30 Nguyễn Phi Khanh, Quảng Thắng, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '76 Đặng Đình Bằng, Tân Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '121 Lê Trọng Tấn, Đông Sơn, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' },
  { fullAddress: '52 Nguyễn Hiệu, Quảng Phú, Thanh Hóa, Việt Nam', placeId: 'ChIJxY2NQs73NjERt8KpzvM9jQw' },
  { fullAddress: '97 Trần Đình Phong, Đông Hương, Thanh Hóa, Việt Nam', placeId: 'ChIJk9mPQ873NjERvL2Qz5N8pXs' },
  { fullAddress: '33 Lê Ngọc Hân, Đông Thọ, Thanh Hóa, Việt Nam', placeId: 'ChIJmQwLTeD3NjERnKpXz8M6tYc' },
  { fullAddress: '79 Nguyễn Đông Chi, Nam Ngạn, Thanh Hóa, Việt Nam', placeId: 'ChIJt8nKPsD3NjERqL5Vz9N7uQw' },
  { fullAddress: '14 Đặng Quốc Trân, Ba Đình, Thanh Hóa, Việt Nam', placeId: 'ChIJz7QwRt73NjERpM2Xy8K9vXs' },
  { fullAddress: '60 Nguyễn Xuân Toán, Quảng Hưng, Thanh Hóa, Việt Nam', placeId: 'ChIJnQwXPsD3NjERvL8Qz9M6tQw' },
  { fullAddress: '105 Phạm Hồng Thám, Đông Hải, Thanh Hóa, Việt Nam', placeId: 'ChIJk8mPQs73NjERtM2Xy5N8pXs' },
  { fullAddress: '41 Lê Thị Hồng Gấm, Ngọc Trạo, Thanh Hóa, Việt Nam', placeId: 'ChIJpV4FPvD3NjEResWuayk6kyw' }
];
