import { Routes } from '@angular/router';
import { RouterResolverService } from '@core/services/router.resolver.service';
import { canActivateCashier } from '@core/guards/auth.guard';
import { ROUTER_LINKS } from 'salehub_shared_contracts';
import { OrderFormComponent } from './order-form/components/order-form.component';

export const SalesRoutes: Routes = [
  {
    path: ROUTER_LINKS.sales.order_form,
    loadComponent: () => import('./order-form/components/order-form.component')
      .then((m) => m.OrderFormComponent),
    title: 'Đơn hàng'
  }
];
