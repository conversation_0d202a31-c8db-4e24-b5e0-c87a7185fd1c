
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1000" height="1000" viewBox="0 0 1000 1000" xml:space="preserve">
<desc>Created with Fabric.js 3.5.0</desc>
<defs>
</defs>
<g transform="matrix(2.2727 0 0 2.2727 499.9945 499.9945)" id="87057">
<rect style="stroke: rgb(0,0,0); stroke-width: 10; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(251,218,95); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" x="-200" y="-200" rx="50" ry="50" width="400" height="400"/>
</g>
<g transform="matrix(0.1867 0 0 0.1867 500.0625 500.0618)" id="498921">
<g style="" vector-effect="non-scaling-stroke">
		<g transform="matrix(1 0 0 1 -829.78 -412.04)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-1670.22, -2087.96)" d="M 1679.94 2083.41 l -14.14 -15.86 c -1.84 -2.07 -3.5 -4.27 -5.3 -6.36 l 19.44 53.54 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 182.225 -1003.595)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-2682.225, -1496.405)" d="M 2684.31 1483.78 l -6.66 -0.25 a 255.14 255.14 0 0 1 1.35 25.65 v 0.1 l 7.8 -19 C 2685.94 1488 2685.09 1485.78 2684.31 1483.78 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -57.695 -415.125)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-2442.305, -2084.875)" d="M 2445.49 2067.55 l -14.14 15.86 v 28.1 l 21.91 -53.27 C 2450.64 2061.32 2448.19 2064.53 2445.49 2067.55 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -10.605 -200.835)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-2489.395, -2299.165)" d="M 2431.35 2233.76 v 5.52 a 148.7 148.7 0 0 0 7.81 47.46 l 108.28 77.83 V 2285.9 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -967.2939 -575.84)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,209,69); fill-rule: nonzero; opacity: 1;" transform=" translate(-1532.706, -1924.16)" d="M 1416.16 2295.58 l 263.78 -112.78 v -68.07 l -19.44 -53.54 A 488.72 488.72 0 0 1 1550.92 1837 c -29.85 -7.86 -64.65 -29.38 -72.33 -77.2 c -12.57 -78.11 -23.43 -120.18 -31.37 -150.9 c -5.36 -20.73 -9.59 -37.33 -12.18 -56.16 C 1399.39 1678 1354.23 1930.62 1416.16 2295.58 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 94.0132 -647.525)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,209,69); fill-rule: nonzero; opacity: 1;" transform=" translate(-2594.0132, -1852.475)" d="M 2679 1509.28 c 0 41.82 -6 64.84 -14.94 99.64 c -7.92 30.72 -18.8 72.79 -31.37 150.89 c -7.66 47.8 -42.46 69.33 -72.31 77.19 a 488.62 488.62 0 0 1 -107.11 221.24 l -21.91 53.27 v 51 l 116.09 52.13 V 2056.45 a 32.52 32.52 0 0 1 32.51 -32.51 H 2755 c 11.94 -286.16 -43.35 -467.7 -68.24 -533.63 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -443.63 -1732.65)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,209,69); fill-rule: nonzero; opacity: 1;" transform=" translate(-2056.37, -767.35)" d="M 1746.55 822.19 a 2263.08 2263.08 0 0 1 619.64 1.16 c -87.8 -72.49 -196.15 -112 -310.54 -112 C 1941.9 711.39 1834.1 750.44 1746.55 822.19 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -444.335 -127.5)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-2055.665, -2372.5)" d="M 2374.93 2298.59 a 212.7 212.7 0 0 1 -8.6 -59.31 V 2128.74 l -153.19 92.8 a 33.86 33.86 0 0 1 -3.58 1.87 a 354.79 354.79 0 0 1 -274.66 5.6 a 32.7 32.7 0 0 1 -3.49 -1.63 L 1745 2126 v 113.3 a 212.68 212.68 0 0 1 -8.6 59.31 L 2055.66 2619 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -491.635 681.72)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(245,56,34); fill-rule: nonzero; opacity: 1;" transform=" translate(-2008.365, -3181.72)" d="M 2580 3660.52 a 32.52 32.52 0 0 1 -32.51 -32.51 V 2565.72 l -175.51 351 a 32.52 32.52 0 0 1 -49.79 10.53 l -266.49 -220 l -266.51 220 a 32.53 32.53 0 0 1 -49.79 -10.53 L 1514 2466 c -71.72 18.29 -153.16 36.73 -232.76 54.71 c -77.09 17.41 -156.81 35.43 -224.3 52.62 a 283.35 283.35 0 0 0 -205.5 205.81 L 694.9 3897.44 H 1241.3 A 228.92 228.92 0 0 1 1454 3700.81 l 110 -213.44 a 32.48 32.48 0 0 1 28.89 -17.62 h 348.85 a 32.51 32.51 0 0 1 28.75 47.71 l -96.54 182.61 h 2.87 a 229.62 229.62 0 0 1 48 5.14 c 93.4 19.91 165.87 96.68 179.49 192.23 h 922.44 V 3754 a 32.52 32.52 0 0 1 32.51 -32.51 h 262.57 v -61 Z m -874.74 -346.79 a 32.52 32.52 0 0 1 -32.51 32.51 H 1183.36 a 32.52 32.52 0 0 1 -32.51 -32.51 V 3095.79 a 32.52 32.52 0 0 1 32.51 -32.51 H 1672.7 a 32.52 32.52 0 0 1 32.51 32.51 Z m 350.43 -506.14 a 47.29 47.29 0 1 1 -47.29 47.29 A 47.29 47.29 0 0 1 2055.65 2807.59 Z m 0 205.15 a 47.29 47.29 0 1 1 -47.29 47.29 A 47.29 47.29 0 0 1 2055.65 3012.74 Z m 0 192 a 47.29 47.29 0 1 1 -47.29 47.29 A 47.29 47.29 0 0 1 2055.65 3204.76 Z m 0 202.92 a 47.29 47.29 0 1 1 -47.29 47.29 A 47.29 47.29 0 0 1 2055.65 3407.68 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -792.38 1117.425)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" points="-94.95,-82.645 -180.13,82.645 92.74,82.645 180.13,-82.645 -94.95,-82.645 "/>
</g>
		<g transform="matrix(1 0 0 1 -444.3701 -671.9377)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(251,206,156); fill-rule: nonzero; opacity: 1;" transform=" translate(-2055.6299, -1828.0623)" d="M 1579 1775.75 a 32.91 32.91 0 0 1 32.09 27.75 a 424.29 424.29 0 0 0 103.28 220.78 l 19.05 21.38 l 227.32 123.64 a 289.3 289.3 0 0 0 220.47 -4.47 l 196.61 -119.1 l 19.12 -21.45 a 424.29 424.29 0 0 0 103.28 -220.78 c 2.35 -16 17.21 -27.55 32.43 -27.75 c 7.18 -0.11 32 -2.31 35.84 -26.26 c 13.06 -81.11 24.36 -124.87 32.62 -156.83 c 8.57 -33.19 12.87 -49.85 12.87 -83.49 a 171.24 171.24 0 0 0 -2 -28 c -158.74 -5.53 -445.75 -14 -714.71 -14 c -161.42 0 -294.23 3 -397 9 c -1.61 7.24 -3 17.75 -3 33.08 c 0 33.63 4.3 50.27 12.87 83.46 c 8.26 32 19.58 75.72 32.62 156.85 C 1546.65 1773.44 1571.48 1775.64 1579 1775.75 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -709.89 100.49)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" points="217.3,62.22 -94.42,-250.59 -217.3,-162.27 -10.85,250.59 217.3,62.22 "/>
</g>
		<g transform="matrix(1 0 0 1 -178.81 100.49)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" points="217.29,-162.27 94.41,-250.59 -217.29,62.22 10.84,250.59 217.29,-162.27 "/>
</g>
		<g transform="matrix(1 0 0 1 -444.35 354.88)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="47.29"/>
</g>
		<g transform="matrix(1 0 0 1 -444.35 560.03)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="47.29"/>
</g>
		<g transform="matrix(1 0 0 1 -444.35 752.05)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="47.29"/>
</g>
		<g transform="matrix(1 0 0 1 -444.35 954.98)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="47.29"/>
</g>
		<g transform="matrix(1 0 0 1 -1071.97 704.765)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" x="-212.16" y="-76.455" rx="0" ry="0" width="424.32" height="152.91"/>
</g>
		<g transform="matrix(1 0 0 1 -1071.97 704.76)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" transform=" translate(-1428.03, -3204.76)" d="M 1183.36 3346.24 H 1672.7 a 32.52 32.52 0 0 0 32.51 -32.51 V 3095.79 a 32.52 32.52 0 0 0 -32.51 -32.51 H 1183.36 a 32.52 32.52 0 0 0 -32.51 32.51 v 217.94 A 32.52 32.52 0 0 0 1183.36 3346.24 Z m 32.51 -217.94 h 424.32 v 152.91 H 1215.87 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -447.62 -1460.5213)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(245,56,34); fill-rule: nonzero; opacity: 1;" transform=" translate(-2052.38, -1039.4787)" d="M 1521.14 931.6 v 281.18 c 347.72 -36.39 714.76 -36.39 1062.48 0 V 931.6 C 2236.37 844.37 1868.38 844.37 1521.14 931.6 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -442.68 -1165.6957)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(245,56,34); fill-rule: nonzero; opacity: 1;" transform=" translate(-2057.32, -1334.3044)" d="M 1897.25 1402.11 c 311.77 0 647.79 11.36 781.49 16.38 l -82.34 -139.33 c -355.06 -38.69 -732.13 -38.72 -1087.22 -0.1 l -73.28 136.21 C 1546.67 1406.53 1701.39 1402.11 1897.25 1402.11 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 0 1625.54)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" points="559.18,-163.07 -425.42,-163.07 -425.84,-163.07 -1228.75,-163.07 -1809.97,-163.07 -1809.97,163.07 1809.97,163.07 1809.97,-163.07 1348.38,-163.07 559.18,-163.07 "/>
</g>
		<g transform="matrix(1 0 0 1 0.01 -0.005)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" transform=" translate(-2500.01, -2499.995)" d="M 657.51 4353.64 h 3685 a 32.52 32.52 0 0 0 32.51 -32.51 V 3930 a 32.52 32.52 0 0 0 -32.51 -32.51 h -461.6 V 3754 a 32.52 32.52 0 0 0 -32.51 -32.51 h -242 v -61 h 736.11 A 32.52 32.52 0 0 0 4375 3628 V 2056.45 a 32.52 32.52 0 0 0 -32.51 -32.51 H 3401 V 1732.11 h 194 a 32.52 32.52 0 0 0 32.51 -32.51 V 1459.4 a 32.52 32.52 0 0 0 -32.51 -32.51 H 2922.43 a 32.52 32.52 0 0 0 -32.51 32.51 v 240.2 a 32.52 32.52 0 0 0 32.51 32.51 h 221.4 v 291.83 h -323.5 c 11.59 -277.85 -38.06 -460.9 -67.23 -542.34 a 32.38 32.38 0 0 0 12 -44.8 l -116.45 -197 V 906.39 a 32.52 32.52 0 0 0 -24.24 -31.45 q -72.88 -19.2 -146.9 -33.31 q -11.47 -13.3 -23.67 -25.93 c -106.16 -109.21 -247.56 -169.35 -398.17 -169.35 s -292 60.14 -398.2 169.35 q -11.63 12 -22.55 24.57 c -51.89 9.77 -103.47 21.21 -154.57 34.67 a 32.52 32.52 0 0 0 -24.24 31.45 v 334 l -106.24 197.51 a 32.52 32.52 0 0 0 28.64 47.91 a 31.91 31.91 0 0 0 3.62 -0.2 c 1.79 -0.2 3.74 -0.39 5.56 -0.58 c -39.67 112.52 -113.87 405.34 -28.32 862.47 a 32.53 32.53 0 0 0 44.75 23.92 l 274.6 -117.4 a 149 149 0 0 1 -6.59 32.68 L 1517.34 2398 c -75.23 19.68 -163.87 39.76 -250.42 59.32 c -77.52 17.52 -157.68 35.63 -226 53 a 348.28 348.28 0 0 0 -253 254.91 c -0.25 1.07 -0.47 2.15 -0.61 3.23 l -162 1157 c -0.11 0.75 0 1.48 -0.08 2.22 s -0.23 1.5 -0.23 2.29 v 391.17 A 32.52 32.52 0 0 0 657.51 4353.64 Z m 1089 -3531.45 c 87.55 -71.75 195.35 -110.8 309.1 -110.8 c 114.38 0 222.73 39.46 310.54 112 A 2263.08 2263.08 0 0 0 1746.55 822.19 Z M 2583.61 931.6 v 281.18 c -347.72 -36.39 -714.76 -36.39 -1062.48 0 V 931.6 C 1868.38 844.37 2236.37 844.37 2583.61 931.6 Z M 2755 2023.94 H 2580 a 32.52 32.52 0 0 0 -32.51 32.51 v 158.17 l -116.09 -52.13 v -79.08 l 14.14 -15.86 c 2.7 -3 5.15 -6.23 7.77 -9.32 A 488.62 488.62 0 0 0 2560.37 1837 c 29.85 -7.86 64.65 -29.39 72.31 -77.19 c 12.57 -78.1 23.45 -120.17 31.37 -150.89 c 9 -34.8 14.93 -57.82 14.94 -99.64 v -0.1 a 255.14 255.14 0 0 0 -1.35 -25.65 l 6.66 0.25 c 0.78 2 1.63 4.25 2.49 6.53 C 2711.69 1556.24 2767 1737.77 2755 2023.94 Z m 453.82 -291.83 H 3336 v 291.83 H 3208.86 Z m -253.91 -240.2 h 607.51 v 175.17 H 2954.95 Z M 3176.35 2089 H 4310 V 3595.49 H 3606.38 V 3344.83 H 3969 a 32.52 32.52 0 0 0 32.51 -32.51 V 2372.13 a 32.52 32.52 0 0 0 -32.51 -32.51 H 2953.45 a 32.52 32.52 0 0 0 -32.51 32.51 v 940.18 a 32.52 32.52 0 0 0 32.51 32.51 h 368.31 v 250.66 H 2612.47 V 2089 Z M 1468.6 3765.1 h 408.19 c 79.78 0 146.51 57 161.64 132.34 H 1307 C 1322.11 3822.05 1388.84 3765.1 1468.6 3765.1 Z m 58.89 -65 l 85.18 -165.29 h 275.08 l -87.39 165.29 Z M 4310 4288.61 H 690 V 3962.47 H 4310 Z m -494.11 -391.17 H 3091.7 V 3786.52 h 724.17 Z M 1572.81 2438.22 l 122.88 -88.32 l 311.72 312.81 l -228.15 188.37 Z m -63.63 -1159.15 c 355.1 -38.62 732.17 -38.59 1087.22 0.1 l 82.34 139.33 c -133.7 -5 -469.72 -16.38 -781.49 -16.38 c -195.87 0 -350.59 4.42 -461.36 13.17 Z m 1 313.57 c -8.57 -33.18 -12.87 -49.83 -12.87 -83.46 c 0 -15.33 1.36 -25.85 3 -33.08 c 102.75 -5.95 235.56 -9 397 -9 c 269 0 556 8.48 714.71 14 a 171.24 171.24 0 0 1 2 28 c 0 33.64 -4.3 50.3 -12.87 83.49 c -8.26 32 -19.56 75.72 -32.62 156.83 c -3.83 24 -28.66 26.15 -35.84 26.26 c -15.22 0.2 -30.08 11.72 -32.43 27.75 a 424.29 424.29 0 0 1 -103.28 220.78 l -19.12 21.45 l -196.61 119.1 a 289.3 289.3 0 0 1 -220.47 4.47 l -227.32 -123.64 l -19.05 -21.38 a 424.29 424.29 0 0 1 -103.28 -220.78 a 32.91 32.91 0 0 0 -32.09 -27.75 c -7.51 -0.11 -32.35 -2.31 -36.2 -26.27 C 1529.75 1668.35 1518.43 1624.59 1510.17 1592.63 Z m 929 694.1 a 148.7 148.7 0 0 1 -7.81 -47.46 v -5.52 l 116.09 52.14 v 78.67 Z m -23.56 63.16 l 122.88 88.32 L 2332 2851.08 L 2103.9 2662.71 Z M 1745 2239.28 V 2126 l 186.43 101.4 a 32.7 32.7 0 0 0 3.49 1.63 a 354.79 354.79 0 0 0 274.66 -5.6 a 33.86 33.86 0 0 0 3.58 -1.87 l 153.19 -92.8 v 110.54 a 212.7 212.7 0 0 0 8.6 59.31 L 2055.66 2619 l -319.29 -320.4 A 212.68 212.68 0 0 0 1745 2239.28 Z m 1241 165.37 h 950.5 V 3279.8 H 3606.38 V 2876.54 a 32.52 32.52 0 0 0 -32.51 -32.51 h -219.6 a 32.52 32.52 0 0 0 -32.51 32.51 V 3279.8 H 2986 Z m 400.82 1316.85 V 2909.06 h 154.57 v 812.44 Z M 1435 1552.74 c 2.58 18.84 6.82 35.43 12.18 56.16 c 7.94 30.72 18.8 72.79 31.37 150.9 c 7.68 47.82 42.48 69.34 72.33 77.2 a 488.72 488.72 0 0 0 109.58 224.19 c 1.8 2.09 3.46 4.29 5.3 6.36 l 14.14 15.86 v 99.39 l -263.78 112.78 C 1354.23 1930.62 1399.39 1678 1435 1552.74 Z m -153.78 968 c 79.59 -18 161 -36.42 232.76 -54.71 l 225.35 450.67 a 32.52 32.52 0 0 0 49.79 10.53 l 266.51 -220 l 266.49 220 a 32.53 32.53 0 0 0 49.79 -10.53 l 175.51 -351 V 3628 a 32.52 32.52 0 0 0 32.51 32.51 h 741.8 v 61 H 3059.18 a 32.52 32.52 0 0 0 -32.51 32.51 v 143.44 H 2104.22 c -13.62 -95.55 -86.08 -172.32 -179.49 -192.23 a 229.62 229.62 0 0 0 -48 -5.14 h -2.87 l 96.54 -182.61 a 32.51 32.51 0 0 0 -28.75 -47.71 H 1592.86 a 32.48 32.48 0 0 0 -28.89 17.62 l -110 213.44 a 228.92 228.92 0 0 0 -212.67 196.63 H 694.9 L 851.46 2779.19 A 283.35 283.35 0 0 1 1057 2573.38 C 1124.44 2556.18 1204.16 2538.17 1281.26 2520.76 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 961.235 342.245)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" transform=" translate(-3461.235, -2842.245)" d="M 2612.47 2089 V 3595.49 h 709.28 V 3344.83 H 2953.45 a 32.52 32.52 0 0 1 -32.51 -32.51 V 2372.13 a 32.52 32.52 0 0 1 32.51 -32.51 H 3969 a 32.52 32.52 0 0 1 32.51 32.51 v 940.18 a 32.52 32.52 0 0 1 -32.51 32.51 H 3606.38 v 250.66 H 4310 V 2089 H 2612.47 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 961.225 342.225)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" transform=" translate(-3461.225, -2842.225)" d="M 3321.75 2876.54 a 32.52 32.52 0 0 1 32.51 -32.51 h 219.6 a 32.52 32.52 0 0 1 32.51 32.51 V 3279.8 h 330.08 V 2404.65 H 2986 V 3279.8 h 335.79 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 964.065 815.28)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(87,84,84); fill-rule: nonzero; opacity: 1;" x="-77.285" y="-406.22" rx="0" ry="0" width="154.57" height="812.44"/>
</g>
		<g transform="matrix(1 0 0 1 953.785 1341.98)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(87,84,84); fill-rule: nonzero; opacity: 1;" x="-362.085" y="-55.46" rx="0" ry="0" width="724.17" height="110.92"/>
</g>
		<g transform="matrix(1 0 0 1 772.42 -621.975)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" x="-63.56" y="-145.915" rx="0" ry="0" width="127.12" height="291.83"/>
</g>
		<g transform="matrix(1 0 0 1 758.705 -920.505)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" transform=" translate(-3258.705, -1579.495)" d="M 3562.46 1667.08 V 1491.91 H 2954.95 v 175.17 Z m -75.7 -28.32 H 3382.68 V 1601 h 104.07 Z m -104.07 -113.83 h 104.07 v 37.75 H 3382.68 Z m -387 0 h 242.12 v 37.75 H 2995.64 Z m 0 76.09 h 242.12 v 37.75 H 2995.64 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -827.295 1331.27)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(87,84,84); fill-rule: nonzero; opacity: 1;" transform=" translate(-1672.705, -3831.27)" d="M 2038.43 3897.44 c -15.14 -75.39 -81.86 -132.34 -161.64 -132.34 H 1468.6 c -79.76 0 -146.48 57 -161.62 132.34 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 616.7 -956.195)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" x="-121.06" y="-18.875" rx="0" ry="0" width="242.12" height="37.75"/>
</g>
		<g transform="matrix(1 0 0 1 616.7 -880.105)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" x="-121.06" y="-18.875" rx="0" ry="0" width="242.12" height="37.75"/>
</g>
		<g transform="matrix(1 0 0 1 934.715 -956.195)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" x="-52.035" y="-18.875" rx="0" ry="0" width="104.07" height="37.75"/>
</g>
		<g transform="matrix(1 0 0 1 934.715 -880.105)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" x="-52.035" y="-18.875" rx="0" ry="0" width="104.07" height="37.75"/>
</g>
</g>
</g>
<g transform="matrix(0.1867 0 0 0.1867 500.0625 500.0618)" id="329202">
<g style="" vector-effect="non-scaling-stroke">
		<g transform="matrix(1 0 0 1 -829.78 -412.04)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-1670.22, -2087.96)" d="M 1679.94 2083.41 l -14.14 -15.86 c -1.84 -2.07 -3.5 -4.27 -5.3 -6.36 l 19.44 53.54 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 182.225 -1003.595)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-2682.225, -1496.405)" d="M 2684.31 1483.78 l -6.66 -0.25 a 255.14 255.14 0 0 1 1.35 25.65 v 0.1 l 7.8 -19 C 2685.94 1488 2685.09 1485.78 2684.31 1483.78 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -57.695 -415.125)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-2442.305, -2084.875)" d="M 2445.49 2067.55 l -14.14 15.86 v 28.1 l 21.91 -53.27 C 2450.64 2061.32 2448.19 2064.53 2445.49 2067.55 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -10.605 -200.835)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-2489.395, -2299.165)" d="M 2431.35 2233.76 v 5.52 a 148.7 148.7 0 0 0 7.81 47.46 l 108.28 77.83 V 2285.9 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -967.2939 -575.84)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,209,69); fill-rule: nonzero; opacity: 1;" transform=" translate(-1532.706, -1924.16)" d="M 1416.16 2295.58 l 263.78 -112.78 v -68.07 l -19.44 -53.54 A 488.72 488.72 0 0 1 1550.92 1837 c -29.85 -7.86 -64.65 -29.38 -72.33 -77.2 c -12.57 -78.11 -23.43 -120.18 -31.37 -150.9 c -5.36 -20.73 -9.59 -37.33 -12.18 -56.16 C 1399.39 1678 1354.23 1930.62 1416.16 2295.58 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 94.0132 -647.525)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,209,69); fill-rule: nonzero; opacity: 1;" transform=" translate(-2594.0132, -1852.475)" d="M 2679 1509.28 c 0 41.82 -6 64.84 -14.94 99.64 c -7.92 30.72 -18.8 72.79 -31.37 150.89 c -7.66 47.8 -42.46 69.33 -72.31 77.19 a 488.62 488.62 0 0 1 -107.11 221.24 l -21.91 53.27 v 51 l 116.09 52.13 V 2056.45 a 32.52 32.52 0 0 1 32.51 -32.51 H 2755 c 11.94 -286.16 -43.35 -467.7 -68.24 -533.63 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -443.63 -1732.65)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,209,69); fill-rule: nonzero; opacity: 1;" transform=" translate(-2056.37, -767.35)" d="M 1746.55 822.19 a 2263.08 2263.08 0 0 1 619.64 1.16 c -87.8 -72.49 -196.15 -112 -310.54 -112 C 1941.9 711.39 1834.1 750.44 1746.55 822.19 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -444.335 -127.5)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-2055.665, -2372.5)" d="M 2374.93 2298.59 a 212.7 212.7 0 0 1 -8.6 -59.31 V 2128.74 l -153.19 92.8 a 33.86 33.86 0 0 1 -3.58 1.87 a 354.79 354.79 0 0 1 -274.66 5.6 a 32.7 32.7 0 0 1 -3.49 -1.63 L 1745 2126 v 113.3 a 212.68 212.68 0 0 1 -8.6 59.31 L 2055.66 2619 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -491.635 681.72)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(245,56,34); fill-rule: nonzero; opacity: 1;" transform=" translate(-2008.365, -3181.72)" d="M 2580 3660.52 a 32.52 32.52 0 0 1 -32.51 -32.51 V 2565.72 l -175.51 351 a 32.52 32.52 0 0 1 -49.79 10.53 l -266.49 -220 l -266.51 220 a 32.53 32.53 0 0 1 -49.79 -10.53 L 1514 2466 c -71.72 18.29 -153.16 36.73 -232.76 54.71 c -77.09 17.41 -156.81 35.43 -224.3 52.62 a 283.35 283.35 0 0 0 -205.5 205.81 L 694.9 3897.44 H 1241.3 A 228.92 228.92 0 0 1 1454 3700.81 l 110 -213.44 a 32.48 32.48 0 0 1 28.89 -17.62 h 348.85 a 32.51 32.51 0 0 1 28.75 47.71 l -96.54 182.61 h 2.87 a 229.62 229.62 0 0 1 48 5.14 c 93.4 19.91 165.87 96.68 179.49 192.23 h 922.44 V 3754 a 32.52 32.52 0 0 1 32.51 -32.51 h 262.57 v -61 Z m -874.74 -346.79 a 32.52 32.52 0 0 1 -32.51 32.51 H 1183.36 a 32.52 32.52 0 0 1 -32.51 -32.51 V 3095.79 a 32.52 32.52 0 0 1 32.51 -32.51 H 1672.7 a 32.52 32.52 0 0 1 32.51 32.51 Z m 350.43 -506.14 a 47.29 47.29 0 1 1 -47.29 47.29 A 47.29 47.29 0 0 1 2055.65 2807.59 Z m 0 205.15 a 47.29 47.29 0 1 1 -47.29 47.29 A 47.29 47.29 0 0 1 2055.65 3012.74 Z m 0 192 a 47.29 47.29 0 1 1 -47.29 47.29 A 47.29 47.29 0 0 1 2055.65 3204.76 Z m 0 202.92 a 47.29 47.29 0 1 1 -47.29 47.29 A 47.29 47.29 0 0 1 2055.65 3407.68 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -792.38 1117.425)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" points="-94.95,-82.645 -180.13,82.645 92.74,82.645 180.13,-82.645 -94.95,-82.645 "/>
</g>
		<g transform="matrix(1 0 0 1 -444.3701 -671.9377)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(251,206,156); fill-rule: nonzero; opacity: 1;" transform=" translate(-2055.6299, -1828.0623)" d="M 1579 1775.75 a 32.91 32.91 0 0 1 32.09 27.75 a 424.29 424.29 0 0 0 103.28 220.78 l 19.05 21.38 l 227.32 123.64 a 289.3 289.3 0 0 0 220.47 -4.47 l 196.61 -119.1 l 19.12 -21.45 a 424.29 424.29 0 0 0 103.28 -220.78 c 2.35 -16 17.21 -27.55 32.43 -27.75 c 7.18 -0.11 32 -2.31 35.84 -26.26 c 13.06 -81.11 24.36 -124.87 32.62 -156.83 c 8.57 -33.19 12.87 -49.85 12.87 -83.49 a 171.24 171.24 0 0 0 -2 -28 c -158.74 -5.53 -445.75 -14 -714.71 -14 c -161.42 0 -294.23 3 -397 9 c -1.61 7.24 -3 17.75 -3 33.08 c 0 33.63 4.3 50.27 12.87 83.46 c 8.26 32 19.58 75.72 32.62 156.85 C 1546.65 1773.44 1571.48 1775.64 1579 1775.75 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -709.89 100.49)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" points="217.3,62.22 -94.42,-250.59 -217.3,-162.27 -10.85,250.59 217.3,62.22 "/>
</g>
		<g transform="matrix(1 0 0 1 -178.81 100.49)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" points="217.29,-162.27 94.41,-250.59 -217.29,62.22 10.84,250.59 217.29,-162.27 "/>
</g>
		<g transform="matrix(1 0 0 1 -444.35 354.88)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="47.29"/>
</g>
		<g transform="matrix(1 0 0 1 -444.35 560.03)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="47.29"/>
</g>
		<g transform="matrix(1 0 0 1 -444.35 752.05)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="47.29"/>
</g>
		<g transform="matrix(1 0 0 1 -444.35 954.98)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="47.29"/>
</g>
		<g transform="matrix(1 0 0 1 -1071.97 704.765)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" x="-212.16" y="-76.455" rx="0" ry="0" width="424.32" height="152.91"/>
</g>
		<g transform="matrix(1 0 0 1 -1071.97 704.76)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" transform=" translate(-1428.03, -3204.76)" d="M 1183.36 3346.24 H 1672.7 a 32.52 32.52 0 0 0 32.51 -32.51 V 3095.79 a 32.52 32.52 0 0 0 -32.51 -32.51 H 1183.36 a 32.52 32.52 0 0 0 -32.51 32.51 v 217.94 A 32.52 32.52 0 0 0 1183.36 3346.24 Z m 32.51 -217.94 h 424.32 v 152.91 H 1215.87 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -447.62 -1460.5213)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(245,56,34); fill-rule: nonzero; opacity: 1;" transform=" translate(-2052.38, -1039.4787)" d="M 1521.14 931.6 v 281.18 c 347.72 -36.39 714.76 -36.39 1062.48 0 V 931.6 C 2236.37 844.37 1868.38 844.37 1521.14 931.6 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -442.68 -1165.6957)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(245,56,34); fill-rule: nonzero; opacity: 1;" transform=" translate(-2057.32, -1334.3044)" d="M 1897.25 1402.11 c 311.77 0 647.79 11.36 781.49 16.38 l -82.34 -139.33 c -355.06 -38.69 -732.13 -38.72 -1087.22 -0.1 l -73.28 136.21 C 1546.67 1406.53 1701.39 1402.11 1897.25 1402.11 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 0 1625.54)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" points="559.18,-163.07 -425.42,-163.07 -425.84,-163.07 -1228.75,-163.07 -1809.97,-163.07 -1809.97,163.07 1809.97,163.07 1809.97,-163.07 1348.38,-163.07 559.18,-163.07 "/>
</g>
		<g transform="matrix(1 0 0 1 0.01 -0.005)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" transform=" translate(-2500.01, -2499.995)" d="M 657.51 4353.64 h 3685 a 32.52 32.52 0 0 0 32.51 -32.51 V 3930 a 32.52 32.52 0 0 0 -32.51 -32.51 h -461.6 V 3754 a 32.52 32.52 0 0 0 -32.51 -32.51 h -242 v -61 h 736.11 A 32.52 32.52 0 0 0 4375 3628 V 2056.45 a 32.52 32.52 0 0 0 -32.51 -32.51 H 3401 V 1732.11 h 194 a 32.52 32.52 0 0 0 32.51 -32.51 V 1459.4 a 32.52 32.52 0 0 0 -32.51 -32.51 H 2922.43 a 32.52 32.52 0 0 0 -32.51 32.51 v 240.2 a 32.52 32.52 0 0 0 32.51 32.51 h 221.4 v 291.83 h -323.5 c 11.59 -277.85 -38.06 -460.9 -67.23 -542.34 a 32.38 32.38 0 0 0 12 -44.8 l -116.45 -197 V 906.39 a 32.52 32.52 0 0 0 -24.24 -31.45 q -72.88 -19.2 -146.9 -33.31 q -11.47 -13.3 -23.67 -25.93 c -106.16 -109.21 -247.56 -169.35 -398.17 -169.35 s -292 60.14 -398.2 169.35 q -11.63 12 -22.55 24.57 c -51.89 9.77 -103.47 21.21 -154.57 34.67 a 32.52 32.52 0 0 0 -24.24 31.45 v 334 l -106.24 197.51 a 32.52 32.52 0 0 0 28.64 47.91 a 31.91 31.91 0 0 0 3.62 -0.2 c 1.79 -0.2 3.74 -0.39 5.56 -0.58 c -39.67 112.52 -113.87 405.34 -28.32 862.47 a 32.53 32.53 0 0 0 44.75 23.92 l 274.6 -117.4 a 149 149 0 0 1 -6.59 32.68 L 1517.34 2398 c -75.23 19.68 -163.87 39.76 -250.42 59.32 c -77.52 17.52 -157.68 35.63 -226 53 a 348.28 348.28 0 0 0 -253 254.91 c -0.25 1.07 -0.47 2.15 -0.61 3.23 l -162 1157 c -0.11 0.75 0 1.48 -0.08 2.22 s -0.23 1.5 -0.23 2.29 v 391.17 A 32.52 32.52 0 0 0 657.51 4353.64 Z m 1089 -3531.45 c 87.55 -71.75 195.35 -110.8 309.1 -110.8 c 114.38 0 222.73 39.46 310.54 112 A 2263.08 2263.08 0 0 0 1746.55 822.19 Z M 2583.61 931.6 v 281.18 c -347.72 -36.39 -714.76 -36.39 -1062.48 0 V 931.6 C 1868.38 844.37 2236.37 844.37 2583.61 931.6 Z M 2755 2023.94 H 2580 a 32.52 32.52 0 0 0 -32.51 32.51 v 158.17 l -116.09 -52.13 v -79.08 l 14.14 -15.86 c 2.7 -3 5.15 -6.23 7.77 -9.32 A 488.62 488.62 0 0 0 2560.37 1837 c 29.85 -7.86 64.65 -29.39 72.31 -77.19 c 12.57 -78.1 23.45 -120.17 31.37 -150.89 c 9 -34.8 14.93 -57.82 14.94 -99.64 v -0.1 a 255.14 255.14 0 0 0 -1.35 -25.65 l 6.66 0.25 c 0.78 2 1.63 4.25 2.49 6.53 C 2711.69 1556.24 2767 1737.77 2755 2023.94 Z m 453.82 -291.83 H 3336 v 291.83 H 3208.86 Z m -253.91 -240.2 h 607.51 v 175.17 H 2954.95 Z M 3176.35 2089 H 4310 V 3595.49 H 3606.38 V 3344.83 H 3969 a 32.52 32.52 0 0 0 32.51 -32.51 V 2372.13 a 32.52 32.52 0 0 0 -32.51 -32.51 H 2953.45 a 32.52 32.52 0 0 0 -32.51 32.51 v 940.18 a 32.52 32.52 0 0 0 32.51 32.51 h 368.31 v 250.66 H 2612.47 V 2089 Z M 1468.6 3765.1 h 408.19 c 79.78 0 146.51 57 161.64 132.34 H 1307 C 1322.11 3822.05 1388.84 3765.1 1468.6 3765.1 Z m 58.89 -65 l 85.18 -165.29 h 275.08 l -87.39 165.29 Z M 4310 4288.61 H 690 V 3962.47 H 4310 Z m -494.11 -391.17 H 3091.7 V 3786.52 h 724.17 Z M 1572.81 2438.22 l 122.88 -88.32 l 311.72 312.81 l -228.15 188.37 Z m -63.63 -1159.15 c 355.1 -38.62 732.17 -38.59 1087.22 0.1 l 82.34 139.33 c -133.7 -5 -469.72 -16.38 -781.49 -16.38 c -195.87 0 -350.59 4.42 -461.36 13.17 Z m 1 313.57 c -8.57 -33.18 -12.87 -49.83 -12.87 -83.46 c 0 -15.33 1.36 -25.85 3 -33.08 c 102.75 -5.95 235.56 -9 397 -9 c 269 0 556 8.48 714.71 14 a 171.24 171.24 0 0 1 2 28 c 0 33.64 -4.3 50.3 -12.87 83.49 c -8.26 32 -19.56 75.72 -32.62 156.83 c -3.83 24 -28.66 26.15 -35.84 26.26 c -15.22 0.2 -30.08 11.72 -32.43 27.75 a 424.29 424.29 0 0 1 -103.28 220.78 l -19.12 21.45 l -196.61 119.1 a 289.3 289.3 0 0 1 -220.47 4.47 l -227.32 -123.64 l -19.05 -21.38 a 424.29 424.29 0 0 1 -103.28 -220.78 a 32.91 32.91 0 0 0 -32.09 -27.75 c -7.51 -0.11 -32.35 -2.31 -36.2 -26.27 C 1529.75 1668.35 1518.43 1624.59 1510.17 1592.63 Z m 929 694.1 a 148.7 148.7 0 0 1 -7.81 -47.46 v -5.52 l 116.09 52.14 v 78.67 Z m -23.56 63.16 l 122.88 88.32 L 2332 2851.08 L 2103.9 2662.71 Z M 1745 2239.28 V 2126 l 186.43 101.4 a 32.7 32.7 0 0 0 3.49 1.63 a 354.79 354.79 0 0 0 274.66 -5.6 a 33.86 33.86 0 0 0 3.58 -1.87 l 153.19 -92.8 v 110.54 a 212.7 212.7 0 0 0 8.6 59.31 L 2055.66 2619 l -319.29 -320.4 A 212.68 212.68 0 0 0 1745 2239.28 Z m 1241 165.37 h 950.5 V 3279.8 H 3606.38 V 2876.54 a 32.52 32.52 0 0 0 -32.51 -32.51 h -219.6 a 32.52 32.52 0 0 0 -32.51 32.51 V 3279.8 H 2986 Z m 400.82 1316.85 V 2909.06 h 154.57 v 812.44 Z M 1435 1552.74 c 2.58 18.84 6.82 35.43 12.18 56.16 c 7.94 30.72 18.8 72.79 31.37 150.9 c 7.68 47.82 42.48 69.34 72.33 77.2 a 488.72 488.72 0 0 0 109.58 224.19 c 1.8 2.09 3.46 4.29 5.3 6.36 l 14.14 15.86 v 99.39 l -263.78 112.78 C 1354.23 1930.62 1399.39 1678 1435 1552.74 Z m -153.78 968 c 79.59 -18 161 -36.42 232.76 -54.71 l 225.35 450.67 a 32.52 32.52 0 0 0 49.79 10.53 l 266.51 -220 l 266.49 220 a 32.53 32.53 0 0 0 49.79 -10.53 l 175.51 -351 V 3628 a 32.52 32.52 0 0 0 32.51 32.51 h 741.8 v 61 H 3059.18 a 32.52 32.52 0 0 0 -32.51 32.51 v 143.44 H 2104.22 c -13.62 -95.55 -86.08 -172.32 -179.49 -192.23 a 229.62 229.62 0 0 0 -48 -5.14 h -2.87 l 96.54 -182.61 a 32.51 32.51 0 0 0 -28.75 -47.71 H 1592.86 a 32.48 32.48 0 0 0 -28.89 17.62 l -110 213.44 a 228.92 228.92 0 0 0 -212.67 196.63 H 694.9 L 851.46 2779.19 A 283.35 283.35 0 0 1 1057 2573.38 C 1124.44 2556.18 1204.16 2538.17 1281.26 2520.76 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 961.235 342.245)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" transform=" translate(-3461.235, -2842.245)" d="M 2612.47 2089 V 3595.49 h 709.28 V 3344.83 H 2953.45 a 32.52 32.52 0 0 1 -32.51 -32.51 V 2372.13 a 32.52 32.52 0 0 1 32.51 -32.51 H 3969 a 32.52 32.52 0 0 1 32.51 32.51 v 940.18 a 32.52 32.52 0 0 1 -32.51 32.51 H 3606.38 v 250.66 H 4310 V 2089 H 2612.47 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 961.225 342.225)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" transform=" translate(-3461.225, -2842.225)" d="M 3321.75 2876.54 a 32.52 32.52 0 0 1 32.51 -32.51 h 219.6 a 32.52 32.52 0 0 1 32.51 32.51 V 3279.8 h 330.08 V 2404.65 H 2986 V 3279.8 h 335.79 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 964.065 815.28)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(87,84,84); fill-rule: nonzero; opacity: 1;" x="-77.285" y="-406.22" rx="0" ry="0" width="154.57" height="812.44"/>
</g>
		<g transform="matrix(1 0 0 1 953.785 1341.98)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(87,84,84); fill-rule: nonzero; opacity: 1;" x="-362.085" y="-55.46" rx="0" ry="0" width="724.17" height="110.92"/>
</g>
		<g transform="matrix(1 0 0 1 772.42 -621.975)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" x="-63.56" y="-145.915" rx="0" ry="0" width="127.12" height="291.83"/>
</g>
		<g transform="matrix(1 0 0 1 758.705 -920.505)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(198,198,198); fill-rule: nonzero; opacity: 1;" transform=" translate(-3258.705, -1579.495)" d="M 3562.46 1667.08 V 1491.91 H 2954.95 v 175.17 Z m -75.7 -28.32 H 3382.68 V 1601 h 104.07 Z m -104.07 -113.83 h 104.07 v 37.75 H 3382.68 Z m -387 0 h 242.12 v 37.75 H 2995.64 Z m 0 76.09 h 242.12 v 37.75 H 2995.64 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -827.295 1331.27)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(87,84,84); fill-rule: nonzero; opacity: 1;" transform=" translate(-1672.705, -3831.27)" d="M 2038.43 3897.44 c -15.14 -75.39 -81.86 -132.34 -161.64 -132.34 H 1468.6 c -79.76 0 -146.48 57 -161.62 132.34 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 616.7 -956.195)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" x="-121.06" y="-18.875" rx="0" ry="0" width="242.12" height="37.75"/>
</g>
		<g transform="matrix(1 0 0 1 616.7 -880.105)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" x="-121.06" y="-18.875" rx="0" ry="0" width="242.12" height="37.75"/>
</g>
		<g transform="matrix(1 0 0 1 934.715 -956.195)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" x="-52.035" y="-18.875" rx="0" ry="0" width="104.07" height="37.75"/>
</g>
		<g transform="matrix(1 0 0 1 934.715 -880.105)" id="&#xd1;&#xeb;&#xee;&#xe9;_1">
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(38,38,38); fill-rule: nonzero; opacity: 1;" x="-52.035" y="-18.875" rx="0" ry="0" width="104.07" height="37.75"/>
</g>
</g>
</g>
</svg>
