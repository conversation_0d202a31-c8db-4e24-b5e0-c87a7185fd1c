<!-- Header v<PERSON><PERSON> tiêu đề động -->
<h2 mat-dialog-title>
  {{ 'FIELD_PERMISSION_MODAL.TITLE' | translate: { fieldName: fieldName() } }}
</h2>

<!-- Nội dung modal -->
<mat-dialog-content class="field-permission-content">
  <div class="table-responsive">
    <table class="table table-bordered table-hover">
      <!-- Header của table -->
      <thead class="table-light">
        <tr>
          <th scope="col" class="profile-column">
            {{ 'FIELD_PERMISSION_MODAL.PROFILES' | translate }}
          </th>
          <th scope="col" class="permission-column text-center">
            <div class="d-flex flex-column align-items-center">
              <span class="mb-2">{{ 'FIELD_PERMISSION_MODAL.READ_WRITE' | translate }}</span>
              <button 
                type="button" 
                class="btn btn-sm btn-outline-primary"
                (click)="setAllReadWrite()"
                [attr.aria-label]="'FIELD_PERMISSION_MODAL.SET_ALL_READ_WRITE' | translate">
                {{ 'FIELD_PERMISSION_MODAL.ALL' | translate }}
              </button>
            </div>
          </th>
          <th scope="col" class="permission-column text-center">
            <div class="d-flex flex-column align-items-center">
              <span class="mb-2">{{ 'FIELD_PERMISSION_MODAL.READ_ONLY' | translate }}</span>
              <button 
                type="button" 
                class="btn btn-sm btn-outline-secondary"
                (click)="setAllReadOnly()"
                [attr.aria-label]="'FIELD_PERMISSION_MODAL.SET_ALL_READ_ONLY' | translate">
                {{ 'FIELD_PERMISSION_MODAL.ALL' | translate }}
              </button>
            </div>
          </th>
          <th scope="col" class="permission-column text-center">
            <div class="d-flex flex-column align-items-center">
              <span class="mb-2">{{ 'FIELD_PERMISSION_MODAL.DONT_SHOW' | translate }}</span>
              <button 
                type="button" 
                class="btn btn-sm btn-outline-danger"
                (click)="setAllDontShow()"
                [attr.aria-label]="'FIELD_PERMISSION_MODAL.SET_ALL_DONT_SHOW' | translate">
                {{ 'FIELD_PERMISSION_MODAL.ALL' | translate }}
              </button>
            </div>
          </th>
        </tr>
      </thead>
      
      <!-- Body của table -->
      <tbody>
        <tr *ngFor="let profile of profiles(); trackBy: trackByProfileId">
          <!-- Cột tên profile -->
          <td class="profile-name">
            <strong>{{ profile.name }}</strong>
          </td>
          
          <!-- Cột Read & Write -->
          <td class="text-center">
            <div class="form-check d-flex justify-content-center">
              <input 
                class="form-check-input" 
                type="radio" 
                [name]="'permission_' + profile._id"
                [id]="'read_write_' + profile._id"
                [checked]="profile.permission === 'read_write'"
                (change)="onPermissionChange(profile._id, 'read_write')"
                [attr.aria-label]="'FIELD_PERMISSION_MODAL.READ_WRITE_FOR' | translate: { profileName: profile.name }">
            </div>
          </td>
          
          <!-- Cột Read Only -->
          <td class="text-center">
            <div class="form-check d-flex justify-content-center">
              <input 
                class="form-check-input" 
                type="radio" 
                [name]="'permission_' + profile._id"
                [id]="'read_' + profile._id"
                [checked]="profile.permission === 'read'"
                (change)="onPermissionChange(profile._id, 'read')"
                [attr.aria-label]="'FIELD_PERMISSION_MODAL.READ_ONLY_FOR' | translate: { profileName: profile.name }">
            </div>
          </td>
          
          <!-- Cột Don't Show -->
          <td class="text-center">
            <div class="form-check d-flex justify-content-center">
              <input 
                class="form-check-input" 
                type="radio" 
                [name]="'permission_' + profile._id"
                [id]="'none_' + profile._id"
                [checked]="profile.permission === 'none'"
                (change)="onPermissionChange(profile._id, 'none')"
                [attr.aria-label]="'FIELD_PERMISSION_MODAL.DONT_SHOW_FOR' | translate: { profileName: profile.name }">
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Thông báo khi không có profiles -->
  <div *ngIf="profiles().length === 0" class="alert alert-info text-center">
    <i class="fas fa-info-circle me-2"></i>
    {{ 'FIELD_PERMISSION_MODAL.NO_PROFILES' | translate }}
  </div>
</mat-dialog-content>

<!-- Footer với các nút hành động -->
<mat-dialog-actions align="end" class="p-3">
  <button 
    type="button"
    mat-button 
    (click)="onCancel()"
    class="me-2">
    {{ 'COMMON.CANCEL' | translate }}
  </button>
  <button 
    type="button"
    mat-raised-button 
    color="primary" 
    (click)="onSave()"
    [disabled]="profiles().length === 0">
    {{ 'COMMON.SAVE' | translate }}
  </button>
</mat-dialog-actions>
