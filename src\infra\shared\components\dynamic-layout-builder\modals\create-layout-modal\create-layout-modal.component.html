<!-- Create Layout Modal Content -->
<div class="create-layout-modal-content">
  <form #layoutForm="ngForm">
    <!-- Layout Name Field -->
    <div class="mb-3">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.LAYOUT_NAME' | translate }}</mat-label>
        <input
          matInput
          [(ngModel)]="layoutName"
          name="layoutName"
          #layoutNameInput="ngModel"
          required
          minlength="3"
          maxlength="50"
          [placeholder]="'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.LAYOUT_NAME_PLACEHOLDER' | translate"
          autocomplete="off">
        <mat-icon matSuffix>edit</mat-icon>

        <!-- Validation Messages -->
        @if (layoutNameInput.invalid && (layoutNameInput.dirty || layoutNameInput.touched)) {
          <mat-error>
            @if (layoutNameInput.errors?.['required']) {
              {{ 'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.ERRORS.NAME_REQUIRED' | translate }}
            }
            @if (layoutNameInput.errors?.['minlength']) {
              {{ 'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.ERRORS.NAME_TOO_SHORT' | translate }}
            }
            @if (layoutNameInput.errors?.['maxlength']) {
              {{ 'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.ERRORS.NAME_TOO_LONG' | translate }}
            }
          </mat-error>
        }
      </mat-form-field>
    </div>

    <!-- Layout Description Field -->
    <div class="mb-3">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.DESCRIPTION' | translate }}</mat-label>
        <textarea
          matInput
          [(ngModel)]="layoutDescription"
          name="layoutDescription"
          rows="3"
          maxlength="200"
          [placeholder]="'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.DESCRIPTION_PLACEHOLDER' | translate"
          autocomplete="off">
        </textarea>
        <mat-icon matSuffix>description</mat-icon>
        <mat-hint>{{ 'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.DESCRIPTION_HINT' | translate }}</mat-hint>
      </mat-form-field>
    </div>

    <!-- Template Selection Info -->
    <div class="alert alert-info d-flex align-items-start">
      <mat-icon class="me-2 mt-1">info</mat-icon>
      <div>
        <strong>{{ 'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.TEMPLATE_INFO_TITLE' | translate }}</strong>
        <p class="mb-0 mt-1">
          {{ 'DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.TEMPLATE_INFO_TEXT' | translate }}
        </p>
      </div>
    </div>
  </form>
</div>
