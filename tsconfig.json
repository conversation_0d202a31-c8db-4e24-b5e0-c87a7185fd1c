/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "outDir": "./dist/out-tsc",
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "moduleResolution": "bundler",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/infra/*"],
      "@core/*": ["src/infra/core/*"],
      "@shared/*": ["src/infra/shared/*"],
      "@config/*": ["src/infra/config/*"],
      "@features/*": ["src/infra/features/*"],
      "@layout/*": ["src/infra/layout/*"],
      "@mock/*": ["src/infra/mock/*"],
      "@domain/*": ["src/domain/*"],
      "@application/*": ["src/application/*"],
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true,
    "extendedDiagnostics": {
      "checks": {
        "optionalChainNotNullable": "suppress"
      }
    }
  },
  "ts-node": {
    "esm": true,
    "experimentalResolver": true
  }
}
