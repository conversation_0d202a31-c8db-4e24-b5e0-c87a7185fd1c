import { Injectable } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { FlashMessageService } from './flash_message.service';
import { TranslateService } from '@ngx-translate/core';
/**
 * https://www.angularjswiki.com/angular/progress-bar-in-angular-mat-progress-bar-examplematerial-design/
 * https://long2know.com/2017/01/angular2-http-interceptor-and-loading-indicator/
 */
@Injectable({
  providedIn: 'root'
})
export class ConnectionCheckService {
  private _isOnline = window.navigator.onLine;

  constructor(
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {
    if(!this._isOnline) {
      // Sử dụng i18n key thay vì hardcode text
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.CONNECTION.OFFLINE')
      );
    }

    window.addEventListener('offline', (event) => {
      // Xóa comment console.log không cần thiết
      if(this._isOnline) {
        this.flashMessageService.error(
          this.translateService.instant('FLASH_MESSAGES.ERRORS.CONNECTION.LOST')
        );
      }

      this._isOnline = false;
    });

    window.addEventListener('online', (event) => {
      // Xóa comment console.log không cần thiết
      if(!this._isOnline) {
        this.flashMessageService.success(
          this.translateService.instant('FLASH_MESSAGES.SUCCESS.CONNECTION.RESTORED')
        );
      }

      this._isOnline = true;
    });
  }

  isOnline() {
    return this._isOnline;
  }
}
export type RequestOptions = {
  useLoader?: boolean,
  body?: any;
  method?: string;
  headers?: HttpHeaders | {
    [header: string]: string | string[];
  };
};
