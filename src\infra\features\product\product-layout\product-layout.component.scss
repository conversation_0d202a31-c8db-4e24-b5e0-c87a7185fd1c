// Product Layout Component Styles
// Responsive grid layout v<PERSON><PERSON> và CSS Grid

.template-grid {
  display: grid;
  gap: 1.5rem;
  
  // Responsive grid columns
  // Desktop: 3 columns
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  
  // Tablet: 2 columns
  @media (max-width: 991.98px) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
  }
  
  // Mobile: 1 column
  @media (max-width: 575.98px) {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

.template-card {
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  
  // Hover effects
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--bs-primary);
    cursor: pointer;
  }
  
  // Focus styles for accessibility
  &:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
  }
  
  // Active state
  &:active {
    transform: translateY(-2px);
  }
}

.template-avatar {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }
}

.template-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
  
  // Truncate long titles
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.stats-container {
  .stat-item {
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: var(--bs-primary);
      background-color: #f8f9ff !important;
    }
  }
  
  .stat-number {
    font-size: 1.5rem;
    line-height: 1.2;
  }
  
  .stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.template-details {
  .small {
    font-size: 0.8rem;
  }
  
  mat-chip {
    font-size: 0.75rem;
    height: 24px;
    min-height: 24px;
  }
}

.template-category {
  mat-chip {
    font-size: 0.7rem;
    height: 20px;
    min-height: 20px;
    font-weight: 500;
  }
}

// Loading spinner customization
.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Error alert styling
.alert {
  border-radius: 8px;
  border: none;
  
  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

// Empty state styling
.display-1 {
  font-size: 4rem;
  opacity: 0.3;
}

// Utility classes
.cursor-pointer {
  cursor: pointer;
}

// Material Design enhancements
mat-card {
  .mat-mdc-card-header {
    padding: 16px 16px 8px 16px;
  }
  
  .mat-mdc-card-content {
    padding: 0 16px 8px 16px;
  }
  
  .mat-mdc-card-actions {
    padding: 8px 16px 16px 16px;
    margin: 0;
  }
}

// Responsive typography
@media (max-width: 575.98px) {
  .template-title {
    font-size: 1rem;
    max-width: 150px;
  }
  
  .stat-number {
    font-size: 1.25rem !important;
  }
  
  .template-grid {
    .template-card {
      .mat-mdc-card-header {
        padding: 12px 12px 6px 12px;
      }
      
      .mat-mdc-card-content {
        padding: 0 12px 6px 12px;
      }
      
      .mat-mdc-card-actions {
        padding: 6px 12px 12px 12px;
      }
    }
  }
}

