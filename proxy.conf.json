{"/socket.io": {"target": "http://localhost:8004/socket.io", "secure": false, "ws": true, "changeOrigin": true, "pathRewrite": {"^/socket.io": ""}, "logLevel": "silent"}, "/api": {"target": "http://localhost:8004/api", "secure": false, "changeOrigin": true, "pathRewrite": {"^/api": ""}, "logLevel": "silent"}, "/manage": {"target": "http://localhost:8004/manage", "secure": false, "changeOrigin": true, "pathRewrite": {"^/manage": ""}, "logLevel": "silent"}, "/employee": {"target": "http://localhost:8004/employee", "secure": false, "changeOrigin": true, "pathRewrite": {"^/employee": ""}, "logLevel": "silent"}, "/account": {"target": "http://localhost:8004/account", "secure": false, "changeOrigin": true, "pathRewrite": {"^/account": ""}, "logLevel": "silent"}, "/ajax": {"target": "http://localhost:8004/ajax", "secure": false, "changeOrigin": true, "pathRewrite": {"^/ajax": ""}, "logLevel": "silent"}, "/pos_ajax": {"target": "http://localhost:8004/pos_ajax", "secure": false, "changeOrigin": true, "pathRewrite": {"^/pos_ajax": ""}, "logLevel": "silent"}, "/pos_updates": {"target": "http://localhost:8004/pos_updates", "secure": false, "changeOrigin": true, "pathRewrite": {"^/pos_updates": ""}, "logLevel": "silent"}, "/upload": {"target": "http://localhost:8004/upload", "secure": false, "changeOrigin": true, "pathRewrite": {"^/upload": ""}, "logLevel": "silent"}, "/account/login": {"target": "http://localhost:8004/account/login", "secure": false, "changeOrigin": true, "pathRewrite": {"^/account/login": ""}, "logLevel": "silent"}, "/metronic": {"target": "http://localhost:8004/metronic", "secure": false, "changeOrigin": true, "pathRewrite": {"^/metronic": ""}, "logLevel": "silent"}, "/assets": {"target": "http://localhost:8004/assets", "secure": false, "changeOrigin": true, "pathRewrite": {"^/assets": ""}, "logLevel": "silent"}, "/media": {"target": "http://localhost:8004/media", "secure": false, "changeOrigin": true, "pathRewrite": {"^/media": ""}, "logLevel": "silent"}}