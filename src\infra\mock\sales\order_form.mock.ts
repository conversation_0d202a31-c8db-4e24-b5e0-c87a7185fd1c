import { OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { OrderStatus } from 'salehub_shared_contracts/entities/oms/order/order_core';
import { mockProductList, mockProductUnits, mockVariantList } from '../product/product.mock';
import { mockBrandList, mockCategoryList, mockWarehouseList } from '../shared/list.mock';


export const mockOrderStatuses: Array<{ id: OrderStatus; name: string }> = [
  { id: 'pending', name: 'Ch<PERSON> xử lý' },
  { id: 'confirmed', name: '<PERSON><PERSON> xác thực' }, // Nh<PERSON> bán hàng đã xác thực đơn hàng
  { id: 'transferred_to_store', name: '<PERSON><PERSON>ển cửa hàng' }, // Đơn hàng đã được chuyển sang chi nhánh khác
  { id: 'in_stock', name: '<PERSON><PERSON><PERSON> hàng' }, // <PERSON> nhánh nhận đơn xác nhận còn hàng
  { id: 'out_of_stock', name: '<PERSON><PERSON><PERSON> hàng' }, // Chi nhánh nhận đơn xác nhận hết hàng
  { id: 'pending_packing', name: 'Chờ đóng gói' }, // Chờ xác nhận đóng gói để tránh trùng lặp
  { id: 'packed', name: 'Đã đóng gói' }, // Nhân viên đã quét mã để xác nhận đóng gói
  { id: 'ready_to_ship', name: 'Chờ vận chuyển' }, // Đã đóng gói, chờ shipper nhận
  { id: 'warehouse_released', name: 'Đã xuất kho' }, // Chi nhánh xác nhận xuất kho
  { id: 'in_transit', name: 'Đang NVC' }, // Đơn đã giao qua nhà vận chuyển và đang giao
  { id: 'self_delivered', name: 'Tự giao' }, // Nhà bán hàng chọn nhà vận chuyển khác
  { id: 'preparing', name: 'Đang chuẩn bị' }, // Thêm trạng thái chuẩn bị (nếu cần)
  { id: 'cancelled', name: 'Đã hủy' }, // Đơn hàng bị hủy
  { id: 'preorder', name: 'Đặt trước' }, // Đơn hàng đặt trước
  { id: 'delivering', name: 'Đang giao hàng' }, // Đơn hàng đang được giao
  { id: 'completed', name: 'Hoàn tất' }, // Đơn hàng được xác nhận hoàn tất
  { id: 'cancelled_after_completion', name: 'Hủy sau hoàn tất' } // Hủy sau khi đã hoàn tất, liên quan đến hóa đơn
];


export const mockProductSelectorConfig = {
  list: mockProductList,
  data: [
    {
      quantity: 1,
      product: {
        ...mockProductList[0],
        variant: mockVariantList[0],
        unit: mockProductUnits[0]
      }
    }
  ] as OrderItemBaseDetails[],
  // Thêm dữ liệu mẫu cho bộ lọc
  warehouseList: mockWarehouseList,
  categoryList: mockCategoryList,
  brandList: mockBrandList
};
