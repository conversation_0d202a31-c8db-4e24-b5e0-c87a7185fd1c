.summary-rows {
  padding: 10px 0;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  min-height: 48px;
}

.summary-row:last-child {
  border-bottom: none;
}

.label {
  flex: 1;
  font-size: 14px;
  color: #666;
}

.value {
  flex: 1;
  text-align: right;
  font-size: 14px;
}

.input-value {
  width: 100%;

  .amount-input {
    width: 100%;

    input {
      text-align: right;
    }
  }
}

.total-row {
  font-weight: bold;

  .label, .value {
    font-size: 16px;
    color: #333;
  }
}

.text-danger {
  color: #f44336;
}

.text-success {
  color: #4caf50;
}

.text-warning {
  color: #ff9800;
}

.fw-bold {
  font-weight: bold;
}

.payment-method-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.payment-radio-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bank-transfer-options {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 10px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.bank-select {
  flex: 1;
}

.mixed-payment-info {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.mixed-payment-label {
  font-weight: bold;
  margin-right: 8px;
}

.mixed-payment-details {
  flex: 1;
}

.w-100 {
  width: 100%;
}

.mt-2 {
  margin-top: 10px;
}

.quick-payment-options {
  width: 100%;
}

.quick-payment-row {
  display: flex;
  justify-content: space-between;
}

/* Overrides for mat-autocomplete options */
::ng-deep .mat-autocomplete-panel {
  width: 320px !important;

  .quick-payment-row .mat-option {
    width: 25%;
    min-width: 75px;
    text-align: center;
    line-height: 1.2;
    height: auto;
    padding: 8px 4px;
  }
}
