import { IntegrationPlatform } from '../../../../models/view/integration.models';

/**
 * Interface cho dữ liệu đầu vào của modal tạo kết nối
 */
export interface ConnectionCreateModalData {
  platform: IntegrationPlatform;
}

/**
 * Interface cho kết quả trả về từ modal tạo kết nối
 */
export interface ConnectionCreateModalResult {
  success: boolean;
  connectionData?: {
    // Cho account_login
    username?: string;
    password?: string;
    
    // Cho api_auth
    authType?: 'api_key' | 'oauth';
    apiKey?: string;
    
    // Cho cookie_auth
    browserAuth?: boolean;
  };
  error?: string;
}

/**
 * Interface cho form data của account login
 */
export interface AccountLoginFormData {
  username: string;
  password: string;
}

/**
 * Interface cho form data của API authentication
 */
export interface ApiAuthFormData {
  authType: 'api_key' | 'oauth';
  apiKey?: string;
}

/**
 * Interface cho form data của cookie authentication
 */
export interface CookieAuthFormData {
  browserAuth: boolean;
}

/**
 * Union type cho tất cả form data types
 */
export type ConnectionFormData = AccountLoginFormData | ApiAuthFormData | CookieAuthFormData;
