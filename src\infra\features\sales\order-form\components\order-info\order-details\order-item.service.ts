import { Injectable } from '@angular/core';
import { OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { CreateOrderRequest } from 'salehub_shared_contracts/requests/sales/create_order';
import { ProductUnit, ProductVariant } from 'salehub_shared_contracts';
import { mockProductList } from '@mock/product/product.mock';

/**
 * Service xử lý thông tin đơn hàng
 */
@Injectable({
  providedIn: 'root'
})
export class OrderItemService {
  /**
   * L<PERSON>y danh sách sản phẩm từ mock data
   * @returns Danh sách sản phẩm
   */
  getProductList() {
    return mockProductList;
  }

  /**
   * Lấy danh sách biến thể của sản phẩm
   * @param productId ID của sản phẩm
   * @returns Danh sách biến thể của sản phẩm
   */
  getProductVariants(productId: string | undefined): ProductVariant[] {
    if (!productId) return [];

    // Tìm sản phẩm theo ID
    const product = mockProductList.find(p => p.productId === productId);
    if (!product || !product.variants) return [];

    return product.variants;
  }

  /**
   * Lấy danh sách đơn vị tính của sản phẩm
   * @param productId ID của sản phẩm
   * @returns Danh sách đơn vị tính của sản phẩm
   */
  getProductUnits(productId: string | undefined): ProductUnit[] {
    if (!productId) return [];

    // Tìm sản phẩm theo ID
    const product = mockProductList.find(p => p.productId === productId);
    if (!product || !product.units) return [];

    return product.units;
  }

  /**
   * Thêm sản phẩm vào đơn hàng
   * @param order Đơn hàng cần thêm sản phẩm
   * @param item Thông tin sản phẩm cần thêm
   * @returns Đơn hàng đã cập nhật
   */
  addItem(order: CreateOrderRequest, item: OrderItemBaseDetails): CreateOrderRequest {
    if (!order.items) {
      order.items = [];
    }

    order.items.push(item);
    return this.recalculateOrder(order);
  }

  /**
   * Cập nhật sản phẩm trong đơn hàng
   * @param order Đơn hàng cần cập nhật
   * @param index Vị trí sản phẩm cần cập nhật
   * @param item Thông tin sản phẩm mới
   * @returns Đơn hàng đã cập nhật
   */
  updateItem(order: CreateOrderRequest, index: number, item: OrderItemBaseDetails): CreateOrderRequest {
    if (!order.items || index < 0 || index >= order.items.length) {
      return order;
    }

    order.items[index] = item;
    return this.recalculateOrder(order);
  }

  /**
   * Xóa sản phẩm khỏi đơn hàng
   * @param order Đơn hàng cần xóa sản phẩm
   * @param index Vị trí sản phẩm cần xóa
   * @returns Đơn hàng đã cập nhật
   */
  removeItem(order: CreateOrderRequest, index: number): CreateOrderRequest {
    if (!order.items || index < 0 || index >= order.items.length) {
      return order;
    }

    order.items.splice(index, 1);
    return this.recalculateOrder(order);
  }

  /**
   * Tính thành tiền cho một sản phẩm
   * @param item Thông tin sản phẩm
   * @returns Thành tiền
   */
  calculateItemTotal(item: OrderItemBaseDetails): number {
    return (item.quantity || 0) * (item.product?.userOverride?.price || 0) - (item.discount || 0);
  }

  /**
   * Tính lại tổng tiền cho đơn hàng
   * @param order Đơn hàng cần tính tổng tiền
   * @returns Đơn hàng đã cập nhật tổng tiền
   */
  private recalculateOrder(order: CreateOrderRequest): CreateOrderRequest {
    if (!order.items) {
      order.items = [];
    }

    // Tính tổng tiền hàng
    let subtotal = 0;
    order.items.forEach((item: OrderItemBaseDetails) => {
      const itemTotal = this.calculateItemTotal(item);
      subtotal += itemTotal;
    });

    // Cập nhật thông tin tổng tiền
    if (!order.payment) {
      order.payment = {};
    }

    order.payment.subtotal = subtotal;
    order.payment.grandTotal = subtotal + (order.payment.shippingFee || 0) - (order.payment.discount || 0);

    return order;
  }
}
