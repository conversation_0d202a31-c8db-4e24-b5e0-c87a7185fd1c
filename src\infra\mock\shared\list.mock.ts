import { ImportAdditionalCost } from 'salehub_shared_contracts/entities/scm/import_additional_cost';
import { WarehouseLocation } from '@shared/models/api/warehouse-entities.dto';
import { CategoryList,
  BrandList,
  SaleChannelList,
  WarehouseList,
  EmployeeList,
  BankList,
  DeliveryCompanyList,
  PickupAddressList,
  SupplierList
} from 'salehub_shared_contracts/requests/shared/list';

// Danh sách danh mục sản phẩm
export const mockCategoryList: CategoryList = [
  { _id: 'cat1', name: 'Quần áo người lớn' },
  { _id: 'cat2', name: '<PERSON>uần áo trẻ em' },
  { _id: 'cat3', name: '<PERSON><PERSON><PERSON><PERSON> dép' },
  { _id: 'cat4', name: '<PERSON><PERSON> kiện thời trang' },
  { _id: 'cat5', name: '<PERSON><PERSON><PERSON> xách' },
  { _id: 'cat6', name: '<PERSON><PERSON><PERSON> hồ' }
];

// Danh sách thương hiệu
export const mockBrandList: BrandList = [
  { _id: 'brand1', name: '<PERSON><PERSON>' },
  { _id: 'brand2', name: 'Adidas' },
  { _id: 'brand3', name: 'Nike' },
  { _id: 'brand4', name: 'Gucci' },
  { _id: 'brand5', name: 'H&M' },
  { _id: 'brand6', name: 'Uniqlo' },
  { _id: 'brand7', name: 'Zara' }
];

// Danh sách kênh bán hàng
export const mockSaleChannelList: SaleChannelList = [
  { _id: 'channel1', name: 'Online' },
  { _id: 'channel2', name: 'Shopee' },
  { _id: 'channel3', name: 'Facebook' },
  { _id: 'channel4', name: 'Điện thoại' },
  { _id: 'channel5', name: 'Cửa hàng' },
  { _id: 'channel6', name: 'Lazada' },
  { _id: 'channel7', name: 'TikTok Shop' }
];

// Danh sách kho hàng
export const mockWarehouseList: WarehouseList = [
  { _id: 'wh1', name: 'Kho mặc định' },
  { _id: 'wh2', name: 'Kho tổng' },
  { _id: 'wh3', name: 'Kho cửa hàng 1' },
  { _id: 'wh4', name: 'Kho cửa hàng 2' },
  { _id: 'wh5', name: 'Kho online' }
];

// Danh sách kho hàng
export const mockPickupAddressList: PickupAddressList = [
  { _id: 'wh1', name: 'Kho mặc định' },
  { _id: 'wh2', name: 'Kho tổng' }
];

// Danh sách nhân viên
export const mockEmployeeList: EmployeeList = [
  { _id: 'emp1', name: 'Nguyễn Văn A' },
  { _id: 'emp2', name: 'Trần Thị B' },
  { _id: 'emp3', name: 'Lê Văn C' },
  { _id: 'emp4', name: 'Phạm Thị D' },
  { _id: 'emp5', name: 'Hoàng Văn E' }
];

// Danh sách ngân hàng
export const mockBankList: BankList = [
  { _id: 'bank1', name: 'Vietcombank', accountNumber: '**********', accountName: 'NGUYEN VAN A', branch: 'Chi nhánh HCM' },
  { _id: 'bank2', name: 'Techcombank', accountNumber: '**********', accountName: 'NGUYEN VAN A', branch: 'Chi nhánh HN' },
  { _id: 'bank3', name: 'BIDV', accountNumber: '**********', accountName: 'NGUYEN VAN A', branch: 'Chi nhánh DN' },
  { _id: 'bank4', name: 'ACB', accountNumber: '**********', accountName: 'NGUYEN VAN A', branch: 'Chi nhánh CT' }
];

// Danh sách đối tác giao hàng
export const mockDeliveryCompanyList: DeliveryCompanyList = [
  { _id: 'delivery1', name: 'Giao hàng nhanh', logo: 'assets/images/ghn.png', fee: 25000 },
  { _id: 'delivery2', name: 'Giao hàng tiết kiệm', logo: 'assets/images/ghtk.png', fee: 20000 },
  { _id: 'delivery3', name: 'J&T Express', logo: 'assets/images/jt.png', fee: 22000 },
  { _id: 'delivery4', name: 'Viettel Post', logo: 'assets/images/viettel.png', fee: 23000 },
  { _id: 'delivery5', name: 'Ninja Van', logo: 'assets/images/ninja.png', fee: 24000 }
];



// Mock data nhà cung cấp
export const mockSuppliers: SupplierList = [
  {
    _id: 'sup1',
    name: 'Công ty TNHH Thực phẩm ABC',
    phoneNumber: '**********',
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'sup2',
    name: 'Công ty CP Đồ uống XYZ',
    phoneNumber: '0909876543',
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    _id: 'sup3',
    name: 'Nhà phân phối DEF',
    phoneNumber: '0905555666',
    email: '<EMAIL>',
    isActive: true,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  }
];

export const mockImportAdditionalCosts: ImportAdditionalCost[] = [
  {
    _id: 'cost001',
    name: 'Phí vận chuyển nội địa',
    costValue: { type: 'fixed', value: 500000 },
    isActive: true,
    autoAddToPurchaseOrder: true,
    refundOnReturn: true,
    paidToSupplier: true,
    allocateToItems: true,
    tax: { rate: 10, amount: 50000 },
  },
  {
    _id: 'cost002',
    name: 'Phí hải quan',
    costValue: { type: 'percentage', value: 5 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: true,
    tax: { rate: 8, amount: 40000 },
  },
  {
    _id: 'cost003',
    name: 'Phí bảo hiểm hàng hóa',
    costValue: { type: 'fixed', value: 300000 },
    isActive: true,
    autoAddToPurchaseOrder: true,
    refundOnReturn: true,
    paidToSupplier: true,
    allocateToItems: true,
    tax: { rate: 10, amount: 30000 },
  },
  {
    _id: 'cost004',
    name: 'Phí lưu kho tạm thời',
    costValue: { type: 'fixed', value: 200000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: false,
  },
  {
    _id: 'cost005',
    name: 'Phí xử lý thủ tục nhập khẩu',
    costValue: { type: 'fixed', value: 1500000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: true,
    tax: { rate: 5, amount: 75000 },
  },
  {
    _id: 'cost006',
    name: 'Phí vận chuyển quốc tế',
    costValue: { type: 'percentage', value: 10 },
    isActive: true,
    autoAddToPurchaseOrder: true,
    refundOnReturn: true,
    paidToSupplier: true,
    allocateToItems: true,
    tax: { rate: 10, amount: 100000 },
  },
  {
    _id: 'cost007',
    name: 'Phí kiểm định chất lượng',
    costValue: { type: 'fixed', value: 400000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: false,
  },
  {
    _id: 'cost008',
    name: 'Phí bốc dỡ hàng hóa',
    costValue: { type: 'fixed', value: 250000 },
    isActive: true,
    autoAddToPurchaseOrder: true,
    refundOnReturn: true,
    paidToSupplier: true,
    allocateToItems: true,
    tax: { rate: 10, amount: 25000 },
  },
  {
    _id: 'cost009',
    name: 'Phí lưu container',
    costValue: { type: 'fixed', value: 600000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: false,
  },
  {
    _id: 'cost010',
    name: 'Phí môi giới hải quan',
    costValue: { type: 'fixed', value: 800000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: true,
    tax: { rate: 10, amount: 80000 },
  },
  {
    _id: 'cost011',
    name: 'Phí nâng hạ container',
    costValue: { type: 'fixed', value: 350000 },
    isActive: true,
    autoAddToPurchaseOrder: true,
    refundOnReturn: true,
    paidToSupplier: true,
    allocateToItems: true,
    tax: { rate: 10, amount: 35000 },
  },
  {
    _id: 'cost012',
    name: 'Phí đóng gói hàng hóa',
    costValue: { type: 'fixed', value: 100000 },
    isActive: true,
    autoAddToPurchaseOrder: true,
    refundOnReturn: true,
    paidToSupplier: true,
    allocateToItems: true,
  },
  {
    _id: 'cost013',
    name: 'Phí kiểm dịch thực phẩm',
    costValue: { type: 'fixed', value: 500000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: false,
    tax: { rate: 5, amount: 25000 },
  },
  {
    _id: 'cost014',
    name: 'Phí thuế nhập khẩu',
    costValue: { type: 'percentage', value: 7 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: true,
    tax: { rate: 10, amount: 70000 },
  },
  {
    _id: 'cost015',
    name: 'Phí giám sát cảng',
    costValue: { type: 'fixed', value: 200000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: false,
  },
  {
    _id: 'cost016',
    name: 'Phí xử lý hóa đơn',
    costValue: { type: 'fixed', value: 100000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: false,
  },
  {
    _id: 'cost017',
    name: 'Phí bảo quản lạnh',
    costValue: { type: 'fixed', value: 700000 },
    isActive: true,
    autoAddToPurchaseOrder: true,
    refundOnReturn: true,
    paidToSupplier: true,
    allocateToItems: true,
    tax: { rate: 10, amount: 70000 },
  },
  {
    _id: 'cost018',
    name: 'Phí kiểm tra an toàn',
    costValue: { type: 'fixed', value: 300000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: false,
  },
  {
    _id: 'cost019',
    name: 'Phí xử lý môi trường',
    costValue: { type: 'percentage', value: 2 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: true,
    tax: { rate: 5, amount: 10000 },
  },
  {
    _id: 'cost020',
    name: 'Phí dịch vụ logistics',
    costValue: { type: 'fixed', value: 450000 },
    isActive: true,
    autoAddToPurchaseOrder: true,
    refundOnReturn: true,
    paidToSupplier: true,
    allocateToItems: true,
    tax: { rate: 10, amount: 45000 },
  },
  {
    _id: 'cost021',
    name: 'Phí cẩu hàng',
    costValue: { type: 'fixed', value: 400000 },
    isActive: true,
    autoAddToPurchaseOrder: true,
    refundOnReturn: true,
    paidToSupplier: true,
    allocateToItems: true,
    tax: { rate: 10, amount: 40000 },
  },
  {
    _id: 'cost022',
    name: 'Phí lưu kho cảng',
    costValue: { type: 'fixed', value: 300000 },
    isActive: true,
    autoAddToPurchaseOrder: false,
    refundOnReturn: false,
    paidToSupplier: false,
    allocateToItems: false,
  },
];

// Mock data vị trí trong kho
export const mockWarehouseLocations: WarehouseLocation[] = [
  {
    _id: 'loc1',
    warehouse: { _id: 'wh1', name: 'Kho mặc định' },
    name: 'Khu A',
    level: 1,
    path: ['Khu A'],
    isActive: true,
    type: 'Zone',
  },
  {
    _id: 'loc2',
    warehouse: { _id: 'wh1', name: 'Kho mặc định' },
    name: 'Kệ A1',
    parentId: 'loc1',
    level: 2,
    path: ['Khu A', 'Kệ A1'],
    isActive: true,
    type: 'Rack',
  },
  {
    _id: 'loc3',
    warehouse: { _id: 'wh1', name: 'Kho mặc định' },
    name: 'Kệ A2',
    parentId: 'loc1',
    level: 2,
    path: ['Khu A', 'Kệ A2'],
    isActive: true,
    type: 'Rack',
  },
  {
    _id: 'loc4',
    warehouse: { _id: 'wh1', name: 'Kho mặc định' },
    name: 'Khu B',
    level: 1,
    path: ['Khu B'],
    isActive: true,
    type: 'Zone',
  },
  {
    _id: 'loc5',
    warehouse: { _id: 'wh1', name: 'Kho mặc định' },
    name: 'Kệ B1',
    parentId: 'loc4',
    level: 2,
    path: ['Khu B', 'Kệ B1'],
    isActive: true,
    type: 'Rack',
  },
  {
    _id: 'loc6',
    warehouse: { _id: 'wh2', name: 'Kho tổng' },
    name: 'Khu C',
    level: 1,
    path: ['Khu C'],
    isActive: true,
    type: 'Zone',
  },
  {
    _id: 'loc7',
    warehouse: { _id: 'wh2', name: 'Kho tổng' },
    name: 'Kệ C1',
    parentId: 'loc6',
    level: 2,
    path: ['Khu C', 'Kệ C1'],
    isActive: true,
    type: 'Rack',
  }
];