import { SettingsListConfig } from '@shared/components/settings/settings-list.component';
import { IntegrationAccountLog } from '@/mock/settings/integration_view_account.mock';

/**
 * Interface cho dữ liệu đầu vào của modal xem chi tiết account
 */
export interface IntegrationAccountViewModalData {
  /**
   * C<PERSON>u hình cho SettingsListComponent
   */
  config: SettingsListConfig;
  
  /**
   * <PERSON><PERSON> s<PERSON>ch logs của account
   */
  logs: IntegrationAccountLog[];
  
  /**
   * Tên platform để hiển thị trong title modal
   */
  platformName: string;
  
  /**
   * Tên account để hiển thị trong title modal
   */
  accountName?: string;
}

/**
 * Interface cho kết quả trả về từ modal xem chi tiết account
 */
export interface IntegrationAccountViewModalResult {
  /**
   * Có thay đổi settings hay không
   */
  settingsChanged: boolean;
  
  /**
   * Dữ liệu settings đã thay đổi (nếu có)
   */
  changedSettings?: Record<string, any>;
  
  /**
   * Action được thực hiện
   */
  action: 'close' | 'save' | 'cancel';
}
