import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FieldType } from '@domain/entities/field.entity';

/**
 * Service để generate dữ liệu mẫu cho các field types trong view mode
 * Sử dụng i18n để hiển thị dữ liệu mẫu phù hợp với ngôn ngữ
 */
@Injectable()
export class MockDataService {

  constructor(private translateService: TranslateService) {}

  /**
   * Generate mock data cho field dựa trên field type
   * @param fieldType Loại field cần generate data
   * @returns Chuỗi dữ liệu mẫu đã được i18n
   */
  generateMockData(fieldType: FieldType): string {
    const mockDataKey = `DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.${fieldType.toUpperCase()}`;
    
    // Fallback data nếu không có translation
    const fallbackData = this.getFallbackMockData(fieldType);
    
    // Lấy translated data, nếu không có thì dùng fallback
    const translatedData = this.translateService.instant(mockDataKey);
    
    // Nếu translation key không tồn tại, instant() sẽ trả về chính key đó
    return translatedData === mockDataKey ? fallbackData : translatedData;
  }

  /**
   * Generate mock data cho array fields (multi-picklist, etc.)
   * @param fieldType Loại field
   * @param itemCount Số lượng items trong array (default: 3)
   * @returns Chuỗi các items được nối bằng dấu phẩy
   */
  generateArrayMockData(fieldType: FieldType, itemCount: number = 3): string {
    const items: string[] = [];
    
    for (let i = 1; i <= itemCount; i++) {
      const itemKey = `DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.${fieldType.toUpperCase()}_ITEM_${i}`;
      const fallbackItem = `${this.getFallbackMockData(fieldType)} ${i}`;
      const translatedItem = this.translateService.instant(itemKey);
      
      items.push(translatedItem === itemKey ? fallbackItem : translatedItem);
    }
    
    return items.join(', ');
  }

  /**
   * Generate mock data cho boolean fields
   * @param value Giá trị boolean (true/false)
   * @returns Chuỗi "Có"/"Không" hoặc "Yes"/"No" tùy theo ngôn ngữ
   */
  generateBooleanMockData(value: boolean): string {
    const key = value ? 'DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.BOOLEAN_TRUE' : 'DYNAMIC_LAYOUT_RENDERER.MOCK_DATA.BOOLEAN_FALSE';
    const fallback = value ? 'Có' : 'Không';
    const translated = this.translateService.instant(key);
    
    return translated === key ? fallback : translated;
  }

  /**
   * Fallback mock data khi không có i18n
   * @param fieldType Loại field
   * @returns Dữ liệu mẫu mặc định
   */
  private getFallbackMockData(fieldType: FieldType): string {
    switch (fieldType) {
      case 'text':
        return 'Lorem ipsum text';
      case 'number':
        return '123';
      case 'decimal':
        return '123.45';
      case 'currency':
        return '1,234,567 VND';
      case 'percent':
        return '85%';
      case 'email':
        return '<EMAIL>';
      case 'phone':
        return '+84 123 456 789';
      case 'url':
        return 'https://example.com';
      case 'textarea':
        return 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...';
      case 'date':
        return '15/12/2024';
      case 'datetime':
        return '15/12/2024 14:30';
      case 'file':
        return 'document.pdf';
      case 'image':
        return 'image.jpg';
      case 'checkbox':
        return this.generateBooleanMockData(true);
      case 'radio':
        return 'Tùy chọn 1';
      case 'select':
        return 'Giá trị được chọn';
      case 'picklist':
        return 'Mục danh sách';
      case 'multi-picklist':
        return this.generateArrayMockData('picklist');
      case 'search':
        return 'Kết quả tìm kiếm';
      case 'user':
        return 'Nguyễn Văn A';
      default:
        return 'Dữ liệu mẫu';
    }
  }
}
