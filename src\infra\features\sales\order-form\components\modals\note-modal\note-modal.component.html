<h2 mat-dialog-title>{{ 'SALES.NOTE_DIALOG.TITLE' | translate }}</h2>
<mat-dialog-content>
  <div class="mb-4">
    <mat-form-field class="w-100">
      <mat-label>{{ 'SALES.NOTE_DIALOG.NOTE_TYPE' | translate }}</mat-label>
      <mat-select [(ngModel)]="selectedNoteType">
        <mat-option value="internal">{{ 'SALES.NOTE_DIALOG.INTERNAL_NOTE' | translate }}</mat-option>
        <mat-option value="public">{{ 'SALES.NOTE_DIALOG.PUBLIC_NOTE' | translate }}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div>
    <mat-form-field class="w-100">
      <mat-label>{{ 'SALES.NOTE_DIALOG.NOTE_CONTENT' | translate }}</mat-label>
      <textarea
        matInput
        [(ngModel)]="noteContent"
        rows="5"
        placeholder="{{ 'SALES.NOTE_DIALOG.ENTER_NOTE' | translate }}">
      </textarea>
    </mat-form-field>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button
    mat-button
    (click)="onCancel()">
    {{ 'COMMON.CANCEL' | translate }}
  </button>
  <button
    mat-raised-button
    color="primary"
    (click)="onSave()">
    {{ 'COMMON.SAVE' | translate }}
  </button>
</mat-dialog-actions>
