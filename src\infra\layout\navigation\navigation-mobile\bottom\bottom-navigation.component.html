<!-- src/app/layout/navigation-mobile/bottom/bottom-navigation.component.html -->
<nav class="bottom-nav-container d-flex justify-content-around align-items-center">
  <!-- Lặp qua các mục hiển thị trên thanh bottom -->
  @for (item of bottomTopLevelItems(); track trackByFn($index, item)) {
  <!-- Mục điề<PERSON> hướ<PERSON> (4 mục đầu) -->
  <span
    *ngIf="!item.isMore"
    class="bottom-nav-item d-flex flex-column align-items-center text-decoration-none py-2"
    [class.active]="isActive(item)"
    [attr.aria-label]="item.moduleShortName | translate"
    (click)="openSubSheet(item, $event)"
  >
    <!-- Sử dụng KeeniconComponent hoặc component icon của bạn -->
    @if(item.icon && item.icon.set) {
      @if(item.icon.set ==='keenicons-filled') {
        <i
          class="ki-filled text-xl bottom-nav-icon"
          [ngClass]="'ki-'+item.icon.code"
          [class.icon-active]="item.routerLink === iconAnimating()"
          >
        </i>
      }

    }
    <!-- Văn bản, ưu tiên shortText nếu có -->
    <span class="bottom-nav-text mt-1">{{ (item.moduleShortName || item.moduleFullName) | translate }}</span>
  </span>

  <!-- Mục "More" (mục thứ 5) -->
  <div
    *ngIf="item.isMore"
    class="bottom-nav-item d-flex flex-column align-items-center text-decoration-none py-2"
    (click)="openMoreSheet()"
    [attr.aria-label]="item.moduleShortName | translate"
  >
    <!-- Icon cho mục "More" -->
    <i
      class="ki-filled text-xl bottom-nav-icon ki-category"
      >
    </i>
    <!-- Văn bản cho mục "More" -->
    <span class="bottom-nav-text mt-1">{{ item.moduleShortName | translate }}</span>
  </div>
  }
</nav>
