### Y<PERSON><PERSON> c<PERSON>u cải thiện FieldFiltersComponent

#### Mụ<PERSON> tiêu
<PERSON> thiện **FieldFiltersComponent** để hỗ trợ lưu và quản lý filter, hiển thị danh sách saved filters, tích hợp modal nhập tên filter, và thêm dữ liệu mẫu (`savedFilters`) vào `mockProductListPage.fieldFiltersConfig` trong `src/infra/mock/product/product_list.mock.ts`. Kiểm tra giao diện tại `http://localhost:4200/#/product/list`.

---

#### 1. Định nghĩa lại các interface
Tôi muốn định nghĩa lại các interface liên quan đến **FieldFiltersComponent** để đảm bảo rõ ràng và phù hợp với chức năng lưu filter. Các interface được cập nhật như sau:

- **`FieldFiltersConfig`**:
  - **<PERSON><PERSON> tả**: <PERSON><PERSON><PERSON> hình cho `FieldFiltersComponent`, bao gồm danh sách field, tùy chọn hiển thị, và mảng saved filters.
  - **Cấu trúc**:
    ```typescript
    export interface FieldFiltersConfig {
      fields: Field[]; // Danh sách field từ salehub_shared_contracts
      showTitle: boolean; // Hiển thị tiêu đề component (ví dụ: "Filters")
      showClearAll: boolean; // Hiển thị nút "Clear All" để xóa tất cả filter
      savedFilters?: SavedFilter[]; // Mảng các filter đã lưu (tùy chọn)
    }
    ```
  - **Vị trí**: `src/infra/shared/modals/view/field-filters.model.ts`.

---

#### 2. Thêm SaveFilterModalComponent
Tôi muốn tạo một **component modal** để xử lý việc nhập tên filter khi lưu filter mới hoặc lưu dưới dạng mới.

- **Tên component**: `SaveFilterModalComponent`
- **Vị trí**: `src/infra/shared/components/field-filters/modals/save-filter-modal/`
- **Vai trò**:
  - Hiển thị modal để người dùng nhập tên filter.
  - Sử dụng **ResponsiveModalService** để mở modal và nhận kết quả (tên filter).
  - Trả về tên filter sau khi nhấn **Save**.
- **Giao diện**:
  - **Tiêu đề**: "Save Filter" (key i18n: `FIELD_FILTERS.SAVE_MODAL.TITLE`).
  - **Input**: Textbox để nhập tên filter (placeholder: `FIELD_FILTERS.SAVE_MODAL.NAME_PLACEHOLDER`).
  - **Nút Save**: Lưu tên filter (key i18n: `FIELD_FILTERS.SAVE_MODAL.SAVE`).
  - **Nút Cancel**: Hủy modal (key i18n: `FIELD_FILTERS.SAVE_MODAL.CANCEL`).
  - Sử dụng Bootstrap classes (`modal-dialog`, `form-control`, `btn`) để đảm bảo responsive.
- **Tích hợp**:
  - Được gọi từ `FilterSaveComponent` khi nhấn **Save Filter** (filter mới) hoặc **Save As New** (filter cũ).
  - Nhận `@Input filters` (danh sách filter hiện tại) và emit `@Output save` với `{ name: string, filters: SavedFilter['filters'] }`.
- **Hành vi**:
  - **Validate input**:
    - Tên filter không được để trống.
    - Tên filter không được trùng với các filter trong `savedFilters`.
  - Khi nhấn **Save**, emit sự kiện với tên filter và danh sách filter.
  - Khi nhấn **Cancel**, đóng modal mà không emit sự kiện.

---

#### 3. Cải thiện FieldFiltersComponent
Tiếp tục cải thiện **FieldFiltersComponent** để hỗ trợ lưu filter, hiển thị saved filters, và tích hợp modal. Các chức năng cụ thể:

- **Hiển thị saved filters**:
  - Ở đầu giao diện, hiển thị danh sách saved filters từ `FieldFiltersConfig.savedFilters`:
    ```
    Saved Filters
      - Filter 1
      - Filter 2
      - ...
    ```
  - Sử dụng `mat-list` hoặc `Bootstrap list-group`.
  - Khi nhấn vào một saved filter (ví dụ: Filter 1), load các giá trị filter (`fieldId`, `filterValue`) vào giao diện (`FilterFieldComponent`).

- **Nút Save Filter**:
  - Sau khi nhấn **Apply**, hiển thị nút **Save Filter** (Bootstrap `btn btn-outline-primary`) bên cạnh nút **Apply**.
  - **Filter mới**:
    - Nhấn **Save Filter** để mở `SaveFilterModalComponent` qua `ResponsiveModalService`.
    - Sau khi nhận tên filter, lưu vào `savedFilters` với `id` mới (UUID), `name`, và `filters`.
  - **Filter cũ**:
    - So sánh filter hiện tại với filter gốc (`fieldId`, `filterValue`).
    - Nếu có thay đổi, hiển thị **mat-menu** bên cạnh nút **Save Filter** với:
      - **Save**: Lưu đè filter cũ (key i18n: `FIELD_FILTERS.SAVE_MENU.SAVE`).
      - **Save As New**: Mở `SaveFilterModalComponent` (key i18n: `FIELD_FILTERS.SAVE_MENU.SAVE_AS_NEW`).

- **Quản lý savedFilters**:
  - Lưu `savedFilters` trong `FieldFiltersConfig` và quản lý qua `FieldFiltersService`.
  - Tải `savedFilters` trong `ngOnInit` của `FieldFiltersComponent`.
  - Cập nhật `savedFilters` khi lưu filter mới hoặc chỉnh sửa filter cũ.

---

#### 4. Thêm savedFilters vào mockProductListPage
Tôi muốn thêm dữ liệu mẫu cho `savedFilters` vào `mockProductListPage.fieldFiltersConfig` trong `src/infra/mock/product/product_list.mock.ts` để kiểm tra chức năng lưu filter tại `http://localhost:4200/#/product/list`.

- **Mô tả**:
  - Cập nhật `mockProductListPage` để bao gồm `savedFilters` trong `fieldFiltersConfig`.
  - Thêm ít nhất **2 saved filters** mẫu với các giá trị phù hợp cho danh sách sản phẩm (ví dụ: lọc theo tên sản phẩm, giá).
- **Yêu cầu**:
  - Đảm bảo `savedFilters` khớp với interface `SavedFilter`.
  - Sử dụng UUID cho `id`.
  - Các `fieldId` phải khớp với `Field._id` trong `fields` của `fieldFiltersConfig`.
  - Kiểm tra giao diện tại `http://localhost:4200/#/product/list` để đảm bảo saved filters hiển thị đúng.

---

#### 5. i18n
- **Vị trí**: `src/infra/i18n/shared/field-filters/`
- **Cấu trúc file**:
  - `src/infra/i18n/shared/field-filters/en.json`
  - `src/infra/i18n/shared/field-filters/vi.json`
- **Key i18n** (định dạng `SCREAMING_SNAKE_CASE`):
  ```json
  // en.json
  {
    "FIELD_FILTERS": {
      "SAVE_MODAL": {
        "TITLE": "Save Filter",
        "NAME_PLACEHOLDER": "Enter filter name",
        "SAVE": "Save",
        "CANCEL": "Cancel",
        "NAME_REQUIRED": "Filter name is required",
        "NAME_DUPLICATED": "Filter name already exists"
      },
      "SAVE_MENU": {
        "SAVE": "Save",
        "SAVE_AS_NEW": "Save as new"
      },
      "SAVED_FILTERS_TITLE": "Saved Filters",
      "CLEAR_ALL": "Clear All"
    }
  }

  // vi.json
  {
    "FIELD_FILTERS": {
      "SAVE_MODAL": {
        "TITLE": "Lưu bộ lọc",
        "NAME_PLACEHOLDER": "Nhập tên bộ lọc",
        "SAVE": "Lưu",
        "CANCEL": "Hủy",
        "NAME_REQUIRED": "Tên bộ lọc không được để trống",
        "NAME_DUPLICATED": "Tên bộ lọc đã tồn tại"
      },
      "SAVE_MENU": {
        "SAVE": "Lưu",
        "SAVE_AS_NEW": "Lưu thành mới"
      },
      "SAVED_FILTERS_TITLE": "Bộ lọc đã lưu",
      "CLEAR_ALL": "Xóa tất cả"
    }
  }
  ```


---



---


#### 8. Code mẫu: Cập nhật product_list.mock.ts

**File**: `src/infra/mock/product/product_list.mock.ts`


**Giải thích**:
- Thêm `savedFilters` với **2 filter mẫu**:
  - **Expensive Shirts**: Lọc sản phẩm có giá > 100 và thuộc danh mục "Shirt".
  - **Cheap Pants**: Lọc sản phẩm có giá từ 20 đến 50 và thuộc danh mục "Pants".
- `fieldId` khớp với `_id` trong `fields` (`product_name`, `price`, `category`).
- `filterValue` sử dụng `operator`, `value`, và `secondaryValue` (nếu cần) theo interface `FilterValue`.
- UUID được sử dụng cho `id` của mỗi `SavedFilter`.

---

#### 9. Kiểm tra giao diện
- **URL**: `http://localhost:4200/#/product/list`
- **Hành vi mong đợi**:
  - Danh sách saved filters hiển thị:
    ```
    Saved Filters
      - Expensive Shirts
      - Cheap Pants
    ```
  - Nhấn **Apply** để hiển thị nút **Save Filter**.
  - Nhấn **Save Filter** để mở modal nhập tên filter.
  - Khi chỉnh sửa filter cũ, hiển thị **mat-menu** với **Save** và **Save As New**.
- **Debug**:
  - Sử dụng **MCP server** (cổng 4200) để debug giao diện trên Chrome.
  - Thêm logic test vào `@test-theme.component.ts` nếu cần.
  - Chạy `ng build` để kiểm tra lỗi biên dịch.

---

#### 10. Quy trình thực hiện
1. Cập nhật `src/infra/mock/product/product_list.mock.ts` để thêm `savedFilters`.
2. Đảm bảo interface `FieldFiltersConfig`, `SavedFilter`, `FilterValue` trong `src/infra/shared/modals/view/field-filters.model.ts`.
3. Tạo `SaveFilterModalComponent` trong `src/infra/shared/components/field-filters/modals/save-filter-modal/`.
4. Thêm key i18n vào `src/infra/i18n/shared/field-filters/en.json` và `vi.json`.
5. Cập nhật `FieldFiltersComponent` để hiển thị saved filters và tích hợp `FilterSaveComponent`.
6. Cập nhật `FieldFiltersService` để quản lý `savedFilters`.
7. Tích hợp `SaveFilterModalComponent` với `ResponsiveModalService`.
8. Viết unit test cho `SaveFilterModalComponent`, `FilterSaveComponent`, `FieldFiltersService`.
9. Kiểm tra giao diện tại `http://localhost:4200/#/product/list`.
10. Chạy `ng build` và lưu tiến độ vào `.cursor/context/field-filters-task.md`.

---

#### 11. Giao diện minh họa
- **Danh sách saved filters**:
  ```
  Saved Filters
    - Expensive Shirts
    - Cheap Pants
  ```
- **Sau khi nhấn Apply**:
  ```
  [Apply] [Save Filter]
  ```
- **Modal Save Filter**:
  ```
  Save Filter
  [Enter filter name]
  [Cancel] [Save]
  ```
- **Khi chỉnh sửa filter cũ**:
  ```
  [Apply] [Save Filter ▼]
            - Save
            - Save As New
  ```



---

### Artifact: Yêu cầu cải thiện FieldFiltersComponent và Mock Data


# Yêu cầu cải thiện FieldFiltersComponent và thêm Mock Data

## Mục tiêu
Cải thiện **FieldFiltersComponent** để hỗ trợ lưu filter, hiển thị saved filters, tích hợp modal, và thêm `savedFilters` vào `mockProductListPage.fieldFiltersConfig` trong `src/infra/mock/product/product_list.mock.ts`. Kiểm tra tại `http://localhost:4200/#/product/list`.

## Yêu cầu chi tiết

### 1. Định nghĩa lại interface
- **FieldFiltersConfig**:
  ```typescript
  export interface FieldFiltersConfig {
    fields: Field[];
    showTitle: boolean;
    showClearAll: boolean;
    savedFilters?: SavedFilter[];
  }
  ```
- **SavedFilter**:
  ```typescript
  export interface SavedFilter {
    id: string;
    name: string;
    filters: Array<{
      fieldId: string;
      filterValue?: FilterValue;
    }>;
  }
  ```
- **FilterValue**:
  ```typescript
  export interface FilterValue {
    operator: string;
    value?: any;
    secondaryValue?: any;
  }
  ```
- **Vị trí**: `src/infra/shared/modals/view/field-filters.model.ts`.

### 2. Thêm SaveFilterModalComponent
- **Vị trí**: `src/infra/shared/components/field-filters/modals/save-filter-modal/`.
- **Vai trò**:
  - Hiển thị modal để nhập tên filter.
  - Sử dụng **ResponsiveModalService** để mở và nhận kết quả.
- **Giao diện**:
  - Tiêu đề: `FIELD_FILTERS.SAVE_MODAL.TITLE`.
  - Input: Textbox với placeholder `FIELD_FILTERS.SAVE_MODAL.NAME_PLACEHOLDER`.
  - Nút: **Save** (`FIELD_FILTERS.SAVE_MODAL.SAVE`), **Cancel** (`FIELD_FILTERS.SAVE_MODAL.CANCEL`).
  - Bootstrap classes: `modal-dialog`, `form-control`, `btn`.
- **Hành vi**:
  - Validate: Tên không trống, không trùng.
  - Emit: `{ name: string, filters: SavedFilter['filters'] }` khi nhấn Save.
- **Tích hợp**:
  - Gọi từ `FilterSaveComponent` khi lưu filter mới hoặc "Save As New".

### 3. Cải thiện FieldFiltersComponent
- **Hiển thị saved filters**:
  ```
  Saved Filters
    - Filter 1
    - Filter 2
  ```
  - Sử dụng `mat-list` hoặc `Bootstrap list-group`.
  - Nhấn saved filter để load giá trị vào `FilterFieldComponent`.
- **Nút Save Filter**:
  - Hiển thị sau **Apply** (Bootstrap `btn btn-outline-primary`).
  - Filter mới: Mở `SaveFilterModalComponent`.
  - Filter cũ (có thay đổi): **mat-menu** với:
    - **Save**: Lưu đè (`FIELD_FILTERS.SAVE_MENU.SAVE`).
    - **Save As New**: Mở modal (`FIELD_FILTERS.SAVE_MENU.SAVE_AS_NEW`).
- **Quản lý savedFilters**:
  - Lưu trong `FieldFiltersConfig.savedFilters`.
  - Tải trong `ngOnInit` qua `FieldFiltersService`.

### 4. Thêm savedFilters vào mockProductListPage
- **Vị trí**: `src/infra/mock/product/product_list.mock.ts`.
- **Yêu cầu**:
  - Thêm 2 saved filters mẫu:
    - **Expensive Shirts**: Giá > 100, danh mục "Shirt".
    - **Cheap Pants**: Giá 20-50, danh mục "Pants".
  - Đảm bảo `fieldId` khớp với `fields`.


### 5. i18n
- **Vị trí**: `src/infra/i18n/shared/field-filters/`.
- **Key**:
  ```json
  // en.json
  {
    "FIELD_FILTERS": {
      "SAVE_MODAL": {
        "TITLE": "Save Filter",
        "NAME_PLACEHOLDER": "Enter filter name",
        "SAVE": "Save",
        "CANCEL": "Cancel",
        "NAME_REQUIRED": "Filter name is required",
        "NAME_DUPLICATED": "Filter name already exists"
      },
      "SAVE_MENU": {
        "SAVE": "Save",
        "SAVE_AS_NEW": "Save as new"
      },
      "SAVED_FILTERS_TITLE": "Saved Filters",
      "CLEAR_ALL": "Clear All"
    }
  }
  // vi.json
  {
    "FIELD_FILTERS": {
      "SAVE_MODAL": {
        "TITLE": "Lưu bộ lọc",
        "NAME_PLACEHOLDER": "Nhập tên bộ lọc",
        "SAVE": "Lưu",
        "CANCEL": "Hủy",
        "NAME_REQUIRED": "Tên bộ lọc không được để trống",
        "NAME_DUPLICATED": "Tên bộ lọc đã tồn tại"
      },
      "SAVE_MENU": {
        "SAVE": "Lưu",
        "SAVE_AS_NEW": "Lưu thành mới"
      },
      "SAVED_FILTERS_TITLE": "Bộ lọc đã lưu",
      "CLEAR_ALL": "Xóa tất cả"
    }
  }
  ```
- **Quy trình**:
  - Thêm key vào `en.json`, `vi.json`.
  - Chạy `compare-translations.js` và `npm run merge-translations`.

### 6. Kiểm tra giao diện
- **URL**: `http://localhost:4200/#/product/list`.
- **Hành vi**:
  - Hiển thị saved filters: "Expensive Shirts", "Cheap Pants".
  - Nhấn **Apply** để hiển thị **Save Filter**.
  - Nhấn **Save Filter** để mở modal.
  - Chỉnh sửa filter cũ để hiển thị **mat-menu**.
- **Debug**:
  - Sử dụng MCP server (cổng 4200) trên Chrome.
  - Thêm logic test vào `@test-theme.component.ts`.
  - Chạy `ng build`.
