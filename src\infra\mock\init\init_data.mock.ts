export const mockInitUserInfo = {
  data: {
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2NjE4ZmY0ZGQ3MmUwODE0YzIyMzhlMzYiLCJlbWFpbCI6InR1bmdAeG9pbW9jLmNvbSIsInBvc2l0aW9uIjpbImFkbWluIl0sImlzT3duZXIiOnRydWUsInN0b3JlcyI6W3sibmFtZSI6IljDtGkgTeG7mWMiLCJzdG9yZUlkIjoiNjc3YjkxYmRlOWJmMjAyNGQzOWJmM2RkIiwiYnJhbmNoSWRzIjpbIjY3N2I5MWJkZTliZjIwMjRkMzliZjNkZSJdfV0sImlhdCI6MTczNjQwNjQ4NiwiZXhwIjoxNzM2NDA4Mjg2fQ.dCuGhbGGAazbwoAoWxawLp4y_d6DW6sqWyIWnZS-oQY',
    _id: '6618ff4dd72e0814c2238e36',
    name: 'Tung',
    email: '<EMAIL>',
    position: [
      'admin'
    ],
    isActive: true,
    isOwner: true,
    stores: [
      {
        name: '<PERSON><PERSON><PERSON>',
        storeId: '679d9603ade807758347f0a3',
        branchIds: [
          '679d9603ade807758347f0a4',
          '679d9603ade807758347f0aa'
        ]
      }
    ],
    iat: 1739606782
  }
};

export const mockInitData = {
  data: {
    init: {
      user: {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2NjE4ZmY0ZGQ3MmUwODE0YzIyMzhlMzYiLCJlbWFpbCI6InR1bmdAeG9pbW9jLmNvbSIsInBvc2l0aW9uIjpbImFkbWluIl0sImlzT3duZXIiOnRydWUsInN0b3JlcyI6W3sibmFtZSI6IljDtGkgTeG7mWMiLCJzdG9yZUlkIjoiNjc3YjkxYmRlOWJmMjAyNGQzOWJmM2RkIiwiYnJhbmNoSWRzIjpbIjY3N2I5MWJkZTliZjIwMjRkMzliZjNkZSJdfV0sImlhdCI6MTczNjQwNjQ4NiwiZXhwIjoxNzM2NDA4Mjg2fQ.dCuGhbGGAazbwoAoWxawLp4y_d6DW6sqWyIWnZS-oQY',
        _id: '6618ff4dd72e0814c2238e36',
        name: 'Tung',
        email: '<EMAIL>',
        position: [
          'admin'
        ],
        isActive: true,
        isOwner: true,
        stores: [
          {
            name: 'Xôi Mộc',
            storeId: '679d9603ade807758347f0a3',
            branchIds: [
              '679d9603ade807758347f0a4',
              '679d9603ade807758347f0aa'
            ]
          }
        ],
        iat: 1739606782
      },
      receiptionists: [
        {
          _id: '6618ff4dd72e0814c2238e36',
          name: 'Tung'
        },
        {
          _id: '661900147c90979377e559c7',
          name: 'Thao'
        },
        {
          _id: '665a43e98c4a61faadef9405',
          name: 'Hằng'
        },
        {
          _id: '66cd1d5731ca26ae522e9b41',
          name: 'Nhung'
        }
      ],
      store: {
        _id: '679d9603ade807758347f0a5',
        name: 'Xôi Mộc',
        storeId: '679d9603ade807758347f0a3',
        branchName: 'Chi nhánh trung tâm',
        branchId: '679d9603ade807758347f0a4',
        shareDataWithBranchIds: [],
        address: '150 Trường Thi, TP. Thanh Hóa',
        addressInfo: {
          fullAddress: '150 Trường Thi, TP. Thanh Hóa',
          mainAddress: '150 Trường Thi',
          instructions: [],
          plus_code: {
            compound_code: 'RQ8J+2M Thanh Hóa, Thanh Hoa, Vietnam',
            global_code: '7PF7RQ8J+2M'
          },
          viewport: {
            northeast: {
              lat: 19.8164150302915,
              lng: 105.************
            },
            southwest: {
              lat: 19.*************,
              lng: 105.************
            }
          },
          placeId: 'ChIJ7eLe_f33NjERgfOM7lyLDgk',
          lat: 19.8150946,
          lng: 105.7816887,
          region: 'vietnam:thanhhoa',
          regionName: 'vietnam:thanhhoa',
          regionSearch: 'thanh hoa'
        },
        openingDate: '2022-11-28T00:00:00.000Z',
        phone: '0916.999.192',
        website: 'xoimoc.com',
        bank: {
          bankName: 'Ngân hàng Vietcombank',
          accountId: 'xoimoc (hoặc *************)',
          accountName: 'BUI NHAT TUNG',
          baseQrCode: 'https://api.vietqr.io/image/970436-*************-vmDpP9D.jpg?accountName=BUI%20NHAT%20TUNG'
        },
        networkPrinter: {
          ip: '*************',
          port: 9100
        },
        grab: {
          merchantId: '5-C36TCK5ENVDZLE',
          advertiserId: '458214696554610648',
          grabId: '518f3079-dcf7-40ea-bc65-26e822f6c06f',
          auth: {
            username: '<EMAIL>',
            password: 'Gr3$tbnt'
          },
          commissionRate: 20,
          autoIncreaseBudgets: [
            {
              campaignID: '528049330611847501',
              day: 0,
              increaseToBudget: 50000,
              normalBudget: 30000,
              _id: '679d9603ade807758347f0a6'
            },
            {
              campaignID: '528050051965015618',
              day: 0,
              increaseToBudget: 100000,
              normalBudget: 50000,
              _id: '679d9603ade807758347f0a7'
            }
          ]
        },
        shopee: {
          merchantId: '********',
          restaurantId: '1163773',
          auth: {
            username: '84916999192',
            password: 'Gr3$tbnt',
            hashedPassword: '572dcd2cda9dc076c0ae192e46e23f1cb78240bcc71972d5d2207cbe0a43096d'
          }
        },
        kiotviet: {
          retailer: 'diyhouse',
          auth: {
            username: '0888928383',
            password: '12345678'
          }
        },
        initCost: {
          total: 150000000,
          paybackInYears: 3
        },
        reportAlertConfigs: {
          discord: {
            token: 'NDY2NzY1NTM3MTg4MzgwNjg0.D3DbHA.kYbMj4aJ3_MvLtu-aiAcTyvMShY',
            prefix: '+',
            channel: '1306064374795997257'
          }
        },
        createdAt: '2025-02-01T03:33:23.202Z',
        updatedAt: '2025-02-01T03:33:23.202Z',
        __v: 0,
        bill: {
          logo: 'https://quanly.xoimoc.com/assets/images/xoimoc_bill_logo.jpg',
          byeMsg: 'Chuc quy khach ngon mieng!'
        }
      },
      countPendingOrders: 6,
      countUnreadReviews: 0,
      currentShift: {
        shiftId: 11,
        employees: [
          {
            _id: '66cd1d5731ca26ae522e9b41',
            name: 'Nhung'
          }
        ],
        startAt: '2025-02-08T08:45:15.843Z',
        startAtText: '15:45 Thứ 7, 8/2/2025'
      },
      appVersion: '1.0.805'
    }
  }
};
