<!-- Dynamic Layout Builder Main Container -->
<div class="dynamic-layout-builder-container">
  <!-- Compact Single-Line Header -->
  <mat-toolbar class="layout-toolbar compact-toolbar">
    <!-- Left Section: Layout Selector + Tabs -->
    <div class="toolbar-left">
      <!-- Layout Selector (Inline) -->
      <div class="inline-layout-selector">
        <app-layout-selector></app-layout-selector>
      </div>

      <!-- Inline Tabs -->
      <div class="inline-tabs">
        <div class="compact-tabs-nav">
          <!-- Create Tab -->
          <button
            type="button"
            class="compact-tab-btn"
            [class.active]="selectedTabIndex() === 0"
            (click)="onTabClick(0)">
            <span class="compact-tab-label">{{ 'DYNAMIC_LAYOUT_BUILDER.TABS.CREATE' | translate }}</span>
            <mat-icon
              class="tab-info-icon"
              [matTooltip]="'DYNAMIC_LAYOUT_BUILDER.TABS.TOOLTIPS.CREATE' | translate"
              matTooltipPosition="below">
              info_outline
            </mat-icon>
          </button>

          <!-- Quick Create Tab (Conditional) -->
          @if (hasQuickCreate()) {
            <button
              type="button"
              class="compact-tab-btn"
              [class.active]="selectedTabIndex() === 1"
              (click)="onTabClick(1)">
              <span class="compact-tab-label">{{ 'DYNAMIC_LAYOUT_BUILDER.TABS.QUICK_CREATE' | translate }}</span>
              <mat-icon
                class="tab-info-icon"
                [matTooltip]="'DYNAMIC_LAYOUT_BUILDER.TABS.TOOLTIPS.QUICK_CREATE' | translate"
                matTooltipPosition="below">
                info_outline
              </mat-icon>
            </button>
          }

          <!-- Detail View Tab (Conditional) -->
          @if (hasDetailViewConfig()) {
            <button
              type="button"
              class="compact-tab-btn"
              [class.active]="selectedTabIndex() === (hasQuickCreate() ? 2 : 1)"
              (click)="onTabClick(hasQuickCreate() ? 2 : 1)">
              <span class="compact-tab-label">{{ 'DYNAMIC_LAYOUT_BUILDER.TABS.DETAIL_VIEW' | translate }}</span>
              <mat-icon
                class="tab-info-icon"
                [matTooltip]="'DYNAMIC_LAYOUT_BUILDER.TABS.TOOLTIPS.DETAIL_VIEW' | translate"
                matTooltipPosition="below">
                info_outline
              </mat-icon>
            </button>
          }
        </div>
      </div>
    </div>

    <span class="spacer"></span>

    <!-- Right Section: Action Buttons -->
    <div class="toolbar-actions">
      <!-- More Options Menu -->
      <button
        mat-icon-button
        class="more-options-btn"
        [matMenuTriggerFor]="moreOptionsMenu"
        [matTooltip]="'DYNAMIC_LAYOUT_BUILDER.ACTIONS.MORE_OPTIONS' | translate">
        <mat-icon>more_vert</mat-icon>
      </button>

      <!-- More Options Menu -->
      <mat-menu #moreOptionsMenu="matMenu" class="more-options-menu">
        <button mat-menu-item (click)="onTogglePreview()">
          <mat-icon>visibility</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.ACTIONS.PREVIEW' | translate }}</span>
        </button>
        <button mat-menu-item (click)="onCancel()">
          <mat-icon>cancel</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.ACTIONS.CANCEL' | translate }}</span>
        </button>
      </mat-menu>

      <!-- Save Button -->
      <button type="button" class="btn btn-primary btn-sm" (click)="onSaveLayout()">
        <i class="fa-light fa-floppy-disk me-1"></i>
        {{ 'DYNAMIC_LAYOUT_BUILDER.ACTIONS.SAVE' | translate }}
      </button>
    </div>
  </mat-toolbar>

  <!-- Main Content Area -->
  <div
    class="layout-content"
    [class.preview-mode]="isPreviewMode()"
    #content
    >

    <!-- Tab Content (Hidden tabs, content shown based on selectedTabIndex) -->
    <div class="tab-content-container">

      <!-- Create Tab Content -->
      @if (selectedTabIndex() === 0) {
        <div class="tab-content active">
          <!-- ✅ REFACTORED: No @Input bindings - component gets data from DynamicLayoutConfigStateService -->
          <app-create-tab #createTab>
          </app-create-tab>
        </div>
      }

      <!-- Quick Create Tab Content -->
      @if (hasQuickCreate() && selectedTabIndex() === 1) {
        <div class="tab-content active">
          <!-- ✅ REFACTORED: No @Input bindings - component gets data from DynamicLayoutConfigStateService -->
          <app-quick-create-tab #quickCreateTab>
          </app-quick-create-tab>
        </div>
      }

      <!-- Detail View Tab Content -->
      @if (hasDetailViewConfig() && selectedTabIndex() === (hasQuickCreate() ? 2 : 1)) {
        <div class="tab-content active">
          <!-- ✅ FIXED: Component subscribes to layout changes internally and re-initializes SortableJS -->
          <app-detail-view-tab #detailViewTab>
          </app-detail-view-tab>
        </div>
      }

    </div>
  </div>
</div>
