.order-form-container {
  display: flex;
  flex-direction: column;
  // height: 100%;
  width: 100%;
  background-color: #f5f8fa;

  .order-form-header {
    padding: 1rem;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;

    h1 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: #212529;
    }
  }

  .order-form-tabs {
    padding: 0 1rem;
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
  }

  .order-form-content {
    flex: 1;
  }
}

// Responsive styles
@media (max-width: 768px) {
  .order-form-container {
    .order-form-header {
      padding: 0.75rem;

      h1 {
        font-size: 1.25rem;
      }
    }

    .order-form-tabs {
      padding: 0 0.5rem;
    }

    .order-form-content {
      overflow: auto;
    }
  }
}
