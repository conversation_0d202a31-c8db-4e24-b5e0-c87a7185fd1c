// Dynamic Layout Renderer Component Styles
.dynamic-layout-renderer-container {
  width: 100%;
  max-width: 100%;
  
  // Header Section Styles
  .renderer-header {
    background-color: #ffffff;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
    
    .header-title {
      h4 {
        color: #495057;
        font-weight: 600;
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
      }
      
      p {
        font-size: 0.9rem;
        line-height: 1.4;
      }
    }
    
    .permission-selector-section {
      max-width: 400px;
    }
    
    .tab-switcher-section {
      .renderer-tabs {
        .mat-mdc-tab-group {
          .mat-mdc-tab-header {
            border-bottom: 2px solid #e9ecef;
          }
          
          .mat-mdc-tab {
            .tab-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }
        }
      }
    }
  }
  
  // Content Section Styles
  .renderer-content {
    padding: 0 1.5rem 1.5rem;
    
    .sections-container {
      .section-wrapper {
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    // Empty State Styles
    .empty-state-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
      
      .empty-state-card {
        max-width: 400px;
        border: 2px dashed #dee2e6;
        background-color: #f8f9fa;
        
        .empty-state-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          color: #6c757d;
        }
        
        h5 {
          color: #495057;
          font-weight: 600;
        }
      }
    }
  }

  // Form Controls Section Styles
  .form-controls-section {
    padding: 0 1.5rem 1.5rem;

    .form-controls-card {
      border: 1px solid #e9ecef;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .form-status-info {
        .badge {
          font-size: 0.75rem;
          padding: 0.375rem 0.75rem;

          mat-icon {
            vertical-align: middle;
          }
        }
      }

      .form-actions {
        .spinner {
          animation: spin 1s linear infinite;
        }

        button {
          min-width: 100px;

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 992px) {
  .dynamic-layout-renderer-container {
    .renderer-header {
      padding: 1.25rem;
      margin-bottom: 1.25rem;
      
      .header-title {
        h4 {
          font-size: 1.15rem;
        }
        
        p {
          font-size: 0.85rem;
        }
      }
      
      .permission-selector-section {
        max-width: 100%;
      }
      
      .tab-switcher-section {
        .renderer-tabs {
          .mat-mdc-tab {
            .tab-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }
    
    .renderer-content {
      padding: 0 1.25rem 1.25rem;
    }

    .form-controls-section {
      padding: 0 1.25rem 1.25rem;
    }
  }
}

@media (max-width: 768px) {
  .dynamic-layout-renderer-container {
    .renderer-header {
      padding: 1rem;
      margin-bottom: 1rem;
      border-radius: 0.375rem 0.375rem 0 0;
      
      .header-title {
        margin-bottom: 1rem;
        
        h4 {
          font-size: 1.1rem;
        }
        
        p {
          font-size: 0.8rem;
        }
      }
      
      .permission-selector-section {
        margin-bottom: 1rem;
      }
      
      .tab-switcher-section {
        .renderer-tabs {
          .mat-mdc-tab {
            min-width: 120px;
            
            .tab-icon {
              font-size: 14px;
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }
    
    .renderer-content {
      padding: 0 1rem 1rem;
      
      .empty-state-container {
        min-height: 250px;
        
        .empty-state-card {
          .empty-state-icon {
            font-size: 40px;
            width: 40px;
            height: 40px;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .dynamic-layout-renderer-container {
    .renderer-header {
      padding: 0.875rem;
      margin-bottom: 0.875rem;
      border-radius: 0.25rem 0.25rem 0 0;
      
      .header-title {
        margin-bottom: 0.875rem;
        
        h4 {
          font-size: 1rem;
        }
        
        p {
          font-size: 0.75rem;
        }
      }
      
      .permission-selector-section {
        margin-bottom: 0.875rem;
      }
      
      .tab-switcher-section {
        .renderer-tabs {
          .mat-mdc-tab {
            min-width: 100px;
            font-size: 0.85rem;
            
            .tab-icon {
              font-size: 12px;
              width: 12px;
              height: 12px;
            }
          }
        }
      }
    }
    
    .renderer-content {
      padding: 0 0.875rem 0.875rem;
      
      .empty-state-container {
        min-height: 200px;
        
        .empty-state-card {
          .empty-state-icon {
            font-size: 32px;
            width: 32px;
            height: 32px;
          }
          
          h5 {
            font-size: 0.95rem;
          }
          
          p {
            font-size: 0.8rem;
          }
        }
      }
    }
  }
}

// Animation for content changes
.renderer-content {
  transition: opacity 0.3s ease-in-out;
}

// Tab animation
.renderer-tabs {
  .mat-mdc-tab-body-wrapper {
    .mat-mdc-tab-body {
      transition: transform 0.3s ease-in-out;
    }
  }
}

// Spinner animation
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
