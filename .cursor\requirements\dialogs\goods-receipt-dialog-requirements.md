## **Giới thiệu và Mục tiêu**
Bạn sẽ phát triển một module frontend hoàn chỉnh cho chức năng **<PERSON><PERSON><PERSON><PERSON> (Goods Receipt)** trong hệ thống ERP, sử dụng **Angular 19** và thư viện **Angular Material**. <PERSON><PERSON><PERSON> này cho phép người dùng quản lý quá trình nhận hàng vào kho từ nhà cung cấp, bao gồm việc chọn sản phẩm, quản lý lô hàng (batches), chọn đơn vị tính (units), xử lý biến thể (variants), và ghi nhận thông tin tài chính (tổng tiền, thanh toán, công nợ). Giao diện phải **responsive**, trự<PERSON> quan, và tích hợp **mock data** đ<PERSON> mô phỏng dữ liệu thực tế mà không cần backend.

<PERSON><PERSON><PERSON> tiêu:
- <PERSON><PERSON><PERSON> một giao di<PERSON> đẹp, d<PERSON> sử dụng với các thành phần Angular Material như `mat-autocomplete`, `mat-chip-list`, `mat-dialog`, `mat-bottom-sheet`.
- Đảm bảo tính tương tác real-time (ví dụ: cập nhật tổng tiền khi thêm sản phẩm hoặc thay đổi số lượng).
- Viết code sạch, có cấu trúc thư mục rõ ràng, sử dụng TypeScript interfaces, và tuân thủ best practices của Angular.

---

Các yêu cầu cơ bản:  
- Luôn tuân thủ rules của [angular.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/angular.mdc) và [general.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/general.mdc).  
- Sử dụng MCP server từ [mcp.json](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/mcp.json) khi cần thiết để debug lỗi và view trên browser.  
- Trước khi tự động chạy, nếu có gì cần làm rõ thì hỏi lại để rõ ràng các ý chính trước khi tự động chạy, không phải sửa đi sửa lại nhiều lần vì không hiểu ý.  

--

Các mock sử dụng: 
- [text](../../src/app/mock/shared/list.mock.ts)
- [text](../../src/app/mock/shared/product.mock.ts)
Interface:
- [text](../../../shared_contracts/js/src/entities/ims/inventory/goods_receipt.ts)
- [text](../../../shared_contracts/js/src/requests/shared/list.ts)

InputWarehouseLocationComponent: [text](../../src/app/shared/components/input/input-warehouse-location/input-warehouse-location.component.ts)
---

Viết tất cả Dialog vào [text](../../src/app/features/warehouse/dialogs/*)

## **Dialogs**
### **Dialog Lô hàng (BatchDialog)**
- **Input**:
  - **Số lô**: `mat-form-field` (text), bắt buộc.
  - **Ngày sản xuất**: `mat-datepicker`, mặc định hôm nay.
  - **Hạn sử dụng**: `mat-datepicker`, bắt buộc.
  - **Số lượng**: Input number, bắt buộc, tối thiểu 1.
- **Nút**:
  - "Hủy" (`mat-button`): Đóng dialog.
  - "Lưu" (`mat-raised-button`): Emit dữ liệu lô và đóng dialog.
- **Validation**: Kiểm tra các trường bắt buộc và số lượng > 0.

### **Dialog Thêm hàng hóa từ nhóm hàng**
- **UI**:
  - Danh sách nhóm hàng (`mat-selection-list`) với checkbox.
  - Mock `mockCategoryList`.
- **Nút**:
  - "Hủy": Đóng dialog.
  - "Thêm": Emit danh sách sản phẩm (`mockProductList`) từ các nhóm đã chọn `mockCategoryList`.
- **Chức năng**: Khi nhấn "Thêm", trả về sản phẩm từ nhóm (filter từ `mockProductList`, cái nào có `categoryIds` = _id các nhóm hàng đã selected).

## **Dialog: AdditionalCostDialog**

### **Mô tả**

Dialog để tạo mới hoặc chỉnh sửa một chi phí khác (`ImportAdditionalCost`).

### **Giao diện**

- **Tiêu đề**:
  - "Thêm chi phí khác" nếu `MAT_DIALOG_DATA.cost` là `undefined` (tạo mới).
  - "Chỉnh sửa chi phí" nếu `MAT_DIALOG_DATA.cost` tồn tại (chỉnh sửa).
- **Form**:
  - **Tên**:
    - `mat-form-field` với input text.
    - Placeholder: "Nhập tên chi phí (VD: Phí vận chuyển)".
    - Bắt buộc, tối đa 100 ký tự.
    - Nếu chỉnh sửa, điền giá trị từ `cost.name`.
  - **Loại chi phí**:
    - `mat-radio-group` với hai tùy chọn:
      - "Cố định" (`costValue.type = 'fixed'`).
      - "Phần trăm" (`costValue.type = 'percentage'`).
    - Bắt buộc, mặc định là `fixed` nếu tạo mới, hoặc `cost.costValue.type` nếu chỉnh sửa.
  - **Giá trị**:
    - `mat-form-field` với input number.
    - Placeholder: "Nhập giá trị (VD: 500000 hoặc 5)".
    - Bắt buộc, tối thiểu 0.
    - Hiển thị đơn vị bên cạnh:
      - "VND" nếu `costValue.type = 'fixed'`.
      - "%" nếu `costValue.type = 'percentage'`.
    - Nếu chỉnh sửa, điền giá trị từ `cost.costValue.value`.
  - **Trả cho nhà cung cấp**:
    - `mat-checkbox` với label "Trả cho nhà cung cấp".
    - Mặc định: Không tích nếu tạo mới, hoặc `cost.paidToSupplier` nếu chỉnh sửa.
  - **Phân bổ vào giá vốn sản phẩm**:
    - `mat-checkbox` với label "Phân bổ vào giá vốn sản phẩm".
    - Mặc định: Tích nếu tạo mới, hoặc `cost.allocateToItems` nếu chỉnh sửa.
  - **Tỷ lệ thuế**:
    - `mat-form-field` với input number.
    - Placeholder: "Tỷ lệ thuế (%)".
    - Tùy chọn, nếu nhập thì tối thiểu 0, tối đa 100, bước nhảy 0.1.
    - Nếu chỉnh sửa, điền giá trị từ `cost.tax.rate`.
  - **Số tiền thuế**:
    - `mat-form-field` với input number.
    - Placeholder: "Số tiền thuế (VND)".
    - Tùy chọn, nếu nhập thì tối thiểu 0.
    - Tự động tính nếu "Tỷ lệ thuế" và "Giá trị" được nhập (xem logic).
    - Nếu chỉnh sửa, điền giá trị từ `cost.tax.amount`.
- **Thông tin bổ sung**:
  - Nếu `MAT_DIALOG_DATA.subTotal` có sẵn, hiển thị text nhỏ dưới form:
    - "Tổng tiền hàng: X VND" (X là `subTotal`, định dạng tiền tệ).
  - Nếu "Tỷ lệ thuế" và "Giá trị" được nhập, hiển thị:
    - "Số tiền thuế tự động: Y VND" (Y = tính tự động, xem logic).
- **Nút**:
  - **Hủy**: `mat-button`, đóng dialog mà không emit dữ liệu.
  - **Lưu**: `mat-raised-button` màu primary, emit object `ImportAdditionalCost` và đóng dialog.
  - Vị trí: Góc dưới bên phải, nút "Lưu" disable nếu form không hợp lệ.

### **Logic**

- **Validation**:
  - **Tên**: Bắt buộc, tối đa 100 ký tự.
  - **Loại chi phí**: Bắt buộc.
  - **Giá trị**: Bắt buộc, >= 0.
  - **Tỷ lệ thuế**: Tùy chọn, nếu nhập thì >= 0 và <= 100.
  - **Số tiền thuế**: Tùy chọn, nếu nhập thì >= 0.
  - Hiển thị lỗi bằng `mat-error` (VD: "Vui lòng nhập tên chi phí", "Giá trị không hợp lệ").
  - Disable nút "Lưu" nếu form không hợp lệ (`form.invalid`).
- **Tự động tính số tiền thuế**:
  - Nếu "Tỷ lệ thuế" (`tax.rate`) và "Giá trị" (`costValue.value`) được nhập:
    - Nếu `costValue.type = 'fixed'`:
      - Tính `tax.amount = costValue.value * tax.rate / 100`.
    - Nếu `costValue.type = 'percentage'` và `MAT_DIALOG_DATA.subTotal` có sẵn:
      - Tính `tax.amount = (costValue.value * subTotal / 100) * tax.rate / 100`.
    - Cập nhật ô "Số tiền thuế" ngay lập tức.
  - Nếu người dùng nhập "Số tiền thuế" thủ công, ưu tiên giá trị này và không cập nhật tự động.
  - Nếu chỉnh sửa (`cost` có sẵn), ưu tiên `cost.tax.amount` ban đầu trừ khi người dùng thay đổi `tax.rate` hoặc `costValue.value`.
- **Dữ liệu đầu vào**:
  - Nhận `MAT_DIALOG_DATA` là một object chứa:
    - `cost`: Object `ImportAdditionalCost` nếu chỉnh sửa, hoặc `undefined` nếu tạo mới.
    - `subTotal`: Number (tùy chọn, để tính thuế tự động cho chi phí phần trăm).
  - Nếu `cost` tồn tại, điền form với:
    - `name`: `cost.name`.
    - `costValue.type`: `cost.costValue.type`.
    - `costValue.value`: `cost.costValue.value`.
    - `paidToSupplier`: `cost.paidToSupplier`.
    - `allocateToItems`: `cost.allocateToItems`.
    - `tax.rate`: `cost.tax.rate` (mặc định 0 nếu không có).
    - `tax.amount`: `cost.tax.amount` (mặc định 0 nếu không có).
  - Nếu `cost` là `undefined`, để form trống với:
    - `costValue.type = 'fixed'`.
    - `paidToSupplier = false`.
    - `allocateToItems = true`.
    - `tax.rate = 0`.
    - `tax.amount = 0`.
- **Emit dữ liệu**:
  - Khi nhấn "Lưu", emit một object `ImportAdditionalCost`:
    - `_id`: Giữ nguyên từ `cost._id` nếu chỉnh sửa, hoặc để trống nếu tạo mới (sẽ được gán bởi backend hoặc service).
    - `name`: Giá trị từ input "Tên".
    - `costValue`:
      - `type`: Giá trị từ `mat-radio-group`.
      - `value`: Giá trị từ input "Giá trị".
    - `paidToSupplier`: Giá trị từ checkbox "Trả cho nhà cung cấp".
    - `allocateToItems`: Giá trị từ checkbox "Phân bổ vào giá vốn sản phẩm".
    - `tax`:
      - `rate`: Giá trị từ input "Tỷ lệ thuế", mặc định 0 nếu không nhập.
      - `amount`: Giá trị từ input "Số tiền thuế" hoặc tính tự động, mặc định 0 nếu không nhập.
  - Đóng dialog bằng `MatDialogRef.close(data)`.
- **UX**:
  - Form bố trí dọc, các trường cách nhau 16px.
  - Đơn vị (VND, %) hiển thị ngay cạnh ô "Giá trị" bằng `mat-hint` hoặc text nhỏ.
  - Ô "Số tiền thuế" cập nhật real-time khi nhập "Tỷ lệ thuế" hoặc "Giá trị" (nếu áp dụng).
  - Text thông tin bổ sung (`subTotal`, `tax.amount` tự động) hiển thị bằng font nhỏ (12px), màu xám.
  - Nút "Lưu" nổi bật, căn phải, hover đổi màu nhẹ.
  - Tooltip trên ô "Tỷ lệ thuế": "Nhập tỷ lệ để tự động tính số tiền thuế dựa trên giá trị chi phí".
  - Khi mở dialog để chỉnh sửa, tự động focus vào ô "Tên".

--

## **Dialog: TaxDialog**

### **Mô tả**

Dialog để tạo mới hoặc chỉnh sửa một khoản thuế (`TaxInfo`).

### **Giao diện**
- **Tiêu đề**:
  - "Thêm thuế" nếu `MAT_DIALOG_DATA.tax` là `undefined` (tạo mới).
  - "Chỉnh sửa thuế" nếu `MAT_DIALOG_DATA.tax` tồn tại (chỉnh sửa).
- **Form**:
  - **Loại thuế**:
    - `mat-select` với các tùy chọn:
      - "Thuế GTGT" (`VAT`).
      - "Thuế nhập khẩu" (`import_tax`).
      - "Khác" (`other`).
    - Placeholder: "Chọn loại thuế".
    - Bắt buộc, mặc định là `VAT` nếu tạo mới, hoặc `tax.type` nếu chỉnh sửa.
  - **Tỷ lệ thuế**:
    - `mat-form-field` với input number.
    - Placeholder: "Tỷ lệ thuế (%)".
    - Tùy chọn, tối thiểu 0, tối đa 100, bước nhảy 0.1.
    - Nếu chỉnh sửa, điền giá trị từ `tax.rate`.
  - **Số tiền thuế**:
    - `mat-form-field` với input number.
    - Placeholder: "Số tiền thuế (VND)".
    - Bắt buộc, tối thiểu 0.
    - Nếu chỉnh sửa, điền giá trị từ `tax.amount`.
- **Thông tin bổ sung**:
  - Nếu `MAT_DIALOG_DATA.subTotal` có sẵn, hiển thị text nhỏ dưới form:
    - "Tổng tiền hàng: X VND" (X là `subTotal`, định dạng tiền tệ).
    - Nếu "Tỷ lệ thuế" được nhập, hiển thị: "Số tiền thuế tự động: Y VND" (Y = `subTotal * rate / 100`).
- **Nút**:
  - **Hủy**: `mat-button`, đóng dialog mà không emit dữ liệu.
  - **Lưu**: `mat-raised-button` màu primary, emit object `TaxInfo` và đóng dialog.
  - Vị trí: Góc dưới bên phải, nút "Lưu" disable nếu form không hợp lệ.

### **Logic**

- **Validation**:
  - **Loại thuế**: Bắt buộc.
  - **Số tiền thuế**: Bắt buộc, >= 0.
  - **Tỷ lệ thuế**: Tùy chọn, nếu nhập thì >= 0 và <= 100.
  - Hiển thị lỗi bằng `mat-error` (VD: "Vui lòng chọn loại thuế", "Số tiền thuế không hợp lệ").
  - Disable nút "Lưu" nếu form không hợp lệ (`form.invalid`).
- **Tự động tính**:
  - Nếu `MAT_DIALOG_DATA.subTotal` có sẵn và người dùng nhập "Tỷ lệ thuế" (`rate`):
    - Tính `amount = subTotal * rate / 100`.
    - Cập nhật ô "Số tiền thuế" ngay lập tức.
  - Nếu người dùng nhập "Số tiền thuế" thủ công, ưu tiên giá trị này và không cập nhật tự động.
  - Nếu chỉnh sửa (`tax` có sẵn), ưu tiên `tax.amount` ban đầu trừ khi người dùng thay đổi `rate` hoặc `amount`.
- **Dữ liệu đầu vào**:
  - Nhận `MAT_DIALOG_DATA` là một object chứa:
    - `tax`: Object `TaxInfo` (`type`, `rate`, `amount`) nếu chỉnh sửa, hoặc `undefined` nếu tạo mới.
    - `subTotal`: Number (tùy chọn, để tính thuế tự động).
  - Nếu `tax` tồn tại, điền form với:
    - `type`: `tax.type`.
    - `rate`: `tax.rate`.
    - `amount`: `tax.amount`.
  - Nếu `tax` là `undefined`, để form trống với `type = 'VAT'`.
- **Emit dữ liệu**:
  - Khi nhấn "Lưu", emit một object `TaxInfo`:
    - `type`: Giá trị từ `mat-select`.
    - `rate`: Giá trị từ input "Tỷ lệ thuế", mặc định 0 nếu không nhập.
    - `amount`: Giá trị từ input "Số tiền thuế" hoặc tính tự động.
  - Đóng dialog bằng `MatDialogRef.close(data)`.
- **UX**:
  - Form bố trí dọc, các trường cách nhau 16px.
  - Ô "Số tiền thuế" cập nhật real-time khi nhập "Tỷ lệ thuế" (nếu có `subTotal`).
  - Text thông tin bổ sung (`subTotal`, `amount` tự động) hiển thị bằng font nhỏ (12px), màu xám.
  - Nút "Lưu" nổi bật, căn phải, hover đổi màu nhẹ.
  - Tooltip trên ô "Tỷ lệ thuế": "Nhập tỷ lệ để tự động tính số tiền thuế nếu có tổng tiền hàng".
  - Khi mở dialog để chỉnh sửa, tự động focus vào ô "Tỷ lệ thuế".


---

## **Dialog: SelectAdditionalCostsDialog**

### **Mô tả**

Dialog để chọn chi phí từ danh sách `mockImportAdditionalCosts`, tùy chỉnh giá trị chi phí, và thêm chi phí mới thông qua **AdditionalCostDialog**.

### **Giao diện**

- **Tiêu đề**: "Chọn chi phí khác".
- **Nút tạo mới**:
  - `mat-raised-button` với label "Tạo mới chi phí" và icon `add`.
  - Vị trí: Góc trên bên phải của dialog.
  - Khi nhấn, mở **AdditionalCostDialog** (không truyền dữ liệu đầu vào).
- **Bảng**:
  - Hiển thị danh sách chi phí từ `MAT_DIALOG_DATA.items` (`mockImportAdditionalCosts`).
  - **Cột**:
    - **Chọn**:
      - `mat-checkbox` để chọn chi phí.
      - Tích mặc định nếu chi phí có trong `MAT_DIALOG_DATA.current` (so sánh bằng `_id`).
    - **Tên**:
      - Hiển thị `name` (VD: "Phí vận chuyển nội địa").
      - Font: Regular.
    - **Loại**:
      - Hiển thị `costValue.type` ("Cố định" nếu `fixed`, "Phần trăm" nếu `percentage`).
    - **Giá trị mặc định**:
      - Hiển thị `costValue.value`, định dạng tiền tệ (VND) nếu `fixed`, hoặc phần trăm (%) nếu `percentage`.
    - **Giá trị tùy chỉnh**:
      - `mat-form-field` với input number.
      - Mặc định:
        - Nếu chi phí có trong `current`, lấy `costValue.value` từ `current`.
        - Nếu không, lấy `costValue.value` từ `items`.
      - Bắt buộc nếu checkbox được tích, tối thiểu 0.
    - **Trả nhà cung cấp**:
      - `mat-checkbox` readonly, hiển thị trạng thái `paidToSupplier`.
    - **Phân bổ**:
      - `mat-checkbox` readonly, hiển thị trạng thái `allocateToItems`.
    - **Thuế**:
      - Hiển thị `tax.rate` và `tax.amount` nếu có (VD: "10% (50,000 VND)"), hoặc "Không" nếu không có thuế.
      - Nếu `tax` có, hiển thị readonly.
  - **Dữ liệu**:
    - Lấy từ `MAT_DIALOG_DATA.items` (22 chi phí từ `mockImportAdditionalCosts`).
    - Các chi phí trong `MAT_DIALOG_DATA.current` được tích checkbox và điền giá trị tùy chỉnh.
- **Nút**:
  - **Hủy**: `mat-button`, đóng dialog mà không emit dữ liệu.
  - **Lưu**: `mat-raised-button` màu primary, emit danh sách chi phí được chọn và đóng dialog.
  - Vị trí: Góc dưới bên phải, nút "Lưu" disable nếu có chi phí được chọn nhưng giá trị tùy chỉnh không hợp lệ.

### **Logic**

- **Validation**:
  - **Giá trị tùy chỉnh**: Bắt buộc nếu checkbox được tích, >= 0.
  - Hiển thị lỗi bằng `mat-error` (VD: "Giá trị tùy chỉnh không hợp lệ").
  - Disable nút "Lưu" nếu bất kỳ chi phí được chọn nào có giá trị tùy chỉnh không hợp lệ.
- **Dữ liệu đầu vào**:
  - Nhận `MAT_DIALOG_DATA` là một object chứa:
    - `items`: Mảng `ImportAdditionalCost[]` từ `mockImportAdditionalCosts`.
    - `current`: Mảng `ImportAdditionalCost[]` từ `GoodsReceipt.additionalCosts` (đã lọc theo `paidToSupplier` nếu áp dụng).
    - `subTotal`: Number (tùy chọn, để tính thuế tự động).
  - Khởi tạo bảng:
    - Hiển thị tất cả chi phí từ `items`.
    - Tích checkbox và điền giá trị tùy chỉnh cho các chi phí có trong `current` (so sánh `_id`).
- **Checkbox**:
  - Mỗi dòng có một `mat-checkbox` để chọn chi phí.
  - Tích mặc định nếu chi phí có trong `current`.
  - Chỉ các chi phí được tích checkbox mới được emit khi lưu.
- **Giá trị tùy chỉnh**:
  - Trường input number (`customValue`) cho phép chỉnh sửa `costValue.value`.
  - Mặc định:
    - Nếu chi phí có trong `current`, lấy `costValue.value` từ `current`.
    - Nếu không, lấy `costValue.value` từ `items`.
  - Khi người dùng chỉnh sửa, lưu giá trị mới để emit.
- **Thuế**:
  - Nếu chi phí có `tax`, hiển thị readonly trong cột "Thuế".
  - Khi người dùng chỉnh sửa "Giá trị tùy chỉnh" (`customValue`):
    - Nếu `costValue.type = 'fixed'` và `tax` có `rate`, tự động cập nhật `tax.amount = customValue * tax.rate / 100`.
    - Nếu `costValue.type = 'percentage'`, giữ nguyên `tax.amount` hoặc yêu cầu người dùng nhập (nếu cần).
  - Nếu `subTotal` có sẵn, hiển thị text nhỏ dưới bảng: "Tổng tiền hàng: X VND" (X là `subTotal`).
- **Tạo mới chi phí**:
  - Khi nhấn nút "Tạo mới chi phí", mở **AdditionalCostDialog** (không truyền dữ liệu đầu vào).
  - Sau khi **AdditionalCostDialog** đóng và trả về một `ImportAdditionalCost`:
    - Thêm chi phí mới vào `dataSource` của bảng (không ảnh hưởng `items` gốc).
    - Tự động tích checkbox cho chi phí mới (đánh dấu là được chọn).
    - Giá trị tùy chỉnh (`customValue`) bằng `costValue.value` của chi phí mới.
    - Cuộn bảng đến dòng mới để người dùng thấy ngay.
- **Emit dữ liệu**:
  - Khi nhấn "Lưu", emit một mảng `ImportAdditionalCost[]` chỉ chứa các chi phí được chọn (checkbox tích), với:
    - `_id`, `name`, `costValue.type`, `paidToSupplier`, `allocateToItems`: Giữ nguyên từ `items` hoặc chi phí mới.
    - `costValue.value`: Lấy từ "Giá trị tùy chỉnh".
    - `tax.amount`: Cập nhật nếu `fixed` và có `tax.rate`, hoặc giữ nguyên.
  - Đóng dialog bằng `MatDialogRef.close(data)`.
- **UX**:
  - Bảng có thanh cuộn ngang nếu quá nhiều cột trên tablet.
  - Hiển thị tooltip cho cột "Thuế": "Thuế tự động cập nhật khi thay đổi giá trị cố định".
  - Nút "Tạo mới chi phí" nổi bật với icon `add`, hover đổi màu.
  - Các chi phí trong `current` được đánh dấu rõ ràng bằng checkbox tích sẵn.
  - Nếu bảng dài, thêm nút sticky ở góc dưới bên phải để cuộn lên đầu.



---

## **Dialog: QualityCheckRejectDialog**

### **Mô tả**

Dialog để **tạo mới** hoặc **chỉnh sửa** một sản phẩm bị từ chối trong kiểm tra chất lượng (`RejectedItem`), điền form với dữ liệu từ `MAT_DIALOG_DATA.rejectedItem` nếu chỉnh sửa, đảm bảo số lượng không vượt quá `quantityReceived` của sản phẩm được chọn.

### **Giao diện**

- **Tiêu đề**:
  - "Thêm sản phẩm bị từ chối" nếu `MAT_DIALOG_DATA.rejectedItem` là `undefined` (tạo mới).
  - "Chỉnh sửa sản phẩm bị từ chối" nếu `MAT_DIALOG_DATA.rejectedItem` tồn tại (chỉnh sửa).
- **Form**:
  - **Sản phẩm**:
    - `mat-select` hiển thị danh sách sản phẩm từ `MAT_DIALOG_DATA.items`.
    - Hiển thị mỗi sản phẩm với: `product.name + " (SKU: " + product.sku + ")"` (VD: "Áo thun nam (SKU: ATN123)").
    - Bắt buộc.
    - Nếu chỉnh sửa, chọn mặc định sản phẩm tương ứng với `rejectedItem.itemId`.
    - Disable nếu chỉnh sửa (để ngăn thay đổi sản phẩm).
  - **Số lượng**:
    - `mat-form-field` với input number.
    - Placeholder: "Số lượng bị từ chối".
    - Bắt buộc, tối thiểu 1, tối đa bằng `quantityReceived` của sản phẩm được chọn.
    - Nếu chỉnh sửa, điền giá trị từ `rejectedItem.quantity`.
  - **Lý do**:
    - `mat-form-field` với textarea.
    - Placeholder: "Nhập lý do từ chối (VD: Hàng hỏng)".
    - Bắt buộc, tối đa 500 ký tự.
    - Nếu chỉnh sửa, điền giá trị từ `rejectedItem.reason`.
    - Textarea có chiều cao cố định (100px).
- **Thông tin bổ sung**:
  - Khi chọn sản phẩm (hoặc khi mở dialog để chỉnh sửa), hiển thị text nhỏ dưới ô "Số lượng":
    - "Số lượng nhận: X" (X là `quantityReceived` của sản phẩm được chọn, lấy từ `MAT_DIALOG_DATA.items`).
- **Nút**:
  - **Hủy**: `mat-button`, đóng dialog mà không emit dữ liệu.
  - **Lưu**: `mat-raised-button` màu primary, emit object `RejectedItem` và đóng dialog.
  - Vị trí: Góc dưới bên phải, nút "Lưu" disable nếu form không hợp lệ.

### **Logic**

- **Validation**:
  - **Sản phẩm**: Bắt buộc.
  - **Số lượng**: Bắt buộc, 1 ≤ giá trị ≤ `quantityReceived` của sản phẩm được chọn.
  - **Lý do**: Bắt buộc, không để trống, tối đa 500 ký tự.
  - Hiển thị lỗi bằng `mat-error`:
    - "Vui lòng chọn sản phẩm" nếu không chọn.
    - "Số lượng phải từ 1 đến X" (X là `quantityReceived`) nếu vượt giới hạn.
    - "Vui lòng nhập lý do" hoặc "Lý do tối đa 500 ký tự" nếu không hợp lệ.
  - Disable nút "Lưu" nếu form không hợp lệ (`form.invalid`).
- **Dữ liệu đầu vào**:
  - Nhận `MAT_DIALOG_DATA` là một object chứa:
    - `items`: Mảng `GoodsReceiptItem[]` để điền danh sách sản phẩm vào `mat-select`.
    - `rejectedItem`: Object `RejectedItem` (`itemId`, `quantity`, `reason`) nếu chỉnh sửa, hoặc `undefined` nếu tạo mới.
  - Nếu `rejectedItem` tồn tại, điền form với:
    - `itemId`: Chọn sản phẩm trong `mat-select` tương ứng với `rejectedItem.itemId`.
    - `quantity`: `rejectedItem.quantity`.
    - `reason`: `rejectedItem.reason`.
    - Disable ô "Sản phẩm" để ngăn thay đổi sản phẩm.
  - Nếu `rejectedItem` là `undefined`, để form trống:
    - `mat-select` không chọn sản phẩm.
    - `quantity` và `reason` trống.
- **Emit dữ liệu**:
  - Khi nhấn "Lưu", emit một object `RejectedItem`:
    - `itemId`: ID của sản phẩm được chọn từ `mat-select` (lấy từ `GoodsReceiptItem._id`).
    - `quantity`: Giá trị từ input "Số lượng".
    - `reason`: Giá trị từ textarea "Lý do".
  - Đóng dialog bằng `MatDialogRef.close(data)`.
- **UX**:
  - Form bố trí dọc, các trường cách nhau 16px.
  - Text bổ sung dưới ô "Số lượng" (`quantityReceived`) hiển thị bằng font nhỏ (12px), màu xám.
  - Textarea có chiều cao cố định (100px), hỗ trợ cuộn nếu nội dung dài.
  - Khi chọn sản phẩm, cập nhật ngay text bổ sung và validation cho ô "Số lượng".
  - Nút "Lưu" nổi bật, căn phải, hover đổi màu nhẹ.
  - Tooltip trên ô "Số lượng": "Số lượng bị từ chối không được vượt quá số lượng nhận".
  - Khi mở dialog để chỉnh sửa, tự động focus vào ô "Số lượng".


