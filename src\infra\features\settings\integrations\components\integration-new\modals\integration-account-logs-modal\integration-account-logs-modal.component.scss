/* Integration Account Logs Modal Styles */

// Modal header styling
.modal-header {
  h4 {
    color: #333;
    font-weight: 600;
  }
  
  p {
    color: #6c757d;
    font-size: 0.875rem;
  }
  
  mat-icon {
    font-size: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

// Modal content styling
.modal-content-logs {
  min-height: 400px;
  max-height: 600px;
}

// Logs container styling
.logs-container {
  .logs-viewport {
    height: 500px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #fafafa;
  }
  
  .log-entry {
    padding: 0.75rem 1rem;
    background-color: white;
    margin-bottom: 1px;
    
    &:hover {
      background-color: #f8f9fa;
    }
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .log-icon {
    .small-icon {
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }
  }
  
  .log-content {
    .log-time {
      font-family: 'Courier New', monospace;
      font-size: 0.75rem;
      color: #6c757d;
    }
    
    .log-text {
      font-size: 0.875rem;
      line-height: 1.4;
      word-wrap: break-word;
    }
  }
}

// Log type colors
.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-info {
  color: #17a2b8 !important;
}

// Modal footer styling
.modal-footer {
  .logs-count,
  .activity-status {
    .small-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      vertical-align: middle;
    }
  }
  
  .logs-count,
  .activity-status {
    font-size: 0.8rem;
  }
}

// Empty state styling
.text-center {
  .display-4 {
    font-size: 3rem;
    opacity: 0.5;
  }
  
  h5 {
    margin-bottom: 0.5rem;
  }
  
  p {
    font-size: 0.875rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-header {
    h4 {
      font-size: 1.1rem;
    }
    
    p {
      font-size: 0.8rem;
    }
  }
  
  .modal-content-logs {
    min-height: 300px;
    max-height: 500px;
  }
  
  .logs-container {
    .logs-viewport {
      height: 350px;
    }
    
    .log-entry {
      padding: 0.5rem 0.75rem;
    }
    
    .log-content {
      .log-time {
        font-size: 0.7rem;
      }
      
      .log-text {
        font-size: 0.8rem;
      }
    }
  }
  
  .modal-footer {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 0.5rem;
    
    .logs-count,
    .activity-status {
      width: 100%;
    }
  }
}

// Dark mode support (optional)
@media (prefers-color-scheme: dark) {
  .modal-header {
    h4 {
      color: #f8f9fa;
    }
    
    p {
      color: #adb5bd;
    }
  }
  
  .logs-container {
    .logs-viewport {
      background-color: #343a40;
      border-color: #495057;
    }
    
    .log-entry {
      background-color: #495057;
      border-color: #6c757d;
      
      &:hover {
        background-color: #5a6268;
      }
    }
    
    .log-content {
      .log-time {
        color: #adb5bd;
      }
    }
  }
}
