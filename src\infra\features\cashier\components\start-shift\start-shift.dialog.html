<div class="p-4">
  <div class="close cursor-pointer" (click)="close()">
    <span class="material-symbols-outlined">
      close
      </span>
  </div>

  <div class="mb-4 fw-500">
    Ch<PERSON><PERSON> nhân viên thu ngân làm trong ca:
  </div>

  @for (employee of data; track employee) {
    <mat-checkbox
      class="select-employee mb-4"
      [value]="employee._id"
      [checked]="false"
      (change)='check($event)'
      >
      {{employee.name}}
    </mat-checkbox>
  }

  <div
    [style.visibility]="checkedIds.length > 0 ? 'visible' : 'hidden'"
    >
    <button
      class="submit btn btn-primary mt-4 d-flex justify-content-center align-items-center"
      (click)="submit()"
      [disabled]="isSubmitting"
      >
      @if (!isSubmitting) {
        Bắt đầu ca làm
      } @else {
        <div class="dot-pulse"></div>
      }
    </button>
  </div>
</div>
