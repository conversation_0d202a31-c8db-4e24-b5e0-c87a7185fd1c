<!-- Order Summary Component -->
<div class="order-summary-container">
  <div class="order-summary-content">
    <!-- Block 1: Chi tiết thanh toán -->
    <div class="payment-details-block">
      <app-payment-details
        [order]="order"
        (basicInfoUpdated)="onBasicInfoUpdated($event)"
        (paymentUpdated)="onPaymentUpdated($event)"
        (summaryUpdated)="onSummaryUpdated($event)">
      </app-payment-details>

      <app-summary-rows
        [order]="order"
        [summary]="summary"
        (summaryUpdated)="onSummaryUpdated($event)">
      </app-summary-rows>
    </div>

    <!-- Block 2: Nút thanh toán -->
    <div class="payment-button-block">
      <app-payment-button
        [totalAmount]="summary.finalAmount"
        [isValid]="isOrderValid"
        (submitClicked)="onSubmitClicked()">
      </app-payment-button>
    </div>
  </div>
</div>
