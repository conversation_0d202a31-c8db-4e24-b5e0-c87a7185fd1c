import { ConnectedAccount } from '../../features/settings/integrations/models/view/integration.models';

/**
 * <PERSON><PERSON> liệu mẫu cho các tài khoản đã kết nối với các nền tảng tích hợp
 * <PERSON><PERSON> gồm các nền tảng: ShopeeFood, GrabFood, Facebook, Zalo, GHTK, Shopee
 */

// Tài khoản ShopeeFood - Nền tảng giao đồ ăn của Shopee
export const mockShopeeFood: ConnectedAccount[] = [
  {
    _id: 'sf_001',
    platformId: 'shopeefood',
    name: '<PERSON><PERSON><PERSON> Tấm Sài Gòn',
    description: 'Tài khoản ShopeeFood chính cho quán cơm tấm',
    logo: '/assets/images/brands/shopeefood.svg',
    status: 'active',
    connectedAt: new Date('2024-01-15T08:30:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z') // <PERSON><PERSON><PERSON> ngắt kết nối
  },
  {
    _id: 'sf_002',
    platformId: 'shopeefood',
    name: '<PERSON><PERSON><PERSON>àng Hải Sản Tươ<PERSON>',
    description: '<PERSON><PERSON><PERSON> khoản ShopeeFood cho nhà hàng hải sản',
    logo: '/assets/images/brands/shopeefood.svg',
    status: 'pending',
    connectedAt: new Date('2024-12-20T14:15:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z')
  }
];

// Tài khoản GrabFood - Nền tảng giao đồ ăn của Grab
export const mockGrabFood: ConnectedAccount[] = [
  {
    _id: 'gf_001',
    platformId: 'grabfood',
    name: 'Phở Hà Nội 24h',
    description: 'Tài khoản GrabFood cho quán phở truyền thống',
    logo: '/assets/images/brands/grab.svg',
    status: 'active',
    connectedAt: new Date('2024-02-10T09:45:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z')
  },
  {
    _id: 'gf_002',
    platformId: 'grabfood',
    name: 'Bánh Mì Thịt Nướng',
    description: 'Tài khoản GrabFood cho cửa hàng bánh mì',
    logo: '/assets/images/brands/grab.svg',
    status: 'inactive',
    connectedAt: new Date('2024-01-05T16:20:00Z'),
    disconnectedAt: new Date('2024-11-30T10:00:00Z') // Đã ngắt kết nối
  }
];

// Tài khoản Facebook - Nền tảng mạng xã hội và bán hàng
export const mockFacebook: ConnectedAccount[] = [
  {
    _id: 'fb_001',
    platformId: 'facebook',
    name: 'Fanpage Thời Trang Trendy',
    description: 'Fanpage Facebook chính cho shop thời trang',
    logo: '/assets/images/brands/facebook.svg',
    status: 'active',
    connectedAt: new Date('2023-11-20T11:30:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z')
  },
  {
    _id: 'fb_002',
    platformId: 'facebook',
    name: 'Cửa Hàng Điện Tử ABC',
    description: 'Fanpage Facebook cho cửa hàng điện tử',
    logo: '/assets/images/brands/facebook.svg',
    status: 'active',
    connectedAt: new Date('2024-03-12T13:45:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z')
  }
];

// Tài khoản Zalo - Nền tảng nhắn tin và bán hàng Việt Nam
export const mockZalo: ConnectedAccount[] = [
  {
    _id: 'zl_001',
    platformId: 'zalo',
    name: 'Zalo OA - Mỹ Phẩm Natural',
    description: 'Tài khoản Zalo Official Account cho shop mỹ phẩm',
    logo: '/assets/images/brands/zalo.svg',
    status: 'active',
    connectedAt: new Date('2024-01-08T10:15:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z')
  },
  {
    _id: 'zl_002',
    platformId: 'zalo',
    name: 'Zalo OA - Giày Dép Thể Thao',
    description: 'Tài khoản Zalo OA cho cửa hàng giày dép',
    logo: '/assets/images/brands/zalo.svg',
    status: 'pending',
    connectedAt: new Date('2024-12-25T15:30:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z')
  }
];

// Tài khoản Giao Hàng Tiết Kiệm - Đối tác vận chuyển
export const mockGiaoHangTietKiem: ConnectedAccount[] = [
  {
    _id: 'ghtk_001',
    platformId: 'giaohangtietkiem',
    name: 'GHTK - Chi Nhánh HCM',
    description: 'Tài khoản GHTK cho khu vực Hồ Chí Minh',
    logo: '/assets/images/brands/giaohangtietkiem.svg',
    status: 'active',
    connectedAt: new Date('2023-12-01T08:00:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z')
  },
  {
    _id: 'ghtk_002',
    platformId: 'giaohangtietkiem',
    name: 'GHTK - Chi Nhánh Hà Nội',
    description: 'Tài khoản GHTK cho khu vực Hà Nội',
    logo: '/assets/images/brands/giaohangtietkiem.svg',
    status: 'active',
    connectedAt: new Date('2024-02-15T09:30:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z')
  }
];

// Tài khoản Shopee - Sàn thương mại điện tử
export const mockShopee: ConnectedAccount[] = [
  {
    _id: 'sp_001',
    platformId: 'shopee',
    name: 'Shop Đồ Gia Dụng Tiện Lợi',
    description: 'Tài khoản Shopee cho shop đồ gia dụng',
    logo: '/assets/images/brands/shopee.svg',
    status: 'active',
    connectedAt: new Date('2023-10-10T12:00:00Z'),
    disconnectedAt: new Date('1970-01-01T00:00:00Z')
  },
  {
    _id: 'sp_002',
    platformId: 'shopee',
    name: 'Cửa Hàng Phụ Kiện Điện Thoại',
    description: 'Tài khoản Shopee cho shop phụ kiện điện thoại',
    logo: '/assets/images/brands/shopee.svg',
    status: 'inactive',
    connectedAt: new Date('2024-01-20T14:45:00Z'),
    disconnectedAt: new Date('2024-10-15T16:30:00Z') // Đã tạm ngưng
  }
];

/**
 * Tổng hợp tất cả các tài khoản đã kết nối
 * Sử dụng để hiển thị danh sách tổng quan các kết nối
 */
export const mockAllConnectedAccounts: ConnectedAccount[] = [
  ...mockShopeeFood,
  ...mockGrabFood,
  ...mockFacebook,
  ...mockZalo,
  ...mockGiaoHangTietKiem,
  ...mockShopee
];